package com.yunqu.cc.advice.dao;



import java.sql.SQLException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.yq.busi.common.util.PrivacyUtil;
import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.model.DeptModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.user.DeptMgr;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.advice.base.AppDaoContext;
import com.yunqu.cc.advice.utils.CommLogger;


@WebObject(name="msgInfo")
public class MsgInfoDao extends AppDaoContext {
	private Logger logger = CommLogger.logger;

	/**
	 * 建议页面list
	 * @return
	 */
	@WebControl(name="Userlist",type=Types.LIST)
	public  JSONObject list(){
		UserModel user = UserUtil.getUser(request);
		  String changePage=this.param.getString("changeIndex");
		  if(!"".equals(changePage)&&null!=changePage){
			  this.param.put("pageIndex", changePage);

		  }
		String AUDITING = param.getString("AUDITING");//判断是否是审核页面  1:是
		String auntype = param.getString("auntype");//判断初审 复审
		EasySQL easySQL = null;
		if("1".equals(AUDITING)){
			if("review".equals(auntype)){
				easySQL = this.getEasySQL("select case when t.message_state='1' then '4' "
						+ "when t.message_state='4' then '3' "
						+ "when t.message_state='5' then '1' "
						+ "when t.message_state='6' then '2' "
						+ "else '4' end as ORDERSTATE,");
			}else{
				easySQL = this.getEasySQL("select case when t.message_state='1' then '1' "
						+ "when t.message_state='4' then '2' "
						+ "when t.message_state='5' then '3' "
						+ "else '4' end as ORDERSTATE,");
			}
			easySQL.append("t.MESSAGE_STATE,t.COMPLETE_STATE,t.MESSAGE_ID,t.REGION,t.MESSAGE_TYPE_ID,t.SESSIONID,t.SESSION_ID,t.CREATE_ACC,t.CREATE_NAME,t.CREATE_TIME"
					+ ",t.SOURCE,t.MESSAGE_CONTENT,t.SUBJECT_NAME,t.CATEGORY,t.BRAND,t.PRODUCT_MODEL,t.BUY_DATE,t.BUY_UNIT_NAME,t.CUSTOMER_NAME,t.TELEPHONE,t.MOBILE_TELEPHONE"
					+ ",t.OFFICE_TELEPHONE,t.ADDRESS,t.PRODUCT_CONTENT,t.CSS_FLOW_ID,t.CSS_FLOW_STATE,t.CSS_UPDATE_NAME,t.CSS_UPDATE_TIME,t.CSS_ACCEPT,t.CSS_RESPONSIBLE_BODY"
					+ ",t.COMPLETE_DATE,t.FLOW_RECORD,t.FLOW_DONE_TIME"
					+ ",t.CSS_ORG_NAME,t.REMARKS,d1.dept_name as DEPT_NAME1,d2.dept_name as DEPT_NAME2,tr1.name as TYPE_NAME,tr2.name as PROJECT_NAME "
					+ ",t2.ID AS VID,t2.CALLER,t2.CALLED,t2.ANSWER_TIME,t2.END_TIME ,listagg(t4.ID,',') within GROUP (ORDER BY t4.ID) as QUESTION_ORDER_NO "
					+ "from MESSAGE_INFO t "
					+ "left join  C_PF_V_CALL_RECORD t2 on t.session_id=t2.SESSION_ID and t.create_acc = t2.AGENT_ACC "
					+ "left join  MARS.EASI_DEPT d1 on d1.dept_code= t.group_id "
					+ "left join  MARS.EASI_DEPT d2 on d2.dept_code= t.REGION "
					+ "left join C_CF_COMMON_TREE tr1 on tr1.id = t.MESSAGE_TYPE_ID "
					+ "left join C_CF_COMMON_TREE tr2 on tr2.id = t.MESSAGE_PROJECT_ID "
					+ "left join CC_VOC_INTELLIGENT_TASK t3 on t.MESSAGE_ID = t3.PROPOSAL_NO "
					+ "left join CC_VOC_QUESTION_ORDER t4 on t3.id = t4.TASK_ID "
					+ "where ");
			if(StringUtils.isNotEmpty(param.getString("MESSAGE_STATE"))){
				easySQL.append(param.getString("MESSAGE_STATE"), " t.MESSAGE_STATE = ?");
			}else{
				easySQL.append(" t.MESSAGE_STATE in (1,2,3,4,5,6) ");
			}
		}else{
			easySQL = this.getEasySQL("select t.*,d1.dept_name as DEPT_NAME1,d2.dept_name as DEPT_NAME2,tr1.name as TYPE_NAME,tr2.name as PROJECT_NAME "
					+ ",t2.ID AS VID,t2.CALLER,t2.CALLED,t2.ANSWER_TIME,t2.END_TIME "
					+ "from MESSAGE_INFO t "
					+ "left join  C_PF_V_CALL_RECORD t2 on t.session_id=t2.SESSION_ID and t.create_acc = t2.AGENT_ACC "
					+ "left join  MARS.EASI_DEPT d1 on d1.dept_code= t.group_id "
					+ "left join  MARS.EASI_DEPT d2 on d2.dept_code= t.REGION "
					+ "left join C_CF_COMMON_TREE tr1 on tr1.id = t.MESSAGE_TYPE_ID "
					+ "left join C_CF_COMMON_TREE tr2 on tr2.id = t.MESSAGE_PROJECT_ID "
					+ "where t.CREATE_ACC='"+user.getUserAcc()+"'");
			if(StringUtils.isNotEmpty(param.getString("MESSAGE_STATE"))){
				easySQL.append(param.getString("MESSAGE_STATE"), " and t.MESSAGE_STATE = ?");
			}
		}
		
		easySQL.appendRLike(param.getString("MESSAGE_ID"), "and t.MESSAGE_ID like ?");
		easySQL.appendRLike(param.getString("CREATE_NAME"), "and t.CREATE_NAME like ?");
		easySQL.appendRLike(param.getString("CREATE_ACC"), "and t.CREATE_ACC like ?");
		String TYPE = param.getString("TYPE");
		easySQL.append(TYPE, " and t.TYPE = ?");
		
		easySQL.append(param.getString("CREATE_TIME_SATET"), " and t.CREATE_TIME >= ?");
		easySQL.append(param.getString("CREATE_TIME_END"), " and t.CREATE_TIME <= '"+param.getString("CREATE_TIME_END")+" 23:59:59'");
		if(StringUtils.isNotEmpty(param.getString("MESSAGE_TYPE_ID"))){
			easySQL.append(param.getString("MESSAGE_TYPE_ID"), " and t.MESSAGE_TYPE_ID = ?");
		}
		easySQL.append(param.getString("MESSAGE_INFO.BRAND"), " and t.BRAND = ?");
		easySQL.append(param.getString("MESSAGE_INFO.SUBJECT"), " and t.SUBJECT = ?");
		
		easySQL.append(param.getString("COMPLETE_STATE"),"AND T.COMPLETE_STATE = ?");
		easySQL.append(param.getString("MESSAGE_PROJECT_ID"), " and t.MESSAGE_PROJECT_ID = ?");
		easySQL.appendRLike(param.getString("PRODUCT_TYPE"), " and t.PRODUCT_TYPE like ?");
		easySQL.append(param.getString("SOURCE"), " and t.SOURCE = ?");
		easySQL.append(12, " AND NVL(t.SOURCE,-1) <> ?");
		easySQL.appendLike(param.getString("MESSAGE_CONTENT"), " and t.MESSAGE_CONTENT like ?");
		easySQL.appendRLike(param.getString("TELEPHONE"), " and t.TELEPHONE like ?");
		easySQL.appendRLike(param.getString("MOBILE_TELEPHONE"), " and t.MOBILE_TELEPHONE like ?");
		easySQL.appendRLike(param.getString("OFFICE_TELEPHONE"), " and t.OFFICE_TELEPHONE like ?");
		easySQL.appendRLike(param.getString("PRODUCT_MODEL"), " and t.PRODUCT_MODEL like ?");
		/*easySQL.append(param.getString("BUY_DATE_SATET"), " and t.BUY_DATE >= ?");
		easySQL.append(param.getString("BUY_DATE_END"), " and t.BUY_DATE <= ?");*/
		easySQL.append(param.getString("BUY_DATE_SATET"), " and t.message_id in (select au.msg_id from message_finding_of_audit au where au.create_time >= ? )  and t.MESSAGE_STATE in(2,3)");
		easySQL.append(param.getString("BUY_DATE_END"), " and t.message_id in (select au.msg_id from message_finding_of_audit au where au.create_time <= '"+param.getString("BUY_DATE_END")+" 23:59:59' ) and t.MESSAGE_STATE in(2,3)");
		easySQL.appendRLike(param.getString("DEPARTMENT"), " and t.DEPARTMENT like ?");
		JSONArray array = new JSONArray();
		String region = param.getString("REGION");
		if(StringUtils.isNotBlank(region)&&region.startsWith("[")&&region.endsWith("]")) {
			array = JSON.parseArray(region);
		}else if(StringUtils.isNotBlank(region)){
			array.add(region);
		}
		String ids = "";
		if(array!=null&&array.size()>0) {
			for(int i=0;i<array.size();i++) {
				ids += "'" + array.getString(i) + "',";
			}
			ids = ids.substring(0,ids.length()-1);
			easySQL.append(" and t.REGION in (" + ids + ")");
		}
		JSONArray groupIds = new JSONArray();
		String groupId = param.getString("GROUP_ID");
		if(StringUtils.isNotBlank(groupId)&&groupId.startsWith("[")&&groupId.endsWith("]")) {
			groupIds = JSON.parseArray(groupId);
		}else if(StringUtils.isNotBlank(groupId)){
			groupIds.add(groupId);
		}
		String ids2 = "";
		if(groupIds!=null&&groupIds.size()>0) {
			for(int i=0;i<groupIds.size();i++) {
				ids2 += "'" + groupIds.getString(i) + "',";
			}
			ids2 = ids2.substring(0,ids2.length()-1);
			easySQL.append(" and t.GROUP_ID in (" + ids2 + ")");
		}
		
		easySQL.append(param.getString("MESSAGE_INFO.CATEGORY"), " and t.CATEGORY = ?");
		
		
		easySQL.append(param.get("MIP_CODE"), " and MIP_CODE = ?");
		easySQL.append(param.get("CSS_FLOW_STATE"), " and CSS_FLOW_STATE = ?");
		easySQL.append(param.get("CSS_FLOW_ID"), " and CSS_FLOW_ID = ?");
		easySQL.append(param.get("CSS_EFFECTIVENESS"), " and CSS_EFFECTIVENESS = ?");
		easySQL.append(param.get("CSS_ACCEPT"), " and CSS_ACCEPT = ?");
		if(StringUtils.equals(param.getString("IS_CREATE_QUESTION_ORDER"),"Y")){
			easySQL.append(" and t4.ID is not null");
		}else if(StringUtils.equals(param.getString("IS_CREATE_QUESTION_ORDER"),"N")){
			easySQL.append(" and t4.ID is null");
		}
		easySQL.append(param.getString("QUESTION_ORDER")," and t4.ID =?");
		easySQL.appendLike(param.get("REMARKS"), " and REMARKS like ?");
		easySQL.append(param.getString("CSS_FEEDBACK_TIME_SATET"), " and t.CSS_FEEDBACK_TIME >= '"+param.getString("CSS_FEEDBACK_TIME_SATET")+" 00:00:00'");
		easySQL.append(param.getString("CSS_FEEDBACK_TIME_END"), " and t.CSS_FEEDBACK_TIME <= '"+param.getString("CSS_FEEDBACK_TIME_END")+" 23:59:59'");

		if("1".equals(AUDITING)){
			easySQL.append(" group by t.MESSAGE_STATE,t.COMPLETE_STATE,t.MESSAGE_ID,t.REGION,t.MESSAGE_TYPE_ID,t.SESSIONID,t.SESSION_ID,t.CREATE_ACC,t.CREATE_NAME,t.CREATE_TIME," +
					"t.SOURCE,t.MESSAGE_CONTENT,t.SUBJECT_NAME,t.CATEGORY,t.BRAND,t.PRODUCT_MODEL,t.BUY_DATE,t.BUY_UNIT_NAME,t.CUSTOMER_NAME," +
					"t.TELEPHONE,t.MOBILE_TELEPHONE,t.OFFICE_TELEPHONE,t.ADDRESS,t.PRODUCT_CONTENT,t.CSS_FLOW_ID,t.CSS_FLOW_STATE,t.CSS_UPDATE_NAME," +
					"t.CSS_UPDATE_TIME,t.CSS_ACCEPT,t.CSS_RESPONSIBLE_BODY,t.CSS_ORG_NAME,t.REMARKS," +
					"t.COMPLETE_DATE,t.FLOW_RECORD,t.FLOW_DONE_TIME,"+
					"d1.dept_name,d2.dept_name,tr1.name,tr2.name,t2.ID,t2.CALLER,t2.CALLED,t2.ANSWER_TIME,t2.END_TIME ");

			if("1".equals(TYPE)){
				easySQL.append(" order by ORDERSTATE,t.CREATE_TIME ");
				
			}else{
				easySQL.append(" order by ORDERSTATE,t.CREATE_TIME desc");
			}
		}else{
			if("1".equals(TYPE)){
				easySQL.append(" order by t.CREATE_TIME ");
				
			}else{
				easySQL.append(" order by t.CREATE_TIME desc");
			}
		}
		logger.info("用户管理sql:"+easySQL.getSQL()+">>param:"+JSON.toJSONString(easySQL.getParams()));
		JSONObject obj = this.queryForPageList(easySQL.getSQL(), easySQL.getParams(),null);
		JSONObject str = new JSONObject().fluentPut("CUSTOMER_NAME", PrivacyUtil.userName).fluentPut("TELEPHONE",PrivacyUtil.phone).fluentPut("MOBILE_TELEPHONE",PrivacyUtil.phone)
				.fluentPut("OFFICE_TELEPHONE",PrivacyUtil.phone).fluentPut("ADDRESS",PrivacyUtil.addr);
		obj = PrivacyUtil.desensitization(obj,str);
		String count = getCount(easySQL);
		obj.put("count", count);
		return obj;
	}
	
	/**
	 * 获取建议单行数据
	 * @return
	 */
	@WebControl(name="getEnt",type=Types.RECORD)
	public  JSONObject record(){
		UserModel user = OpenApiUserUtil.getUser(request);
		String pk=param.getString("MSGID");
		EasySQL easySQL = this.getEasySQL("select * from MESSAGE_INFO where MESSAGE_ID ='"+pk+"'");
		JSONObject record = queryForRecord(easySQL.getSQL(),easySQL.getParams(),null);
		JSONObject MESSAGE_INFO = record.getJSONObject("data");
		String MESSAGE_ID= (String)MESSAGE_INFO.get("MESSAGE_ID");
		String MESSAGE_STATE= (String)MESSAGE_INFO.get("MESSAGE_STATE");//当前状态判断是否为暂存
		String REGION= (String)MESSAGE_INFO.get("REGION");
		//附件ID
		logger.info("MESSAGE_INFO:"+MESSAGE_INFO.toJSONString());
		String attBusiId= (String)MESSAGE_INFO.get("ATT_BUSI_ID");
		if(StringUtils.isBlank(attBusiId)){
			//不存在附件随机生成一个附件ID
			attBusiId = RandomKit.randomStr();
		}
		MESSAGE_INFO.put("ATT_BUSI_ID",attBusiId);
		String userAcc = user.getUserAcc();
		if(StringUtils.isEmpty(MESSAGE_ID)||("2".equals(MESSAGE_STATE)&&StringUtils.isEmpty(REGION))){//新增或者 暂存状态未选择区域
			EasyRow EASI_DEPT;
			try {
				EASI_DEPT = this.getQuery().queryForRow("select d.p_dept_code,d.dept_code from mars.EASI_DEPT d "
						+ "left join mars.EASI_DEPT_USER u on u.dept_id=d.dept_id "
						+ "left join mars.Easi_User_Login l on l.user_id = u.user_id "
						+ "where l.user_acct='"+userAcc+"'", null);
				if(EASI_DEPT!=null){
					MESSAGE_INFO.put("REGION", EASI_DEPT.getColumnValue("P_DEPT_CODE"));
					MESSAGE_INFO.put("GROUP_ID", EASI_DEPT.getColumnValue("DEPT_CODE"));
				}
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		JSONObject result = record.getJSONObject("data");
		getLogger().info("result---->:{}"+result);
		result.remove("CREATE_NAME");
		result.remove("CREATE_ACC");
		result.put("CREATE_NAME","***");
		result.put("CREATE_ACC","***");
		record.put("data",result);
		getLogger().info("result---->end:{}"+result);
		getLogger().info("record:---->end:{}"+record);
		return record;
	}
	/**
	 * 获取审核记录
	 * @return
	 */
	@WebControl(name="getMfoa",type=Types.LIST)
	public  JSONObject MFOArecord(){
		String pk=param.getString("MSGID");
		EasySQL easySQL = this.getEasySQL("select * from MESSAGE_FINDING_OF_AUDIT where MSG_ID='"+pk+"' ORDER BY CREATE_TIME");
		JSONObject queryForPageList = this.queryForPageList(easySQL.getSQL(), easySQL.getParams(),null);
		return queryForPageList;
	}
	/**
	 * 获取第四层组织架构
	 * @return
	 */
	@WebControl(name="getDept4",type=Types.RECORD)
	public JSONObject getDept4(){
		List<DeptModel> ls = DeptMgr.getDeptByLevel(null, 4, true);
		Map<String, Object> m=new  HashMap<String, Object>();
		JSONObject resultJson = new JSONObject();
		for(DeptModel row:ls){
			m.put(row.getDeptCode(), row.getDeptName());
		}
		resultJson.put("data", m);//001001001002
		
		return   resultJson;

	}
	
	/**
	 * 获取第五层组织架构
	 * @return
	 */
	@WebControl(name="getDept5",type=Types.DICT)
	public JSONObject getDept5(){
		JSONArray array = param.getJSONArray("deptId");
		String ids = "";
		if(array!=null&&array.size()>0) {
			for(int i=0;i<array.size();i++) {
				ids += "'" + array.getString(i) + "',";
			}
			ids = ids.substring(0,ids.length()-1);
			return getDictByQuery("select d.dept_code,d.dept_name from MARS.EASI_DEPT d where d.p_dept_code in("+ids+")");
		}else {
			return getDictByQuery("select d.dept_code,d.dept_name from MARS.EASI_DEPT d where d.p_dept_code='"+ getMethodParam(0).toString()+"'");
		}
	}
	
	/**
	 * 获取建议树第二节点数据
	 * @return
	 */
	@WebControl(name="getAdviseTree",type=Types.DICT)
	public JSONObject getAdviseTree(){
		return getDictByQuery("select t.id,t.name from C_CF_COMMON_TREE t where t.p_id='84717298700579923974593'");
	}
	
	/**
	 * 根据ID获取建议树第三层节点数据
	 * @return
	 */
	@WebControl(name="getAdviseTreeByID",type=Types.DICT)
	public JSONObject getAdviseTreeByID(){
		return getDictByQuery("select t.id,t.name from C_CF_COMMON_TREE t where t.p_id='"+ getMethodParam(0).toString()+"'");

	}
	
	public String getCount(EasySQL sql){
		 this.param.put("pageIndex", "-1");
		JSONObject obj=this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return obj.getString("totalRow");
		
	}

	@WebControl(name="getClust",type=Types.LIST)
	public JSONObject getClust(){
		try{
			String messageId = param.getString("MSGID");
			EasySQL easySQL = new EasySQL();
			easySQL.append("select * from CC_VOC_ANALYSIS_RESULT_CLUST t1");
			easySQL.append("left join CC_VOC_ANALYSIS_RESULT t2 on t1.RESULT_ID = t2.ID");
			easySQL.append(messageId,"where t2.ADVICE_NO=?",false);
			return queryForPageList(easySQL.getSQL(),easySQL.getParams(),new JSONMapperImpl());
		}catch (Exception e){
			logger.error("请求参数"+JSON.toJSONString(param)+",原因"+e.getMessage());
			return EasyResult.fail(e.getMessage());
		}
	}

}
