<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>现金补偿执行情况（CSS）</title>
    <style>

    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form autocomplete="off" name="searchForm" class="form-inline" id="searchForm">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5>
                        <span class="glyphicon glyphicon-list"></span> 现金补偿执行情况（CSS）
                    </h5>
                </div>
                <hr style="margin: 5px -15px">
                <div class="form-group">
<%--                    <div class="input-group width-18">--%>
<%--                        <span class="input-group-addon" style="width:30%">主体</span>--%>
<%--                        <select class="form-control input-sm width-70" name="ORG_CODE" id="orgCode"--%>
<%--                                data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')">--%>
<%--                            <option value="" selected="selected">--请选择--</option>--%>
<%--                        </select>--%>
<%--                    </div>--%>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon">赠送人账号</span>
                        <input type="text" name="operatePerson" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px;">
                        <span class="input-group-addon" style="width: 80px;">用户号码</span>
                        <input type="text" name="customerPhone" class="form-control input-sm">
                    </div>
                    <div class="input-group width-36">
                        <span class="input-group-addon">单据状态</span>
                        <select id="APPLY_STATUS" class="form-control input-sm" name="applyStatusList"
                                data-cust-context-path="/neworder"
                                data-cust-mars="comm.sysCode('COMPENSATE_APPLY_STATUS')" data-mars-top="true" multiple="multiple">
                            <!-- <option value="">请选择</option> -->
                        </select>
                    </div>
                    <div class="input-group width-36">
                        <span class="input-group-addon">补偿项目</span>
                        <select name="compensateItems" id="COMPENSATE_MODE" class="form-control input-sm"
                                data-cust-context-path="/yq_common"
                                data-cust-mars="dict.getDictList('PEAK_END_COMPENSATE_ITEM')" data-mars-top="true">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group width-36">
                        <span class="input-group-addon">补偿原因类别</span>
                        <select name="compensateCauseType" data-rules="required" id="COMPENSATE_TYPE"
                                class="form-control input-sm" data-cust-context-path="/neworder"
                                data-cust-mars="comm.sysCode('COMPENSATE_CAUSE_TYPE')" data-mars-top="true">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm">
                        <span class="input-group-addon" style="width: 80px;">赠送时间</span>
                        <input type="text" name="pubCurrentDate" id="start_create_time"
                               class="form-control input-sm Wdate"
                               onFocus="WdatePicker({onpicked:function(){this.blur();maxTime()},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                        <span class="input-group-addon">-</span>
                        <input type="text" name="pubCurrentDateEnd" id="end_create_time"
                               class="form-control input-sm Wdate"
                               onclick="WdatePicker({minDate:'#F{$dp.$D(\'start_create_time\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                    </div>
                    <div class="input-group input-group-sm ">
                        <span class="input-group-addon">运营区域</span>
                        <input name="operatorArea" id="operatorArea" type="hidden">
                        <select class="form-control input-sm" id="areaCode" name="areaCode" data-mars="common.getDept"
                                style="width:100px;">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm ">
                        <span class="input-group-addon">坐席班组</span>
                        <input name="staffName" id="staffName" type="hidden">
                        <select class="form-control input-sm" id="agentDept" name="agentDept" style="width:100px;">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px;">
                        <span class="input-group-addon" style="width: 80px;">申请人</span>
                        <input type="text" name="man" id="man" class="form-control input-sm">
                    </div>
                    <div class="input-group input-group-sm" style="width: 200px; display: none;">
                        <input type="text" value="CASH" name="compensateItems" class="form-control input-sm">
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group input-group-sm pull-right">
                        <button type="button" class="btn btn-sm btn-default" onclick="reset()">重置</button>
                        <button type="button" class="btn btn-sm btn-default ml-10" onclick="cash.reload($.trim($('#APPLY_STATUS').val()))">搜索</button>
                        <EasyTag:res resId="peak_end_cash_compensate_admin">
                            <input type="hidden" id="peak_end_cash_compensate_admin" value="1">
                        </EasyTag:res>
                    </div>
                </div>
                <div class="ibox-content">
                    <div class="row table-responsive">
                        <table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead"
                               data-mars="peakEnd.compensateList">
                            <thead>
                            <tr>
                                <th class="text-c">序号</th>
                                <th class="text-c">补偿单号</th>
                                <th class="text-c">服务单号</th>
                                <th class="text-c">补偿项目</th>
                                <th class="text-c">补偿金额</th>
                                <th class="text-c">补偿原因类别</th>
                                <th class="text-c">补偿状态</th>
                                <th class="text-c" style="width:170px;max-width:170px;min-width:170px">补偿失败原因</th>
                                <th class="text-c">委托方</th>
                                <th class="text-c">品牌</th>
                                <th class="text-c">品类</th>
                                <th class="text-c">用户姓名</th>
                                <th class="text-c">用户电话</th>
                                <th class="text-c">服务请求大类</th>
                                <th class="text-c">服务请求小类</th>
                                <th class="text-c">申请人（首次赠送人）MIP</th>
                                <th class="text-c">申请时间</th>
                                <th class="text-c">网点</th>
                                <th class="text-c">工程师</th>
                                <th class="text-c">openid查询时间</th>
                                <th class="text-c">登录查询时间</th>
                                <th class="text-c">登录时间</th>
                                <th class="text-c">发起补偿人</th>
                                <th class="text-c">发起补偿时间</th>
                                <th class="text-c">补偿成功时间</th>
                                <th class="text-c">补偿失败时间</th>
                                <th class="text-c">取消操作人</th>
                                <th class="text-c">取消时间</th>
                                <th class="text-c">取消原因</th>
                                <th class="text-c" style="width:200px;max-width:200px;min-width:200px;">补偿说明</th>
                                <th class="text-c">操作</th>
                            </tr>
                            </thead>
                            <tbody id="dataList"></tbody>
                        </table>
                        <script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
			                    <td>{{:#index+1}}</td>
							    <td>{{:compensateOrderNo}}</td>
                                <td>{{:serviceOrderNo}}</td>
							    <td>{{dictFUN:compensateItems "PEAK_END_COMPENSATE_ITEM"}}</td>
                                <td>{{:compensateCash}}</td>
							    <td>{{sysCodeFUN:compensateCauseType "COMPENSATE_CAUSE_TYPE"}}</td>
							    <td>{{sysCodeFUN:applyStatus "COMPENSATE_APPLY_STATUS"}}</td>
                                <td style="width:170px;max-width:170px;min-width:170px;overflow: hidden;" title='{{:compensateFailCaus}}'>{{:compensateFailCaus}}</td>
                                <td>{{:clientName}}</td>
                                <td>{{:brandName}}</td>
                                <td>{{:prodName}}</td>
								<td>{{:customerName}}</td>
								<td>{{:customerPhone}}</td>
								<td>{{:serviceLargeCategoryName}}</td>
								<td>{{:serviceSmallCategoryName}}</td>
								<td>{{:applyPerson}}</td>
                                <td>{{:pubCreateDateStr}}</td>
                                <td>{{:unitName}}</td>
                                <td>{{:engineerName}}</td>
                                <td>{{:loginTimeStr}}</td>
                                <td>{{:queryRegisterTimeStr}}</td>
                                <td>{{:loginTimeStr}}</td>
                                <td>{{:pubCreatePerson}}</td>
                                <td>{{:compensateApplyTimeStr}}</td>
                                <td>{{:compensateEndSuccessTimeStr}}</td>
                                <td>{{:compensateEndPailTimeStr}}</td>
                                <td>{{:cancelOperatePerson}}</td>
                                <td>{{:cancelTimeStr}}</td>
                                <td style="width:200px;max-width:200px;min-width:200px;overflow: hidden;" title='{{:cancelCaus}}'>{{:cancelCaus}}</td>
                                <td style="width:200px;max-width:200px;min-width:200px;overflow: hidden;" title='{{:compensateExplain}}'>{{:compensateExplain}}</td>
                                
                                

                                <td>
                                {{if modifyflag==true}}
                                    <a href='javascript:cash.edit({{getJSONStr:#data}})' class='hidden adminBtn userBtn-{{:applyPerson}}'>修改号码</a>
                                {{/if}}
                                {{if cancelFlag==true}}
                                	<a href='javascript:cash.cancel({{getJSONStr:#data}})' class='hidden adminBtn userBtn-{{:applyPerson}}'>取消补偿</a>
                                {{/if}}
                                {{if secondFlag==true}}
                                      <a href='javascript:cash.apply({{getJSONStr:#data}})' class='hidden adminBtn userBtn-{{:applyPerson}}'>重新发起补偿</a>
                                {{/if}}
                                </td>
							</tr>
						{{/for}}				         

                        </script>
                    </div>
                    <div class="row paginate" id="page">
                        <jsp:include page="/pages/common/pagination.jsp">
                            <jsp:param value="10" name="pageSize"/>
                        </jsp:include>
                    </div>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
    <script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript"
            src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript">
        jQuery.namespace("cash");
        //加载时间控件
        requreLib.setplugs('wdate')

        // 多选配置
        cash.multiSetting = {
            buttonWidth: '100%',
            allSelectedText: "全部",
            nonSelectedText:"请选择",
            nSelectedText:"个被选中",
            selectAllNumber:false,
            maxHeight:350,
            includeSelectAllOption: true,
            selectAllText:'全选',
            enableFiltering: true
        };

        //搜索功能
        cash.reload = function (applyStatusList)  {
            if($("#areaCode").val()){
                $("#operatorArea").val($("#areaCode").find("option:selected").text())
            }else{
                $("#operatorArea").val("")
            }
            if($("#agentDept").val()){
                $("#staffName").val($("#agentDept").find("option:selected").text())
            }else{
                $("#staffName").val("")
            }

            // 处理多选状态值
            if(applyStatusList && applyStatusList != "") {
                // 将多选值转换为字符串拼接格式，用于后端查询
                // 保持原始的逗号分隔格式，如: "10,11,12"
                // 创建隐藏字段传递处理后的值
                // $("#searchForm").find("input[name='applyStatusList']").remove();
                // $("#searchForm").append("<input type='hidden' name='applyStatusList' value='" + applyStatusList + "'>");
            }

            $("#searchForm").searchData({
                success: function (data) {
                    console.log(data,"swarchData");
                    showbtn();
                }
            });
        }

        //根据运营区域变化获取坐席班组
        $('#areaCode').change(function () {
            var value = $(this).val();
            if (value != "") {
                value = "'" + value.replace(/,/g, "':'") + "'";
            }
            $('#agentDept').data('mars', 'common.getDeptAll("5", "' + value + '")');
            $('#agentDept').render();
        });
        //初始化
        var loginAcct = "";
        $(function () {
            var startDate = getMonthRange().start;
            var endDate = getMonthRange().end;
            // 从URL参数中获取查询条件
            var urlParams = new URLSearchParams(window.location.search);
            $("#start_create_time").val(startDate);
            $("#end_create_time").val(endDate);
            // $("#man").val(getCookie('apmUser')||'')
            if (!urlParams.has('isOver24')){
                $("input[name='operatePerson']").val(getCookie('apmUser')||'');
            }
            $("#searchForm").render({
                success: function (data) {
                    // 初始化多选功能
                    $('#APPLY_STATUS').multiselect(cash.multiSetting);

                    if(data && data["peakEnd.compensateList"]){
                        var list = data["peakEnd.compensateList"];
                        if(list!=null && list.loginAcct){
                            loginAcct = list.loginAcct;
                            showbtn();
                        }
                    }
                    // 设置状态 - 支持多选
                    if (urlParams.has('APPLY_STATUS')) {
                        var statusValues = urlParams.get('APPLY_STATUS').split(',');
                        $("#APPLY_STATUS").val(statusValues);
                        $('#APPLY_STATUS').multiselect('refresh');
                    }
                    if(urlParams.has('START_CREATE_TIME')){
                        $("#start_create_time").val(urlParams.get('START_CREATE_TIME'));
                    }
                    if(urlParams.has('END_CREATE_TIME')){
                        $("#end_create_time").val(urlParams.get('END_CREATE_TIME'));
                    }

                    // 处理isOver24参数 - 当isOver24=1时，除了16(发起补偿成功)和18(补偿成功)外，其他状态都默认选中
                    if (urlParams.has('isOver24') && urlParams.get('isOver24') === '1') {
                        // 设置时间范围：一个月前当前时间到一个月前的前一天当前时间
                        var now = new Date();
                        var oneMonthAgo = new Date();
                        oneMonthAgo.setMonth(oneMonthAgo.getMonth() - 1);

                        // var oneDayBeforeAgo = new Date(oneMonthAgo);
                        // oneDayBeforeAgo.setDate(new Date().getDate() - 1);

                        var oneDayBeforeOneMonthAgo = new Date();
                        oneDayBeforeOneMonthAgo.setDate(new Date().getDate() - 1);

                        // 格式化时间为 YYYY-MM-DD HH:mm:ss
                        function formatDateTime(date) {
                            var year = date.getFullYear();
                            var month = (date.getMonth() + 1).toString().padStart(2, '0');
                            var day = date.getDate().toString().padStart(2, '0');
                            var hours = date.getHours().toString().padStart(2, '0');
                            var minutes = date.getMinutes().toString().padStart(2, '0');
                            var seconds = date.getSeconds().toString().padStart(2, '0');
                            return year + '-' + month + '-' + day + ' ' + hours + ':' + minutes + ':' + '00';
                        }

                        $("#start_create_time").val(formatDateTime(oneMonthAgo));
                        $("#end_create_time").val(formatDateTime(oneDayBeforeOneMonthAgo));

                        // 获取所有可用的状态选项
                        var allOptions = $("#APPLY_STATUS option");
                        var selectedValues = [];

                        allOptions.each(function() {
                            var value = $(this).val();
                            // 排除状态16(发起补偿成功)和18(补偿成功)
                            if (value && value !== '20' && value !== '18') {
                                selectedValues.push(value);
                            }
                        });

                        if (selectedValues.length > 0) {
                            $("#APPLY_STATUS").val(selectedValues);
                            $('#APPLY_STATUS').multiselect('refresh');
                        }
                    }

                    if (urlParams.has('CREATE_ACC')) {
                        $("input[name='operatePerson']").val(urlParams.get('CREATE_ACC'));
                    }
                    // 设置运营区域
                    if (urlParams.has('AREA_CODE') && urlParams.get('AREA_CODE')) {
                        $("#areaCode").val(urlParams.get('AREA_CODE'));
                        // 触发运营区域变更事件，加载班组信息
                        $('#areaCode').change();

                        // 设置坐席班组（需要延迟设置，确保运营区域的下拉框已经加载完成）
                        if (urlParams.has('AGENT_DEPT') && urlParams.get('AGENT_DEPT')) {
                            setTimeout(function() {
                                $("#agentDept").val(urlParams.get('AGENT_DEPT'));
                            }, 500);
                        }
                    }

                    // 如果有URL参数，自动触发搜索
                    if (window.location.search) {
                        setTimeout(function() {
                            cash.reload($.trim($('#APPLY_STATUS').val()));
                        }, 800); // 延迟执行，确保所有下拉框都已加载完成
                    }
                }
            });
        });
        function showbtn(){
            if($("#peak_end_cash_compensate_admin").val()=="1"){
                $(".adminBtn").removeClass("hidden");
            }else{
                $(".userBtn-"+loginAcct).removeClass("hidden");
            }
        }

        //与标题有关
        function toggleMore() {
            var btn = $("#moreBtn").find(".glyphicon");
            $(".moreSearch").slideToggle('fast');
            btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
        }

        /**
         * 获取近一个月的日期范围
         * @returns {Object} 包含start和end的对象，表示开始和结束日期
         */
        function getMonthRange() {
            var end = new Date(); // 当前日期
            var start = new Date();
            start.setMonth(start.getMonth() - 1); // 一个月前的日期

            // 格式化日期为 YYYY-MM-DD
            function formatDate(date) {
                var year = date.getFullYear();
                var month = (date.getMonth() + 1).toString().padStart(2, '0');
                var day = date.getDate().toString().padStart(2, '0');
                return year + '-' + month + '-' + day;
            }

            return {
                start: formatDate(start)+ ' 00:00:00',
                end: formatDate(end) + ' 23:59:59'
            };
        }

        //导出功能
        cash.exportExcel = function () {
            var startDate = $("#start_create_time").val();
            var endDate = $("#end_create_time").val();
            var diffTime = diffDateTime(startDate, endDate);
            var retMsg = checkTimeRange("1", diffTime);
            if (retMsg != true) {
                alert(retMsg);
                return false;
            }
            location.href = "${ctxPath}/servlet/export?action=ExportPeakEndRecord&"
                + $("#searchForm").serialize();
        }
        function getCookie(name) {
            const value = '; '+ document.cookie;
            const parts = value.split('; '+name+'=');
            if (parts.length === 2) return parts.pop().split(';').shift();
        }
        //重新搜索参数
        function reset() {
            document.searchForm.reset();
            var startDate = getTodayDate(-29);
            var endDate = getTodayDate();
            $("#start_create_time").val(startDate + " 00:00:00");
            $("#end_create_time").val(endDate + " 23:59:59");
            $("#tableHead").data("mars", "peakEnd.compensateList");
            $("#searchForm").searchData({
                success: function (data) {
                    showbtn();
                }
            });
        }

        //时间戳转时间字符
        $.views.converters("getData", function (val, type) {
            if (type == 1) {
                if (val && val.length > 6) {
                    return desensitization(val, 3, 7);
                } else if (val && val.length > 3) {
                    return desensitization(val, 3, val.length);
                } else if (val && val.length > 1) {
                    return desensitization(val, 1, val.length);
                } else if (val) {
                    return "*";
                }
            } else if (type == 2) {
                if (val && val.length > 1) {
                    return desensitization(val, 1, val.length);
                } else if (val) {
                    return "*";
                }
            } else if (type == 3) {
                if (val && val.length > 9) {
                    return desensitization(val, 9, val.length - 4);
                } else if (val) {
                    return "***";
                }
            }
            return val;
        });
        //时间戳转时间字符
        $.views.converters("cardType", function (val) {
            if (val != "") {
                return "1年延保";
            }
            return "";
        });

        //时间戳转换具体方法
        function desensitization(str, beginStr, endStr) {
            var len = str.length;
            var leftStr = str.substring(0, beginStr);
            var rightStr = str.substring(endStr, len);
            var str = ''
            var i = 0;
            try {
                for (i = 0; i < endStr - beginStr; i++) {
                    str = str + '*';
                }
            } catch (error) {

            }
            str = leftStr + str + rightStr;
            return str;
        }

        //封装请求方法
        function webcall(url, param, callback) {
            axios
                .post(url, param, {
                    headers: {
                        "Content-Type": "application/x-www-form-urlencoded; charset=UTF-8",
                    },
                })
                .then((result) => {
                    callback && callback(result);
                });
        }

        //修改号码
        cash.edit = function (params) {
            console.log(params);
            console.log("compensateOrderNo",params.compensateOrderNo);
            console.log("cancelOperatePerson",params.cancelOperatePerson);
            popup.layerShow({
                type: 2,
                title: '修改号码',
                area: ['430px', '200px'],
                offset: '20px'
            }, "${ctxPath}/pages/peakEnd/cashCompensate/peakEnd-cash-edit.jsp", params);
        }
        //取消补偿  需要取消说明参数(非必填)
        cash.cancel = function (params) {
            console.log("params:", params)
            console.log("compensateOrderNo",params.compensateOrderNo);
            console.log("cancelOperatePerson",params.cancelOperatePerson);
            popup.layerShow({
                type: 2,
                title: '取消补偿',
                area: ['430px', '230px'],
                offset: '20px'
            }, "${ctxPath}/pages/peakEnd/cashCompensate/peakEnd-cash-cancel.jsp",params);
        }
        //重新提交 即二次补偿  展示相关信息    (携带所有参数进行提交)确认提交
        cash.apply = function (params) {
            console.log("params:", params);
            layer.confirm('确认要重新提交吗？', {
                btn : [ '确定', '取消' ]
                //按钮
            }, function(index) {
                ajax.remoteCall('/PeakEnd/servlet/peakEnd?action=CompensateApplySecond', params, function (result) {
                    console.log("返回", result);
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1});
                        cash.reload($.trim($('#APPLY_STATUS').val()));
                    } else {
                        layer.alert(result.msg, {icon: 5});
                        // layer.alert(result.data.errorMsg, {icon: 5});
                    }
                });
            });
        }
        //字符串转换为json
        $.views.converters('getJSONStr',function(obj){
            // console.log("obj",obj)
            return JSON.stringify(obj).replace(/"/g, '"');
        })
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
