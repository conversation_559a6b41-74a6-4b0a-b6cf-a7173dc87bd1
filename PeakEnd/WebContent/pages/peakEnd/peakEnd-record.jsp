<%@ page language="java" contentType="text/html;charset=UTF-8" %>
    <%@ include file="/pages/common/global.jsp" %>
        <EasyTag:override name="head">
            <title>坐席申请补偿明细(CC)</title>
        </EasyTag:override>
        <EasyTag:override name="content">
            <form autocomplete="off" name="searchForm" class="form-inline" id="searchForm">
                <div class="ibox">
                    <div class="ibox-title clearfix">
                        <div class="form-group paramDiv">
                            <h5>
                                <span class="glyphicon glyphicon-list"></span> 坐席申请补偿明细(CC)
                            </h5>
                        </div>
                        <hr class="paramDiv" style="margin: 5px -15px">
                        <div class="form-group paramDiv">
                            <div class="input-group width-18">
                                <span class="input-group-addon" style="width:30%">主体</span>
                                <select class="form-control input-sm width-70" name="ORG_CODE" id="orgCode"
                                    data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')">
                                    <option value="" selected="selected">--请选择--</option>
                                </select>
                            </div>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">赠送人账号</span>
                                <input type="text" name="CREATE_ACC" class="form-control input-sm">
                            </div>
                            <div class="input-group input-group-sm" style="width: 200px;">
                                <span class="input-group-addon" style="width: 80px;">用户号码</span>
                                <input type="text" name="PHONE" class="form-control input-sm"
                                    value="${param.customerTel}">
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">补偿项目</span>
                                <select name="COMPENSATE_MODE" id="COMPENSATE_MODE" class="form-control input-sm"
                                    data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_MODE')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">补偿类型</span>
                                <select name="COMPENSATE_TYPE" data-rules="required" id="COMPENSATE_TYPE"
                                    class="form-control input-sm" data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">场景类型</span>
                                <select name="SCENE_TYPE" id="SCENE_TYPE" class="form-control input-sm"
                                    data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('SCENE_TYPE_VALUE')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon" style="width: 80px;">赠送时间</span>
                                <input type="text" name="START_CREATE_TIME" id="start_create_time"
                                    class="form-control input-sm Wdate"
                                    onFocus="WdatePicker({onpicked:function(){this.blur();maxTime()},dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                                <span class="input-group-addon">-</span>
                                <input type="text" name="END_CREATE_TIME" id="end_create_time"
                                    class="form-control input-sm Wdate"
                                    onclick="WdatePicker({minDate:'#F{$dp.$D(\'start_create_time\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                            </div>
                            <div class="input-group input-group-sm ">
                                <span class="input-group-addon">运营区域</span>
                                <select class="form-control input-sm" id="areaCode" name="areaCode"
                                    data-mars="common.getDept" style="width:100px;">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group input-group-sm ">
                                <span class="input-group-addon">坐席班组</span>
                                <select class="form-control input-sm" id="agentDept" name="agentDept"
                                    style="width:100px;">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">是否补偿</span>
                                <!-- data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('PEAKEND_COMPENSATE_TYPE')"  -->
                                <select name="SUBMIT_STATES" data-rules="required" id="SUBMIT_STATES"
                                    class="form-control input-sm" data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('SUBMIT_STATES')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">超时情况</span>
                                <select name="TIME_OUT_STATE" id="TIME_OUT_STATE" class="form-control input-sm"
                                    data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('TIME_OUT_STATE_VALUE')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36" id="duringReasonDiv">
                                <span class="input-group-addon">不符合原因</span>
                                <select name="NON_CONFORMANCE_REASON" id="NON_CONFORMANCE_REASON"
                                    class="form-control input-sm" data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('NON_COMPLIANCE_REASONS')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36" id="afterReasonDiv">
                                <span class="input-group-addon">不符合原因</span>
                                <select name="AFTER_NON_CONFORMANCE_REASON" id="AFTER_NON_CONFORMANCE_REASON"
                                    class="form-control input-sm" data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('AFTER_NON_COMPLIANCE_REASONS')"
                                    data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">反馈结果</span>
                                <select name="FEEDBACK_RESULT" id="FEEDBACK_RESULT" class="form-control input-sm"
                                    data-cust-context-path="/yq_common"
                                    data-cust-mars="dict.getDictList('FEEDBACK_RESULT_VALUE')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                            <div class="input-group width-36">
                                <span class="input-group-addon">AI推荐方案</span>
                                <select name="AI_RESULT" id="AI_RESULT" class="form-control input-sm"
                                        data-cust-context-path="/yq_common"
                                        data-cust-mars="dict.getDictList('AI_RESULT_VALUE')" data-mars-top="true">
                                    <option value="">请选择</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group paramDiv">
                            <div class="input-group input-group-sm pull-right">
                                <button type="button" class="btn btn-sm btn-default" onclick="reset()">重置</button>
                                <button type="button" class="btn btn-sm btn-default ml-10"
                                    onclick="reload()">搜索</button>
                                <EasyTag:res resId="peak_end_compensate_export">
                                    <button type="button" class="btn btn-sm btn-success ml-10"
                                        onclick="record.exportExcel()">导出</button>
                                </EasyTag:res>
                            </div>
                        </div>
                        <div class="ibox-content">
                            <div class="row table-responsive" style="overflow-x: auto;">
                                <table class="table table-auto table-bordered table-hover table-condensed text-c"
                                    id="tableHead" data-mars="peakEnd.recordList">
                                    <thead>
                                        <tr>
                                            <th class="text-c" style="position:sticky;left:0;z-index:2;background:#fff;">序号</th>
                                            <th class="text-c" style="position:sticky;left:40px;z-index:2;background:#fff;">接入单号</th>
                                            <th class="text-c">产品主体</th>
                                            <th class="text-c">产品品类</th>
                                            <th class="text-c">产品品牌</th>
                                            <th class="text-c">服务单号</th>
                                            <th class="text-c">区号</th>
                                            <th class="text-c">用户姓名</th>
                                            <th class="text-c">号码</th>
                                            <th class="text-c">地址</th>
                                            <th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
                                            <th class="text-c">服务请求</th>
                                            <th class="text-c">服务请求大类</th>
                                            <th class="text-c">服务请求小类</th>
                                            <th class="text-c">回访结果</th>
                                            <th class="text-c">补偿类型</th>
                                            <th class="text-c">场景类型</th>
                                            <th class="text-c">补偿项目</th>
                                            <th class="text-c">卡券码</th>
                                            <th class="text-c">卡券名称</th>
                                            <th class="text-c">卡券金额</th>
                                            <th class="text-c">责任人</th>
                                            <th class="text-c">委托方</th>
                                            <th class="text-c">延保卡号</th>
                                            <th class="text-c">延保类型</th>
                                            <th class="text-c">赠送人</th>
                                            <th class="text-c">运营区域</th>
                                            <th class="text-c">坐席班组</th>
                                            <th class="text-c">赠送时间</th>
                                            <th class="text-c">备注</th>
                                            <th class="text-c">是否补偿</th>
                                            <th class="text-c">不符合补偿原因</th>
                                            <th class="text-c">超时情况</th>
                                            <th class="text-c">反馈结果</th>
                                            <th class="text-c">AI推荐方案</th>
                                            <th class="text-c">操作</th>
                                        </tr>
                                    </thead>
                                    <!-- {{:~registerData(#index, #data)}} -->
                                    <tbody id="dataList"></tbody>
                                </table>
                                <script id="list-template" type="text/x-jsrender">

						{{for list}}
							<tr>
			                    <td style="position:sticky;left:0;z-index:1;background:#fff;">{{:#index+1}}</td>
                                    <td style="position:sticky;left:40px;z-index:1;background:#fff;">
                                {{if CONTACT_ORDER_CODE && CONTACT_ORDER_CODE !== ""}}
									<a href="javascript:void(0);" onclick="goToOrderDetail('{{:CONTACT_ORDER_CODE}}', '{{:ORG_CODE}}', '{{:PHONE}}')" style="color: #1E90FF; font-weight: bold; text-decoration: underline;">{{:CONTACT_ORDER_CODE}}</a>
								{{else}}
									{{if CONTACT_ORDER_ID && CONTACT_ORDER_ID !== ""}}
									<a href="javascript:void(0);" onclick="goToOrderDetail('{{:CONTACT_ORDER_ID}}', '{{:ORG_CODE}}', '{{:PHONE}}')" style="color: #1E90FF; font-weight: bold; text-decoration: underline;">{{:CONTACT_ORDER_ID}}</a>
									{{else}}
									-
									{{/if}}
								{{/if}}
								</td>
							    <td>{{sysCodeFUN:ORG_CODE "ORG_CODE"}}</td>
                                <td>{{:PROD_NAME}}</td>
                                <td>{{:BRAND_NAME}}</td>
                                <td>{{:SERVICE_ORDER_NO}}</td>
                                <td>{{:AREA_NUM}}</td>
                                <td>{{call:CUSTOMER_NAME CUSTOMER_NAME_data #index+1 'name' fn='getData2'}}</td>
                                <td>{{call:PHONE PHONE_data #index+1 'phone' fn='getData2'}}</td>
                                <td>{{call:ADDRESS ADDRESS_data #index+1 'addr' fn='getData2'}}</td>
                                <td title="査看" class="text-c">
												<span onclick="showDetail('{{:#index+1}}','{{:CUSTOMER_NAME_data}}','{{:PHONE_data}}','{{:ADDRESS_data}}')" class="glyphicon glyphicon-eye-open"
												id="show{{:#index+1}}"
												style="color:rgb(255,140,60);">
												</span>
											</td>
                                <td>{{:ORDER_SERV_TYPE_NAME}}</td>
								<td>{{:ORDER_SER_ITEM1_NAME}}</td>
								<td>{{:ORDER_SER_ITEM2_NAME}}</td>
                                <td>{{dictFUN:VISIT_RESULT "PEAK_END_RESULT"}}</td>
								<td>{{dictFUN:COMPENSATE_TYPE "PEAKEND_COMPENSATE_TYPE"}}</td>
								                                <td>{{dictFUN:SCENE_TYPE "SCENE_TYPE_VALUE"}}</td>
                                <td>{{dictFUN:COMPENSATE_MODE "PEAKEND_COMPENSATE_MODE"}}</td>
                                <td>
								{{if COMPENSATE_MODE=='4'||COMPENSATE_MODE=='5'}}
									{{:COMPENSATE_NO}}
								{{/if}}
								</td>
                                <td>{{:COMPENSATE_NAME}}</td>
                                <td>{{amount:COMPENSATE_AMOUNT}}</td>
                                <td>{{dictFUN:RESPONSIBLE "PEAK_END_RESPONSIBLE"}}</td>
                                <td>{{:CKIENT_NAME}}</td>
                                <td>{{getData:CARD_CODE 3 }}</td>
                                <td>{{cardType:CARD_CODE }}</td>
                                <td>{{:CREATE_ACC}}</td>
                                <td>{{:AGENT_AREA_NAME}}</td>
                                <td>{{:AGENT_DEPT_NAME}}</td>
                                <td>{{:CREATE_TIME}}</td>
                                <td>{{:REMARKS}}</td>
                                <td>{{dictFUN:SUBMIT_STATES "SUBMIT_STATES"}}</td>
                                <td>{{dictFUN:NON_CONFORMANCE_REASON "NON_COMPLIANCE_REASONS"}}</td>
                                <td>{{dictFUN:TIME_OUT_STATE "TIME_OUT_STATE_VALUE"}}</td>
                                <td>{{dictFUN:FEEDBACK_RESULT "FEEDBACK_RESULT_VALUE"}}</td>
                                <td>{{dictFUN:AI_RESULT "AI_RESULT_VALUE"}}</td>
                                {{if SUBMIT_STATES == '0'}}
                                <td><button type="button" data-data="{{:~encodeData(#data)}}" onclick="submitCompensation(this)">提交</button></td>
                                {{else}}
                                <td></td>
                                {{/if}}
							</tr>
						{{/for}}
					</script>
                            </div>
                            <div class="row paginate" id="page">
                                <jsp:include page="/pages/common/pagination.jsp">
                                    <jsp:param value="10" name="pageSize" />
                                </jsp:include>
                            </div>
                        </div>
                    </div>
                </div>
            </form>
        </EasyTag:override>

        <EasyTag:override name="script">

            <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
            <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
            <script type="text/javascript">
                function goToOrderDetail(contactId, orgCode, phone) {
                    // 判断接入单号是否以'JR'开头
                    var isJRPrefix = contactId && (contactId.indexOf('JR') === 0);
                    var status = isJRPrefix ? '' : '10';
                    var param = isJRPrefix ?
                        "&id=" + contactId + "&orgCode=" + orgCode + "&status=" + status + "&phone=" + phone :
                        "&serialId=" + contactId + "&orgCode=" + orgCode + "&status=" + status + "&phone=" + phone;

                    if (!top.pageControl || !top.pageControl.getEnv) {
                        popup.openTab("${ctxPath}/pages/access/order-new.jsp?type=look" + param, "接入单详情", {});
                    } else {
                        var data = {
                            serialId: isJRPrefix ? '' : contactId,
                            id: isJRPrefix ? contactId : '',
                            orgCode: orgCode,
                            status: status,
                            type: "look",
                            phone: phone,
                            callId: Math.random().toString(36).slice(2, 9)
                        }
                        console.log(data, 'data')
                        top.pageControl.openVoiceOrder(data)
                    }
                }

                var targetItems = null;
                jQuery.namespace("record");
                requreLib.setplugs('wdate')//加载时间控件
                $.views.helpers({
                    encodeData: function (data) {
                        return encodeURIComponent(JSON.stringify(data));
                    }
                });
                function reload() {
                    $("#searchForm").searchData();
                }
                var isShowParam = '${param.isShowParam}';
                $('#areaCode').change(function () {
                    var value = $(this).val();
                    if (value != "") {
                        value = "'" + value.replace(/,/g, "':'") + "'";
                    }
                    $('#agentDept').data('mars', 'common.getDeptAll("5", "' + value + '")');
                    $('#agentDept').render();
                });

                // 控制不符合补偿原因显示/隐藏
                function toggleNonComplianceReasons() {
                    var compensateType = $("#COMPENSATE_TYPE").val();
                    // 当补偿类型为事中补偿时
                    if (compensateType === "1") {
                        $("#duringReasonDiv").show();
                        $("#afterReasonDiv").hide();
                        // // 清空事后不符合原因的值
                        // $("#AFTER_NON_CONFORMANCE_REASON").val("");
                    }
                    // 当补偿类型为事后补偿时
                    else if (compensateType === "2") {
                        $("#duringReasonDiv").show();
                        // $("#duringReasonDiv").hide();
                        $("#afterReasonDiv").hide();
                        // // 清空事中不符合原因的值
                        // $("#NON_CONFORMANCE_REASON").val("");
                    }
                    // 当补偿类型为空或其他情况时，两者都显示
                    else {
                        $("#duringReasonDiv").show();
                        $("#afterReasonDiv").hide();
                    }
                }

                $(function () {
                    var startDate = getMonthRange().start;
                    var endDate = getMonthRange().end;
                    $("#start_create_time").val(startDate);
                    $("#end_create_time").val(endDate);
                    // 从URL参数中获取查询条件
                    var urlParams = new URLSearchParams(window.location.search);

                    if (isShowParam && isShowParam == "0") {
                        $(".paramDiv").addClass("hide");
                        $("#start_create_time").val("");
                        $("#end_create_time").val("");
                    }


                    // 初始化时执行一次
                    toggleNonComplianceReasons();

                    // 监听补偿类型选择变化
                    $("#COMPENSATE_TYPE").change(function () {
                        toggleNonComplianceReasons();
                    });

                    $("#searchForm").render({
                        success: function (result) {
                            // 设置时间范围
                            // if (urlParams.has('START_CREATE_TIME')) {
                            // 	$("#start_create_time").val(urlParams.get('START_CREATE_TIME'));
                            // } else if(!(isShowParam && isShowParam == "0")) {
                            // 	$("#start_create_time").val(startDate);
                            // }
                            // 渲染完成后再执行一次，确保选择框已经渲染
                            toggleNonComplianceReasons();

                            // 设置补偿类型默认选中事中补偿
                            $("#COMPENSATE_TYPE").val("");
                            // 触发change事件，确保显示/隐藏逻辑生效
                            $("#COMPENSATE_TYPE").trigger("change");
                            // if (urlParams.has('END_CREATE_TIME')) {
                            // 	$("#end_create_time").val(urlParams.get('END_CREATE_TIME'));
                            // } else if(!(isShowParam && isShowParam == "0")) {
                            // 	$("#end_create_time").val(endDate + " 23:59:59");
                            // }
                            // 设置开始时间、结束时间
                            if (urlParams.has('START_CREATE_TIME')) {
                                $("#start_create_time").val(urlParams.get('START_CREATE_TIME'));
                            }
                            // 设置开始时间、结束时间
                            if (urlParams.has('END_CREATE_TIME')) {
                                $("#end_create_time").val(urlParams.get('END_CREATE_TIME'));
                            }


                            // 设置补偿类型、补偿项目
                            if (urlParams.has('COMPENSATE_TYPE')) {
                                $("select[name='COMPENSATE_TYPE']").val(urlParams.get('COMPENSATE_TYPE'));
                            }

                            if (urlParams.has('COMPENSATE_MODE')) {
                                $("select[name='COMPENSATE_MODE']").val(urlParams.get('COMPENSATE_MODE'));
                            }

                            // 设置是否补偿
                            if (urlParams.has('SUBMIT_STATES')) {
                                $("select[name='SUBMIT_STATES']").val(urlParams.get('SUBMIT_STATES'));
                            }

                            // 设置超时情况
                            if (urlParams.has('TIME_OUT_STATE')) {
                                $("select[name='TIME_OUT_STATE']").val(urlParams.get('TIME_OUT_STATE'));
                            }

                            // 设置电话号码
                            if (urlParams.has('PHONE')) {
                                $("input[name='PHONE']").val(urlParams.get('PHONE'));
                            }

                            // 设置不符合补偿原因
                            if (urlParams.has('NON_CONFORMANCE_REASON')) {
                                $("select[name='NON_CONFORMANCE_REASON']").val(urlParams.get('NON_CONFORMANCE_REASON'));
                            }

                            // 设置反馈结果
                            if (urlParams.has('FEEDBACK_RESULT')) {
                                $("select[name='FEEDBACK_RESULT']").val(urlParams.get('FEEDBACK_RESULT'));
                            }
                            // 设置AI推荐方案
                            if (urlParams.has('AI_RESULT')) {
                                $("select[name='AI_RESULT']").val(urlParams.get('AI_RESULT'));
                            }
                            // 设置赠送人
                            if (urlParams.has('CREATE_ACC')) {
                                $("input[name='CREATE_ACC']").val(urlParams.get('CREATE_ACC'));
                            }
                            console.log(urlParams.get('CREATE_ACC'), 'CREATE_ACC')
                            // 设置运营区域和班组的函数
                            function setAreaAndDept() {
                                // 检查区域下拉框是否已加载选项
                                function checkAreaLoaded() {
                                    if ($("#areaCode option").length > 1) {
                                        // 区域下拉框已加载，设置值
                                        if (urlParams.has('areaCode') && urlParams.get('areaCode')) {
                                            $("#areaCode").val(urlParams.get('areaCode'));
                                            console.log("区域赋值成功:", $("#areaCode").val());
                                            // 触发区域变更事件
                                            $("#areaCode").trigger('change');
                                            // 检查班组下拉框
                                            checkDeptLoaded();
                                        } else {
                                            // 如果有URL参数，自动触发搜索
                                            if (window.location.search) {
                                                setTimeout(function () {
                                                    reload();
                                                }, 500);
                                            }
                                        }
                                    } else {
                                        // 继续等待区域下拉框加载
                                        setTimeout(checkAreaLoaded, 200);
                                    }
                                }

                                // 检查班组下拉框是否已加载选项
                                function checkDeptLoaded() {
                                    // 如果需要设置班组
                                    if (urlParams.has('agentDept') && urlParams.get('agentDept')) {
                                        // 检查班组下拉框是否有选项
                                        if ($("#agentDept option").length > 1) {
                                            // 班组下拉框已加载，设置值
                                            $("#agentDept").val(urlParams.get('agentDept'));
                                            console.log("班组赋值成功:", $("#agentDept").val());
                                            // 如果有URL参数，自动触发搜索
                                            if (window.location.search) {
                                                setTimeout(function () {
                                                    reload();
                                                }, 500);
                                            }
                                        } else {
                                            // 继续等待班组下拉框加载，最多等待10次（2秒）
                                            var attempts = 0;
                                            var deptInterval = setInterval(function () {
                                                attempts++;
                                                if ($("#agentDept option").length > 1 || attempts >= 10) {
                                                    clearInterval(deptInterval);
                                                    if ($("#agentDept option").length > 1) {
                                                        $("#agentDept").val(urlParams.get('agentDept'));
                                                        console.log("班组赋值成功:", $("#agentDept").val());
                                                    } else {
                                                        console.log("班组下拉框未加载完成，但已超时");
                                                    }

                                                    // 如果有URL参数，自动触发搜索
                                                    if (window.location.search) {
                                                        setTimeout(function () {
                                                            reload();
                                                        }, 500);
                                                    }
                                                }
                                            }, 200);
                                        }
                                    } else {
                                        // 如果有URL参数，自动触发搜索
                                        if (window.location.search) {
                                            setTimeout(function () {
                                                reload();
                                            }, 500);
                                        }
                                    }
                                }

                                // 开始检查区域下拉框
                                checkAreaLoaded();
                            }

                            // 替换原来的区域和班组设置代码
                            if (urlParams.has('areaCode') || urlParams.has('agentDept')) {
                                setTimeout(setAreaAndDept, 300);
                            } else if (window.location.search) {
                                // 如果没有区域和班组参数但有其他URL参数，仍然触发搜索
                                setTimeout(function () {
                                    reload();
                                }, 1000);
                            }
                        }
                    });
                });
                function toggleMore() {
                    var btn = $("#moreBtn").find(".glyphicon");
                    $(".moreSearch").slideToggle('fast');
                    btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
                }

                function submitCompensation(buttonElement) {
                    var encodedDataStr = buttonElement.getAttribute('data-data');
                    var decodedDataStr = decodeURIComponent(encodedDataStr);
                    var dataObj = JSON.parse(decodedDataStr);
                    let params = {
                        ...dataObj
                    }
                    popup.layerShow({
                        type: 2,
                        title: '输入接入单号',
                        area: ['430px', '200px'],
                        offset: '20px'
                    }, "${ctxPath}/pages/peakEnd/peakEnd-record-submit.jsp", params);
                    // // 现在您可以使用 data 对象中的数据了
                    // var contactOrderNo=prompt("请输入接入单号",""); //显示默认文本 "Keafmd"
                    // if (contactOrderNo!=null && contactOrderNo!=""){ 
                    //     var data = {
                    //         ...dataObj,
                    //         contactOrderNo: contactOrderNo
                    //     }
                    //     ajax.remoteCall("${ctxPath}/servlet/peakEnd?action=SubmitErrorMessage",data,function(result) {
                    //         console.log("result:", result);
                    //     })
                    // }
                    // console.log(JSON.stringify(data)); // 输出数据以验证
                }

                record.exportExcel = function () {
                    var startDate = $("#start_create_time").val();
                    var endDate = $("#end_create_time").val();
                    var diffTime = diffDateTime(startDate, endDate);
                    var retMsg = checkTimeRange("1", diffTime);
                    if (retMsg != true) {
                        alert(retMsg);
                        return false;
                    }
                    location.href = "${ctxPath}/servlet/export?action=ExportPeakEndRecord&"
                        + $("#searchForm").serialize();
                }

                function reset() {
                    document.searchForm.reset();
                    var startDate = getMonthRange().start;
                    var endDate = getMonthRange().end;
                    $("#start_create_time").val(startDate);
                    $("#end_create_time").val(endDate);
                    $("#tableHead").data("mars", "peakEnd.recordList");

                    // 设置补偿类型默认为事中补偿
                    setTimeout(function () {
                        $("#COMPENSATE_TYPE").val("").trigger("change");
                    }, 100);

                    $("#searchForm").searchData();
                }
                $.views.converters("getData", function (val, type) {//时间戳转时间字符
                    if (type == 1) {
                        if (val && val.length > 6) {
                            return desensitization(val, 3, 7);
                        } else if (val && val.length > 3) {
                            return desensitization(val, 3, val.length);
                        } else if (val && val.length > 1) {
                            return desensitization(val, 1, val.length);
                        } else if (val) {
                            return "*";
                        }
                    } else if (type == 2) {
                        if (val && val.length > 1) {
                            return desensitization(val, 1, val.length);
                        } else if (val) {
                            return "*";
                        }
                    } else if (type == 3) {
                        if (val && val.length > 9) {
                            return desensitization(val, 9, val.length - 4);
                        } else if (val) {
                            return "***";
                        }
                    }
                    return val;
                });

                /**
                 * 获取近一个月的日期范围
                 * @returns {Object} 包含start和end的对象，表示开始和结束日期
                 */
                function getMonthRange() {
                    var end = new Date(); // 当前日期
                    var start = new Date();
                    start.setMonth(start.getMonth() - 1); // 一个月前的日期

                    // 格式化日期为 YYYY-MM-DD
                    function formatDate(date) {
                        var year = date.getFullYear();
                        var month = (date.getMonth() + 1).toString().padStart(2, '0');
                        var day = date.getDate().toString().padStart(2, '0');
                        return year + '-' + month + '-' + day;
                    }

                    return {
                        start: formatDate(start) + ' 00:00:00',
                        end: formatDate(end) + ' 23:59:59'
                    };
                }
                $.views.converters("cardType", function (val) {//时间戳转时间字符
                    if (val != "") {
                        return "1年延保";
                    }
                    return "";
                });
                function desensitization(str, beginStr, endStr) {
                    var len = str.length;
                    var leftStr = str.substring(0, beginStr);
                    var rightStr = str.substring(endStr, len);
                    var str = ''
                    var i = 0;
                    try {
                        for (i = 0; i < endStr - beginStr; i++) {
                            str = str + '*';
                        }
                    } catch (error) {

                    }
                    str = leftStr + str + rightStr;
                    return str;
                }

                $.views.converters("amount", function (val) {//时间戳转时间字符
                    if (val && val.indexOf(".") == 0) {
                        return "0" + val
                    }
                    return val;
                });
                function showDetail(id, customerName, customerTel1, address) {
                    showDetailCommon({
                        "model": "PeakEnd",
                        "url": "/PeakEnd/pages/peakEnd/peakEnd-record.jsp",
                        "action": "acces",
                        "describe": "用户査询[坐席申请补偿明细]数据，査看[{{customerName}}]敏感信息:[用户号码1：{{customerTel1}},地址：{{address}}]"
                    }, id, customerName, address, customerTel1);
                }
            </script>
            <script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
        </EasyTag:override>
        <%@ include file="/pages/common/layout_list.jsp" %>