package com.yunqu.cc.PeakEnd.servlet;

import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.model.RoleModel;
import com.yq.busi.common.model.UserModel;
import com.yq.busi.common.user.UserMgr;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.PeakEnd.base.AppBaseServlet;
import com.yunqu.cc.PeakEnd.base.CommLogger;
import com.yunqu.cc.PeakEnd.base.Constants;
import com.yunqu.cc.PeakEnd.entity.HomeTodoResp;
import com.yunqu.cc.PeakEnd.enums.ErrorBack;
import com.yunqu.cc.PeakEnd.enums.SubmitStates;
import com.yunqu.cc.PeakEnd.utils.CsUtil;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;

import javax.servlet.annotation.WebServlet;
import java.util.*;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 首页待办任务查询服务
 *
 * @Author: liao
 * @Description:
 * @DateTime: 2025/5/13 17:30
 */
@WebServlet("/servlet/homeTodo")
public class HomeTodoServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    private static final Logger logger = CommLogger.getCommLogger("homeTodo");
    private static final Logger loggerSql = CommLogger.getCommLogger("homeTodo_sql");

    // 字典值常量
    private static final String DICT_QUOTA_NOT_ENOUGH = "额度不足";
    private static final String DICT_EXCEED_AUTHORITY = "超权限范围";

    // 字典组编码
    private static final String DICT_GROUP_MIDDLE = "NON_COMPLIANCE_REASONS";
    private static final String DICT_GROUP_AFTER = "AFTER_NON_COMPLIANCE_REASONS";

    /**
     * 获取系统MARS数据库连接
     */
    private EasyQuery getMarsQuery() {
        return EasyQuery.getQuery(Constants.APP_NAME, Constants.FRAME_DS);
    }

    /**
     * 获取业务数据库连接
     */
    public EasyQuery getQuery() {
        EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
        if (query == null) {
            logger.error("获取数据库连接失败");
            throw new RuntimeException("获取数据库连接失败");
        }
        query.setMaxRow(100000);
        return query;
    }

    /**
     * 处理待办任务查询请求
     */
    public JSONObject actionForGetTodo() {
        String type = this.getPara("type");
        if (type == null) {
            logger.error("待办类型不能为空");
            return EasyResult.fail("待办类型不能为空");
        }
        logger.info("开始处理待办任务查询，类型：" + type);

        HomeTodoResp homeTodoResp = new HomeTodoResp();
        UserModel user = UserUtil.getUser(getRequest());
        if (user == null) {
            logger.error("获取用户信息失败");
            return EasyResult.fail("获取用户信息失败");
        }
        List<String> roleNameList = getUserRoles(user);
        String userAcc = user.getUserAcc();
        if (userAcc == null) {
            logger.error("用户账号不能为空");
            return EasyResult.fail("用户账号不能为空");
        }
        getDept(user, homeTodoResp);
        logger.info("当前用户：" + userAcc + "，角色列表：" + String.join(",", roleNameList));

        try {
            switch (type) {
                case "1": // 个人待办
                    processPersonalTodo(homeTodoResp, userAcc);
                    break;
                case "2": // 部门待办
                    if (hasRole(roleNameList, Constants.getMonitorRoleName())) {
                        processDeptTodo(homeTodoResp, user.getDeptCode());
                        homeTodoResp.setUserAcc(userAcc);
                    } else {
                        logger.info("用户 " + userAcc + " 尝试访问部门待办但不具备班长角色");
                    }
                    break;
                case "3": // 管理员待办
                    if (hasRole(roleNameList, Constants.getAdminRoleName())) {
                        processAdminTodo(homeTodoResp);
                        homeTodoResp.setUserAcc(userAcc);
                    } else {
                        logger.info("用户 " + userAcc + " 尝试访问管理员待办但不具备管理员角色");
                    }
                    break;

                default:
                    logger.info("未知的待办类型：" + type);
                    break;
            }
        } catch (Exception e) {
            logger.error("获取首页待办数据失败:" + e.getMessage(), e);
        }

        logger.info("待办查询结束，结果：" + JSON.toJSONString(homeTodoResp));
        return EasyResult.ok(homeTodoResp);
    }

    /**
     * 获取用户角色列表
     */
    private List<String> getUserRoles(UserModel user) {
        if (user == null) {
            return new ArrayList<>();
        }
        return Optional.ofNullable(user.getRoles())
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .map(RoleModel::getRoleName)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private void getDept(UserModel userModel, HomeTodoResp homeTodoResp) {
        String deptCode = userModel.getDeptCode();
        String deptName = userModel.getDeptName();
        logger.info("当前用户部门：" + deptCode + "，部门名称：" + deptName);
        if (deptCode.length() == 15) {
            // 查询班组和运营区域
            EasySQL easySQL = new EasySQL();
            easySQL.append("SELECT D1.DEPT_CODE,D1.DEPT_NAME,D2.DEPT_NAME P_DEPT_NAME,D2.DEPT_CODE P_DEPT_CODE");
            easySQL.append("FROM EASI_DEPT D1 ");
            easySQL.append("LEFT JOIN EASI_DEPT D2 on D1.P_DEPT_CODE=D2.DEPT_CODE ");
            easySQL.append(deptCode, "WHERE D1.DEPT_CODE=?");
            try {
                JSONObject easyRow = getMarsQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(),
                        new JSONMapperImpl());
                if (easyRow != null) {
                    homeTodoResp.setDeptCode(easyRow.getString("DEPT_CODE"));
                    homeTodoResp.setDeptName(easyRow.getString("DEPT_NAME"));
                    homeTodoResp.setOperatorAreaCode(easyRow.getString("P_DEPT_CODE"));
                    homeTodoResp.setOperatorAreaName(easyRow.getString("P_DEPT_NAME"));
                }
            } catch (Exception e) {
                logger.error("获取用户班组和运营区域失败：" + e.getMessage(), e);
            }
        } else {
            // 查询运营区域
            EasySQL easySQL = new EasySQL();
            easySQL.append("SELECT D1.DEPT_NAME,D1.DEPT_CODE FROM EASI_DEPT D1");
            easySQL.append(deptCode, "AND D1.DEPT_CODE=?");
            try {
                JSONObject easyRow = getMarsQuery().queryForRow(easySQL.getSQL(), easySQL.getParams(),
                        new JSONMapperImpl());
                homeTodoResp.setDeptCode(deptCode);
                homeTodoResp.setDeptName(deptName);
                homeTodoResp.setOperatorAreaCode(easyRow.getString("DEPT_CODE"));
                homeTodoResp.setOperatorAreaName(easyRow.getString("DEPT_NAME"));
            } catch (Exception e) {
                logger.error("获取用户运营区域失败：" + e.getMessage(), e);
            }
        }
    }

    /**
     * 判断用户是否具有指定角色
     */
    private boolean hasRole(List<String> roleNameList, String roleName) {
        return roleNameList.contains(roleName);
    }

    /**
     * 构建基础SQL
     */
    private String buildBaseSql(String deptCode) {
        if (deptCode != null) {
            return "SELECT COUNT(1) FROM C_NO_PEAK_END_RECORD t1 " +
                    "LEFT JOIN C_YG_EMPLOYEE t4 ON t1.CREATE_ACC = t4.USER_ACC " +
                    "WHERE 1=1 " +
                    "AND t4.DEPT_CODE = '" + deptCode + "' " +
                    "AND t1.CREATE_TIME >= ?";
        }
        return "SELECT * FROM C_NO_PEAK_END_RECORD t1 " +
                "WHERE 1=1 " +
                "AND t1.CREATE_TIME >= ?";
    }

    /**
     * 获取字典值编码
     */
    private Map<String, String> getDictCodes(UserModel user, String dictValue) {
        Map<String, String> result = new HashMap<>();
        if (user == null || user.getEpCode() == null || dictValue == null) {
            return result;
        }

        Map<String, Object> middleReasonMap = Optional.ofNullable(
                        DictCache.getMapEnableDictListByGroupCode(user.getEpCode(), DICT_GROUP_MIDDLE))
                .orElse(new HashMap<>());

        Map<String, Object> afterReasonMap = Optional.ofNullable(
                        DictCache.getMapEnableDictListByGroupCode(user.getEpCode(), DICT_GROUP_AFTER))
                .orElse(new HashMap<>());

        for (Map.Entry<String, Object> entry : middleReasonMap.entrySet()) {
            if (dictValue.equals(entry.getValue())) {
                result.put("middle", entry.getKey());
                break;
            }
        }

        for (Map.Entry<String, Object> entry : afterReasonMap.entrySet()) {
            if (dictValue.equals(entry.getValue())) {
                result.put("after", entry.getKey());
                break;
            }
        }

        return result;
    }

    /**
     * 处理个人待办数据
     */
    private void processPersonalTodo(HomeTodoResp homeTodoResp, String userAcc) throws Exception {
        if (homeTodoResp == null || userAcc == null) {
            logger.error("处理个人待办参数不能为空");
            return;
        }
        DateTime oneMonthAgo = DateUtil.offsetMonth(DateUtil.date(), -1);
        DateTime oneDayAgo = DateUtil.offsetDay(DateUtil.date(), -1);
        DateTime lastMonthSameDayStart = DateUtil.beginOfDay(DateUtil.offsetMonth(DateUtil.date(), -1));
        // 获取现金24小时待领
        JSONObject cash24HoursParam = new JSONObject();
        cash24HoursParam.put("operatePerson", userAcc);
        cash24HoursParam.put("pubCurrentDate", DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN));
        cash24HoursParam.put("pubCurrentDateEnd", DateUtil.format(oneDayAgo, DatePattern.NORM_DATETIME_PATTERN));
        JSONObject cash24Hours = getCash24Hours(cash24HoursParam);
        Integer Over24HoursCount = filterCash24Hours(cash24Hours);
        homeTodoResp.setOver24Hours(Over24HoursCount);

        // 应补未补-事中：查询赠送人账号：与当前账号一致；补偿类型-事中补偿;是否补偿-应补未补；时间：近一个月
        EasySQL sql = new EasySQL();
        sql.append("SELECT COUNT(1) FROM C_NO_PEAK_END_RECORD WHERE 1=1");
        sql.append(userAcc, " AND CREATE_ACC = ?");
        sql.append(Constants.PEAKEND_TYPE1, " AND COMPENSATE_TYPE = ?"); // 事中补偿
        sql.append(SubmitStates.NOT_SAVE.getStates(), " AND SUBMIT_STATES = ?"); // 应补未补
        sql.append(oneMonthAgo, " AND CREATE_TIME >= ?"); // 一个月内

        // 应补未补-事中
        processNotCompensated(homeTodoResp, userAcc, lastMonthSameDayStart, Constants.PEAKEND_TYPE1);

        // 应补未补-事后
        processNotCompensated(homeTodoResp, userAcc, lastMonthSameDayStart, Constants.PEAKEND_TYPE2);
        homeTodoResp.setUserAcc(userAcc);
    }

    public Integer filterCash24Hours(JSONObject cash24Hours) {
        Integer Over24HoursCount = 0;
        if (ObjectUtil.isNotEmpty(cash24Hours) && cash24Hours.containsKey("data")) {
            List<JSONObject> cash24HoursList = JSON.parseArray(cash24Hours.getString("data"), JSONObject.class);
            long count = cash24HoursList.stream()
                    // 过滤状态非补偿成功的
                    .filter(item -> !StrUtil.equalsAny(item.getString("applyStatus"),"18","20"))
                    // 过滤超过24小时的
                    .filter(item -> {
                        String pubCreateDateStr = item.getString("pubCreateDateStr");
                        if (StrUtil.isBlank(pubCreateDateStr)) {
                            return false;
                        }
                        DateTime pubCreateDate = DateUtil.parse(pubCreateDateStr, DatePattern.NORM_DATETIME_PATTERN);
                        DateTime now = DateUtil.date(); // 当前时间
                        return now.getTime() - pubCreateDate.getTime() > 24 * 60 * 60 * 1000; // 判断是否超过24小时
                    })
                    .count();
            Over24HoursCount = (int) count;
        }
        return Over24HoursCount;
    }

    public JSONObject getCash24Hours(JSONObject cash24HoursParam) {
        JSONObject params = new JSONObject();
        JSONObject result = new JSONObject();
        cash24HoursParam.put("compensateItems", "CASH");
        cash24HoursParam.put("pageIndex", "1");
        cash24HoursParam.put("pageSize", "1000");

        List<String> applyStatusList = new ArrayList<>();
        applyStatusList.add("10");
        applyStatusList.add("11");
        applyStatusList.add("12");
        applyStatusList.add("13");
        applyStatusList.add("14");
        applyStatusList.add("15");
        applyStatusList.add("16");
        applyStatusList.add("17");
        applyStatusList.add("19");
        cash24HoursParam.put("applyStatusList", applyStatusList);

        params.put("command", "compensateQueryList");
        params.put("params", cash24HoursParam);
        try {
            logger.info("查询现金24小时待领请求参数：" + JSON.toJSONString(cash24HoursParam));
            IService service = ServiceContext.getService(Constants.COMPENSATE_SERVICE_ID);
            JSONObject resp = service.invoke(params);
            result = CsUtil.getData(resp, Types.LIST);
            logger.info("查询现金24小时待领结果：" + JSON.toJSONString(result));
        } catch (ServiceException e) {
            logger.error("IService请求失败,请求参数" + JSON.toJSONString(cash24HoursParam) + ",原因" + e.getMessage(), e);
            result = JSONObject.parseObject(ErrorBack.OK_LIST.getValue());
        }
        return result;
    }

    /**
     * 处理应补未补数据
     */
    private void processNotCompensated(HomeTodoResp homeTodoResp, String userAcc, DateTime oneMonthAgo,
                                       String compensateType) throws Exception {
        EasySQL sql = new EasySQL();
        sql.append("SELECT COUNT(1) FROM C_NO_PEAK_END_RECORD WHERE 1=1");
        sql.append(userAcc, " AND CREATE_ACC = ?");
        sql.append(compensateType, " AND COMPENSATE_TYPE = ?");
        sql.append(SubmitStates.NOT_SAVE.getStates(), " AND SUBMIT_STATES = ?");

        // 只有事后需要添加FEEDBACK_RESULT = '1'条件
        if (Constants.PEAKEND_TYPE2.equals(compensateType)) {
            sql.append(" AND FEEDBACK_RESULT = '1'");
        }

        sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), " AND CREATE_TIME >= ?");
        loggerSql.info("个人待办处理应补未补SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
        int count = getQuery().queryForInt(sql.getSQL(), sql.getParams());
        if (Constants.PEAKEND_TYPE1.equals(compensateType)) {
            homeTodoResp.setMiddle(count);
        } else {
            homeTodoResp.setAfter(count);
        }
    }

    /**
     * 处理部门待办数据
     */
    private void processDeptTodo(HomeTodoResp homeTodoResp, String deptCode) throws Exception {
        if (homeTodoResp == null || deptCode == null) {
            logger.error("处理部门待办参数不能为空");
            return;
        }
        DateTime oneMonthAgo = DateUtil.offsetMonth(DateUtil.date(), -1);
        DateTime lastMonthSameDayStart = DateUtil.beginOfDay(DateUtil.offsetMonth(DateUtil.date(), -1));
        DateTime oneDayAgo = DateUtil.offsetDay(DateUtil.date(), -1);
        DateTime fourHoursAgo = DateUtil.offsetHour(DateUtil.date(), -4);

        String baseSqlStr = buildBaseSql(deptCode);
        // 获取现金24小时待领
        JSONObject cash24HoursParam = new JSONObject();
        cash24HoursParam.put("staffName", homeTodoResp.getDeptName());
        cash24HoursParam.put("agentDept", homeTodoResp.getDeptCode());
        cash24HoursParam.put("compensateItems", "CASH");
        cash24HoursParam.put("pubCurrentDate", DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN));
        cash24HoursParam.put("pubCurrentDateEnd", DateUtil.format(oneDayAgo, DatePattern.NORM_DATETIME_PATTERN));
        JSONObject cash24Hours = getCash24Hours(cash24HoursParam);
        Integer Over24HoursCount = filterCash24Hours(cash24Hours);
        homeTodoResp.setOver24Hours(Over24HoursCount);

        // 应补未补-事中
        processDeptNotCompensated(homeTodoResp, baseSqlStr, lastMonthSameDayStart, Constants.PEAKEND_TYPE1);

        // 应补未补-事后
        processDeptNotCompensated(homeTodoResp, baseSqlStr, lastMonthSameDayStart, Constants.PEAKEND_TYPE2);

        // 额度不足
        processDeptQuotaNotEnough(homeTodoResp, baseSqlStr, fourHoursAgo);
    }

    /**
     * 处理部门应补未补数据
     */
    private void processDeptNotCompensated(HomeTodoResp homeTodoResp, String baseSqlStr, DateTime oneMonthAgo,
                                           String compensateType) throws Exception {
        EasySQL sql = new EasySQL();
        sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
        sql.append(compensateType, " AND t1.COMPENSATE_TYPE = ?");
        sql.append(SubmitStates.NOT_SAVE.getStates(), " AND t1.SUBMIT_STATES = ?");

        // 只有事后需要添加FEEDBACK_RESULT = '1'条件
        if (Constants.PEAKEND_TYPE2.equals(compensateType)) {
            sql.append(" AND FEEDBACK_RESULT = '1'");
        }

        loggerSql.info("部门待办处理应补未补SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
        int count = getQuery().queryForInt(sql.getSQL(), sql.getParams());
        if (Constants.PEAKEND_TYPE1.equals(compensateType)) {
            homeTodoResp.setMiddle(count);
        } else {
            homeTodoResp.setAfter(count);
        }
    }

    /**
     * 处理部门额度不足数据
     */
    private void processDeptQuotaNotEnough(HomeTodoResp homeTodoResp, String baseSqlStr, DateTime fourHoursAgo)
            throws Exception {
        UserModel user = UserUtil.getUser(getRequest());
        Map<String, String> dictCodes = getDictCodes(user, DICT_QUOTA_NOT_ENOUGH);

        // 处理部门事中额度不足
        processTypeQuota(homeTodoResp, baseSqlStr, fourHoursAgo, dictCodes, Constants.PEAKEND_TYPE1, "部门事中额度不足",
                homeTodoResp::setLackMiddle, true);

        // 处理部门事后额度不足
        processTypeQuota(homeTodoResp, baseSqlStr, fourHoursAgo, dictCodes, Constants.PEAKEND_TYPE2, "部门事后额度不足",
                homeTodoResp::setLackAfter, true);
    }

    /**
     * 处理管理员待办数据
     */
    private void processAdminTodo(HomeTodoResp homeTodoResp) throws Exception {
        if (homeTodoResp == null) {
            logger.error("处理管理员待办参数不能为空");
            return;
        }
        DateTime oneMonthAgo = DateUtil.offsetMonth(DateUtil.date(), -1);
        DateTime oneDayAgo = DateUtil.offsetDay(DateUtil.date(), -1);
        DateTime fourHoursAgo = DateUtil.offsetHour(DateUtil.date(), -4);

        String baseSqlStr = buildBaseSql(null);

        // 现金24小时待领
        JSONObject cash24HoursParam = new JSONObject();
        cash24HoursParam.put("compensateItems", "CASH");
        cash24HoursParam.put("pubCurrentDate", DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN));
        cash24HoursParam.put("pubCurrentDateEnd", DateUtil.format(oneDayAgo, DatePattern.NORM_DATETIME_PATTERN));
        JSONObject cash24Hours = getCash24Hours(cash24HoursParam);
        Integer Over24HoursCount = filterCash24Hours(cash24Hours);
        homeTodoResp.setOver24Hours(Over24HoursCount);

        // 额度不足
        processAdminQuotaNotEnough(homeTodoResp, baseSqlStr, oneMonthAgo, fourHoursAgo);

        // 超权限范围
        processAdminExceedAuthority(homeTodoResp, baseSqlStr, oneMonthAgo, fourHoursAgo);
    }

    /**
     * 处理管理员额度不足数据
     */
    private void processAdminQuotaNotEnough(HomeTodoResp homeTodoResp, String baseSqlStr, DateTime oneMonthAgo,
                                            DateTime fourHoursAgo) throws Exception {
        UserModel user = UserUtil.getUser(getRequest());
        Map<String, String> dictCodes = getDictCodes(user, DICT_QUOTA_NOT_ENOUGH);
        List<String> userAccList = getMonitorUserAccList();

        // 处理管理员事中额度不足
        processAdminTypeData(homeTodoResp, baseSqlStr, oneMonthAgo, fourHoursAgo, userAccList, dictCodes,
                Constants.PEAKEND_TYPE1, "管理员事中额度不足", homeTodoResp::setLackMiddle,
                DICT_QUOTA_NOT_ENOUGH);

        // 处理管理员事后额度不足
        processAdminTypeData(homeTodoResp, baseSqlStr, oneMonthAgo, fourHoursAgo, userAccList, dictCodes,
                Constants.PEAKEND_TYPE2, "管理员事后额度不足", homeTodoResp::setLackAfter,
                DICT_QUOTA_NOT_ENOUGH);
    }

    /**
     * 处理管理员超权限范围数据
     */
    private void processAdminExceedAuthority(HomeTodoResp homeTodoResp, String baseSqlStr, DateTime oneMonthAgo,
                                             DateTime fourHoursAgo) throws Exception {
        UserModel user = UserUtil.getUser(getRequest());
        Map<String, String> dictCodes = getDictCodes(user, DICT_EXCEED_AUTHORITY);
        List<String> userAccList = getMonitorUserAccList();

        // 处理管理员事中超权限范围
        processAdminTypeData(homeTodoResp, baseSqlStr, oneMonthAgo, fourHoursAgo, userAccList, dictCodes,
                Constants.PEAKEND_TYPE1, "管理员事中超权限范围", homeTodoResp::setExceedMiddle,
                DICT_EXCEED_AUTHORITY);

        // 处理管理员事后超权限范围
        processAdminTypeData(homeTodoResp, baseSqlStr, oneMonthAgo, fourHoursAgo, userAccList, dictCodes,
                Constants.PEAKEND_TYPE2, "管理员事后超权限范围", homeTodoResp::setExceedAfter,
                DICT_EXCEED_AUTHORITY);
    }

    /**
     * 通用方法：处理部门额度不足/超权限范围
     */
    private void processTypeQuota(HomeTodoResp homeTodoResp, String baseSqlStr, DateTime dateTime,
                                  Map<String, String> dictCodes, String compensateType, String logPrefix,
                                  java.util.function.Consumer<Integer> setter, boolean isQueryInt) throws Exception {
        String type = compensateType.equals(Constants.PEAKEND_TYPE1) ? "middle" : "after";

        if (dictCodes.get(type) != null) {
            EasySQL sql = new EasySQL();
            sql.append(DateUtil.format(dateTime, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
            sql.append(SubmitStates.NOT_SATISFIABLE.getStates(), " AND t1.SUBMIT_STATES = ?");
            sql.append(compensateType, " AND t1.COMPENSATE_TYPE = ?");
            sql.append(dictCodes.get(type), " AND t1.NON_CONFORMANCE_REASON = ?");

            loggerSql.info(logPrefix + "SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));

            if (isQueryInt) {
                int count = getQuery().queryForInt(sql.getSQL(), sql.getParams());
                setter.accept(count);
                logger.info(logPrefix + "数量：" + count);
            } else {
                List<JSONObject> dataList = getQuery().queryForList(sql.getSQL(), sql.getParams(),
                        new JSONMapperImpl());
                setter.accept(dataList.size());
                logger.info(logPrefix + "数量：" + dataList.size());
            }
        } else {
            setter.accept(0);
        }
    }

    /**
     * 通用方法：处理管理员额度不足/超权限范围
     */
    private void processAdminTypeData(HomeTodoResp homeTodoResp, String baseSqlStr, DateTime oneMonthAgo,
                                      DateTime fourHoursAgo, List<String> userAccList, Map<String, String> dictCodes,
                                      String compensateType, String logPrefix, Consumer<Integer> setter, String dictType)
            throws Exception {
        String type = compensateType.equals(Constants.PEAKEND_TYPE1) ? "middle" : "after";

        if (dictCodes.get(type) != null) {
            EasySQL sql = new EasySQL();
            sql.append(DateUtil.format(oneMonthAgo, DatePattern.NORM_DATETIME_PATTERN), baseSqlStr);
            sql.append(SubmitStates.NOT_SATISFIABLE.getStates(), " AND t1.SUBMIT_STATES = ?");
            sql.append(compensateType, " AND t1.COMPENSATE_TYPE = ?");
            sql.append(dictCodes.get(type), " AND t1.NON_CONFORMANCE_REASON = ?");

            loggerSql.info(logPrefix + "SQL：" + sql.getSQL() + ", 参数: " + JSON.toJSONString(sql.getParams()));
            List<JSONObject> dataList = getQuery().queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());

            // 额度不足需要根据创建时间和用户账号过滤
            if (DICT_QUOTA_NOT_ENOUGH.equals(dictType)) {
                long count = dataList.stream()
                        .filter(item -> {
//                            String userAcc = item.getString("CREATE_ACC");
                            DateTime createTime = DateUtil.parse(item.getString("CREATE_TIME"),
                                    DatePattern.NORM_DATETIME_PATTERN);
                            return createTime.compareTo(fourHoursAgo) <= 0;
                        })
                        .count();
                setter.accept((int) count);
                logger.info(logPrefix + "数量：" + count);
            } else {
                // 超权限范围直接使用所有数据
                setter.accept(dataList.size());
                logger.info(logPrefix + "数量：" + dataList.size());
            }
        } else {
            setter.accept(0);
        }
    }

    /**
     * 获取班长用户账号列表
     */
    private List<String> getMonitorUserAccList() {
        return Optional.ofNullable(UserMgr.getUserByRoleName(Constants.getMonitorRoleName()))
                .orElse(new ArrayList<>())
                .stream()
                .filter(Objects::nonNull)
                .map(UserModel::getUserAcc)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }
}
