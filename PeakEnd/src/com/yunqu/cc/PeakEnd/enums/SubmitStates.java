package com.yunqu.cc.PeakEnd.enums;

/**
 * @ClassName: SubmitStates
 * @description:
 * @author: <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
 * @create: 2025-04-15 14:39
 */
public enum SubmitStates {
    SAVE_BUT_NOT_SUBMIT("0","已补未提"),
    SUBMITTED("1","已补偿"),
    NOT_SATISFIABLE("2","不符合补偿要求"),
    NOT_SAVE("3","应补未补"),
    NOT_SATISFIABLE_HANDLE("4","不符合补偿要求-已处理"),
    NOT_SAVE_HANDLE("5","应补未补-已处理"),
    ;
    private String states;
    private String desc;
    SubmitStates(String states, String desc) {
        this.states = states;
        this.desc = desc;
    }
    public String getStates() {
        return states;
    }
    public String getDesc() {
        return desc;
    }

    public static SubmitStates getDescByStates(String states) {
        for (SubmitStates value : SubmitStates.values()) {
            if (value.getStates().equals(states)) {
                return value;
            }
        }
        return null;
    }

    public static SubmitStates getStatesByDesc(String desc) {
        for (SubmitStates value : SubmitStates.values()) {
            if (value.getDesc().equals(desc)) {
                return value;
            }
        }
        return null;
    }

}
