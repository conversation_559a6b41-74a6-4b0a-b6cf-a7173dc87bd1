# VIP敏感标签处理优化

## 需求说明

对VIP查询接口的返回结果进行优化，新增敏感标签判断和数据合并功能。

## 原始返回格式

```json
{
    "code": "000000",
    "msg": "操作成功",
    "msgTemp": "",
    "errorList": [
        "000000:操作成功"
    ],
    "data": {
        "localLabels": [
            {
                "labelCode": "mt",
                "labelName": "媒体"
            }
        ],
        "ddyLabels": [
            {
                "labelCode": "32520889",
                "labelName": "商家代报"
            },
            {
                "labelCode": "32520975",
                "labelName": "记者"
            }
        ]
    },
    "service": "cc-workbench-center",
    "exName": null,
    "sysErr": false,
    "level": 0
}
```

## 优化后的返回格式

```json
{
    "respCode": "000000",
    "respDesc": "操作成功",
    "respData": {
        "isSensitive": true,
        "respData": [
            {
                "labelCode": "mt",
                "labelName": "媒体"
            },
            {
                "labelCode": "32520889",
                "labelName": "商家代报"
            },
            {
                "labelCode": "32520975",
                "labelName": "记者"
            }
        ]
    }
}
```

## 处理逻辑

### 1. 敏感标签判断

```java
// 判断是否敏感：如果localLabels或ddyLabels其中一个不为空，isSensitive就为true
if ((localLabels != null && localLabels.size() > 0) || 
    (ddyLabels != null && ddyLabels.size() > 0)) {
    isSensitive = true;
}
```

**判断规则：**
- `localLabels` 不为空且有数据 → `isSensitive = true`
- `ddyLabels` 不为空且有数据 → `isSensitive = true`
- 两个都为空或没有数据 → `isSensitive = false`

### 2. 数据合并

```java
// 合并localLabels和ddyLabels数据
JSONArray respData = new JSONArray();
if (localLabels != null && localLabels.size() > 0) {
    respData.addAll(localLabels);
}
if (ddyLabels != null && ddyLabels.size() > 0) {
    respData.addAll(ddyLabels);
}
```

**合并规则：**
- 先添加 `localLabels` 中的所有数据
- 再添加 `ddyLabels` 中的所有数据
- 最终形成一个统一的标签数组

## 测试场景

### 场景1：两个标签都有数据
**输入：**
```json
{
    "localLabels": [{"labelCode": "mt", "labelName": "媒体"}],
    "ddyLabels": [{"labelCode": "32520889", "labelName": "商家代报"}]
}
```

**输出：**
```json
{
    "isSensitive": true,
    "respData": [
        {"labelCode": "mt", "labelName": "媒体"},
        {"labelCode": "32520889", "labelName": "商家代报"}
    ]
}
```

### 场景2：只有localLabels有数据
**输入：**
```json
{
    "localLabels": [{"labelCode": "mt", "labelName": "媒体"}],
    "ddyLabels": []
}
```

**输出：**
```json
{
    "isSensitive": true,
    "respData": [
        {"labelCode": "mt", "labelName": "媒体"}
    ]
}
```

### 场景3：只有ddyLabels有数据
**输入：**
```json
{
    "localLabels": [],
    "ddyLabels": [{"labelCode": "32520889", "labelName": "商家代报"}]
}
```

**输出：**
```json
{
    "isSensitive": true,
    "respData": [
        {"labelCode": "32520889", "labelName": "商家代报"}
    ]
}
```

### 场景4：两个标签都为空
**输入：**
```json
{
    "localLabels": [],
    "ddyLabels": []
}
```

**输出：**
```json
{
    "isSensitive": false,
    "respData": []
}
```

### 场景5：标签字段为null
**输入：**
```json
{
    "localLabels": null,
    "ddyLabels": null
}
```

**输出：**
```json
{
    "isSensitive": false,
    "respData": []
}
```

## 代码实现

### 核心处理逻辑

```java
// 获取原始数据
JSONObject data = r.getJSONObject("data");
JSONArray localLabels = data.getJSONArray("localLabels");
JSONArray ddyLabels = data.getJSONArray("ddyLabels");

// 判断是否敏感
Boolean isSensitive = false;
if ((localLabels != null && localLabels.size() > 0) || 
    (ddyLabels != null && ddyLabels.size() > 0)) {
    isSensitive = true;
}

// 合并数据
JSONArray respData = new JSONArray();
if (localLabels != null && localLabels.size() > 0) {
    respData.addAll(localLabels);
}
if (ddyLabels != null && ddyLabels.size() > 0) {
    respData.addAll(ddyLabels);
}

// 构造返回结果
JSONObject responseData = new JSONObject();
responseData.put("isSensitive", isSensitive);
responseData.put("respData", respData);

result.put("respData", responseData);
```

### 日志记录

```java
logger.info("处理结果 - isSensitive: " + isSensitive + ", respData size: " + respData.size());
```

## 优势

### 1. 简化客户端判断
- 客户端只需要检查 `isSensitive` 字段就能知道是否有敏感标签
- 不需要分别检查 `localLabels` 和 `ddyLabels`

### 2. 统一数据格式
- 将两个不同来源的标签合并为一个数组
- 便于客户端统一处理和展示

### 3. 向后兼容
- 保持原有的错误处理逻辑
- 只在成功时进行数据处理和转换

### 4. 性能优化
- 减少客户端的数据处理逻辑
- 服务端一次性完成数据合并

## 注意事项

### 1. 空值处理
- 对 `null` 和空数组都进行了安全检查
- 避免 `NullPointerException`

### 2. 数据顺序
- `localLabels` 在前，`ddyLabels` 在后
- 如果需要调整顺序，修改合并逻辑即可

### 3. 标签去重
- 当前实现没有去重逻辑
- 如果需要去重，可以基于 `labelCode` 进行处理

### 4. 错误场景
- 如果原始接口返回错误，不进行数据处理
- 保持原有的错误信息返回

这样的优化让接口返回的数据更加简洁和易用，同时提供了敏感标签的快速判断能力。
