package com.yunqu.cc.cssgw.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.cssgw.base.CommonLogger;
import com.yunqu.cc.cssgw.base.OrderURL;
import com.yunqu.cc.cssgw.utils.CsUtil;
import com.yunqu.cc.cssgw.utils.csHttp.HttpClientUtil;
import com.yunqu.cc.cssgw.utils.tempHttp.HttpResp;
import com.yunqu.cc.cssgw.utils.tempHttp.HttpUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;


public class CssGetVipService extends IService {

    public Logger logger = CommonLogger.getLogger("cssVip");
    private Logger timeLogger = CommonLogger.getLogger("inf_time");
    private String funcUrl = "";

    @Override
    public JSONObject invoke(JSONObject resqJson) throws ServiceException {
        String command = resqJson.getString("command");

        String startTime = DateUtil.getCurrentDateStr();
        try {
            if ("getCssVipInfo".equals(command)) {
                return getCssVipInfo(resqJson.getJSONObject("params"));
            }else{
                JSONObject result = new JSONObject();
                result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
                result.put("respDesc", "不存在的command,请重现检查！");
                return result;
            }
        } finally {
            String endTime = DateUtil.getCurrentDateStr();
            int time = DateUtil.bwSeconds(endTime, startTime);
            if(time>1){
                timeLogger.info("[threadName]:" + Thread.currentThread().getName() + ">>>[command]:" + command + ",end time:" + startTime + ",请求时长：" + "【" + time + "】秒");
            }
        }
    }

    /**
     * 同步用户信息大到2.0
     * @param json
     * @return
     */

    private JSONObject getCssVipInfo(JSONObject json) {
        // TODO Auto-generated method stub
        logger.info("获取售后vip会员信息入参"+ JSON.toJSONString(json));
        funcUrl =OrderURL.getCssVipUrl();
        logger.info("获取售后vip会员信息地址"+ funcUrl);
        JSONObject restParams = new JSONObject();
        restParams.put("restParams", json);
        String res = "";
        res = HttpClientUtil.postJsonBody(funcUrl, restParams.toJSONString());
        logger.info("获取售后vip会员信息结果"+res);
        JSONObject r = JSONObject.parseObject(res);
        JSONObject result = new JSONObject();
        result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
        result.put("respDesc", "售后系统接口返回错误");
        JSONObject resultData = new JSONObject();
        Boolean isSensitive = false;
        try{
            if(r.getString("code").equals("000000")){
                // 获取原始数据
                JSONObject data = r.getJSONObject("data");
                JSONArray localLabels = data.getJSONArray("localLabels");
                JSONArray ddyLabels = data.getJSONArray("ddyLabels");

                // 判断是否敏感：如果localLabels或ddyLabels其中一个不为空，isSensitive就为true
                if ((localLabels != null && localLabels.size() > 0) ||
                    (ddyLabels != null && ddyLabels.size() > 0)) {
                    isSensitive = true;
                }
                // 合并localLabels和ddyLabels数据
                JSONArray respData = new JSONArray();
                if (localLabels != null && localLabels.size() > 0) {
                    respData.addAll(localLabels);
                }
                if (ddyLabels != null && ddyLabels.size() > 0) {
                    respData.addAll(ddyLabels);
                }
                // 构造返回结果
                JSONObject responseData = new JSONObject();
                responseData.put("isSensitive", isSensitive);
                responseData.put("respData", respData);

                result.put("respData", responseData);
                result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                result.put("respDesc", "操作成功");

                logger.info("处理结果 - isSensitive: " + isSensitive + ", respData size: " + respData.size());
            }else{
                logger.info(" 获取售后vip会员信息结果失败~~ 请求返回= "+res);
                result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                result.put("respDesc", "售后系统接口返回错误");
            }
        }catch(Exception e){
            logger.error("获取售后vip会员信息结果失败："+e.getMessage(),e);
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "程序异常！");
        }
        return result;
    }


}
