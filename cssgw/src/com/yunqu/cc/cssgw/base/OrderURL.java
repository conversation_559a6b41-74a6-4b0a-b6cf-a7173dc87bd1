package com.yunqu.cc.cssgw.base;

import com.yq.busi.common.util.ParamUtil;
import com.yunqu.cc.cssgw.utils.ConfigUtil;

/**
 * 售后接口url常量
 * <AUTHOR>
 *
 */
public class OrderURL {

	public final static String CS_DOMAIN = ConfigUtil.getString("CS_DOMAIN"); //售后系统接口所在域名
	public final static String CS_DOMAIN_NEW = ConfigUtil.getString("CS_DOMAIN_NEW","https://apiprod.midea.com"); //售后系统接口所在新域名
	public final static String CS_API_KEY = ConfigUtil.getString("CS_API_KEY", "80b09b9dbd144718b08b340b3789c9bf");
	public final static String CS_API_SECRET = ConfigUtil.getString("CS_API_SECRET", "73f6ad124f63400fb66c446fe2df0f68");
	// 顺德genesys接口
	public final static String GENESYS_DOMAIN_SD = ConfigUtil.getString("GENESYS_DOMAIN_SD");
	// 合肥genesys接口
	public final static String GENESYS_DOMAIN_HF = ConfigUtil.getString("GENESYS_DOMAIN_HF");

	public final static String CS_PORT = ConfigUtil.getString("CS_PORT"); //售后系统端口

	public final static String getAiIopUrl (){
		return  ParamUtil.getParam("mixgw","IOP_URL", "https://iop.midea.com");
	}



	//报障信息接口
	public final static String RP_DOMAIN = ConfigUtil.getString("RP_DOMAIN");


	/**
	 * 分中心查询
	 */
	public final static String COMM_BRANCH_CODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listbranch"; //
	/**
	 * 地区编码查询
	 */
	public final static String COMM_PAGEREGION = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/pagesyscodelist"; //
	/**
	 * 产品型号模糊查询
	 */
	public final static String COMM_PROMODEL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/sysbasedata/queryproductinfolist";
	/**
	 * 产品型号模糊查询(es)
	 */
	public final static String COMM_PROMODEL_BYES = CS_DOMAIN_NEW + "/c-css/sup-bff-ipms/cc/bff/sup/product/info/queryByEs";
	/**
	 * 根据条件查询品类与型号前缀关系
	 */
	public final static String LIST_BY_CONDTION = CS_DOMAIN_NEW + "/c-css/sup-bff-ipms/cc/bff/sup/prod/model/prefix/listByCondition";
	/**
	 * 根据手机号查询投诉次数和升级次数
	 */
	public final static String COMPLAIN_UPGRADE_TIMES = CS_DOMAIN_NEW+ "/c-css/wom-bff-ipms/api/wom/complaintupgradehandle/queryComplaintUpgradeTimes";

	/**
	 * 丰图地址联想
	 */
	public final static String OPQUERY_TIPBS = CS_DOMAIN_NEW+ "/c-css/sys-bff-ipms/bff/opquery/tipbs?origin=CC";

	/**
	 * 地图标准化接口
	 */
	public final static String OPQUERY_MAP_ADDANALYZER = CS_DOMAIN_NEW+ "/c-css/sys-bff-ipms/c-cc/bff/opquery/map/addrAnalyzer?origin=CC";
	/**
	 * 根据三级地区编码获取不明分区
	 */
	public final static String UNKNOWN_REGIONCODE = CS_DOMAIN_NEW+ "/c-css/sys-bff-ipms/c-cc/bff/opquery/map/findUnknownFourLevelRegionCode";
	/**
	 * 智能总结.工单小结
	 */
	public final static String CCDIALOG_SUKMMARY = getAiIopUrl()+ "/aiTools/api/chat/ccDialogue/fault/summary";

	/**
	 * 基础配置数据查询
	 */
	public final static String COMM_SYSCODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/pagesyscodelist";
	/**
	 * 业务类型查询
	 */
	public final static String COMM_SERVICETYPE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/pageservicetypelist";
	/**
	 * 封单反馈项 查询
	 */
	//public final static String COMM_FEEDBACKITEMLIST = CS_DOMAIN + "/csscc/api/wom/sysbasedata/pagefeedbackitemlist";
	public final static String COMM_FEEDBACKITEMLIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/getbackitemlist";
	/**
	 * 服务请求查询 （原始：pageservicerequire）
	 */
	public final static String COMM_SERVICE_REQUIRE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/listservicerequire";
	/**
	 * 服务请求查询
	 */
	public final static String COMM_PAGE_SERVICE_REQUIRE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/pageservicerequire";
	/**
	 * 服务请求查询 (新)
	 */
	public final static String COMM_NEW_SERVICE_REQUIRE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/newServicerequire";


	/**
	 * 品类查询
	 */
	public final static String COMM_PRODUCT_CODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listprodtype";
	/**
	 * 大类查小类查询
	 */
	public final static String COMM_QUERY_PROD_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/queryprodinfo";

	/**
	 * 常用品类/品牌查询 c-css-cc-81
	 */
	public final static String COMM_HOT_PRODUCT_CODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listhotprod";
	/**
	 * 销售单位查询
	 */
	public final static String COMM_STORE_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/pagestorelist";
	/**
	 * 机显代码查询
	 */
	public final static String COMM_MACHINE_ERROR_FACADE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/machineerror/pagemachineerror";
	/**
	 * 网点信息查询
	 */
	public final static String COMM_CC_UNIT_FACADE = CS_DOMAIN + "";

	/**
	 * 用户地址查询
	 */
	public final static String COMM_CUSTOMER_ADDRESS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/customer/pagecustomeraddress";

	/**
	 * 品牌品类关系查询
	 */
	public final static String COMM_BRAND_PRODUCT_SHIP = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/prodtype/listprodinfo";
	/**
	 * 所有的业务类型查询
	 */
	public final static String COMM_LSERVICE_TYPE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/pageallservicetypelist";

	/**
	 * 【基础】根据上级品类编码获取品类[品牌]接口
	 */
	public final static String COMM_BRAND_BY_PRODCODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listprodbyparentcode";

	/**
	 * 【基础】根据品类名称获取品类[品牌]接口
	 */
	public final static String COMM_BRAND_BY_PRODNAME = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listprodbyprodname";
	/**
	 * 产品型号模糊查询产品信息
	 */
	public final static String COMM_PRODUCT_INFO_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/sysbasedata/queryproductinfolist";
	/**
	 * 品牌全部
	 */
	public final static String COMM_PAGE_BRAND_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/pagebrandlist";
	/**
	 * 【地区查询】模糊查询全级地区信息
	 */
	public final static String COMM_LIST_REGION_BY_KEY = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listregionbykey";
	/**
	 * 【地区查询】查询父级地区下的子地区
	 */
	public final static String COMM_REGION_BY_PARENT_CODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listregionbyparentcode";
	/**
	 * 【地区查询】根据区号查询父级地区下的子地区
	 */
	public final static String COMM_LIST_REGION_BY_PARAM = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/listregionbyparm";
	/**
	 * 【基础】根据上级编码获取品类【品牌】接口
	 */
	public final static String COMM_PROD_BY_PARENT_CODE = CS_DOMAIN + "/csscc/api/wom/sysbasedata/listProdByParentCode";
	/**
	 * 【基础】根据品类名称获取品类【品牌】接口
	 */
	public final static String COMM_PROD_BY_PARENT_NAME = CS_DOMAIN + "/csscc/api/wom/sysbasedata/listProdByProdName";
	/**
	 * 根据主体，地区查询分中心
	 */
	public final static String COMM_BRANCH_BY_REGION = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/getbranchbyregion";
	/**
	 * 网点查询
	 */
	public final static String COMM_WEBSITE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/unit/pageaccreditedunit";
	/**
	 * 用户指定网点查询
	 */
	public final static String COMM_USERDESIGNATEDWEBSITE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/unit/pageAuthorUnit";

	/**
	 * 电商地址解析
	 */
	public final static String COMM_ECM_ADDRESS_ANALYSIS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/supermap/findccaddress";
	/**
	 * 条码获取接口
	 */
	public final static String COMM_SEARCHFROMSNCC = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/searchfromsncc";
///////////////////////////////////////////////////////////////////
//////////////////////////////接入单////////////////////////
	/**
	 * 接入单列表查询
	 */
	public final static String CONTACT_ORDER_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/pagecontactorder";
	/**
	 * 接入单主页查询(未关闭单查询)
	 */
	public final static String CONTACT_DO_QUERY_ORDER = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/doquerycontactorder";
	/**
	 * 接入单创建接口
	 */
	public final static String CONTACT_ORDER_CREATE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/dosavecontactorder";
	/**
	 * 地图信息提交接口
	 */
	public final static String CONTACT_SAVEMAP = CS_PORT + "/ccss-track-rpc/map/saveMap";
	/**
	 * 接入单更新
	 */
	public final static String CONTACT_ORDER_UPDATE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/doupdatecontactorder";
	/**
	 * 接入单详细信息查询
	 */
	public final static String CONTACT_ORDER_DETAIL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/getcontactorderdetail";
	/**
	 * 服务单列表查询
	 */
	public final static String CONTACT_SERVICE_ORDER_SEARCH = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceorderbyphone";
	/**
	 * 服务单列表查询
	 */
	public final static String CONTACT_SERVICE_ORDER_SEARCH_UE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceorderbyjingdongphone";
	/**
	 * 服务单详细查询
	 */
	public final static String CONTACT_SERVICE_ORDER_DETAIL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/queryserviceorderdetail";
	/**
	 * 服务单用户诉求查询
	 */
	public final static String CONTACT_SERVICE_ORDER_USER_APPEAL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageserviceuserdemand";
	/**
	 * 服务单产品列表查询
	 */
	public final static String CONTACT_SERVICE_ORDER_PRODUCT_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageserviceuserdemand";
	/**
	 * 备件申请状态查询接口
	 */
	public final static String CONTACT_PAGE_SERVICE_MATERIAL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageservicematerial";
	/**
	 * 用户历史服务单列表查询接口(原版本："/csscc/api/wom/serviceorder/listserviceorderbycustomercode")
	 */
	public final static String CONTACT_SERVICE_ORDER_BY_CUSTOMERCODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceorderbycustomercodeext";

	/**
	 * 用户历史服务单列表查询接口（新增接口，根据用户号码，时间等查询）
	 */
	public final static String CONTACT_LIST_SERVICE_ORDER =  CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceorder";
	/**
	 * 设备档案查询
	 */
	public final static String CONTACT_PAGE_CUSTOMER_MACHINE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/customer/pagecustomermachine";
	/**
	 * 用户档案查询接口
	 */
	public final static String CONTACT_PAGE_CUSTOMER_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/customer/pagecustomer";
	/**
	 * 用户档案详情查询（根据客户编码）
	 */
	public final static String CONTACT_CUSTOMER_DETAIL_BY_CODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/customer/getcustomerunionaddressbycode";
	/**
	 * 假性故障查询
	 */
	public final static String CONTACT_EXCLUDED_FAULT_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/excludedfault/getexcludedfaultlist";
	/**
	 * 项目档案详情查询
	 */
	public final static String CONTACT_PROJECT_FILE_DETAILS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryprojectfiledetails";
	/**
	 * 服务单进度查询
	 */
	public final static String CONTACT_SERVICE_DETAIL_BY_CONTACT_NO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/queryServiceDetailByContactOrderNo";
	/**
	 * 是否美的会员
	 */
	public final static String CONTACT_IS_VIP_BY_TEL = CS_DOMAIN + "";

///////////////////////////////////////////////////////////////////
//////////////////////////////回访单////////////////////////
	/**
	 * 回访列表查询
	 */
	public final static String REVISIT_ORDER_LIST = CS_DOMAIN + "/csscc/api/wom/contactorder/pagecontactorder";
	/**
	 * 用户档案
	 */
	public final static String REVISIT_USER_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/pagecontactorder";
	/**
	 * 用户档案信息
	 */
	public final static String REVISIT_USER_INFO_BY_CODE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/customer/getcustomerunionaddressbycode";
	/**
	 * 用户历史服务单列表
	 */
	public final static String REVISIT_PAGE_CONTACT_ORDER = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceorderbycustomercodeext";
	/**
	 * 用户历史投诉单列表
	 */
	public final static String REVISIT_PAGE_CCUNIFYEXPLAIN = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageccunifyexplain";


	/**
	 * 费用明细
	 * 0912增加鉴权
	 */
	public final static String REVISIT_PAGE_EXPENSES_INFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/expensesinfo/pageexpensesinfo";
	/**
	 * 退换
	 * 0912增加鉴权
	 */
	public final static String REVISIT_GETRETURNABLEARCHIVESINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/returnablearchives/getreturnablearchivesinfo";
	/**
	 * 项目
	 */
	public final static String REVISIT_QUERYPROJECTFILEDETAILS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryprojectfiledetails";
	/**
	 * 项目
	 * 0926增加鉴权
	 */
	public final static String REVISIT_QUERYPROJECTMACHDETAILS = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryprojectmachdetails";
	/**
	 * 洗悦家
	 * 0926增加鉴权
	 */
	public final static String REVISIT_QUERYWASHPRODUCTINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/querywashproductinfo";
	/**
	 * 维修
	 * 0912增加鉴权
	 */
	public final static String REVISIT_PAGEMAINTENANCEPROJECT = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/pagemaintenanceproject";
	/**
	 * 备件
	 * 0912增加鉴权
	 */
	public final static String REVISIT_QUERYMATERIALUSEDETAIL = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/servicearchives/querymaterialusedetail";
	/**
	 * 不满意原因
	 * 0912增加鉴权
	 */
	public final static String REVISIT_LISTUNSATISFYREASONCONF = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/listunsatisfyreasonconf";
	/**
	 * 回访项
	 * 0926增加鉴权
	 */
	public final static String REVISIT_PAGECOMMONCALLBACKITEM = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/callback/pagecommoncallbackitem";
//	public final static String REVISIT_PAGECOMMONCALLBACKITEM = CS_DOMAIN+"/csscc/api/wom/callback/pagecallbackitem";

	/**
	 * 新回访项 评分版本
	 * 0926增加鉴权
	 */
	public final static String REVISIT_PAGETELCALLBACKITEM = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/callback/pagetelcallbackitem";
	/**
	 * 耗材信息列表
	 */
	public final static String REVISIT_PAGEMAINTAINMATERIALINFO = CS_DOMAIN+"/csscc/api/wom/planremindorder/pagemaintainmaterialinfo";
	/**
	 * 申诉列表
	 * 0926增加鉴权
	 */
	public final static String REVISIT_PAGECALLBACKAPPEAL = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackappeal/pagecallbackappeal";
	/**
	 * 申诉详情
	 * 0926增加鉴权
	 */
	public final static String REVISIT_QUERYCALLBACKAPPEALDETAIL = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackappeal/querycallbackappealdetail";
	/**
	 *0926增加鉴权
	 */
	public final static String REVISIT_DOCALLBACKAPPEAL = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackappeal/docallbackappeal";
	/**
	 * 回访结果查询
	 * 0926增加鉴权
	 */
	public final static String REVISIT_PAGECALLBACKINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/pagecallbackinfo";
//	/**
//	 * 稽查工单回访结果查询
//	 */
//	public final static String INSPECTION_REVISIT_PAGECALLBACKINFO = CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/bff/sup/cc/insp/query";
	/**
	 * 二次回访项
	 * 0926增加鉴权
	 */
	public final static String REVISIT_PAGECALLBACKITEM = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/callback/pagecallbackitem";
	/**
	 * 二次回访项
	 * 0926增加鉴权
	 */
	public final static String REVISIT_PAGECUSTOMERCALLBACKINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/pagecustomercallbackinfo";
	/**
	 * 线下提交
	 * 0926增加鉴权
	 */
	public final static String REVISIT_DOCREATEXXCALLBACKINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/docreatexxcallbackinfo";
	/**
	 * 回访结果创建
	 * 0926增加鉴权
	 */
	public final static String REVISIT_DOCREATECALLBACKINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/docreatecallbackinfo";
//	/**
//	 * 稽查工单回访结果创建
//	 */
//	public final static String INSPECTION_REVISIT_DOCREATECALLBACKINFO = CS_DOMAIN_NEW+ "/c-css/sup-bff-ipms/bff/sup/cc/insp/flow/callback";
	/**
	 * 新回访结果创建 评分
	 * 0926增加鉴权
	 */
	public final static String REVISIT_DOCREATETELCALLBACKINFO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/docreatetelcallbackinfo";
	/**
	 * 回访结果创建（单次）
	 * 0926增加鉴权
	 */
	public final static String REVISIT_DOCREATECALLBACKINFOBYID = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/docreatecallbackinfobyid";
	/**
	 * 回访结果创建（单次）
	 * 0926增加鉴权
	 */
	public final static String REVISIT_DOCREATEXXCALLBACKINFOBYID = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/docreatexxcallbackinfobyid";
	/**
	 * 回访结果详情
	 * 0926增加鉴权
	 */
	public final static String REVISIT_QUERYCALLBACKDETAIL = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/querycallbackdetail";

	/**
	 * 是否网点号码
	 * 0926增加鉴权
	 */
	public final static String REVISIT_ISEXISTUNITMANAGERTEL = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/sysbasedata/isexistunitmanagertel";
	/**
	 * 是否网点号码
	 * 0926增加鉴权
	 */
	public final static String REVISIT_QUERYCHARGEDETAILSBYNO = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/order/querychargedetailsByNo";

///////////////////////////////////////////////////////////////////
//////////////////////////////投诉单////////////////////////

	/**
	 * 投诉列表查询
	 */
	public final static String COMPLAIN_ORDER_LIST = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/pagecomplainthandleinfo";
	/**
	 * 投诉处理详情查询
	 */
	public final static String COMPLAIN_ORDER_DETAIL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/querycomplaintdetail";
	/**
	 * 投诉信息查询
	 */
	public final static String COMPLAIN_PAGE_COMPLAINT_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/pagecomplaintinfo";
	/**
	 * 投诉处理提交
	 */
	public final static String COMPLAIN_ORDER_HANDLE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/docomplainthandle";
	/**
	 * 投诉处理批量指派
	 */
	public final static String COMPLAIN_DISPATCH_HANDLE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/dispatchcomplainthandle";
	/**
	 * 投诉单信息修改
	 */
	public final static String COMPLAIN_ORDER_INFO_UPDATE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/updatecomplaintshandle";
	/**
	 * 投诉信息批量废弃
	 */
	public final static String COMPLAIN_ABANDON_HANDLE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/abandoncomplainthandle";
	/**
	 * 投诉线上升级
	 */
	public final static String CREATE_UPGRADE_HANDLE_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintupgradehandle/createupgradehandleinfo";
	/**
	 * 投诉线上更新
	 */
	public final static String MODIFY_UPGRADE_HANDLE_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintupgradehandle/modifyupgradehandleinfo";
	/**
	 *投诉升级单查询
	 */
	public final static String SERACH_UPGRADE_HANDLE_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintupgradehandle/serachupgradehandleinfo";
	/**
	 * 投诉升级单驳回
	 */
	public final static String REJECT_COMPLAINT_HANDLE = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/api/wom/complaintupgradehandle/ccReject";
	/**
	 *驳回
	 */
	public final static String COMPLAIN_TURNDOW = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintupgradehandle/turndown";
	/**
	 * 投诉自动领取
	 */
	public final static String COMPLAIN_ORDER_ACCEPT = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/complaintinfo/accept";

	///////////////////////////////////////////////////////////////

	/**
	 * CC销售物流处理单列表查询
	 */
	public final static String LOGISTICS_ORDER_LIST =   "/cc/api/wom/saleslogisticsprocessing/queryList";

	/**
	 * CC销售物流处理单详情查询
	 */
	public final static String LOGISTICS_ORDER_DETAIL =  "/cc/api/wom/saleslogisticsprocessing/getDetail";

	/**
	 * CC销售物流处理单领取
	 */
	public final static String RECEIVE_LOGISTICS_ORDER = "/cc/api/wom/saleslogisticsprocessing/receive";

	/**
	 * CC销售物流处理单处理
	 */
	public final static String HANDLE_LOGISTICS_ORDER = "/cc/api/wom/saleslogisticsprocessing/handle";

///////////////////////////////////////////////////////////////////
/////////////////////////////其他url////////////////////////
	/**
	 * 特批资源创建申请
	 */
	public final static String OTHER_APPROVE_APPLY = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/ccapprovalorder/createapproveorder";

	/**
	 * 延时回访结果推送
	 */
	public final static String YS_CALLBACK_PUSH =  CS_DOMAIN_NEW+"/c-css/wom-bff-ipms/bff/wom/aiOutbound/resultConfirm";

	/**
	 * 特批资源列表查询
	 */
	public final static String OTHER_APPROVE_SELECT =  CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/ccapprovalorder/pageapproveorder";
	/**
	 * 特批资源详细查询
	 */
	public final static String OTHER_APPROVE_DETAIL =  CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/ccapprovalorder/initapprovalorderview";
	/**
	 * 统一口径查询
	 */
	public final static String OTHER_CCUNIFY_EXPLAIN_SELECT = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageccunifyexplain";
	/**
	 * 统一口径发布
	 */
	public final static String OTHER_CCUNIFY_EXPLAIN_PUBLISH = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/dopublishunifyplain";
	/**
	 * 统一口径创建
	 */
	public final static String OTHER_CCUNIFY_EXPLAIN_CREATE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/dosaveunifyplain";
	/**
	 * 统一口径审核
	 */
	public final static String OTHER_CCUNIFY_EXPLAIN_CHECK = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageccunifyexplain";
	/**
	 * 统一口径删除
	 */
	public final static String OTHER_CCUNIFY_EXPLAIN_DELETE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/doabandonunifyplain";
	/**
	 * 计划提醒单查询
	 * 0912增加鉴权
	 */
	public final static String OTHER_PLAN_REMIND_SELECT = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/pageplanremindorder";
	/**
	 * 计划提醒单预约查询
	 * 0912增加鉴权
	 */
	public final static String OTHER_PLAN_REMIND_RELATED = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/getplanremindorderrelated";
	/**
	 * 计划提醒单预约
	 * 0912增加鉴权
	 */
	public final static String OTHER_PLAN_REMIND_BOOKING = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/saveplanremindorderbooking";
	/**
	 * 计划提醒单预约确认
	 * 0912增加鉴权
	 */
	public final static String OTHER_PLAN_REMIND_BOOKING_CONFIRM = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/saveplanremindorderbookingconfirm";
	/**
	 * 计划提醒单预约列表查询
	 */
	public final static String OTHER_PLAN_REMIND_BESPEAK_INFO = CS_DOMAIN + "/csscc/api/wom/planremindorder/pageplanremindbespeakinfo";
	/**
	 * 计划提醒单-滤芯查询接口
	 * 0912增加鉴权
	 */
	public final static String OTHER_FILTER_ELEMENT_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/pagefilterelementinfo";
	/**
	 * 计划提醒单-批量修改接口（机器人部分）
	 * 0912增加鉴权
	 */
	public final static String UPDATE_PLAN_REMIND_ORDER_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/updateplanremindorderinfo";
	/**
	 * 计划提醒单 保养-耗材查询
	 * 0912增加鉴权
	 */
	public final static String OTHER_MAINTAIN_MATERIAL_INFO = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/planremindorder/pagemaintainmaterialinfo";
	/**
	 * 计划提醒单-网点派单接口
	 */
	public final static String OTHER_PLAN_REMIND_SEND = CS_DOMAIN + "/csscc/api/wom/planremindorder/planremindordersend";
	/**
	 * 计划提醒单-清洗机器信息查询
	 */
	public final static String OTHER_WASH_PRODUCT_INFO = CS_DOMAIN + "";
	/**
	 * 免考核服务单-列表查询
	 * 0912增加鉴权
	 */
	public final static String OTHER_UNCHECK_ORDER_LIST =CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/pageServiceOrder";
	/**
	 * 免考核单-标记服务单虚假封单或免考核
	 * 0912增加鉴权
	 */
	public final static String OTHER_UNCHECK_ORDER_COUNTERFEIT = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/docounterfeitoruncheck";
	/**
	 *服务单进度查询
	 */
	public final static String SENTIMENT_ORDER_PROGRESS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/queryServiceDetailByContactOrderNo";
	/**
	 * 回访结果信息查询
	 * 0926增加鉴权
	 */
	public final static String SENTIMENT_ORDER_VISIT = CS_DOMAIN_NEW+"/c-css/c-css-ipms/csscc/api/wom/cccallbackinfo/queryCallbackInfoByContactNo";
	/**
	 * 舆情工作量获取(渠道分类)
	 * 0912增加鉴权
	 */
	public final static String OTHER_SYN_SENTIMENT = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/findServiceOrderCount";
	/**
	 * 服务政策接口
	 */
	public final static String warrantydescbycodeorsn = CS_DOMAIN + "/api/wom/order/querywarrantydescbycodeorsn";
	/**
	 * 服务政策接口1
	 */
	public final static String warrantyDescByCodeOrSnOrProduct = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/order/queryWarrantyDescByCodeOrSnOrProduct";
	/**
	 * 备件申请记录接口
	 */
	public final static String getMaterialApplyList = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/querymaterialapplylist";
	/**
	 * 备件申请详情
	 */
	public final static String getMaterialApplyDetail = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/querymaterialapplydetail";
	/**
	 * v0829收费标准
	 */
	public final static String ChargeStandardList = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/api/mmp/insp/getChargeStandardList";
	/**
	 * v0829备件价格详情接口
	 */
	public final static String getPriceInfoForMaterialDetail = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/api/mmp/insp/getPriceInfoForMaterialDetail";
	/**
	 * 备件申请单状态修改（取消）
	 */
	public final static String getMaterialApplyCancel = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/oi/api/wom/order/materialapplystatusupdate";

//	/**
//	 * 备件价格详情接口
//	 */
//	public final static String getPriceInfoForMaterialDetail = CS_PORT + "/c-css-ipms/css/api/mmp/insp/getPriceInfoForMaterialDetail";
//	/**
//	 *收费标准
//	 */
//	public final static String ChargeStandardList = CS_PORT + "/c-css-ipms/css/api/mmp/insp/getChargeStandardList";
	/**
	 * 收费明细
	 */
	public final static String QUERY_CHARGE_DETAILS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/order/querychargedetails";
	/**
	 * V0911延保档案查询
	 */
	public final static String prolongservarchives = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryprolongservarchives";
	/**
	 * 工程师位置信息
	 */
	public final static String engineerstateposition = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/order/engineerstateposition";
	/**
	 * 工程师位置信息
	 */
	public final static String queryserviceorderdetail = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/cssmobile/api/mmp/queryserviceorderdetail";
	/**
	 * 取消服务单
	 */
	public final static String cancelServiceOrderUrl = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/oi/api/wom/order/cancelserviceorder";
	/**
	 * 通过电话号码查询是否寻找智能产品
	 * 0912增加鉴权
	 */
	public final static String queryIntelFlagByMobile = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/mmp/queryIntelFlagByMobile";
	/**
	 * 工单附件文件查看/下载
	 */
	public final static String orderFileDownload = CS_PORT + "/c-css-ipms/fileAccess/file/";
	/**
	 * 工单附件查询
	 */
	public final static String orderFileQuery = CS_PORT + "/c-css-ipms/fileAccess/getCCOrderFile";
	/**
	 * 工单附件上传
	 */
	public final static String orderFileUpload = CS_PORT + "/c-css-ipms/css/api/sup/cc/uploadfile";
	/**
	 * CSS地址纠错
	 * V0926增加鉴权
	 */
	public final static String addrAnalyzerUrl = CS_DOMAIN_NEW + "/c-css/c-css-ipms/api/map/addrAnalyzer";
	/**
	 * V0911便民服务查询
	 */
	public final static String homeserviceList = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/getfeestandard";
	/**
	 * V0911便民服务更新
	 */
	public final static String updateHomeservice =  CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/updatehomeservice";
	/**
	 * 便民服务更新
	 */
	public final static String getrobotmaintenancetoken = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/getrobotmaintenancetoken";
	/**
	 * Fail - V0911四级地址查询接口
	 */
	public final static String getforthaddress = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/getforthaddress";
	/**
	 * V0911故障服务数据同步接口
	 */
	public final static String syncrobotfaultservicedata = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicerequire/syncrobotfaultservicedata";
//	/**
//	 * 故障服务数据同步接口
//	 */
//	public final static String getprodtypemaxlist = CS_PORT + "/c-css-ipms/csscc/api/sup/prodtype/getprodtypemaxlist";
	/**
	 * 小程序链接封装接口
	 */
	public final static String convert2ShortLink = CS_DOMAIN_NEW + "/c-css/c-css-ipms/t/common/convert2ShortLink";
	/**
	 * VOC-投诉单详情查询
	 */
	public final static String VOC_COMPLAIN_DETAIL_URL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/querycomplaintupgradeinfo";
	/**
	 * VOC-接入单详情查询
	 */
	public final static String VOC_CONTACT_DETAIL_URL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/doquerycontactorder";
	/**
	 * VOC-服务单详情查询
	 */
	public final static String VOC_SERVICE_DETAIL_URL =  CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/queryserviceorderinfo";
	/**
	 * 服务单详情查询
	 *0926增加鉴权
	 */
	public final static String SERVICE_DETAIL_URL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/queryserviceorder";

	/**
	 * 附近上传
	 */
	public final static String UPLOAD_INVOICE_URL = CS_PORT + "/c-css-ipms/csscc/api/wom/sysbasedata/uploadInvoice"; //作废

	/**
	*0912增加鉴权
	*/
	public final static String UPLOAD_INVOICE_PIC_URL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/sysbasedata/uploadinvoicepic";

	/**
	 * 对接CSS，美云销催单、虚假完结
	 */
	public final static String MCSP_CONTACT_ORDER_MARK = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/markFlag";
	/**
	 * 对接CSS，美云销催单
	 */
	public final static String MCSP_CONTACT_ORDER_URGE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/contactorder/urge";
	/**
	 * v0912服务过程接口
	 */
	public final static String SERVICE_PROCESS_QUERY = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/listserviceprocess";
	/**
	 * Fail - v0912派工历史
	 */
	public final static String QUERY_DISPATCH_ORDER = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicearchives/querydispatchorder";
	/**
	 * v0912命令信息
	 */
	public final static String QUERY_CONTACT_USER_REQUIRE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/querycontactuserrequire";
	/**
	 * 鉴定进度查询
	 */
	public final static String QUERY_IDENTIFY_ARCHIVES = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/servicearchives/queryidentifyarchives";
	/**
	 * 工程师日程查询
	 */
	public final static String QUERY_ENGINEER_SCHEDULE_DETAIL = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/queryengineerscheduledetail";
	/**
	 * 用户延保机器信息
	 * 0912增加鉴权
	 */
	public final static String PEAK_END_MACHINE_HISTORY = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/cc/user/machine/history";
	/**
	 * 金卡剩余数量
	 * 0912增加鉴权
	 */
	public final static String PEAK_END_GOLDCARD_REMAINDER = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/cc/goldcard/remainder";
	/**
	 * 延保卡补偿接口
	 * 0912增加鉴权
	 */
	public final static String PEAK_END_GOLDCARD_GIVE = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/api/wom/cc/goldcard/give";
	/**
	 * 命令单跟进状态更新接口
	 */
	public final static String UPDATE_CMD_STATUS = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/wom/serviceorder/updateCmdStatus";

	/**
	 * IOT故障信息拉通查询接口 -- 基本
	 */
	public final static String QUERY_REPORT_MES = RP_DOMAIN + "/v1/rest/holacon/insight/metric/error/faultMessages";

	/**
	 * IOT故障信息拉通查询接口 -- 详细
	 */
	public final static String QUERY_REPORT_MESDETAIL = RP_DOMAIN + "/v1/rest/holacon/insight/metric/error/faultMessageDetails";


	//售后系统现金补偿接口所在域名
	public final static String CS_COMPENSATE_DOMAIN = ConfigUtil.getString("CS_COMPENSATE_DOMAIN");
	/**
	 * 查询补偿列表
	 */
	public final static String COMPENSATE_QUERY_LIST = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/querylist";
	/**
	 * 查询实物补偿列表
	 */
	public final static String QUERY_MATERIAL_LIST = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/queryMaterialList";
	/**
	 * 查询实物补偿列表详情
	 */
	public final static String QUERY_MATERIAL_DETAIL_LIST = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/queryDetail";
	/**
	 * 查询实物补偿列表物流详情
	 */
	public final static String QUERY_COMPENSATE_LOGISTICS = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/queryCompensateLogistics";
	/**
	 * 查询实物补偿列表售后详情
	 */
	public final static String QUERY_AFTER_SALES_DETAILS = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/queryAfterSalesDetail";
	/**
	 * 坐席反馈异常
	 */
	public final static String FEEDBACK_ANOMALY = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/feedbackAnomaly";
	/**
	 * 确认收货
	 */
	public final static String CONFIRM_RECEIPT = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/confirmReceipt";
	/**
	 * 补偿申请
	 */
	public final static String COMPENSATE_APPLY = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/apply";
	/**
	 * 商品列表
	 */
	public final static String COMPENSATE_PRODUCT = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/productList";
	/**
	 * 取消补偿
	 */
	public final static String COMPENSATE_APPLY_CANCEL = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/applycancel";
	/**
	 * 更新用户信息
	 */
	public final static String COMPENSATE_APPLY_MODIFY = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/applymodify";
	/**
	 * 查询用户登陆状态
	 */
	public final static String COMPENSATE_USER_LOGIN_STATUS = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/userloginstatus";
	/**
	 * 触发重新补偿
	 */
	public final static String COMPENSATE_APPLY_SECOND = CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/csscc/api/wom/compensate/applysecond";

	/**
	 * 隐私号通话记录查询
	 */
	public final static String PRIVACY_CALL_URL = CS_COMPENSATE_DOMAIN + "/csscc/api/wom/virtualno/callrecord/queryList";


	/**
	 * 服务请求查询树 (新)
	 */
	public final static String COMM_SERVICE_REQUIRE_TREE = CS_COMPENSATE_DOMAIN + "/csscc/api/wom/require/treeListData";

	/**
	 * 二次维修
	 */
	public final static String SECOND_REPAIR_URL = CS_COMPENSATE_DOMAIN + "/csscc/api/wom/twoRepairsReplaceMachine/getRepairsList";
	/**
	 * v0829备件价格查询接口
	 */
	public final static String ChargePriceForMaterial = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/api/mmp/insp/getChargePriceForMaterial";
	/**
	 * v0829备件价格查询接口
	 */
	public final static String getPriceInfoForMaterialList = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/api/mmp/insp/getPriceInfoForMaterialList";
	/**
	 * v0829备件列表查询接口(新20220728)
	 */
	public final static String getPriceInfoForMaterialListNew = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/api/mmp/insp/getPriceInfoDetailForMaterialList";
	/**
	 * v0829免费备件列表接口
	 */
	public final static String freeMaterialListUrl = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/material/getListFreeMaterial";
	/**
	 * Fail - v0829故障服务数据同步接口1
	 */
	public final static String getprodtypemaxlist = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/sup/prodtype/getprodtypemaxlist";
	/**
	 * v0829备件记录查询
	 */
	public final static String MATERIAL_RECORD_QUERY = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/spo/order/queryMaterialUseGdOrder";
	/**
	 * v0829备件物流查询
	 */
	public final static String MATERIAL_LOGISTICS_QUERY = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/spo/order/statusFlow";
	/**
	 * v0829订单跟踪接口1
	 */
	public final static String CONTACT_PAGE_STATUS_FLOW = CS_DOMAIN_NEW + "/c-css/c-css-ipms/csscc/api/spo/order/statusFlow";
	/**
	 * v0829建议单同步接口(尚书台)
	 */
	public final static String uploadFeedbackSuggestion = CS_DOMAIN_NEW + "/c-css/c-css-ipms/cc/sup/uploadFeedbackSuggestion";
	/**
	 * v0829稽查工单回访结果查询
	 */
	public final static String INSPECTION_REVISIT_PAGECALLBACKINFO = CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/bff/sup/cc/insp/query";
	/**
	 * v0829稽查工单回访结果创建
	 */
	public final static String INSPECTION_REVISIT_DOCREATECALLBACKINFO = CS_DOMAIN_NEW+ "/c-css/sup-bff-ipms/bff/sup/cc/insp/flow/callback";

	/**
	 * 新-网点查询
	 */
	public  static String getNewCommWebsite(){
		return CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/cc/bff/sup/unit/queryUnitArchivesByProdCodePage";
	}


	/**
	 * 网点授权明细查询 (查询服务商授权明细)
	 */
	public  static String getNewCommWebsiteDetailList(){
		return CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/cc/bff/sup/unit/auth/queryUnitAuthorizeDetail";
	}

	/**
	 * 查询工程师授权明细
	 */
	public  static String getEngineerDetailList(){
		return CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/cc/bff/sup/engineerauth/queryEngineerAuthDetail";
	}

	/**
	 * 新网点明细解密接口
	 */
	public  static String getUnitDetailDecrypt(){
		return CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/cc/bff/sup/unit/queryUnitArchivesBaseInfo";
	}

	/**
	 * 查询工程师授权明细
	 */
	public  static String getEngineerDetailDecrypt(){
		return CS_DOMAIN_NEW+"/c-css/sup-bff-ipms/cc/bff/sup/engineer/queryEngineerBaseInfo";
	}

	/**
	 * 用户小类列表查询
	 */
	public  static String getUserThirdListUrl(){
		return CS_DOMAIN_NEW+"/c-css/css-sup-center/c-cc/sup/user/prod/queryUserThirdList";
	}

	/**
	 * 查询录单次数
	 */
	public static String getCountComplaintContactRequire(){
		return CS_DOMAIN_NEW + "/c-css/wom-bff-ipms/bff/wom/countComplaintContactRequire";
	}

	/**
	 * 对接2.0传入4a用户信息以及jsessionssoid
	 */
	public static String getUserInfoSaveInRedisUrl(){
		return CS_DOMAIN_NEW + "/c-css/c-css-api-service/cc/userInfo/saveInRedis";
	}

	/**
	 * 对接2.0传入4a用户信息以及jsessionssoid
	 */
	public static String getCssVipUrl(){
		return CS_DOMAIN_NEW + "/c-css/c-css-api-service/cc/vip/queryVipLevel2ByPhone";
	}


	/**
	 * 获取AIMP服务分类的URL
	 *
	 * 该方法使用ConfigUtil类的getString方法来获取配置项"AIMP_SERVICE_URL"的值
	 * 如果该配置项未设置或为空，则返回一个默认的URL地址
	 * 这个URL用于与美的集团的AIMP服务中的客户分类接口进行通信
	 *
	 * @return AIMP服务分类的URL地址如果配置中未设置，则返回默认URL
	 */
	public static String getAimpServiceClassificationUrl(){
	    return ConfigUtil.getString("AIMP_SERVICE_URL", "https://aimpapi.midea.com/t-aigc/aimp-customer-classification/llm/customer-assistant/service_classification");
	}

	/**
	 * 获取AIMP服务的认证信息
	 *
	 * 该方法用于从配置中获取AIMP服务的认证信息字符串如果配置中未找到相应的认证信息，
	 * 则返回一个预设的认证信息字符串
	 *
	 * @return AIMP服务的认证信息字符串
	 */
	public static String getAimpAuthorization(){
	    return ConfigUtil.getString("AIMP_AUTHORIZATION", "msk-2f8ea0a83063bdcab01d18f9a5ed1c9386d3c60da95246bb6bf6d055ac9181c9");
	}

	/**
	 * 根据服务单号获取最新预约时间和小号联系记录时间
	 */
	public  static String getAppointTimeByServiceOrderNo(){
		return CS_DOMAIN_NEW+"/c-css/wom-bff-ipms/csscc/api/wom/serviceorder/findAppointTimeByServiceOrderNo";
	}

	public static String getCustomerServiceSummaryApiUrl(){
		return ConfigUtil.getString("CUSTOMER_SERVICE_SUMMARY_API_URL","https://aimpapi.midea.com/t-aigc/customer-service-summary-api/api/chat/ccDialogue/fault/summary");
	}

	public static String getCustomerServiceSummaryApiKey(){
		return ConfigUtil.getString("CUSTOMER_SERVICE_SUMMARY_API_KEY","msk-99a9b7e6db994d48b215c62f5be6a884f5ced6825a77f6d8bc77f49803418a1e");
	}

	/**
	 * 根据网点编码查询网点信息
	 * @return
	 */
	public static String getUnitArchivesByProdcode() {
		return CS_DOMAIN_NEW + "/c-css/sup-bff-ipms/cc/bff/sup/unit/getunitarchivesbycode";
	}
}
