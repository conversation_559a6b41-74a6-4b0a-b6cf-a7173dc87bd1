package com.yunqu.cc.cssgw.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.WebListener;

import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.cssgw.base.Constants;

@WebListener
public class InterfaceLinstener extends ServiceContextListener{

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {

		List<ServiceResource> list = new ArrayList<ServiceResource>();

		//处理售后接口
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.cssgw.inf.InterfaceService"	;
		resource.description = "处理与售后系统对接的接口";
		resource.serviceId = ServiceID.CSSGW_INTERFACE;
		resource.serviceName = "处理与售后系统对接的接口";
		list.add(resource);

		//处理售后接入单相关接口
		ServiceResource contact = new ServiceResource();
		contact.appName = Constants.APP_NAME;
		contact.className = "com.yunqu.cc.cssgw.inf.ContactService"	;
		contact.description = "处理与售后系统接入单相关对接的接口";
		contact.serviceId = "CSSGW-CONTACT";
		contact.serviceName = "处理与售后系统接入单相关对接的接口";
		list.add(contact);

		//处理售后回访相关接口
		ServiceResource revisit = new ServiceResource();
		revisit.appName = Constants.APP_NAME;
		revisit.className = "com.yunqu.cc.cssgw.inf.RevisitService"	;
		revisit.description = "处理与售后系统回访相关对接的接口";
		revisit.serviceId = "CSSGW-REVISIT";
		revisit.serviceName = "处理与售后系统回访相关对接的接口";
		list.add(revisit);

		//处理售后通用数据相关接口
		ServiceResource comm = new ServiceResource();
		comm.appName = Constants.APP_NAME;
		comm.className = "com.yunqu.cc.cssgw.inf.CommService"	;
		comm.description = "处理与售后系统通用数据相关对接的接口";
		comm.serviceId = "CSSGW-COMM";
		comm.serviceName = "处理与售后系统通用数据相关对接的接口";
		list.add(comm);

		//处理售后投诉相关接口
		ServiceResource complain = new ServiceResource();
		complain.appName = Constants.APP_NAME;
		complain.className = "com.yunqu.cc.cssgw.inf.ComplainService"	;
		complain.description = "处理与售后系统投诉相关对接的接口";
		complain.serviceId = "CSSGW-COMPLAIN";
		complain.serviceName = "处理与售后系统投诉相关对接的接口";
		list.add(complain);

		//处理售后其他业务相关接口（统一口径、计划提醒、免考核、特批申请）
		ServiceResource other = new ServiceResource();
		other.appName = Constants.APP_NAME;
		other.className = "com.yunqu.cc.cssgw.inf.OtherService"	;
		other.description = "处理与售后系统其他业务相关对接的接口";
		other.serviceId = "CSSGW-OTHER";
		other.serviceName = "处理与售后系统其他业务相关对接的接口";
		list.add(other);

		//处理售后其他业务相关接口（统一口径、计划提醒、免考核、特批申请）
		ServiceResource orderService = new ServiceResource();
		orderService.appName = Constants.APP_NAME;
		orderService.className = "com.yunqu.cc.cssgw.inf.OrderService"	;
		orderService.description = "处理与css对接相关接口";
		orderService.serviceId = "CSSGW-ORDER";
		orderService.serviceName = "处理与css对接相关接口";
		list.add(orderService);


		//处理售后其他业务相关接口（统一口径、计划提醒、免考核、特批申请）
		ServiceResource sentiment = new ServiceResource();
		sentiment.appName = Constants.APP_NAME;
		sentiment.className = "com.yunqu.cc.cssgw.inf.SentimentService"	;
		sentiment.description = "处理舆情工单与售后系统的业务对接的接口";
		sentiment.serviceId = "CSSGW-SENTIMENT";
		sentiment.serviceName = "处理舆情工单与售后系统的业务对接的接口";
		list.add(sentiment);

		// 处理调用genesys接口
		ServiceResource genesys = new ServiceResource();
		genesys.appName = Constants.APP_NAME;
		genesys.className = "com.yunqu.cc.cssgw.inf.GenesysService"	;
		genesys.description = "处理调用genesys接口功能";
		genesys.serviceId = "CSSGW-GENESYS";
		genesys.serviceName = "处理调用genesys接口功能";
		list.add(genesys);

		// 处理调用iot接口
		ServiceResource iot = new ServiceResource();
		iot.appName = Constants.APP_NAME;
		iot.className = "com.yunqu.cc.cssgw.inf.IotRevisitService"	;
		iot.description = "处理调用iot接口功能";
		iot.serviceId = "CSSGW-IOT";
		iot.serviceName = "处理调用iot接口功能";
		list.add(iot);

		// 处理调用现金补偿接口
		ServiceResource compensate = new ServiceResource();
		compensate.appName = Constants.APP_NAME;
		compensate.className = "com.yunqu.cc.cssgw.inf.CompensateService"	;
		compensate.description = "处理调用现金补偿功能";
		compensate.serviceId = "CSSGW-COMPENSATE";
		compensate.serviceName = "处理调用现金补偿功能";
		list.add(compensate);

		// 处理调用现金补偿接口
		ServiceResource logistics = new ServiceResource();
		logistics.appName = Constants.APP_NAME;
		logistics.className = "com.yunqu.cc.cssgw.inf.LogisticsUpgradeService"	;
		logistics.description = "物流升级接口";
		logistics.serviceId = "CSSGW-LOGISTICS-UPGRADE";
		logistics.serviceName = "物流升级接口";
		list.add(logistics);

		// 处理调用现金补偿接口
		ServiceResource cxsso = new ServiceResource();
		cxsso.appName = Constants.APP_NAME;
		cxsso.className = "com.yunqu.cc.cssgw.inf.CxSsoService"	;
		cxsso.description = "对接2.0同步用户信息";
		cxsso.serviceId = "CSSGW-CXSSO-USERINFO";
		cxsso.serviceName = "对接2.0同步用户信息";
		list.add(cxsso);
		// 获取css用户vip信息
		ServiceResource getCssVip = new ServiceResource();
		getCssVip.appName = Constants.APP_NAME;
		getCssVip.className = "com.yunqu.cc.cssgw.inf.CssGetVipService"	;
		getCssVip.description = "获取cssvip信息";
		getCssVip.serviceId = "CSSGW-GETVIP-USERINFO";
		getCssVip.serviceName = "获取cssvip信息";
		list.add(getCssVip);

		return list;
	}

}
