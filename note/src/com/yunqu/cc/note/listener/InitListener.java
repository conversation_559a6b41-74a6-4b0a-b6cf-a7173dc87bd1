package com.yunqu.cc.note.listener;

import cn.hutool.core.thread.ThreadUtil;
import com.yunqu.cc.note.base.CommonLogger;
import com.yunqu.cc.note.base.Constants;
import com.yunqu.cc.note.utils.mq.MqBrokerControl;
import com.yunqu.cc.note.utils.mq.NoticeConsumerManager;
import com.yunqu.cc.note.utils.mq.NoticeMessageConsumer;
import org.apache.log4j.Logger;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

@WebListener
public class InitListener implements ServletContextListener {

	public static boolean start = false;

	public Logger logger = CommonLogger.logger;

	@Override
	public void contextInitialized(ServletContextEvent servletContextEvent) {
		try {
			start = true;

			if("Y".equals(Constants.getMqConsumerFlag())){
				MqBrokerControl instance = MqBrokerControl.getInstance();
				instance.initBroker();
				
				// 启动公告消费者
				NoticeConsumerManager.startConsumer();
			}
		} catch (Exception e) {
			CommonLogger.getLogger("").error(e.getMessage(), e);
		}
	}

	@Override
	public void contextDestroyed(ServletContextEvent servletContextEvent) {
		try {
			start = false;

			if("Y".equals(Constants.getMqConsumerFlag())) {
				// 停止公告消费者
				NoticeConsumerManager.stopConsumer();
				
				MqBrokerControl.getInstance().shutDown();
			}
		} catch (Exception e) {
			CommonLogger.getLogger("").error(e.getMessage(), e);
		}
	}
}