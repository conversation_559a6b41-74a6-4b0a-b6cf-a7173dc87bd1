package com.yunqu.cc.note.inf;

import java.io.UnsupportedEncodingException;
import java.net.URLDecoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.List;

import com.alibaba.fastjson.JSONArray;
import com.yq.busi.common.util.ConfigUtil;
import com.yunqu.cc.note.base.Constants;
import com.yunqu.cc.note.model.NoticeInfo;
import com.yunqu.cc.note.model.NoticeRecv;
import com.yunqu.cc.note.model.NoticeSend;
import com.yunqu.cc.note.utils.mq.MqBrokerControl;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.note.base.CommonLogger;

import javax.jms.MessageProducer;
import javax.jms.Session;
import javax.jms.TextMessage;

public class NoticeInfoSaveService extends IService{

    @Override
    public JSONObject invoke(JSONObject json) throws ServiceException {
      String flag = Constants.getMqConsumerFlag();
      if ("N".equalsIgnoreCase(flag)) {
		  CommonLogger.logger.info("[NoticeInfoSaveService]" + "调用原新增公告接口invokeOriginal");
        return invokeOriginal(json);
      } else {
		  CommonLogger.logger.info("[NoticeInfoSaveService]" + "调用消息队列接口invokeAsync");
        return invokeAsync(json);
      }
    }

	/**
	 * 异步改造
	 * 1. 接口改造
	 * invoke方法现在只负责参数验证和消息入队
	 * 快速返回响应，提高接口性能
	 * 使用MqBrokerControl发送消息到ADD_ANNOUNCEMENT_INFO队列
	 * @param json
	 * @return
	 * @throws ServiceException
	 */
	private JSONObject invokeAsync(JSONObject json) throws ServiceException {
		CommonLogger.logger.info("[NoticeInfoSaveService->invoke]接收到公告推送请求: " + json.toJSONString());

		JSONObject result = new JSONObject();

		try {
			// 基本参数验证
			if (!validateBasicParams(json)) {
				result.put("respDesc", "请求异常:参数验证失败");
				result.put("respCode", "001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数验证失败");
				return result;
			}

			// 将公告数据发送到MQ队列
			boolean sendSuccess = sendToQueue(json);
			if (sendSuccess) {
				result.put("respDesc", "公告已成功入队，等待处理");
				result.put("respCode", "000");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke] 公告数据已成功发送到MQ队列");
			} else {
				result.put("respDesc", "公告入队失败，请稍后重试");
				result.put("respCode", "002");
				CommonLogger.logger.warn("[NoticeInfoSaveService->invoke] 公告数据入队失败");
			}

		} catch (Exception e) {
			result.put("respDesc", "系统异常");
			result.put("respCode", "999");
			CommonLogger.logger.error("[NoticeInfoSaveService.invoke error:] " + e.getMessage(), e);
		}

		return result;
	}

	/**
	 * 基本参数验证
	 */
	private boolean validateBasicParams(JSONObject json) {
		String[] requiredParams = {"senderId", "senderName", "title", "content", "noticeReceiver"};

		for (String param : requiredParams) {
			if (StringUtils.isBlank(json.getString(param))) {
				CommonLogger.logger.warn("[NoticeInfoSaveService->validateBasicParams] 参数缺失: " + param);
				return false;
			}
		}
		return true;
	}

	/**
	 * 发送公告数据到MQ队列
	 */
	private boolean sendToQueue(JSONObject json) {
		try {
			MqBrokerControl mqControl = MqBrokerControl.getInstance();
			MessageProducer producer = mqControl.getProducer("ADD_ANNOUNCEMENT_INFO");
			Session session = mqControl.getSession("ADD_ANNOUNCEMENT_INFO");

			if (producer == null || session == null) {
				CommonLogger.logger.error("[NoticeInfoSaveService->sendToQueue] MQ连接或生产者未初始化");
				return false;
			}

			// 创建消息
			TextMessage message = session.createTextMessage(json.toJSONString());

			// 设置消息属性，便于消费者识别
			message.setStringProperty("messageType", "ANNOUNCEMENT");
			message.setStringProperty("senderId", json.getString("senderId"));
			message.setStringProperty("title", json.getString("title"));
			message.setLongProperty("timestamp", System.currentTimeMillis());

			// 发送消息到队列
			producer.send(message);

			CommonLogger.logger.info("[NoticeInfoSaveService->sendToQueue] 公告数据已发送到队列: " + json.getString("title"));
			return true;

		} catch (Exception e) {
			CommonLogger.logger.error("[NoticeInfoSaveService->sendToQueue] 发送消息到队列失败: " + e.getMessage(), e);
			return false;
		}
	}



	/**
	 * 原接口
	 * @param str
	 * @return
	 */
	private JSONObject invokeOriginal(JSONObject json) throws ServiceException {
		CommonLogger.logger.info("[NoticeInfoSaveService->invoke]JSONObject:"+json.toJSONString());
		JSONObject result = new JSONObject();
		JSONArray noticeReceiverJsonArr = null;
		String appName=Constants.APP_NAME;
		String appDatasourceName= Constants.DS_NAME;
		EasyQuery query = EasyQuery.getQuery(appName, appDatasourceName);
		try {
			String senderId = json.getString("senderId");//发送者ID
			String senderName = json.getString("senderName");//发送者姓名
			String title = json.getString("title");//公告标题
			String content = json.getString("content");//公告内容
			
			Boolean urgent = Boolean.valueOf(json.getString("urgent"));//是否紧急，是：true，否:false。为空时默认不紧急
			String publishTime = json.getString("publishTime");//生效时间，如：1970-01-01 00:00:00为空时即时生效
			String validTime = json.getString("validTime");//失效时间，如：1970-01-01 00:00:00为空时即时生效
			
			String noticeReceiver = json.getString("noticeReceiver");
			
			if(noticeReceiver == null){
				result.put("respDesc","请求异常:参数缺失[noticeReceiver]");
				result.put("respCode","001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[noticeReceiver]");
				return result;
			}else{
				noticeReceiverJsonArr = JSONArray.parseArray(noticeReceiver);
			}
			
			if(StringUtils.isBlank(senderId)){
				result.put("respDesc","请求异常:参数缺失[senderId]");
				result.put("respCode","001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[senderId]");
				return result;
			}else{
				String sql =  "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER WHERE 1=1 AND AGENT_ID = '"+senderId+"'";
				List<JSONObject> membeSend = query.queryForList(sql, null,new JSONMapperImpl());
				if(membeSend.size() == 0){
					result.put("respDesc","添加失败,发送者ID[senderId:"+senderId+"']未找到");
					result.put("respCode","999");
					CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[senderId]");
					return result;
				}
			}
			
			if(StringUtils.isBlank(senderName)){
				result.put("respDesc","请求异常:参数缺失[senderName]");
				result.put("respCode","001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[senderName]");
				return result;
			}
			if(StringUtils.isBlank(title)){
				result.put("respDesc","请求异常:参数缺失[title]");
				result.put("respCode","001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[title]");
				return result;
			}
			if(StringUtils.isBlank(content)){
				result.put("respDesc","请求异常:参数缺失[content]");
				result.put("respCode","001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[content]");
				return result;
			}
			if(urgent != null && urgent != true && urgent != false && !urgent.equals("")){
				result.put("respDesc","请求异常:参数格式错误[urgent]:"+urgent);
				result.put("respCode","001");
				CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数格式错误[urgent]:"+urgent);
				return result;
			}
			if(publishTime != null && !publishTime.equals("")){
				if(isValidDate(publishTime) == false){
					result.put("respDesc","请求异常:参数格式错误[publishTime]:"+publishTime);
					result.put("respCode","001");
					CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数格式错误[publishTime]:"+publishTime);
					return result;
				}
			}else{
				publishTime = EasyDate.getCurrentDateString();
			}
			
			if(validTime != null && !validTime.equals("")){
				if(isValidDate(validTime) == false){
					result.put("respDesc","请求异常:参数格式错误[validTime]:"+validTime);
					result.put("respCode","001");
					CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数格式错误[validTime]:"+validTime);
					return result;
				}
			}else{
				validTime = "2999-12-31 00:00:00";
			}
			
			String noticeInfoId = RandomKit.randomStr();
			Integer recvType = null;
			String recvID = null;
			int successNum = 0;
			int failNum = 0;
			String failName = "";
			if(noticeReceiverJsonArr.size() > 0){
				  for(int i=0;i<noticeReceiverJsonArr.size();i++){
				    JSONObject noticeReceiverJson = noticeReceiverJsonArr.getJSONObject(i);
				    recvType = noticeReceiverJson.getInteger("recvType");
				    recvID = noticeReceiverJson.getString("recvID");
				    
				    if(recvType != 0 && recvType != 1 && recvType != 2){
						result.put("respDesc","请求异常:参数错误[recvType]:"+recvType);
						result.put("respCode","001");
						CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数错误[recvType]:"+recvType);
						return result;
					}
				    if(recvID == null){
						result.put("respDesc","请求异常:参数缺失[recvID]");
						result.put("respCode","001");
						CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[recvID]");
						return result;
					}
				    if(StringUtils.isBlank(noticeReceiverJson.getString("recvType"))){
						result.put("respDesc","请求异常:参数缺失[recvType]");
						result.put("respCode","001");
						CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:请求异常:参数缺失[recvType]");
						return result;
					}
				    
				    if(recvType == 0){
						String sql =  "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER WHERE 1=1 AND AGENT_ID = '"+recvID+"'";
						List<JSONObject> memberRecv = query.queryForList(sql, null,new JSONMapperImpl());
						if(memberRecv.size() == 0){
							//对于个人情况，发送失败跳过情况
							failNum = failNum + 1;
							failName = recvID + ",";
							CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:添加失败,接收者ID[recvID:"+recvID+"']未找到");
							continue;
						}
						NoticeRecv noticeRecv = new NoticeRecv();
						noticeRecv.put("NOTICE_ID", noticeInfoId);
						noticeRecv.put("AGENT_ID", recvID);
						noticeRecv.put("AGENT_NAME", memberRecv.get(0).get("AGENT_NAME"));
						noticeRecv.put("IS_READ", Constants.IS_READ_NO);
						noticeRecv.put("IS_COLLECT", Constants.IS_COLLECT_NO);
						noticeRecv.put("IS_TOP", Constants.IS_TOP_NO);
						noticeRecv.put("IS_REPLY", Constants.IS_REPLY_NO);
						noticeRecv.put("IS_COPY_PERSON", Constants.IS_COPY_PERSON_NO);
						successNum = successNum + 1;
						query.save(noticeRecv);
					}else if(recvType == 1){
						String sql =  "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER WHERE 1=1 AND T_GROUP_ID = '"+recvID+"'";
						List<JSONObject> memberList = query.queryForList(sql, null,new JSONMapperImpl());
						if(memberList == null){
							result.put("respDesc","添加失败,群组ID为["+recvID+"]不存在或者该群组下没有成员");
							result.put("respCode","999");
							CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:添加失败,群组ID为["+recvID+"]不存在或者该群组下没有成员");
							return result;
						}else{
							successNum = memberList.size();
							for (JSONObject rang : memberList) {
								NoticeRecv noticeRecv = new NoticeRecv();
								noticeRecv.put("NOTICE_ID", noticeInfoId);
								noticeRecv.put("AGENT_NO", null);
								noticeRecv.put("AGENT_ID", rang.get("AGENT_ID"));
								noticeRecv.put("AGENT_NAME", rang.get("AGENT_NAME"));
								noticeRecv.put("IS_READ", Constants.IS_READ_NO);
								noticeRecv.put("IS_COLLECT", Constants.IS_COLLECT_NO);
								noticeRecv.put("IS_TOP", Constants.IS_TOP_NO);
								noticeRecv.put("IS_REPLY", Constants.IS_REPLY_NO);
								noticeRecv.put("IS_COPY_PERSON", Constants.IS_COPY_PERSON_NO);
								noticeRecv.put("CREATE_TIME", EasyDate.getCurrentDateString());
								noticeRecv.put("IS_DEL", Constants.IS_DEL_NO);
								query.save(noticeRecv);
							}
						}
					}else if(recvType == 2){
						String sql =  "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER";
						List<JSONObject> memberList = query.queryForList(sql, null,new JSONMapperImpl());
						if(memberList == null){
							result.put("respDesc","添加失败,所有的群组成员为空");
							result.put("respCode","999");
							CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:添加失败,所有的群组成员为空");
							return result;
						}else{
							successNum = memberList.size();
							for (JSONObject rang : memberList) {
								NoticeRecv noticeRecv = new NoticeRecv();
								noticeRecv.put("NOTICE_ID", noticeInfoId);
								noticeRecv.put("AGENT_NO", null);
								noticeRecv.put("AGENT_ID", rang.get("AGENT_ID"));
								noticeRecv.put("AGENT_NAME", rang.get("AGENT_NAME"));
								noticeRecv.put("IS_READ", Constants.IS_READ_NO);
								noticeRecv.put("IS_COLLECT", Constants.IS_COLLECT_NO);
								noticeRecv.put("IS_TOP", Constants.IS_TOP_NO);
								noticeRecv.put("IS_REPLY", Constants.IS_REPLY_NO);
								noticeRecv.put("IS_COPY_PERSON", Constants.IS_COPY_PERSON_NO);
								noticeRecv.put("IS_DEL", Constants.IS_DEL_NO);
								noticeRecv.put("CREATE_TIME", EasyDate.getCurrentDateString());
								query.save(noticeRecv);
							}
						}
					}
				  }
			}
			
			//对于个人情况，发送失败跳过情况
			if(recvType == 0){
				if(failNum >= noticeReceiverJsonArr.size()){
					result.put("respDesc","添加成功人数："+successNum+"人,添加失败人数："+failNum+"人,失败人员Id为："+failName);
					result.put("respCode","000");
					CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:添加成功人数："+successNum+"人,添加失败人数："+failNum+"人,失败人员Id为："+failName);
					return result;
				}else{
					NoticeInfo noticeInfo = new NoticeInfo();
					noticeInfo.put("SENDER_NO", "");
					noticeInfo.put("SENDER_ID", senderId);
					noticeInfo.put("SENDER_NAME", senderName);
					noticeInfo.put("NOTICE_TITLE", title);
					if(urgent == null || urgent.equals("")){
						noticeInfo.put("IS_IMPORTANT", "0");
					}else if(urgent == true){
						noticeInfo.put("IS_IMPORTANT", "1");
					}else if(urgent == false){
						noticeInfo.put("IS_IMPORTANT", "0");
					}
					noticeInfo.put("TYPE_ID", "其他模块调用");
					noticeInfo.put("NOTICE_CONTENT", URLDecoder.decode(content,"UTF-8"));
					noticeInfo.put("SEND_TIME", EasyDate.getCurrentDateString());
					noticeInfo.put("PUBLISH_TIME", publishTime);
					noticeInfo.put("INVALID_TIME", validTime);
					noticeInfo.put("IS_RECALL", Constants.IS_RECALL_NO);
					noticeInfo.put("IS_DRAFT", Constants.IS_DRAFT_NO);
					noticeInfo.put("IS_DEL", Constants.IS_DEL_NO);
					noticeInfo.put("NOTICE_ID",noticeInfoId);
					noticeInfo.put("NOTICE_RECEIVER",recvID);
					query.save(noticeInfo);
					
					NoticeSend noticeSend = new NoticeSend();
					noticeSend.put("NOTICE_ID", noticeInfoId);
					noticeSend.put("AGENT_NO", "");
					noticeSend.put("AGENT_ID", senderId);
					noticeSend.put("AGENT_NAME", senderName);
					noticeSend.put("CREATE_TIME", EasyDate.getCurrentDateString());
					noticeSend.put("IS_DEL", Constants.IS_DEL_NO);
					boolean boo = query.save(noticeSend);
					if(boo){
						result.put("respDesc","添加成功人数："+successNum+"人,添加失败人数："+failNum+"人,失败人员Id为："+failName);
						result.put("respCode","000");
						CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:添加成功人数："+successNum+"人,添加失败人数："+failNum+"人,失败人员Id为："+failName);
						return result;
					}
				}
			}else{
				NoticeInfo noticeInfo = new NoticeInfo();
				noticeInfo.put("SENDER_NO", "");
				noticeInfo.put("SENDER_ID", senderId);
				noticeInfo.put("SENDER_NAME", senderName);
				noticeInfo.put("NOTICE_TITLE", title);
				if(urgent == null && urgent.equals("")){
					noticeInfo.put("IS_IMPORTANT", "0");
				}else if(urgent == true){
					noticeInfo.put("IS_IMPORTANT", "1");
				}else if(urgent == false){
					noticeInfo.put("IS_IMPORTANT", "0");
				}
				noticeInfo.put("TYPE_ID", "其他模块调用");
				noticeInfo.put("NOTICE_CONTENT", URLDecoder.decode(content,"UTF-8"));
				noticeInfo.put("SEND_TIME", EasyDate.getCurrentDateString());
				noticeInfo.put("PUBLISH_TIME", publishTime);
				noticeInfo.put("INVALID_TIME", validTime);
				noticeInfo.put("IS_RECALL", Constants.IS_RECALL_NO);
				noticeInfo.put("IS_DRAFT", Constants.IS_DRAFT_NO);
				noticeInfo.put("IS_DEL", Constants.IS_DEL_NO);
				noticeInfo.put("NOTICE_ID",noticeInfoId);
				noticeInfo.put("NOTICE_RECEIVER",recvID);
				query.save(noticeInfo);
				
				NoticeSend noticeSend = new NoticeSend();
				noticeSend.put("NOTICE_ID", noticeInfoId);
				noticeSend.put("AGENT_NO", "");
				noticeSend.put("AGENT_ID", senderId);
				noticeSend.put("AGENT_NAME", senderName);
				noticeSend.put("CREATE_TIME", EasyDate.getCurrentDateString());
				noticeSend.put("IS_DEL", Constants.IS_DEL_NO);
				boolean boo = query.save(noticeSend);
				if(boo){
					result.put("respDesc","添加成功");
					result.put("respCode","000");
					CommonLogger.logger.info("[NoticeInfoSaveService->invoke]:添加成功,人数："+successNum+"人");
					return result;
				}
			}
		} catch (Exception e) {
			result.put("respDesc","添加失败");
			result.put("respCode","999");
			CommonLogger.logger.error("[NoticeInfoSaveService.invoke error:]"+e.getMessage(),e);
			return result;
		}
		return null;
	}
	
	private boolean isValidDate(String str) {
        boolean convertSuccess = true;
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
        try {
            format.setLenient(false);
            format.parse(str);
        } catch (ParseException e) {
            convertSuccess = false;
        }
        return convertSuccess;
    }

	public static void main(String[] args) throws UnsupportedEncodingException {
		System.out.println(URLDecoder.decode("%3Chtml%3E%3C%2Fhtml%3E","UTF-8"));
	}
}
