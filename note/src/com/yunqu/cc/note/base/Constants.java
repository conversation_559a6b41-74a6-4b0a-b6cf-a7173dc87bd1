package com.yunqu.cc.note.base;

import com.yq.busi.common.util.ConfigUtil;
import org.easitline.common.core.context.AppContext;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {
	
	public final static String DS_NAME = "yw-ds";     //默认数据源名称
	public final static String MARS_DS_NAME = "mars-ds";     //mars数据源名称
	
	public final static String APP_NAME = "note";     //应用
	
	public final static String IS_READ_NO = "0";     //是否阅读 否
	public final static String IS_RECALL_NO = "0";     //是否撤回 否
	public final static String IS_RECALL_YES = "1";     //是否撤回 是
	public final static String IS_DRAFT_NO = "0";     //是否草稿 否
	public final static String IS_COLLECT_NO = "0";     //是否收藏 否
	public final static String IS_TOP_NO = "0";     //是否置顶 否
	public final static String IS_REPLY_NO = "0";     //是否回复 否
	public final static String IS_COPY_PERSON_NO = "0";     //是否抄送人 否
	public final static String IS_COPY_PERSON_YES = "1";     //是否抄送人 是
	
	public final static String IS_DEL_YES = "1";     //是否删除 是
	public final static String IS_DEL_NO = "0";     //是否删除 否

	//public final static String DS_NAME = "note";     //默认数据源名称
	
//	public final static String APP_NAME = "note";     //应用

	public static final AppContext context = AppContext.getContext(APP_NAME);

	/**
	 * 是否启动消费者
	 */
	public static String getMqConsumerFlag() {
		return ConfigUtil.getString(Constants.APP_NAME,"START_CONSUMER_FLAG");
	}

	public static String getMqAddr() {
		return ConfigUtil.getString(Constants.APP_NAME,"ActiveMQ_ADDR");
	}

}
