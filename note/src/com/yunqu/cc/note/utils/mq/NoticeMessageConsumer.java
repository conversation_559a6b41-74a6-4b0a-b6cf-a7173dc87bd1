package com.yunqu.cc.note.utils.mq;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.note.base.CommonLogger;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import javax.jms.JMSException;
import javax.jms.MessageConsumer;
import javax.jms.TextMessage;
import java.net.URLDecoder;
import java.util.List;
import java.util.concurrent.TimeUnit;

import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.cc.note.base.Constants;
import com.yunqu.cc.note.model.NoticeInfo;
import com.yunqu.cc.note.model.NoticeRecv;
import com.yunqu.cc.note.model.NoticeSend;

/**
 * 公告消息消费者
 */
public class NoticeMessageConsumer implements Runnable {
    private static final Logger logger = CommonLogger.getLogger("notice_consumer");
    private static final String NOTICE_QUEUE_NAME = "ADD_ANNOUNCEMENT_INFO";
	private static final String appName = Constants.APP_NAME;
	private static final String appDatasourceName = Constants.DS_NAME;
	private static EasyQuery query = EasyQuery.getQuery(appName, appDatasourceName);

    @Override
    public void run() {
        MqBrokerControl mqBrokerControl = MqBrokerControl.getInstance();
        MessageConsumer consumer = mqBrokerControl.getConsumer(NOTICE_QUEUE_NAME);
        try {
            consumer.setMessageListener(message -> {
                TextMessage textMessage = (TextMessage) message;
                String msg = null;
                try {
                    msg = textMessage.getText();
                } catch (JMSException e) {
                    logger.info(e.getMessage(), e);
                }
                if (StringUtils.isBlank(msg)) {
                    return;
                }
                execute(msg);
            });
        } catch (Exception e) {
            logger.info("[NoticeMessageConsumer.error] e:" + e.getMessage());
        }
    }

    private void execute(String msg) {
        logger.info("[NoticeMessageConsumer.onMessage]<<<" + msg);
        try {
            JSONObject json = JSONObject.parseObject(msg);

            long startTime = System.currentTimeMillis();
            logger.info("[NoticeMessageConsumer.execute] 开始处理公告消息，开始时间: " + startTime);

            // 处理公告业务逻辑
            processNoticeData(json);

            long endTime = System.currentTimeMillis();
            logger.info("[NoticeMessageConsumer.execute] 公告消息处理完成，结束时间: " + endTime + "，耗时: " + (endTime - startTime) + "ms");

        } catch (Exception e) {
            logger.error("[NoticeMessageConsumer.execute] 处理公告消息异常: " + e.getMessage(), e);
        }
    }

    /**
     * 处理公告数据的核心业务逻辑
     */
    private void processNoticeData(JSONObject json) throws Exception {
        JSONArray noticeReceiverJsonArr = null;

        if (query == null) {
			query = EasyQuery.getQuery(appName, appDatasourceName);
		}
        
        try {
            String senderId = json.getString("senderId");
            String senderName = json.getString("senderName");
            String title = json.getString("title");
            String content = json.getString("content");
            
            Boolean urgent = Boolean.valueOf(json.getString("urgent"));
            String publishTime = json.getString("publishTime");
            String validTime = json.getString("validTime");
            
            String noticeReceiver = json.getString("noticeReceiver");
            
            // 参数验证
            if(noticeReceiver == null){
                logger.error("[NoticeMessageConsumer] 请求异常:参数缺失[noticeReceiver]");
                return;
            }else{
                noticeReceiverJsonArr = JSONArray.parseArray(noticeReceiver);
            }
            
            // 验证发送者
            if(StringUtils.isBlank(senderId)){
                logger.error("[NoticeMessageConsumer] 请求异常:参数缺失[senderId]");
                return;
            }else{
                String sql = "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER WHERE 1=1 AND AGENT_ID = '"+senderId+"'";
                List<JSONObject> membeSend = query.queryForList(sql, null, new JSONMapperImpl());
                if(membeSend.size() == 0){
                    logger.error("[NoticeMessageConsumer] 添加失败,发送者ID[senderId:"+senderId+"']未找到");
                    return;
                }
            }
            
            // 验证其他必需参数
            if(StringUtils.isBlank(senderName) || StringUtils.isBlank(title) || StringUtils.isBlank(content)){
                logger.error("[NoticeMessageConsumer] 请求异常:必需参数缺失");
                return;
            }
            
            // 处理时间参数
            if(publishTime == null || publishTime.equals("")){
                publishTime = EasyDate.getCurrentDateString();
            }
            if(validTime == null || validTime.equals("")){
                validTime = "2999-12-31 00:00:00";
            }
            
            // 处理接收者逻辑
            String noticeInfoId = RandomKit.randomStr();
            Integer recvType = null;
            String recvID = null;
            int successNum = 0;
            int failNum = 0;
            String failName = "";
            
            if(noticeReceiverJsonArr.size() > 0){
                for(int i=0; i<noticeReceiverJsonArr.size(); i++){
                    JSONObject noticeReceiverJson = noticeReceiverJsonArr.getJSONObject(i);
                    recvType = noticeReceiverJson.getInteger("recvType");
                    recvID = noticeReceiverJson.getString("recvID");
                    
                    // 验证接收者参数
                    if(recvType != 0 && recvType != 1 && recvType != 2){
                        logger.error("[NoticeMessageConsumer] 请求异常:参数错误[recvType]:" + recvType);
                        return;
                    }
                    if(recvID == null){
                        logger.error("[NoticeMessageConsumer] 请求异常:参数缺失[recvID]");
                        return;
                    }
                    
                    // 处理不同类型的接收者
                    if(recvType == 0){
                        // 个人接收者
                        String sql = "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER WHERE 1=1 AND AGENT_ID = '"+recvID+"'";
                        List<JSONObject> memberRecv = query.queryForList(sql, null, new JSONMapperImpl());
                        if(memberRecv.size() == 0){
                            failNum = failNum + 1;
                            failName = recvID + ",";
                            logger.info("[NoticeMessageConsumer] 添加失败,接收者ID[recvID:"+recvID+"']未找到");
                            continue;
                        }
                        NoticeRecv noticeRecv = new NoticeRecv();
                        noticeRecv.put("NOTICE_ID", noticeInfoId);
                        noticeRecv.put("AGENT_ID", recvID);
                        noticeRecv.put("AGENT_NAME", memberRecv.get(0).get("AGENT_NAME"));
                        noticeRecv.put("IS_READ", Constants.IS_READ_NO);
                        noticeRecv.put("IS_COLLECT", Constants.IS_COLLECT_NO);
                        noticeRecv.put("IS_TOP", Constants.IS_TOP_NO);
                        noticeRecv.put("IS_REPLY", Constants.IS_REPLY_NO);
                        noticeRecv.put("IS_COPY_PERSON", Constants.IS_COPY_PERSON_NO);
                        successNum = successNum + 1;
                        query.save(noticeRecv);
                    }else if(recvType == 1){
                        // 群组接收者
                        String sql = "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER WHERE 1=1 AND T_GROUP_ID = '"+recvID+"'";
                        List<JSONObject> memberList = query.queryForList(sql, null, new JSONMapperImpl());
                        if(memberList == null || memberList.size() == 0){
                            logger.error("[NoticeMessageConsumer] 添加失败,群组ID为["+recvID+"]不存在或者该群组下没有成员");
                            return;
                        }else{
                            successNum = memberList.size();
                            for (JSONObject rang : memberList) {
                                NoticeRecv noticeRecv = new NoticeRecv();
                                noticeRecv.put("NOTICE_ID", noticeInfoId);
                                noticeRecv.put("AGENT_NO", null);
                                noticeRecv.put("AGENT_ID", rang.get("AGENT_ID"));
                                noticeRecv.put("AGENT_NAME", rang.get("AGENT_NAME"));
                                noticeRecv.put("IS_READ", Constants.IS_READ_NO);
                                noticeRecv.put("IS_COLLECT", Constants.IS_COLLECT_NO);
                                noticeRecv.put("IS_TOP", Constants.IS_TOP_NO);
                                noticeRecv.put("IS_REPLY", Constants.IS_REPLY_NO);
                                noticeRecv.put("IS_COPY_PERSON", Constants.IS_COPY_PERSON_NO);
                                noticeRecv.put("CREATE_TIME", EasyDate.getCurrentDateString());
                                noticeRecv.put("IS_DEL", Constants.IS_DEL_NO);
                                query.save(noticeRecv);
                            }
                        }
                    }else if(recvType == 2){
                        // 全体接收者
                        String sql = "select AGENT_ID,AGENT_NAME from C_NOTICE_GROUP_MEMBER";
                        List<JSONObject> memberList = query.queryForList(sql, null, new JSONMapperImpl());
                        if(memberList == null || memberList.size() == 0){
                            logger.error("[NoticeMessageConsumer] 添加失败,所有的群组成员为空");
                            return;
                        }else{
                            successNum = memberList.size();
                            for (JSONObject rang : memberList) {
                                NoticeRecv noticeRecv = new NoticeRecv();
                                noticeRecv.put("NOTICE_ID", noticeInfoId);
                                noticeRecv.put("AGENT_NO", null);
                                noticeRecv.put("AGENT_ID", rang.get("AGENT_ID"));
                                noticeRecv.put("AGENT_NAME", rang.get("AGENT_NAME"));
                                noticeRecv.put("IS_READ", Constants.IS_READ_NO);
                                noticeRecv.put("IS_COLLECT", Constants.IS_COLLECT_NO);
                                noticeRecv.put("IS_TOP", Constants.IS_TOP_NO);
                                noticeRecv.put("IS_REPLY", Constants.IS_REPLY_NO);
                                noticeRecv.put("IS_COPY_PERSON", Constants.IS_COPY_PERSON_NO);
                                noticeRecv.put("IS_DEL", Constants.IS_DEL_NO);
                                noticeRecv.put("CREATE_TIME", EasyDate.getCurrentDateString());
                                query.save(noticeRecv);
                            }
                        }
                    }
                }
            }
            
            // 保存公告信息
            saveNoticeInfo(query, noticeInfoId, senderId, senderName, title, content, 
                          urgent, publishTime, validTime, recvID);
            
            // 保存发送记录
            saveNoticeSend(query, noticeInfoId, senderId, senderName);
            
            logger.info("[NoticeMessageConsumer] 公告处理完成，成功人数: " + successNum + "，失败人数: " + failNum);
            
        } catch (Exception e) {
            logger.error("[NoticeMessageConsumer] 处理公告数据异常: " + e.getMessage(), e);
        }
    }
    
    /**
     * 保存公告信息
     */
    private void saveNoticeInfo(EasyQuery query, String noticeInfoId, String senderId, 
                               String senderName, String title, String content, 
                               Boolean urgent, String publishTime, String validTime, 
                               String recvID) throws Exception {
        NoticeInfo noticeInfo = new NoticeInfo();
        noticeInfo.put("SENDER_NO", "");
        noticeInfo.put("SENDER_ID", senderId);
        noticeInfo.put("SENDER_NAME", senderName);
        noticeInfo.put("NOTICE_TITLE", title);
        
        if(urgent == null || urgent.equals("")){
            noticeInfo.put("IS_IMPORTANT", "0");
        }else if(urgent == true){
            noticeInfo.put("IS_IMPORTANT", "1");
        }else if(urgent == false){
            noticeInfo.put("IS_IMPORTANT", "0");
        }
        
        noticeInfo.put("TYPE_ID", "其他模块调用");
        noticeInfo.put("NOTICE_CONTENT", URLDecoder.decode(content,"UTF-8"));
        noticeInfo.put("SEND_TIME", EasyDate.getCurrentDateString());
        noticeInfo.put("PUBLISH_TIME", publishTime);
        noticeInfo.put("INVALID_TIME", validTime);
        noticeInfo.put("IS_RECALL", Constants.IS_RECALL_NO);
        noticeInfo.put("IS_DRAFT", Constants.IS_DRAFT_NO);
        noticeInfo.put("IS_DEL", Constants.IS_DEL_NO);
        noticeInfo.put("NOTICE_ID", noticeInfoId);
        noticeInfo.put("NOTICE_RECEIVER", recvID);
        
        query.save(noticeInfo);
        logger.info("[NoticeMessageConsumer] 公告信息保存成功: " + noticeInfoId);
    }
    
    /**
     * 保存发送记录
     */
    private void saveNoticeSend(EasyQuery query, String noticeInfoId, 
                               String senderId, String senderName) throws Exception {
        NoticeSend noticeSend = new NoticeSend();
        noticeSend.put("NOTICE_ID", noticeInfoId);
        noticeSend.put("AGENT_NO", "");
        noticeSend.put("AGENT_ID", senderId);
        noticeSend.put("AGENT_NAME", senderName);
        noticeSend.put("CREATE_TIME", EasyDate.getCurrentDateString());
        noticeSend.put("IS_DEL", Constants.IS_DEL_NO);
        
        boolean boo = query.save(noticeSend);
        if(boo){
            logger.info("[NoticeMessageConsumer] 发送记录保存成功: " + noticeInfoId);
        }
    }
}