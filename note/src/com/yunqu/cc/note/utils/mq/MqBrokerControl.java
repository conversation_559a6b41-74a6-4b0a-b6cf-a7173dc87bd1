package com.yunqu.cc.note.utils.mq;

import com.yunqu.cc.note.base.Constants;
import com.yunqu.cc.note.base.CommonLogger;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.log4j.Logger;

import javax.jms.*;
import javax.jms.Queue;
import java.util.*;

/**
 * MQ处理工具类
 * 2. 消息队列集成
 * 消息包含完整的公告数据和元数据
 * 支持消息属性设置，便于消费者识别
 */
public class MqBrokerControl {

	private static class holder {

		private static final Logger logger = CommonLogger.getLogger("notice_service");

		private static final String ADD_ANNOUNCEMENT_INFO = "ADD_ANNOUNCEMENT_INFO";

		private static final List<String> queueNameList = Arrays.asList(
				ADD_ANNOUNCEMENT_INFO
		);

		private static final MqBrokerControl INSTANCE = new MqBrokerControl();

		private static ActiveMQConnectionFactory activeMQConnectionFactory;

		private static Connection connection;

		private static Map<String,Session> sessions = new HashMap<>();

//		private static Topic topic;

		private static Map<String, MessageProducer> producers = new HashMap<>();

		private static Map<String, MessageConsumer> consumers = new HashMap<>();

		private static void init() {
			String addr = Constants.getMqAddr();
			activeMQConnectionFactory = new ActiveMQConnectionFactory(addr);
			try {
				connection = activeMQConnectionFactory.createConnection();
				connection.start();
				for(String queueName:queueNameList){
					Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);//设置消息自动确认模式
					sessions.put(queueName,session);
					Queue queue = session.createQueue(queueName);
					producers.put(queueName,session.createProducer(queue));
					//创建消费者
					consumers.put(queueName,session.createConsumer(queue));
				}
				logger.info("mq service init success!");
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		}

		private static void shutDown() {
			try {
				List<MessageConsumer> consumerList = new ArrayList<>(consumers.values());
				for(MessageConsumer consumer:consumerList){
					consumer.close();
				}
				consumers = new HashMap<>();
				List<MessageProducer> producerList = new ArrayList<>(producers.values());
				for(MessageProducer producer:producerList){
					producer.close();
				}
				producers = new HashMap<>();
				for(Session session:sessions.values()){
					session.close();
				}
				sessions = new HashMap<>();
				connection.close();
				connection = null;
			} catch (Exception e) {
				logger.error(e.getMessage(), e);
			}
		}
	}

	private MqBrokerControl() {
	}

	public static MqBrokerControl getInstance() {
		return holder.INSTANCE;
	}

	public void initBroker() {
		holder.init();
	}

	/**
	 * 关闭资源
	 *
	 */
	public void shutDown() {
		holder.shutDown();
	}

	public MessageConsumer getConsumer(String topicName) {
		return holder.consumers.get(topicName);
	}

	public MessageProducer getProducer(String topicName) {
		return holder.producers.get(topicName);
	}

	public Session getSession(String topicName) {
		return holder.sessions.get(topicName);
	}
}