package com.yunqu.cc.note.utils.mq;

import cn.hutool.core.thread.ThreadUtil;
import com.yunqu.cc.note.base.CommonLogger;
import org.apache.log4j.Logger;

/**
 * 公告消费者管理器
 */
public class NoticeConsumerManager {

    private static final Logger logger = CommonLogger.getLogger("notice_manager");
    private static NoticeMessageConsumer consumer;
    private static Thread consumerThread;

    /**
     * 启动消费者
     */
    public static void startConsumer() {
        if (consumer == null) {
            try {
                consumer = new NoticeMessageConsumer();
                ThreadUtil.execAsync(consumer);
                logger.info("公告消息消费者已启动");
            } catch (Exception e) {
                logger.error("启动公告消息消费者失败: " + e.getMessage(), e);
            }
        }
    }

    /**
     * 停止消费者
     */
    public static void stopConsumer() {
        if (consumer != null) {
            try {
                if (consumerThread != null && consumerThread.isAlive()) {
                    consumerThread.interrupt();
                    consumerThread.join(5000); // 等待5秒
                }
                consumer = null;
                consumerThread = null;
                logger.info("公告消息消费者已停止");
            } catch (Exception e) {
                logger.error("停止公告消息消费者失败: " + e.getMessage(), e);
            }
        }
    }
}