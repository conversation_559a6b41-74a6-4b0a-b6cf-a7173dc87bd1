<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>短信日志</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchSmsForm" class="form-inline" id="searchSmsForm" onsubmit="return false" data-toggle="">
             	<input type="hidden" name="TYPE" value="02">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 短信日志查询</h5>
	             		       <div class="input-group ">
								      <span class="input-group-addon">操作员账号</span>	
									  <input type="text" name="CREATE_ACC" class="form-control input-sm" style="width:100px">
							   </div>
							 
							   <div class="input-group ">
             		              <span class="input-group-addon">发送日期</span>	
								  <input type="text" name="beginTime" id="beginTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm'})" style="width:138px">
             		              <span class="input-group-addon">-</span>	
								  <input type="text" name="endTime" id="endTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm'})" style="width:138px">
							  </div>
							   <div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="messLog.searchData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							   
	             		       <div class="input-group  pull-right mr-10">
	             		            <button class="btn btn-sm btn-success" type="button" onclick="messLog.downloadExl()"><span class="glyphicon glyphicon-export"></span> 导出</button>
	             		       </div>
	             		 </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="sendLog.sengList">
                             <thead>
	                         	 <tr>
	                         	      <th width="13%">工单号</th>
								      <th width="10%">操作员</th>
								      <th width="15%">发送时间</th>
								      <th width="5%">发送成功</th>
								      <th width="5%">发送失败</th>
								       <th width="15%">发送成功</th>
								      <th width="15%">发送失败</th>
								      <th width="22%">内容</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                  <tr>
                  
                                  </tr>
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td width="13%">{{:ORDER_ID}}</td>
											<td width="10%">{{:CREATE_ACC}}</td>
											<td width="15%">{{:CREATE_TIME}}</td>
											<td width="5%">{{:SUCCESS}}</td>
											<td width="5%">{{:FAIL}}</td>
											<td width="15%">{{substr:SUCCESS_ACC "20"}}</td>
											<td width="15%">{{substr:FAIL_ACC "20"}}</td>
											<td width="22%">{{substr:CONTENT "30"}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate">
					<jsp:include page="/pages/common/pagination.jsp" />
				</div>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		jQuery.namespace("messLog");
		$(function() {
			$("#searchSmsForm").render();
		});
	
		messLog.searchData = function() {
			$("#searchSmsForm").searchData();
		}
		messLog.downloadExl = function() {
			var data = form.getJSONObject("searchSmsForm");
			var last = JSON.stringify(data); //将JSON对象转化为JSON字符
			location.href = "${ctxPath}/servlet/sendUpload?action=export&" + $("#searchSmsForm").serialize();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>