<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>邮件日志</title>
	<style>
	    #dataList content{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchEmailForm" class="form-inline" id="searchEmailForm" onsubmit="return false" data-toggle="">
             	<input type="hidden" name="TYPE" value="01">
             	 <div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 邮件查询</h5>
	             		       <div class="input-group ">
								      <span class="input-group-addon">操作员账号</span>	
									  <input type="text" name="CREATE_ACC" class="form-control input-sm" style="width:100px">
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">关键字名</span>	
									  <input type="text" name="TOPIC" class="form-control input-sm" style="width:120px">
							   </div>  
							   <div class="input-group ">
             		              <span class="input-group-addon">发送日期</span>	
								  <input type="text" name="beginTime" id="beginTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm'})" style="width:128px">
             		              <span class="input-group-addon">-</span>	
								  <input type="text" name="endTime" id="endTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm'})" style="width:128px">
							  </div>
							   <div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="emailLog.searchData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							   
	             		       <div class="input-group  pull-right mr-10">
	             		            <button class="btn btn-sm btn-success" type="button" onclick="emailLog.downloadExl()"><span class="glyphicon glyphicon-export"></span> 导出</button>
	             		       </div>
	             		 </div>
             	    </div>  
	              	<div class="ibox-content">
	              			           	     <div class="row table-responsive">	              		
	              	
		           	   <table style="min-width:2000px;width:100%" class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="sendLog.sengList">
                             <thead>
	                         	 <tr>
	                         	      <th width="5%">工单号</th>
								      <th width="5%">操作员</th>
								      <th width="10%">发送时间</th>
								      <th width="10%">发送成功</th>
								      <th width="10%">发送失败</th>
								      <th width="10%">成功邮件</th>
								      <th width="10%">失败邮件</th>
								      <th width="10%">接收人</th>
								      <th width="10%">抄送人</th>
								      <th width="10%">主题</th>
								      <th width="10%">内容</th>
								      
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                            
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:ORDER_ID}}</td>
											<td>{{:CREATE_ACC}}</td>
											<td>{{:CREATE_TIME}}</td>
											<td>{{:SUCCESS}}</td>
											<td>{{:FAIL}}</td>
											<td>{{substr:SUCCESS_ACC "20"}}</td>
											<td>{{substr:FAIL_ACC "20"}}</td>
											<td>{{substr:RECEIVER "20"}}</td>
											<td>{{substr:COPY "20"}}</td>
											<td>{{substr:TOPIC "20"}}</td>
											<td><div style="height:100%;overflow:auto;">{{:CONTENT}}</div></td>
									    </tr>
								   {{/for}}					         
							 </script>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	                     </div>
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
		jQuery.namespace("emailLog");
	    
		$(function() {
			$("#searchEmailForm").render();
		});
	
		emailLog.searchData = function() {
			$("#searchEmailForm").searchData();
		}
		emailLog.downloadExl = function() {
			var data = form.getJSONObject("searchEmailForm");
			var last = JSON.stringify(data); //将JSON对象转化为JSON字符
			location.href = "${ctxPath}/servlet/sendUpload?action=export1&" + $("#searchEmailForm").serialize();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>