/*!
 *  ___  ___  _____   ______  __   __ _____ ______ __    __
 * |   \/   |/  _  \ /  __  \|  \ |  |_   _|   ___|  \  /  |
 * |        |  / \  |  |  \__|   \|  | | | |  |__  \  \/  /
 * |  |\/|  |  |_|  |  |  ___        | | | |   __|  \    /
 * |  |  |  |   _   |  \_/   |  |\   |_| |_|  |      |  |
 * |__|  |__|__| |__|\____/|_|__| \__|_____|__|      |__|
 *
 * jquery.magnify - v1.2.0
 * A jQuery plugin to view images just like in windows
 * https://github.com/nzbin/magnify#readme
 *
 * Copyright (c) 2017 nzbin
 * Released under the MIT License
 *
 */
!function(t){"function"==typeof define&&define.amd?define(["jquery"],t):t("object"==typeof exports?require("jquery"):jQuery)}(function(t){"use strict";function i(i){return t(i).attr("data-src")?t(i).attr("data-src"):t(i).attr("href")}function e(t){var i=t.match(/\d+/g);return parseFloat(i[0])}function a(t,i,e,a){var s=a?t.h:t.w,o=a?t.w:t.h;(o>i.h||s>i.w)&&e.addClass("is-grab"),o<=i.h&&s<=i.w&&e.removeClass("is-grab")}function s(){return!!("ontouchstart"in window||window.DocumentTouch&&document instanceof DocumentTouch)}function o(){return"Microsoft Internet Explorer"==navigator.appName&&navigator.appVersion.indexOf("MSIE 8.0")>0||"Microsoft Internet Explorer"==navigator.appName&&navigator.appVersion.indexOf("MSIE 7.0")>0}var n=t(window),h=t(document),r=s()?"touchstart":"mousedown",l=s()?"touchmove":"mousemove",g=s()?"touchend":"mouseup",m=".magnify",f={draggable:!0,resizable:!0,movable:!0,keyboard:!0,title:!0,modalWidth:320,modalHeight:320,fixedContent:!0,fixedModalSize:!1,initMaximized:!1,gapThreshold:.02,ratioThreshold:.1,minRatio:.1,maxRatio:16,headToolbar:["maximize","close"],footToolbar:["zoomIn","zoomOut","prev","fullscreen","next","actualSize","rotateRight"],icons:{minimize:"fa fa-window-minimize",maximize:"fa fa-window-maximize",close:"fa fa-close",zoomIn:"fa fa-search-plus",zoomOut:"fa fa-search-minus",prev:"fa fa-arrow-left",next:"fa fa-arrow-right",fullscreen:"fa fa-photo",actualSize:"fa fa-arrows-alt",rotateLeft:"fa fa-rotate-left",rotateRight:"fa fa-rotate-right"},i18n:{minimize:"minimize",maximize:"maximize",close:"close",zoomIn:"zoom-in(+)",zoomOut:"zoom-out(-)",prev:"prev(←)",next:"next(→)",fullscreen:"fullscreen",actualSize:"actual-size(Ctrl+Alt+0)",rotateLeft:"rotate-left(Ctrl+,)",rotateRight:"rotate-right(Ctrl+.)"},multiInstances:!0,initEvent:"click",initAnimation:!0,fixedModalPos:!1,zIndex:1090,dragHandle:!1,callbacks:{beforeOpen:t.noop,opened:t.noop,beforeClose:t.noop,closed:t.noop,beforeChange:t.noop,changed:t.noop}},d={isMoving:!1,isResizing:!1,zIndex:f.zIndex},c=null,p=function(i,e){this.options=t.extend(!0,{},f,e),e&&t.isArray(e.footToolbar)&&(this.options.footToolbar=e.footToolbar),e&&t.isArray(e.headToolbar)&&(this.options.headToolbar=e.headToolbar),this.$el=t(i),this.isOpened=!1,this.isMaximized=!1,this.isRotated=!1,this.rotateAngle=0,this.imageData={},this.modalData={width:null,height:null,left:null,top:null},this.init(i,this.options)};p.prototype={init:function(e,a){var s=i(e);this.groupName=null;var n=t(e).attr("data-group"),r=h.find('[data-group="'+n+'"]');void 0!==n?(this.groupName=n,this.getImgGroup(r,s)):this.getImgGroup(c.not("[data-group]"),s),this.open(),this.loadImg(s),a.draggable&&this.draggable(this.$magnify,this.dragHandle,".magnify-button"),a.movable&&this.movable(this.$stage,o()?".magnify-image":this.$image),a.resizable&&this.resizable(this.$magnify,this.$stage,o()?".magnify-image":this.$image,a.modalWidth,a.modalHeight)},_creatBtns:function(i,e){var a="";return t.each(i,function(t,i){a+=e[i]}),a},_creatTitle:function(){return this.options.title?'<div class="magnify-title"></div>':""},creatDOM:function(){var t={minimize:'<button class="magnify-button magnify-button-minimize" title="'+this.options.i18n.minimize+'">                      <i class="'+this.options.icons.minimize+'" aria-hidden="true"></i>                    </button>',maximize:'<button class="magnify-button magnify-button-maximize" title="'+this.options.i18n.maximize+'">                      <i class="'+this.options.icons.maximize+'" aria-hidden="true"></i>                    </button>',close:'<button class="magnify-button magnify-button-close" title="'+this.options.i18n.close+'">                      <i class="'+this.options.icons.close+'" aria-hidden="true"></i>                    </button>',zoomIn:'<button class="magnify-button magnify-button-zoom-in" title="'+this.options.i18n.zoomIn+'">                      <i class="'+this.options.icons.zoomIn+'" aria-hidden="true"></i>                    </button>',zoomOut:'<button class="magnify-button magnify-button-zoom-out" title="'+this.options.i18n.zoomOut+'">                      <i class="'+this.options.icons.zoomOut+'" aria-hidden="true"></i>                    </button>',prev:'<button class="magnify-button magnify-button-prev" title="'+this.options.i18n.prev+'">                      <i class="'+this.options.icons.prev+'" aria-hidden="true"></i>                    </button>',next:'<button class="magnify-button magnify-button-next" title="'+this.options.i18n.next+'">                      <i class="'+this.options.icons.next+'" aria-hidden="true"></i>                    </button>',fullscreen:'<button class="magnify-button magnify-button-fullscreen" title="'+this.options.i18n.fullscreen+'">                      <i class="'+this.options.icons.fullscreen+'" aria-hidden="true"></i>                    </button>',actualSize:'<button class="magnify-button magnify-button-actual-size" title="'+this.options.i18n.actualSize+'">                      <i class="'+this.options.icons.actualSize+'" aria-hidden="true"></i>                    </button>',rotateLeft:'<button class="magnify-button magnify-button-rotate-left" title="'+this.options.i18n.rotateLeft+'">                      <i class="'+this.options.icons.rotateLeft+'" aria-hidden="true"></i>                    </button>',rotateRight:'<button class="magnify-button magnify-button-rotate-right" title="'+this.options.i18n.rotateRight+'">                      <i class="'+this.options.icons.rotateRight+'" aria-hidden="true"></i>                    </button>'};return'<div class="magnify-modal">                        <div class="magnify-header">                          <div class="magnify-toolbar magnify-head-toolbar">'+this._creatBtns(this.options.headToolbar,t)+"</div>"+this._creatTitle()+'                        </div>                        <div class="magnify-stage">                          <img class="magnify-image" src="" alt="" />                        </div>                        <div class="magnify-footer">                          <div class="magnify-toolbar magnify-foot-toolbar">'+this._creatBtns(this.options.footToolbar,t)+"</div>                        </div>                      </div>"},build:function(){var i=this.creatDOM(),e=t(i);this.$magnify=e,this.$header=e.find(".magnify-header"),this.$headToolbar=e.find(".magnify-head-toolbar"),this.$footer=e.find(".magnify-footer"),this.$footToolbar=e.find(".magnify-foot-toolbar"),this.$stage=e.find(".magnify-stage"),this.$title=e.find(".magnify-title"),this.$image=e.find(".magnify-image"),this.$close=e.find(".magnify-button-close"),this.$maximize=e.find(".magnify-button-maximize"),this.$minimize=e.find(".magnify-button-minimize"),this.$zoomIn=e.find(".magnify-button-zoom-in"),this.$zoomOut=e.find(".magnify-button-zoom-out"),this.$actualSize=e.find(".magnify-button-actual-size"),this.$fullscreen=e.find(".magnify-button-fullscreen"),this.$rotateLeft=e.find(".magnify-button-rotate-left"),this.$rotateRight=e.find(".magnify-button-rotate-right"),this.$prev=e.find(".magnify-button-prev"),this.$next=e.find(".magnify-button-next"),this.$stage.addClass("stage-ready"),this.$image.addClass("image-ready"),this.$magnify.css("z-index",d.zIndex),this.options.dragHandle&&".magnify-modal"!==this.options.dragHandle?this.dragHandle=this.$magnify.find(this.options.dragHandle):this.dragHandle=this.$magnify},open:function(){if(this.options.multiInstances||t(".magnify-modal").eq(0).remove(),!t(".magnify-modal").length&&this.options.fixedContent&&(t("html").css({overflow:"hidden"}),document.body.scrollHeight>(window.innerHeight||document.documentElement.clientHeight))){var i=function(){var t=document.createElement("div");t.style.cssText="width: 99px; height: 99px; overflow: scroll; position: absolute; top: -9999px;",document.body.appendChild(t);var i=t.offsetWidth-t.clientWidth;return document.body.removeChild(t),i}();i&&t("html").css({"padding-right":i})}this.build(),this._triggerHook("beforeOpen",this.$el),t("body").append(this.$magnify),this.addEvents(),this.setModalPos(this.$magnify),this._triggerHook("opened",this.$el)},close:function(i){this._triggerHook("beforeClose",this.$el),this.$magnify.remove(),this.isOpened=!1,this.isMaximized=!1,this.isRotated=!1,this.rotateAngle=0;var e=!t(".magnify-modal").length;e&&this.options.fixedContent&&t("html").css({overflow:"","padding-right":""}),e&&this.options.multiInstances&&(d.zIndex=this.options.zIndex),t(".magnify-modal").length||(h.off("keydown.magnify"),n.off("resize.magnify")),this._triggerHook("closed",this.$el)},setModalPos:function(t){var i=n.width(),e=n.height(),a=h.scrollLeft(),s=h.scrollTop(),o=this.options.modalWidth,r=this.options.modalHeight;this.options.initMaximized?(t.addClass("magnify-maximize"),t.css({width:"100%",height:"100%",left:0,top:0}),this.isOpened=!0,this.isMaximized=!0):t.css({width:o,height:r,left:(i-o)/2+a+"px",top:(e-r)/2+s+"px"})},setModalSize:function(t){var i=this,a=n.width(),s=n.height(),o=h.scrollLeft(),r=h.scrollTop(),l={left:this.$stage.css("left"),right:this.$stage.css("right"),top:this.$stage.css("top"),bottom:this.$stage.css("bottom"),borderLeft:this.$stage.css("border-left-width"),borderRight:this.$stage.css("border-right-width"),borderTop:this.$stage.css("border-top-width"),borderBottom:this.$stage.css("border-bottom-width")},g=t.width+e(l.left)+e(l.right)+e(l.borderLeft)+e(l.borderRight),m=t.height+e(l.top)+e(l.bottom)+e(l.borderTop)+e(l.borderBottom),f=(this.options.gapThreshold>0?this.options.gapThreshold:0)+1,d=Math.min(a/(g*f),s/(m*f),1),c=Math.max(g*d,this.options.modalWidth),p=Math.max(m*d,this.options.modalHeight),u={width:(c=this.options.fixedModalSize?this.options.modalWidth:Math.round(c))+"px",height:(p=this.options.fixedModalSize?this.options.modalHeight:Math.round(p))+"px",left:(a-c)/2+o+"px",top:(s-p)/2+r+"px"};this.options.initAnimation?this.$magnify.animate(u,function(){i.setImageSize(t)}):(this.$magnify.css(u),this.setImageSize(t)),this.isOpened=!0},setImageSize:function(i){var e=o()?this.$stage.find(".magnify-image"):this.$image,s={w:this.$stage.width(),h:this.$stage.height()},n=1;n=this.isRotated?Math.min(s.w/i.height,s.h/i.width,1):Math.min(s.w/i.width,s.h/i.height,1),e.css({width:Math.ceil(i.width*n)+"px",height:Math.ceil(i.height*n)+"px",left:(s.w-Math.ceil(i.width*n))/2+"px",top:(s.h-Math.ceil(i.height*n))/2+"px"}),o()&&e.find("group").css({width:Math.floor(i.width*n)+"px",height:Math.floor(i.height*n)+"px"}),t.extend(this.imageData,{width:i.width*n,height:i.height*n,left:(s.w-i.width*n)/2,top:(s.h-i.height*n)/2}),a({w:e.width(),h:e.height()},{w:this.$stage.width(),h:this.$stage.height()},this.$stage,this.isRotated),this.$magnify.find(".magnify-loader").remove(),this.options.initAnimation&&e.fadeIn()},loadImg:function(t){var i=this;this.$magnify.append('<div class="magnify-loader"></div>'),this.options.initAnimation&&this.$image.hide(),o()?this.$stage.html('<img class="magnify-image" id="magnify-image" src="'+t+'" alt="" />'):this.$image.attr("src",t),function(t,i,e){var a=new Image;a.onload=function(){i(a)},a.onerror=function(){e(a)},a.src=t}(t,function(t){i.imageData={originalWidth:t.width,originalHeight:t.height},i.isMaximized||i.isOpened&&i.options.fixedModalPos?i.setImageSize(t):i.setModalSize(t),i.$stage.removeClass("stage-ready"),i.$image.removeClass("image-ready")},function(){i.$magnify.find(".magnify-loader").remove()}),this.options.title&&this.setImgTitle(t)},getImgGroup:function(e,a){var s=this;s.groupData=[],t(e).each(function(e,o){var n=i(this);s.groupData.push({src:n,caption:t(this).attr("data-caption")}),a===n&&(s.groupIndex=e)})},setImgTitle:function(t){var i=this.groupIndex,e=(e=this.groupData[i].caption)||function(t){return t.replace(/^.*?\/*([^/?]*)\.[a-z]+(\?.+|$)/gi,"$1")}(t);this.$title.html(e)},jump:function(t){this.groupIndex=this.groupIndex+t,this.jumpTo(this.groupIndex)},jumpTo:function(t){(t%=this.groupData.length)>=0?t%=this.groupData.length:t<0&&(t=(this.groupData.length+t)%this.groupData.length),this.groupIndex=t,this._triggerHook("beforeChange",t),this.loadImg(this.groupData[t].src),this._triggerHook("changed",t)},wheel:function(t){t.preventDefault();var i=1;t.originalEvent.deltaY?i=t.originalEvent.deltaY>0?1:-1:t.originalEvent.wheelDelta?i=-t.originalEvent.wheelDelta/120:t.originalEvent.detail&&(i=t.originalEvent.detail>0?1:-1);var e=-i*this.options.ratioThreshold,a={x:t.originalEvent.clientX-this.$stage.offset().left+h.scrollLeft(),y:t.originalEvent.clientY-this.$stage.offset().top+h.scrollTop()};this.zoom(e,a,t)},zoom:function(t,i,e){this.$image=o()?this.$stage.find(".magnify-image"):this.$image,(t=t<0?1/(1-t):1+t)>.95&&t<1.05&&(t=1),t=this.$image.width()/this.imageData.originalWidth*t,t=Math.max(t,this.options.minRatio),t=Math.min(t,this.options.maxRatio),this.zoomTo(t,i,e)},zoomTo:function(i,e,s){var n=o()?this.$stage.find(".magnify-image"):this.$image,h=this.$stage,r={w:this.imageData.width,h:this.imageData.height,x:this.imageData.left,y:this.imageData.top},l={w:h.width(),h:h.height(),x:h.offset().left,y:h.offset().top},g=this.imageData.originalWidth*i,m=this.imageData.originalHeight*i,f=e.x-(e.x-r.x)/r.w*g,d=e.y-(e.y-r.y)/r.h*m,c=this.isRotated?(g-m)/2:0,p=this.isRotated?m:g,u=this.isRotated?g:m,y=l.w-g,_=l.h-m;
// The conditions with image rotate 90 degree drive me crazy alomst!
d=u<=l.h?(l.h-m)/2:d>c?c:d>_-c?d:_-c,f=p<=l.w?(l.w-g)/2:f>-c?-c:f>y+c?f:y+c,n.css({width:Math.round(g)+"px",height:Math.round(m)+"px",left:Math.round(f)+"px",top:Math.round(d)+"px"}),o()&&n.find("group").css({width:Math.ceil(g)+"px",height:Math.ceil(m)+"px"}),t.extend(this.imageData,{width:g,height:m,left:f,top:d}),a({w:Math.round(p),h:Math.round(u)},{w:l.w,h:l.h},this.$stage)},rotate:function(t){this.rotateAngle=this.rotateAngle+t,this.rotateAngle/90%2==0?this.isRotated=!1:this.isRotated=!0,this.rotateTo(this.rotateAngle)},rotateTo:function(t){(o()?this.$stage.find(".magnify-image"):this.$image).rotate({angle:t}),this.setImageSize({width:this.imageData.originalWidth,height:this.imageData.originalHeight}),this.$stage.removeClass("is-grab")},resize:function(){var t=this;return function(t,i){var e=null;return function(){var a=this,s=arguments;clearTimeout(e),e=setTimeout(function(){t.apply(a,s)},i)}}(function(){t.isOpened&&(t.isMaximized?t.setImageSize({width:t.imageData.originalWidth,height:t.imageData.originalHeight}):t.setModalSize({width:t.imageData.originalWidth,height:t.imageData.originalHeight}))},500)},maximize:function(){this.isMaximized?(this.$magnify.removeClass("magnify-maximize"),this.$magnify.css({width:this.modalData.width?this.modalData.width:this.options.modalWidth,height:this.modalData.height?this.modalData.height:this.options.modalHeight,left:this.modalData.left?this.modalData.left:(n.width()-this.options.modalWidth)/2+h.scrollLeft(),top:this.modalData.top?this.modalData.top:(n.height()-this.options.modalHeight)/2+h.scrollTop()}),this.isMaximized=!1):(this.modalData={width:this.$magnify.width(),height:this.$magnify.height(),left:this.$magnify.offset().left,top:this.$magnify.offset().top},this.$magnify.addClass("magnify-maximize"),this.$magnify.css({width:"100%",height:"100%",left:0,top:0}),this.isMaximized=!0),this.setImageSize({width:this.imageData.originalWidth,height:this.imageData.originalHeight})},fullscreen:function(){!function(t){t.requestFullscreen?t.requestFullscreen():t.mozRequestFullScreen?t.mozRequestFullScreen():t.webkitRequestFullscreen?t.webkitRequestFullscreen():t.msRequestFullscreen&&t.msRequestFullscreen()}(this.$magnify[0])},keydown:function(t){if(!this.options.keyboard)return!1;var i=t.keyCode||t.which||t.charCode,e=t.ctrlKey||t.metaKey,a=t.altKey||t.metaKey;switch(i){case 37:this.jump(-1);break;case 39:this.jump(1);break;case 187:this.zoom(3*this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 189:this.zoom(3*-this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 61:this.zoom(3*this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 173:this.zoom(3*-this.options.ratioThreshold,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 48:e&&a&&this.zoomTo(1,{x:this.$stage.width()/2,y:this.$stage.height()/2},t);break;case 188:e&&this.rotate(-90);break;case 190:e&&this.rotate(90)}},addEvents:function(){var t=this;this.$close.off("click.magnify").on("click.magnify",function(i){t.close()}),this.$stage.off("wheel mousewheel DOMMouseScroll.magnify").on("wheel mousewheel DOMMouseScroll.magnify",function(i){t.wheel(i)}),this.$zoomIn.off("click.magnify").on("click.magnify",function(i){t.zoom(3*t.options.ratioThreshold,{x:t.$stage.width()/2,y:t.$stage.height()/2},i)}),this.$zoomOut.off("click.magnify").on("click.magnify",function(i){t.zoom(3*-t.options.ratioThreshold,{x:t.$stage.width()/2,y:t.$stage.height()/2},i)}),this.$actualSize.off("click.magnify").on("click.magnify",function(i){t.zoomTo(1,{x:t.$stage.width()/2,y:t.$stage.height()/2},i)}),this.$prev.off("click.magnify").on("click.magnify",function(){t.jump(-1)}),this.$fullscreen.off("click.magnify").on("click.magnify",function(){t.fullscreen()}),this.$next.off("click.magnify").on("click.magnify",function(){t.jump(1)}),this.$rotateLeft.off("click.magnify").on("click.magnify",function(){t.rotate(-90)}),this.$rotateRight.off("click.magnify").on("click.magnify",function(){t.rotate(90)}),this.$maximize.off("click.magnify").on("click.magnify",function(){t.maximize()}),h.off("keydown.magnify").on("keydown.magnify",function(i){t.keydown(i)}),n.on("resize.magnify",t.resize())},_triggerHook:function(i,e){this.options.callbacks[i]&&this.options.callbacks[i].apply(this,t.isArray(e)?e:[e])}},t.fn.magnify=function(i){c=t(this);for(var e in i)"string"!=typeof i[e]||isNaN(i[e])||(i[e]=parseFloat(i[e]));var a=t.extend(!0,{},f,i);return d.zIndex=a.zIndex,"string"==typeof i||("dblclick"===a.initEvent&&c.off("click.magnify").on("click.magnify",function(t){t.preventDefault(),t.stopPropagation()}),c.off(a.initEvent+m).on(a.initEvent+m,function(e){e.preventDefault(),e.stopPropagation(),t(this).data("magnify",new p(this,i))})),c},h.on("click.magnify","[data-magnify]",function(i){c=t("[data-magnify]"),i.preventDefault(),t(this).data("magnify",new p(this,f))});t.extend(p.prototype,{draggable:function(i,e,a){var s=this,o=!1,n=0,f=0,c=0,p=0,u=function(e){if((e=e||window.event).preventDefault(),o&&!d.isMoving&&!d.isResizing&&!s.isMaximized){var a="touchmove"===e.type?e.originalEvent.targetTouches[0].pageX:e.clientX,h="touchmove"===e.type?e.originalEvent.targetTouches[0].pageY:e.clientY,r=a-n,l=h-f;t(i).css({left:r+c+"px",top:l+p+"px"})}},y=function(t){h.off(l+m,u).off(g+m,y),o=!1};t(e).on(r+m,function(e){if(e=e||window.event,s.options.multiInstances&&i.css("z-index",++d.zIndex),t(e.target).closest(a).length)return!0;o=!0,n="touchstart"===e.type?e.originalEvent.targetTouches[0].pageX:e.clientX,f="touchstart"===e.type?e.originalEvent.targetTouches[0].pageY:e.clientY,c=t(i).offset().left,p=t(i).offset().top,h.on(l+m,u).on(g+m,y)})}});t.extend(p.prototype,{movable:function(i,e){var a=this,s=!1,n=0,f=0,c=0,p=0,u=0,y=0,_=0,v=function(h){(h=h||window.event).preventDefault();var r=o()?t(i).find(e):t(e);if(s){var l="touchmove"===h.type?h.originalEvent.targetTouches[0].pageX:h.clientX,g="touchmove"===h.type?h.originalEvent.targetTouches[0].pageY:h.clientY,m=l-n,d=g-f,v=m+c,b=d+p;y>0?d+p>_?b=_:d+p<-y+_&&(b=-y+_):b=p,u>0?m+c>-_?v=-_:m+c<-u-_&&(v=-u-_):v=c,r.css({left:v+"px",top:b+"px"}),t.extend(a.imageData,{left:v,top:b})}},b=function(i){h.off(l+m,v).off(g+m,b),s=!1,d.isMoving=!1,t("html,body,.magnify-modal,.magnify-stage,.magnify-button,.magnify-resizable-handle").removeClass("is-grabbing")};t(i).on(r+m,function(r){(r=r||window.event).preventDefault();var x=o()?t(i).find(e):t(e),w=x.width(),z=x.height(),$=t(i).width(),T=t(i).height();n="touchstart"===r.type?r.originalEvent.targetTouches[0].pageX:r.clientX,f="touchstart"===r.type?r.originalEvent.targetTouches[0].pageY:r.clientY,_=a.isRotated?(w-z)/2:0,u=a.isRotated?z-$:w-$,y=a.isRotated?w-T:z-T,s=u>0||y>0,d.isMoving=u>0||y>0,c=x.position().left-(o()?0:_),p=x.position().top+(o()?0:_),i.hasClass("is-grab")&&t("html,body,.magnify-modal,.magnify-stage,.magnify-button,.magnify-resizable-handle").addClass("is-grabbing"),h.on(l+m,v).on(g+m,b)})}});t.extend(p.prototype,{resizable:function(i,e,s,n,f){var c=this,p=t('<div class="magnify-resizable-handle magnify-resizable-handle-e"></div>'),u=t('<div class="magnify-resizable-handle magnify-resizable-handle-w"></div>'),y=t('<div class="magnify-resizable-handle magnify-resizable-handle-s"></div>'),_=t('<div class="magnify-resizable-handle magnify-resizable-handle-n"></div>'),v=t('<div class="magnify-resizable-handle magnify-resizable-handle-se"></div>'),b=t('<div class="magnify-resizable-handle magnify-resizable-handle-sw"></div>'),x=t('<div class="magnify-resizable-handle magnify-resizable-handle-ne"></div>'),w=t('<div class="magnify-resizable-handle magnify-resizable-handle-nw"></div>'),z={e:p,s:y,se:v,n:_,w:u,nw:w,ne:x,sw:b};t(i).append(p,u,y,_,v,b,x,w);var $=!1,T=0,M=0,k={w:0,h:0,l:0,t:0},C={w:0,h:0,l:0,t:0},D={w:0,h:0,l:0,t:0},S=0,E=0,I=0,O="",W=function(a,n){(n=n||window.event).preventDefault();var r=o()?t(e).find(s):t(s);$=!0,d.isResizing=!0,T="touchstart"===n.type?n.originalEvent.targetTouches[0].pageX:n.clientX,M="touchstart"===n.type?n.originalEvent.targetTouches[0].pageY:n.clientY,k={w:t(i).width(),h:t(i).height(),l:t(i).offset().left,t:t(i).offset().top},C={w:t(e).width(),h:t(e).height(),l:t(e).offset().left,t:t(e).offset().top},D={w:r.width(),h:r.height(),l:r.position().left,t:r.position().top},S=c.isRotated?(D.w-D.h)/2:0,E=c.isRotated?D.h:D.w,I=c.isRotated?D.w:D.h,O=a,t("html,body,.magnify-modal,.magnify-stage,.magnify-button").css("cursor",a+"-resize"),h.on(l+m,H).on(g+m,R)},H=function(a){(a=a||window.event).preventDefault();var h=o()?t(e).find(s):t(s);if($&&!c.isMaximized){var r="touchmove"===a.type?a.originalEvent.targetTouches[0].pageX:a.clientX,l="touchmove"===a.type?a.originalEvent.targetTouches[0].pageY:a.clientY,g=r-T,m=l-M,d=function(t,i,e){var a=-i+k.w>n?i+k.l:k.l+k.w-n,s=-e+k.h>f?e+k.t:k.t+k.h-f;return{e:{width:Math.max(i+k.w,n)+"px"},s:{height:Math.max(e+k.h,f)+"px"},se:{width:Math.max(i+k.w,n)+"px",height:Math.max(e+k.h,f)+"px"},w:{width:Math.max(-i+k.w,n)+"px",left:a+"px"},n:{height:Math.max(-e+k.h,f)+"px",top:s+"px"},nw:{width:Math.max(-i+k.w,n)+"px",height:Math.max(-e+k.h,f)+"px",top:s+"px",left:a+"px"},ne:{width:Math.max(i+k.w,n)+"px",height:Math.max(-e+k.h,f)+"px",top:s+"px"},sw:{width:Math.max(-i+k.w,n)+"px",height:Math.max(e+k.h,f)+"px",left:a+"px"}}[t]}(O,g,m);t(i).css(d);var p=function(i,a,h){var r=o()?t(e).find(s):t(s),l=o()?r.position().left+S:r.position().left,g=o()?r.position().top-S:r.position().top,m=a+k.w>n?C.w-E+a-S:n-(k.w-C.w)-E-S,d=h+k.h>f?C.h-I+h+S:f-(k.h-C.h)-I+S,c=-a+k.w>n?C.w-E-a-S:n-(k.w-C.w)-E-S,p=-h+k.h>f?C.h-I-h+S:f-(k.h-C.h)-I+S,u=(m>0?l:l<0?l:0)-S,y=(d>0?g:g<0?g:0)+S,_=(c>0?l:l<0?l:0)-S,v=(p>0?g:g<0?g:0)+S;return{e:{left:m>=-S?(m-S)/2+"px":u>m?u+"px":m+"px"},s:{top:d>=S?(d+S)/2+"px":y>d?y+"px":d+"px"},se:{top:d>=S?(d+S)/2+"px":y>d?y+"px":d+"px",left:m>=-S?(m-S)/2+"px":u>m?u+"px":m+"px"},w:{left:c>=-S?(c-S)/2+"px":_>c?_+"px":c+"px"},n:{top:p>=S?(p+S)/2+"px":v>p?v+"px":p+"px"},nw:{top:p>=S?(p+S)/2+"px":v>p?v+"px":p+"px",left:c>=-S?(c-S)/2+"px":_>c?_+"px":c+"px"},ne:{top:p>=S?(p+S)/2+"px":v>p?v+"px":p+"px",left:m>=-S?(m-S)/2+"px":u>m?u+"px":m+"px"},sw:{top:d>=S?(d+S)/2+"px":y>d?y+"px":d+"px",left:c>=-S?(c-S)/2+"px":_>c?_+"px":c+"px"}}[i]}(O,g,m);h.css(p)}},R=function(i){h.off(l+m,H).off(g+m,R),d.isResizing&&a({w:E,h:I},{w:t(e).width(),h:t(e).height()},e),$=!1,d.isResizing=!1,t("html,body,.magnify-modal,.magnify-stage,.magnify-button").css("cursor","")};t.each(z,function(t,i){i.on(r+m,function(i){W(t,i)})})}})}),function($){for(var supportedCSS,supportedCSSOrigin,styles=document.getElementsByTagName("head")[0].style,toCheck="transformProperty WebkitTransform OTransform msTransform MozTransform".split(" "),a=0;a<toCheck.length;a++)void 0!==styles[toCheck[a]]&&(supportedCSS=toCheck[a]);supportedCSS&&"T"==(supportedCSSOrigin=supportedCSS.replace(/[tT]ransform/,"TransformOrigin"))[0]&&(supportedCSSOrigin[0]="t"),eval('IE = "v"=="\v"'),jQuery.fn.extend({rotate:function(t){if(0!==this.length&&void 0!==t){"number"==typeof t&&(t={angle:t});for(var i=[],e=0,a=this.length;e<a;e++){var s=this.get(e);if(s.Wilq32&&s.Wilq32.PhotoEffect)s.Wilq32.PhotoEffect._handleRotation(t);else{var o=$.extend(!0,{},t),n=new Wilq32.PhotoEffect(s,o)._rootObj;i.push($(n))}}return i}},getRotateAngle:function(){for(var t=[0],i=0,e=this.length;i<e;i++){var a=this.get(i);a.Wilq32&&a.Wilq32.PhotoEffect&&(t[i]=a.Wilq32.PhotoEffect._angle)}return t},stopRotate:function(){for(var t=0,i=this.length;t<i;t++){var e=this.get(t);e.Wilq32&&e.Wilq32.PhotoEffect&&clearTimeout(e.Wilq32.PhotoEffect._timer)}}}),Wilq32=window.Wilq32||{},Wilq32.PhotoEffect=supportedCSS?function(t,i){t.Wilq32={PhotoEffect:this},this._img=this._rootObj=this._eventObj=t,this._handleRotation(i)}:function(t,i){if(this._img=t,this._onLoadDelegate=[i],this._rootObj=document.createElement("span"),this._rootObj.style.display="inline-block",this._rootObj.Wilq32={PhotoEffect:this},t.parentNode.insertBefore(this._rootObj,t),t.complete)this._Loader();else{var e=this;jQuery(this._img).bind("load",function(){e._Loader()})}},Wilq32.PhotoEffect.prototype={_setupParameters:function(t){this._parameters=this._parameters||{},"number"!=typeof this._angle&&(this._angle=0),"number"==typeof t.angle&&(this._angle=t.angle),this._parameters.animateTo="number"==typeof t.animateTo?t.animateTo:this._angle,this._parameters.step=t.step||this._parameters.step||null,this._parameters.easing=t.easing||this._parameters.easing||this._defaultEasing,this._parameters.duration="duration"in t?t.duration:t.duration||this._parameters.duration||1e3,this._parameters.callback=t.callback||this._parameters.callback||this._emptyFunction,this._parameters.center=t.center||this._parameters.center||["50%","50%"],"string"==typeof this._parameters.center[0]?this._rotationCenterX=parseInt(this._parameters.center[0],10)/100*this._imgWidth*this._aspectW:this._rotationCenterX=this._parameters.center[0],"string"==typeof this._parameters.center[1]?this._rotationCenterY=parseInt(this._parameters.center[1],10)/100*this._imgHeight*this._aspectH:this._rotationCenterY=this._parameters.center[1],t.bind&&t.bind!=this._parameters.bind&&this._BindEvents(t.bind)},_emptyFunction:function(){},_defaultEasing:function(t,i,e,a,s){return-a*((i=i/s-1)*i*i*i-1)+e},_handleRotation:function(t,i){supportedCSS||this._img.complete||i?(this._setupParameters(t),this._angle==this._parameters.animateTo?this._rotate(this._angle):this._animateStart()):this._onLoadDelegate.push(t)},_BindEvents:function(t){if(t&&this._eventObj){if(this._parameters.bind){var i=this._parameters.bind;for(var e in i)i.hasOwnProperty(e)&&jQuery(this._eventObj).unbind(e,i[e])}this._parameters.bind=t;for(var e in t)t.hasOwnProperty(e)&&jQuery(this._eventObj).bind(e,t[e])}},_Loader:IE?function(){var t=this._img.width,i=this._img.height;this._imgWidth=t,this._imgHeight=i,this._img.parentNode.removeChild(this._img),this._vimage=this.createVMLNode("image"),this._vimage.src=this._img.src,this._vimage.style.height=i+"px",this._vimage.style.width=t+"px",this._vimage.style.position="absolute",// FIXES IE PROBLEM - its only rendered if its on absolute position!
this._vimage.style.top="0px",this._vimage.style.left="0px",this._aspectW=this._aspectH=1,this._container=this.createVMLNode("group"),this._container.style.width=t,this._container.style.height=i,this._container.style.position="absolute",this._container.style.top="0px",this._container.style.left="0px",this._container.setAttribute("coordsize",t-1+","+(i-1)),this._container.appendChild(this._vimage),this._rootObj.appendChild(this._container),this._rootObj.style.position="relative",this._rootObj.style.width=t+"px",this._rootObj.style.height=i+"px",this._rootObj.setAttribute("id",this._img.getAttribute("id")),this._rootObj.className=this._img.className,this._eventObj=this._rootObj;for(var e;e=this._onLoadDelegate.shift();)this._handleRotation(e,!0)}:function(){this._rootObj.setAttribute("id",this._img.getAttribute("id")),this._rootObj.className=this._img.className,this._imgWidth=this._img.naturalWidth,this._imgHeight=this._img.naturalHeight;var t=Math.sqrt(this._imgHeight*this._imgHeight+this._imgWidth*this._imgWidth);this._width=3*t,this._height=3*t,this._aspectW=this._img.offsetWidth/this._img.naturalWidth,this._aspectH=this._img.offsetHeight/this._img.naturalHeight,this._img.parentNode.removeChild(this._img),this._canvas=document.createElement("canvas"),this._canvas.setAttribute("width",this._width),this._canvas.style.position="relative",this._canvas.style.left=-this._img.height*this._aspectW+"px",this._canvas.style.top=-this._img.width*this._aspectH+"px",this._canvas.Wilq32=this._rootObj.Wilq32,this._rootObj.appendChild(this._canvas),this._rootObj.style.width=this._img.width*this._aspectW+"px",this._rootObj.style.height=this._img.height*this._aspectH+"px",this._eventObj=this._canvas,this._cnv=this._canvas.getContext("2d");for(var i;i=this._onLoadDelegate.shift();)this._handleRotation(i,!0)},_animateStart:function(){this._timer&&clearTimeout(this._timer),this._animateStartTime=+new Date,this._animateStartAngle=this._angle,this._animate()},_animate:function(){var t=+new Date,i=t-this._animateStartTime>this._parameters.duration;if(i&&!this._parameters.animatedGif)clearTimeout(this._timer);else{if(this._canvas||this._vimage||this._img){var e=this._parameters.easing(0,t-this._animateStartTime,this._animateStartAngle,this._parameters.animateTo-this._animateStartAngle,this._parameters.duration);this._rotate(~~(10*e)/10)}this._parameters.step&&this._parameters.step(this._angle);var a=this;this._timer=setTimeout(function(){a._animate.call(a)},10)}this._parameters.callback&&i&&(this._angle=this._parameters.animateTo,this._rotate(this._angle),this._parameters.callback.call(this._rootObj))},_rotate:function(){var t=Math.PI/180;return IE?function(t){this._angle=t,this._container.style.rotation=t%360+"deg",this._vimage.style.top=-(this._rotationCenterY-this._imgHeight/2)+"px",this._vimage.style.left=-(this._rotationCenterX-this._imgWidth/2)+"px",this._container.style.top=this._rotationCenterY-this._imgHeight/2+"px",this._container.style.left=this._rotationCenterX-this._imgWidth/2+"px"}:supportedCSS?function(t){this._angle=t,this._img.style[supportedCSS]="rotate("+t%360+"deg)",this._img.style[supportedCSSOrigin]=this._parameters.center.join(" ")}:function(i){this._angle=i,i=i%360*t,this._canvas.width=this._width,this._canvas.height=this._height,this._cnv.translate(this._imgWidth*this._aspectW,this._imgHeight*this._aspectH),this._cnv.translate(this._rotationCenterX,this._rotationCenterY),this._cnv.rotate(i),this._cnv.translate(-this._rotationCenterX,-this._rotationCenterY),this._cnv.scale(this._aspectW,this._aspectH),this._cnv.drawImage(this._img,0,0)}}()},IE&&(Wilq32.PhotoEffect.prototype.createVMLNode=function(){document.createStyleSheet().addRule(".rvml","behavior:url(#default#VML)");try{return!document.namespaces.rvml&&document.namespaces.add("rvml","urn:schemas-microsoft-com:vml"),function(t){return document.createElement("<rvml:"+t+' class="rvml">')}}catch(t){return function(t){return document.createElement("<"+t+' xmlns="urn:schemas-microsoft.com:vml" class="rvml">')}}}())}(jQuery);