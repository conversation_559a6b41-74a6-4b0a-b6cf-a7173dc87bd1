<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>@我的微博</title>
	<style>
	    .list-item{border-bottom:1px dashed #ccc}
	    .list-item a:link,a:hover,a:visited{text-decoration:none;}
	    .list-item .list-inner{padding:10px 0;}
	    .list-item .list-inner:hover{background-color:#f9f9f9;}
	    .list-item-header img{height:52px;width:52px;margin-right:10px}
	    .list-item-header h5{font-weight:bold;}
	    .list-item-header p{font-size:12px}
	    .list-item-conent{padding-left:60px;color:#666}
	    .list-item-conent span{cursor:pointer; padding-right: 90px; display: inline-block;}
	    .list-item-conent span:hover{color:#337ab7;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
               <div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
							   <h5><span class="glyphicon glyphicon-list"></span> @我的微博</h5>
						       <div class="input-group input-group-sm">
						             <span class="input-group-addon">是否已读</span>
									 <select name="isRead" class="form-control input-sm">
									 	 <option value="">请选择</option>
									     <option value="1">已读</option>
									     <option value="0">未读</option>
									 </select>
							   </div>
							   <div class="input-group input-group-sm">
						             <span class="input-group-addon">微博账号</span>
									 <select name="id" class="form-control input-sm" data-mars="Interact.blogDict" >
									     <option value="">请选择</option>
									 </select>
							   </div>
							   <div class="input-group input-group-sm">
						             <span class="input-group-addon">查找内容</span>
									  <input type="text" name="content" class="form-control input-sm" placeholder="微博内容或用户昵称" >
							   </div>
							   <div class="input-group input-group-sm">
             		              <span class="input-group-addon">发送时间</span>	
								  <input type="text" name="beginTime" id="beginTime" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
             		              <span class="input-group-addon">-</span>	
								  <input type="text" name="endTime" id="endTime"  class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="query.loadData()">
									    <span class="glyphicon glyphicon-search"></span> 查询 
									    <span id="tipBadge" class="badge" style="background-color:#d9534f">0</span></button>
							   </div>
						 </div>
				    </div>
	 	            <div class="ibox-content">
	 	              <div data-container="#query-list" data-template="query-template" id="query-list"
		                data-mars="Interact.mentions" data-mars-search-list="true" class="bg-white mr-10 pd-15"></div>	
		                <script id="query-template" type="text/x-jsrender">
						  {{for list}}
                            <div class="list-item">
								<div class="list-inner">
		           	                      <div class="list-item-header">
                                              <a href="jacascript:void(0)" onclick="query.Mess('{{:CREATE_ID}}','{{:CREATE_TIME}}','{{:USER_ID}}')"><img src="{{:CREATE_PIC}}" class="img-circle pull-left"></a>
		           	                          <h5>
                                                  <span><a href="//weibo.com/u/{{:CREATE_ID}}" target = "_blank" rel="nofollow">{{:CREATE_NAME}}</a></span>
                                                  {{if IS_READ == '0'}}
                                                      &nbsp;&nbsp;<span id="{{:INTERACTION_ID}}" class="badge badge-danger">$</span>
                                                  {{/if}}
                                              </h5>   
		           	                          <p><span class="mr-20">{{:CREATE_TIME}}</span><span><a href="{{:CSOURCE_URL}}" target="_blank" rel="nofollow">{{:CSOURCE_NAME}}</a></span></p>
		           	                      </div>
		           	                      <div class="list-item-conent">
		           	                          <span onclick="doViewContent('{{:BLOG_USER}}','{{:BLOG_ID}}')"> {{format:CREATE_TEXT}}</span>
		           	                      </div>
		           	                      <div class="clearfix mb-5">
		           	                          <button class="btn btn-xs btn-success pull-right mr-10" type="button" onclick="query.reply('{{:INTERACTION_ID}}','{{:CREATE_ID}}','{{:CREATE_TIME}}','{{:BLOG_ID}}','{{:USER_ID}}')"><span class="glyphicon glyphicon-share-alt"></span> 回复</button>
		           	                      </div>
								</div>
		           	         </div>
                          {{/for}}	         
						 </script>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     		    <jsp:param value="5,10,25,50,100" name="pageSizes"/>
	                     			<jsp:param value="5" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
               </div>
     </form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript" src="wbface/face.js"></script>
<script type="text/javascript">	
	jQuery.namespace("query");
	requreLib.setplugs("wdate");
	
	$(function(){
		$("#searchForm").render();
		showTip();
		setInterval("showTip()", 60 * 1000);
	});
	      
	query.loadData = function() {
		$("#searchForm").searchData();
	} 
	
	query.reply = function(interactionId, createId, createTime, blogId, userId){
		$("#" + interactionId).remove();		
		var data = {"interactionId" : interactionId};
		ajax.remoteCall("/sentiment/servlet/Interact?action=updateInt", data, function(result) {
			if(result.state == 1){
				showTip();
			}
		});
		
		var url = "${ctxPath}/pages/interactive/replyCom.jsp?interactionId=" + interactionId 
		+ "&createId=" + createId + "&createTime=" + createTime + "&blogId=" + blogId + "&userId=" + userId;
        layer.open({type:2,title:'回复微博',content:[url, 'no'],shadeClose:true,area:['600px','90%'],offset:'10px',			
        	shadeClose:false});
	}
	
	query.Mess = function(createId, createTime, userId){	
		var data = {"createId" : createId, "receiveId" : userId, "createTime" : createTime};
		ajax.remoteCall("/sentiment/servlet/Interact?action=updateMess", data, function(result) {
			if(result.state == 1){
				//showTip();
			}
		});
		
		var url = "${ctxPath}/pages/interactive/replyMess.jsp?createId=" + createId 
				+ "&createTime=" + createTime + "&receiveId=" + userId;
		layer.open({type:2,title:'私信聊天',content:[url, 'no'],shadeClose:true,area:['600px','90%'],offset:'10px',			
			shadeClose:false});
	}
	
	function showTip() {
		var data = {"type" : "1"};
		ajax.remoteCall("/sentiment/servlet/Interact?action=tip", data, function(result) { 
			if(result.state == 1){
				$("#tipBadge").text(result.data);
			}
		});
	}
	
	$.views.converters("format", function(str) {
		if(window.formatUtil){
			str = formatUtil.face(str);
			str = formatUtil.preview(str);
			str = formatUtil.atWho(str);
		}
		return str;
	});
	
	function doViewContent(uid,id){
		//查看详情内容，跳转到该ID页面
		var hyperlink = document.createElement('a');
		hyperlink.className="open";
		hyperlink.href = 'http://api.weibo.com/2/statuses/go?uid='+uid+'&id='+id;
		hyperlink.target = '_blank';
		var evt = new MouseEvent('click', {
			view: window,
			bubbles: true,
			cancelable: true
		});
		hyperlink.dispatchEvent(evt);	
	}
	
	function doShowImage(url){
		window.event.stopPropagation();
		url = decodeURIComponent(url);
		layer.open({type:2,title:'内容预览',content:url,shadeClose:true,area: ['60%', '90%']});
	}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>