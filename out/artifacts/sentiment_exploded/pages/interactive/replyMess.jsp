<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
<title>回复私信</title>
<style>
a:link,a:hover,a:visited {
	text-decoration:none;
}
ul.reply-list {
	padding: 0px 10px;
	overflow-y:auto;
}
ul.reply-list li {
	list-style: none;
	border-bottom: 1px dashed #ddd;
	margin-bottom: 10px
}
ul.reply-list li .reply-time {
	font-size: 12px;
	text-align: right;
}
.reply-input p span {
	cursor: pointer
}
.ficon_face {
	color: #ffa405;
	font-size: 20px;
	margin-right: 5px;
	cursor: pointer;
	vertical-align: middle;
	font-family: "wbficonregular" !important;
	display: inline-block;
	-webkit-font-smoothing: antialiased;
}
.facePart{
    display:none; 
    position:absolute; 
    left:32px; 
    bottom:0;
 }
.facePart .inner{position: relative;
    background: #fff;
    border-radius: 3px;
    border: 1px solid #ccc;
    box-shadow: 0 4px 20px 1px rgba(0,0,0,0.2);
    width: 323px;
    padding: 20px 16px 15px;
}
.facePart ul,.facePart li{
    margin:0; 
    padding:0;
}
.facePart .faceHead{
	margin: 0 0 10px;
    text-align: left;
    overflow: hidden;
}
.facePart .faceHead li{
	display: inline-block;
    float: left;
    height: 28px;
    line-height: 26px;
    padding:0 4px;
    border-style: solid;
    border-width: 1px;
    border-color: #d9d9d9;
    background-color:#d9d9d9;
    border-right-color:#cacaca;
    font-size:12px;
}
.facePart .faceHead li:last-child{
	border-right-color:#d9d9d9;
}
.facePart .faceHead .active{
	background-color:#fff;
	font-weight: bold;
	color:red;
}
.facePart .faceItems{
	height: 168px;
}
.facePart .faceItems li{
	cursor: pointer;
    float: left;
    border: 1px solid #e8e8e8;
    height: 25px;
    width: 25px;
    overflow: hidden;
    margin: -1px 0 0 -1px;
    text-align: center;
    display:inline-block;
}
.facePart .faceItems li img{
	width: 20px;
    height: 20px;
}
.facePart .faceItems li:hover{
	background-color: #ff9b9b;
}
.usefullPart{
	position:absolute; 
	background: #fff;
    border-radius: 3px;
    border: 1px solid #ccc;
    box-shadow: 0 4px 20px 1px rgba(0,0,0,0.2);
    left:0;
    bottom:125px;
    z-index:5;
    width:100%;
    overflow:hidden;
    display:none;
}
.phraseList,.phraseList li{
	list-style:none;
	padding:0; margin:0;
}
.phraseList li{
white-space:nowrap;
padding: 5px 10px;
border-bottom:1px solid #eee;
}
.phraseList li:last-child{
border-bottom:0;
}
.media-max{
max-width:200px;
max-height:250px;
}
</style>
<link rel="stylesheet" href="wbface/jquery.magnify.min.css">
<script type="text/javascript" src="wbface/face.js"></script>
<script type="text/javascript" src="wbface/jquery.magnify.min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
	<form method="post" name="editForm" class="form-inline" id="editForm"
		onsubmit="return false" data-toggle="">
		<input type="hidden" name="createId" value="${param.createId}">
		<input type="hidden" name="receiveId" value="${param.receiveId}">
		<div class="ibox">
			<ul data-container="#edit-list" data-template="edit-template"
				id="edit-list" data-mars="Interact.messList"
				data-mars-search-list="true" class="reply-list"></ul>
			<script id="edit-template" type="text/x-jsrender">
              {{for list}}
                  <li>				      
                      {{if TYPE == 'reply'}}
                      <p style="color:#55a7f1;text-align:right;">
                          <a href="//weibo.com/u/{{:RECEIVE_ID}}" target ="_blank" rel="nofollow">{{:RECEIVE_NAME}}</a>
                      {{else}}
                      <p style="color:#55a7f1;text-align:left;">
                          <a href="//weibo.com/u/{{:CREATE_ID}}" target ="_blank" rel="nofollow">{{:CREATE_NAME}}</a>                                     
                      {{/if}}
                      </p>
                      <div style="overflow:hidden;">
                      {{if TYPE == 'reply'}}                    
                          <div style="float:right;">
                      {{else}}
                          <div style="float:left;">                          
                      {{/if}}
                          {{include tmpl="#textOper"/}}</div>
                      </div>
					  <p class="reply-time">{{:CREATE_TIME}}</p>
			      </li>
              {{/for}}					         
          </script>
			<script id="textOper" type="text/x-jsrender">
                {{if TYPE == 'text' || TYPE == 'reply'}}
                    <span>{{format:CREATE_TEXT}}</span>
                {{else TYPE == 'image'}}
                    <img class='media-max' data-magnify='gallery' data-src='/attachment/servlet/attachment?action=download&filePath={{:CREATE_TEXT}}' src='/attachment/servlet/attachment?action=download&filePath={{:CREATE_TEXT}}'></img>
                {{else TYPE == 'voice'}}
                    <audio controls src='/attachment/servlet/attachment?action=download&filePath={{:CREATE_TEXT}}'></audio>
                {{else}}
                    <video class='media-max' controls src='/attachment/servlet/attachment?action=download&filePath={{:CREATE_TEXT}}'></video>
                {{/if}}	
	        </script>
			<div class="row paginate" id="replyPage">
				<jsp:include page="/pages/common/pagination.jsp">
					<jsp:param value="5,10,25,50,100" name="pageSizes" />
					<jsp:param value="100" name="pageSize" />
				</jsp:include>
			</div>
			<div class="reply-input mt-20">
				<textarea class="form-control" rows="3" placeholder="请输入回复内容500字以内..." id="inp"
					style="width: 100%; height: 90px;"></textarea>
				<div class="mt-10" style="position: relative;">
					<span class="mr-5" title="表情" onclick="doShowFaceList(this)"><img src="images/2.png"></span> 
					<span class="glyphicon glyphicon-option-horizontal" title="常用语" onclick="doShowUsefull()"></span>
					<div id="face" class="facePart"></div>
					<div id="usefull" class="usefullPart">
						<ul data-container="#phrase-list" data-template="phrase-template"
							id="phrase-list" data-mars="Interact.phraseList"
							data-mars-search-list="true" class="phraseList">
						</ul>
					</div>
					<script id="phrase-template" type="text/x-jsrender">
                             {{for list}}
                                 <li title="{{:CONTENT}}">{{substr:CONTENT}}</li>
                             {{/for}}					         
				         </script>
				</div>
			</div>
			<p class="mt-20">
				<button type="button" class="btn btn-sm btn-primary" onclick="checkReply()">回复</button>
			</p>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
<script type="text/javascript">
		jQuery.namespace("edit");
		
		$(window).resize(function() {
			$('.reply-list').css('height',$(document.body).height()-$('.reply-input').height()-$('p.mt-20').height()-60);
		});

		$(function() {
			edit.loadData();
		});
		
		edit.loadData = function() {
			$("#editForm").render({
				success : function(result) {
					/*图片预览*/
					$('[data-magnify=gallery]').magnify({
				        Toolbar: ['rotateLeft','rotateRight','zoomIn','actualSize','zoomOut' ],
				        keyboard:true,
				        draggable:true,
				        movable:true,
				        modalSize:[800,600],
				    });	
					
					$('.reply-list').css('height',$(document.body).height()-$('.reply-input').height()-$('p.mt-20').height()-60);
					$('#replyPage').hide();
					$('#edit-list').scrollTop($('#edit-list')[0].scrollHeight);					
				}
			});
			initFace(window.wbface);
		}
		
		$("#inp").on('input',function(){        
			var text = $("#inp").val();
			if(text.length > 1 && text.length < 10) {
				$("#usefull").render({
					data:{text:text},
				    success:function(result) {
				    	if($('#phrase-list li').length > 0)
				    		$('#usefull').show();
				    	else 
				    		$('#usefull').hide();
				    }
				});		
			}
			else if(text.length <= 1) {
				$('#usefull').hide();
				$("#usefull").render();	
			}
		})
		
		$('#phrase-list').on("click",'li',function(e){
			$('#inp').val(this.title);
			$('#usefull').hide();
	    });
		
		function doShowUsefull(){
			$('#usefull').toggle();
		}
		
		$.views.converters("format", function(str) {
			if(window.formatUtil){
				str = formatUtil.face(str);
				str = formatUtil.link(str);
				str = formatUtil.atWho(str);
				str = formatUtil.space(str);
			}
			return str;
		});
		
		$.views.converters("substr", function(str) {
			if(str.length > 38){
				str = str.substr(0, 38) + "......";
			}
			return str;
		});
		
		
	   function initFace(data){
		   var hd='<ul class="faceHead">',z=function(data){
			   var td='<ul class="faceItems">';
			   for(var i=0; i<7*12; i++){
				   if(i>=data.group.length){
					   td+='<li></li>';
				   }else{
					   td+='<li class="item"><img src="wbface/'+data.group[i].img+'" title="'+data.group[i].name+'"/></li>';
				   }
			   }
			   td+='</ul>';
			   return td;
		   };
		   for(var i=0; i<data.length; i++){
			   hd+='<li'+(i==0?' class="active"':'')+'>'+data[i].name+'</li>';
		   }
		   hd+='</ul>';
		   hd+=z(data[0]);
		   document.getElementById('face').innerHTML='<div class="inner">'+hd+'</div>';
		   $('.faceHead li').click(function(e){
			  $(this).siblings('.active').removeClass('active');
			  $(this).addClass('active');
			  var index=$(this).index();
			  var html=z(data[index]);
			  if($('.faceItems').length>0) $('.faceItems').remove();
			  $('#face').children('.inner').append(html);
			  $('.faceItems').on('click','li.item',function(e){
				  var face=this.children[0].title;
				  $('#inp').val($('#inp').val() + '[' + face + ']');
				  $('#face').hide();
			   });
		   });
		   $('.faceItems').on('click','li.item',function(e){
			  var face=this.children[0].title;
			  $('#inp').val($('#inp').val() + '[' + face + ']');
			  $('#face').hide();
		   });
	   }
	   
		//显示表情集
	   function doShowFaceList(node){
		   $('#face').toggle();
		   $('#usefull').hide();
	   }
	   
	   //回复私信
	   function checkReply(){
		   $('#usefull').hide();
		   var text = $.trim($("#inp").val());
		   var receiveId = "${param.receiveId}";
		   
		   if(text == "" || text.length == 0) {
			   layer.alert("回复内容不能为空！", {icon : 5, title:'提示'});			  
			   return;
		   }
		   else if(text.length > 500) {
			   layer.alert("回复内容超出字数限制！", {icon : 5, title:'提示'});
			   return;
		   }
		   
		   var data = {createId:"${param.createId}",
				              receiveId:receiveId,
				              text:text}

		   if(receiveId == "2254189012" && text.indexOf("小天") > -1) {
			   layer.confirm("回复内容与所用账号不符合逻辑，<br>确定继续发送？", {icon: 3, title:'提示'}, function(index){
				   layer.close(index);
				   sendReply(data);
				});
		   }		   
		   else if(receiveId == "2255453230" && text.indexOf("小美") > -1) {
			   layer.confirm("回复内容与所用账号不符合逻辑，<br>确定继续发送？", {icon: 3, title:'提示'}, function(index){
				   layer.close(index);
				   sendReply(data);
				});
		   }
		   else {
			   sendReply(data);
		   }
	   }
	   
	   function sendReply(data){
		   ajax.remoteCall("/sentiment/servlet/Interact?action=replyMess", data,
				   function(result) {
			           if (result.state == 1) {
			        	   layer.msg(result.data, {icon: 1, time:1000}, function(){	
			           		   $("#inp").val("");
			           		   edit.loadData();
			           		   parent.query.loadData();
						    });
						}
			           else {
			        	   layer.alert(result.msg, {icon : 5});
			           }
			     });
	   }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>