<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>工单处理</title>
		<style>	  	    
	    #tableHead th{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	    #tableHead td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
		.btn-container {
			width: 100%;
			display: flex;
			justify-content: flex-end;
			align-items: center;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             	   <input type="hidden" name="SERVICE_TYPE" id="SERVICE_TYPE" >
             	    <input type="hidden" name="PRODUCT_DIVISION" id="PRODUCT_DIVISION" >
             	      <input type="hidden" id="changePage" name="changePage">
		   <input type="hidden" id="COMPLAIN_REASON_NAME" name="COMPLAIN_REASON_NAME">

		   <div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 工单查询</h5>
	             		        <div class="input-group  pull-right">
	             		            <button class="btn btn-sm btn-success" type="button" id="fullTemplateBtn" onclick="downloadExl('1')"><span class="glyphicon glyphicon-export"></span> 导出全模板</button>
	             		       </div>
	             		       <div class="input-group  pull-right mr-10">
	             		            <button class="btn btn-sm btn-success" type="button" id="liteTemplateBtn" onclick="downloadExl('2')"><span class="glyphicon glyphicon-export"></span> 导出精简版</button>
	             		       </div>
	             		       <div class="input-group  pull-right mr-10">
	             		            <button class="btn btn-sm btn-success" type="button" onclick="addOrder()"> +新建工单</button>	             		            
	             		       </div>
	             		 </div>
	             
	             		 <hr style="margin:5px -15px">
	             		 <div class="form-group">
             		           <div class="input-group ">
								      <span class="input-group-addon">网络名</span>	
									  <input type="text" id="NICKNAME" name="NICKNAME" class="form-control input-sm" style="width:120px">
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">用户名</span>	
									  <input type="text" id="CUST_NAME" name="CUST_NAME" class="form-control input-sm" style="width:120px">
							   </div>

							   <div class="input-group ">
								      <span class="input-group-addon">联系电话</span>	
									  <input type="text" id="TELEPHONE_NUM" name="TELEPHONE_NUM" class="form-control input-sm" style="width:90px">
							   </div>
							 <div class="input-group input-group-sm">
								 <span class="input-group-addon">数据来源</span>
								 <select name="SENTIMENT_FROM" id="SENTIMENT_FROM" class="form-control"
										 style="width:100px;" multiple="multiple">
								 </select>
							 </div>
							 <div class="input-group input-group-sm">
								 <span class="input-group-addon">帖子状态</span>
								 <select name="CARD_STATUS" id="CARD_STATUS" class="form-control"  data-mars="comm.getDict('CARD_STATUS')">
									 <option value="">请选择</option>
								 </select>
							 </div>
							 <div class="input-group input-group-sm">
								 <span class="input-group-addon">工单渠道来源</span>
								 <select name="DATA_SOURCES" id="DATA_SOURCES" class="form-control"  >
									 <option value="">请选择</option>
									 <option value="01">系统爬取</option>
									 <option value="02">手工建单</option>
									 <option value="03">系统&手工</option>
								 </select>
							 </div>
							  <div class="input-group  pull-right">
								 	<button type="button" id="moreBtn" class="btn btn-sm btn-link" onclick="toggleMore()" title="展开"> 展开 <span class="glyphicon glyphicon glyphicon-menu-up"></span>&nbsp;</button>
							  </div>
							  
							   
						  </div>
						  <div class="form-group moreSearch" style="display:none">
							  <div class="input-group input-group-sm">
								  <span class="input-group-addon">产品主体</span>
								  <select data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')"
										  name="PRODUCT_DIVISIONS" id="PRODUCT_DIVISIONS" class="form-control"
										  style="width:100px;" multiple="multiple">
								  </select>
							  </div>
             		           <div class="input-group ">
								      <span class="input-group-addon">产品类别</span>	
									    <div class="input-group  new-input-group">
					                             <input type="text"  name="PRODUCT_CATEGORY" id="prodName"  class="form-control input-sm" style="width: 130px">
					                             <span class="input-group-addon" onclick="orderDeal3.productType()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                          </div>
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">工单状态</span>	
									   <select  class="form-control input-sm"  data-cust-context-path="/yq_common" name="STATUS" data-cust-mars="dict.getDictList('SENTIMENT_ORDER_STATUS')"> 
			                                 <option value="">请选择</option>
			                            </select>
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">受理操作员</span>	
									  <input type="text" name="CREATE_NAME" class="form-control input-sm" style="width:90px">
							   </div>
							 	   <div class="input-group ">
								      <span class="input-group-addon">处理操作员</span>	
									  <input type="text" name="PROCESS_NAME" class="form-control input-sm" style="width:90px">
							   </div>
							  <div class="input-group ">
								  <span class="input-group-addon">服务请求</span>
								  <select name="SERVICE_TYPES" id="SERVICE_TYPES" data-mars="order.serviceType" class="form-control input-sm"onchange="changRequest(this.value) " >
									  <option value="">请选择</option>
								  </select>
							  </div>
							  <div class="input-group input-group-sm">
								  <span class="input-group-addon">投诉原因</span>
								  <select  data-mars="order.complainReason"
										   name="COMPLAIN_REASON" id="COMPLAIN_REASON" class="form-control"
										   style="width:100px;" multiple="multiple">
								  </select>
							  </div>
							 
						   </div>
						   	 <div class="form-group moreSearch" style="display:none">
						 <div class="input-group ">
             		              <span class="input-group-addon">创建日期</span>	
								  <input type="text" name="beginTime" id="beginTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 00:00:00'})" style="width:110px">
             		              <span class="input-group-addon">-</span>	
								  <input type="text" name="endTime" id="endTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 23:59:59'})" style="width:110px">
							  </div>
							     <div class="input-group ">
								      <span class="input-group-addon">是否有客户资料</span>	
									<select id="EXIST_CUST"  class="form-control input-sm" data-cust-context-path="/yq_common" name="EXIST_CUST" data-cust-mars="dict.getDictList('PUSH_STATIS')"> 
			                                 <option value="">请选择</option>
			                            </select>
							   </div>
								 <div class="input-group ">
									 <span class="input-group-addon">舆情工单号</span>
									 <input type="text" id="ID" name="ID" class="form-control input-sm" style="width:120px">
								 </div>
								 <div class="input-group ">
									 <span class="input-group-addon">升级投诉单号</span>
									 <input type="text" id="COMPLAINT_UPGRADE_NO" name="COMPLAINT_UPGRADE_NO"
											class="form-control input-sm" style="width:90px">
								 </div>
						   </div>
						<div class="form-group moreSearch" style="display:none">
							<div class="input-group ">
								<span class="input-group-addon">是否线上升级</span>
								<select id="ON_LINE"  class="form-control input-sm" name="ON_LINE">
									<option value="">请选择</option>
									<option value="1">是</option>
									<option value="0">否</option>
								</select>
							</div>
							<div class="input-group ">
								<span class="input-group-addon">是否线下升级</span>
								<select id="DOWN_LINE"  class="form-control input-sm" name="DOWN_LINE">
									<option value="">请选择</option>
									<option value="1">是</option>
									<option value="0">否</option>
								</select>
							</div>
							<div class="input-group ">
								<span class="input-group-addon">投诉原文</span>
								<textarea class="form-control input-sm" name="CONTENT" maxlength="4000" id="CONTENT"></textarea>
<%--								<input type="text" id="CONTENT" name="CONTENT"--%>
<%--									   class="form-control input-sm" style="width:150px">--%>
							</div>
							<div class="input-group ">
								<span class="input-group-addon">是否沟通删除</span>
								<select id="IS_TALK_DELETED"  class="form-control input-sm" name="IS_TALK_DELETED" data-mars="order.info">
									<option value="">请选择</option>
								</select>
							</div>
							<div class="input-group input-group-sm">
								<span class="input-group-addon">是否删帖</span>
								<select  data-mars="comm.getDict('IS_DELETE_CARD')"
										 name="IS_DELETE" id="IS_DELETE" class="form-control"
										 style="width:100px;" multiple="multiple">
								</select>
							</div>
						</div>
						<div class="form-group">
							<div class="btn-container">
							<div class="input-group pull-right" style="padding-right: 10px;padding-left: 5px">
								<button type="button" class="btn btn-sm btn-success" onclick="handleReset()">重置</button>
							</div>
						   <div class="input-group  pull-right ">
									  <button type="button" class="btn btn-sm btn-default" onclick="selectList()"><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							</div>
						   </div>
             	    </div>  
	              		<div class="ibox-content">
		           	    <div class="row table-responsive">
							<table style="min-width:2000px;width:100%" class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead" data-mars="order.orderTypeList">
                             <thead>
	                         	 <tr>
	                         	 	<th width="40px"></th>
									 <th class="text-c" style="width:220px">舆情工单号</th>
									 <th class="text-c" style="width:220px">升级投诉单号</th>
	                         	      <th  class="text-c" style="width:220px">网络名</th>
	                         	      <th class="text-c" style="width:220px">原文链接</th>
	                         	      <th class="text-c" style="width:220px">投诉主题</th>
	                         	      <th  class="text-c" style="width:220px">数据来源</th>
								      <th  class="text-c" style="width:220px">是否有客户资料</th>
									 <th  class="text-c" style="width:220px">用户发帖时间</th>
								      <th  class="text-c" style="width:220px">用户</th>
								      <th  class="text-c" style="width:220px">联系电话1</th>
								      <th  class="text-c" style="width:220px">联系电话2</th>
									 <th class="text-c" style="width:50px;max-width:50px;min-width:50px">查看</th>
								      <th  class="text-c" style="width:220px">产品主体</th>
								      <th  class="text-c" style="width:220px">产品大类</th>
								      <th  class="text-c" style="width:220px">投诉原因</th>
								      <th  class="text-c" style="width:220px">工单状态</th>
								      <th  class="text-c" style="width:220px">受理操作员</th>
								      <th  class="text-c" style="width:220px">处理操作员</th>
								      <th  class="text-c" style="width:220px">工单类型</th>
								      <th  class="text-c" style="width:220px">帖子状态</th>
									 <th  class="text-c" style="width:220px">工单渠道来源</th>
								 </tr>
                             </thead>
                             <tbody id="dataList">
                                  <tr>
                                  
                                  </tr>
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td  class="text-c"><input type="radio"  name="radio" value='{{:ID}};{{:ORDER_TYPE}}'></td>
											<td style="width:220px;max-width:220px" title="{{:ID}}">{{:ID}}</td>
											<td style="width:220px;max-width:220px" title="{{:COMPLAINT_UPGRADE_NO}}">{{:COMPLAINT_UPGRADE_NO}}</td>
											<td style="width:220px;max-width:220px"><a href="javascript:;" onclick="orderDeal3.editData('{{:ID}}','{{:ORDER_TYPE}}')">{{:NICKNAME}}</a></td>
											<td style="width:220px;max-width:220px" title="{{:URL}}">{{:URL}}</td>
											<td style="width:220px;max-width:220px" title="{{:COMPLIAN_TOPIC}}">{{:COMPLIAN_TOPIC}}</td>
											<td style="width:220px;max-width:220px">{{dictFUN:SENTIMENT_FROM "SENTIMENT_FROM"}}</td>
											<td style="width:220px;max-width:220px">{{dictFUN:EXIST_CUST "PUSH_STATIS"}}</td>
											<td style="width:220px;max-width:220px" title="{{:DISTRIBUTED_TIME}}">{{:DISTRIBUTED_TIME}}</td>
											<td style="width:220px;max-width:220px">{{call:CUST_NAME CUST_NAME_data #index+1 'name' fn='getData2'}}</td>
											<td style="width:220px;max-width:220px">{{call:TELEPHONE_NUM TELEPHONE_NUM_data #index+1 'phone' fn='getData2'}}</td>
											<td style="width:220px;max-width:220px">{{call:TELEPHONE_NUM2 TELEPHONE_NUM2_data #index+1 'phone2' fn='getData2'}}</td>
											<td title="查看" class="text-c">
												<span onclick="showDetail('{{:#index+1}}','{{:CUST_NAME_data}}','{{:TELEPHONE_NUM_data}}','{{:TELEPHONE_NUM2_data}}')"
												class="glyphicon glyphicon-eye-open" id="show{{:#index+1}}" style="color: rgb(255, 140, 60);"></span>
										 	</td>
											<td style="width:220px;max-width:220px">{{:PRODUCT_DIVISION}}</td>
											<td style="width:220px;max-width:220px">{{:PRODUCT_CATEGORY}}</td>
											<td style="width:220px;max-width:220px">{{:COMPLAIN_REASON}}</td>
											<td style="width:220px;max-width:50px">{{dictFUN:STATUS "SENTIMENT_ORDER_STATUS"}}</td>
											<td style="width:220px;max-width:50px">{{:CREATE_NAME}}</td>
											<td style="width:220px;max-width:220px">{{:PROCESS_NAME}}</td>
											<td style="width:220px;max-width:220px">{{dictFUN:ORDER_TYPE "SENTIMENT_ORDER_TYPE"}}</td>
											<td style="width:220px;max-width:220px">{{dictFUN:CARD_STATUS "CARD_STATUS"}}</td>
											<td style="width:220px;max-width:220px">{{getText:DATA_SOURCES '#DATA_SOURCES'}}</td>

									    </tr>
								   {{/for}}					         
							 </script>
							 </div>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<link type="text/css" rel="stylesheet"
		  href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css"/>
	<script type="text/javascript"
			src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>

	<script type="text/javascript">
		jQuery.namespace("orderDeal3");
		orderDeal3.multiSetting = {
			buttonWidth: '100%',
			allSelectedText: "全部",
			nonSelectedText: "请选择",
			nSelectedText: "个被选中",
			selectAllNumber: false,
			maxHeight: 350,
			includeSelectAllOption: true,
			selectAllText: '全选',
			enableFiltering: true
		};
		requreLib.setplugs("wdate")
		$(function(){
			var beginTime = getTodayDate(-364);
			var endDate = getTodayDate();
			$("#beginTime").val(beginTime + " 00:00:00");
			$("#endTime").val(endDate + " 23:59:59");
			orderDeal3.getCache();
			$("#searchForm").render({success : function(result){
					$('#PRODUCT_DIVISIONS').multiselect(orderDeal3.multiSetting);
					$('#IS_DELETE').multiselect(orderDeal3.multiSetting);
					//$('#COMPLAIN_REASON').multiselect(orderDeal3.multiSetting);
					var data = {"params":{},"controls":["dataInfo.sentimentChannelList"]}
					ajax.remoteCall("${ctxPath}/webcall",data,function(result2) {
						var dataInfo= result2['dataInfo.sentimentChannelList']['data'];
						// 获取下拉框元素
						var selectElement = $('#SENTIMENT_FROM');
						// 遍历 Map 对象并生成选项
						dataInfo.forEach(function(item) {
							var option = $('<option></option>')
									.attr('value', item.value)
									.text(item.text);
							selectElement.append(option);
						});
						selectElement.multiselect(orderDeal3.multiSetting);
					});
				}
			});
		})

		let intervalId = null;
		function changRequest(val){
			//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_REASON").render({data:{id:val},success:function(){
					$('#COMPLAIN_REASON').multiselect(orderDeal3.multiSetting);
				}});
		}
		orderDeal3.getCache = function () {
			var data = {
				type: "6",
			}
			ajax.remoteCall("${ctxPath}/servlet/upload?action=cache", data, function (result) {
				if (result.state !== 1) {
					if (!intervalId) { // 确保只设置一次定时器
						intervalId = setInterval(orderDeal3.getCache, 10000);
					}
					var fullTemplateBtn = document.getElementById('fullTemplateBtn');
					var liteTemplateBtn = document.getElementById('liteTemplateBtn');
					// 禁用按钮
					fullTemplateBtn.disabled = true;
					liteTemplateBtn.disabled = true;
				} else {
					clearInterval(intervalId);
					var fullTemplateBtn = document.getElementById('fullTemplateBtn');
					var liteTemplateBtn = document.getElementById('liteTemplateBtn');
					// 取消禁用按钮
					fullTemplateBtn.disabled = false;
					liteTemplateBtn.disabled = false;
				}
			}, {loading:false});
		}
        orderDeal3.editData = function(val,val1){
			if('01'==val1){
				 popup.layerShow({type:2,title:'正向舆情工单',shadeClose:false,area:['880px','600px'],offset:'20px'},"${ctxPath}/pages/order/order-archive-one.jsp",{id:val,type:'88'});
			}else{
				$.ajax({
					url:"${ctxPath}/servlet/orderInf?query=isAre",
					data:{"id":val},
					async:false,
					success:function(result) { 
						if(result.state == 1){
							popup.openTab("${ctxPath}/pages/order/order-deal-type.jsp","舆情工单",{id:val,proId:result.data,revId:result.msg,type:'88'});

						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
			});
			}
		}
		orderDeal3.productType = function(){			
			   	popup.layerShow({type:2,title:'产品品类',shadeClose:false,area:['860px','700px'],offset:'20px'},"/neworder/pages/access/product-type.jsp?brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName");
			   	//$('#orgCode').click();
		   }
		function selectList(count){
			if (getDateTimeValue('beginTime') === false || getDateTimeValue('endTime') === false) return;

			if(''!=count&&undefined!=count){
				var num= $("input[name='pageIndex']").val();
				$("#changePage").val(num);
			}else{
				$("#changePage").val('');

			}
			var option=$("#orgCode option:selected");				
			var PRODUCT_DIVISION=option.text();
			var option1=$("#SERVICE_TYPES option:selected");				
			var SERVICE_TYPE=option1.text();
			if('请选择'==PRODUCT_DIVISION){
				$("#PRODUCT_DIVISION").val('');
			}else{
				$("#PRODUCT_DIVISION").val(PRODUCT_DIVISION);
			}
			if('请选择'==SERVICE_TYPE){
				$("#SERVICE_TYPE").val('');
			}else{
				$("#SERVICE_TYPE").val(SERVICE_TYPE);
			}
			var selectedTexts = $('#COMPLAIN_REASON option:selected').map(function() {
				return $(this).text();
			}).get();
			if(selectedTexts && selectedTexts !=null){
				$('#COMPLAIN_REASON_NAME').val(selectedTexts);
			}
			$("#searchForm").searchData({success:function(result){
				$("#changePage").val('');

				}});
		}

		function handleReset() {
			document.searchForm.reset();
			var beginTime = getTodayDate(-364);
			var endDate = getTodayDate();
			console.log('beginTime:'+beginTime)
			console.log('endTime'+endDate)
			$("#beginTime").val(beginTime + " 00:00:00");
			$("#endTime").val(endDate + " 23:59:59");
			var multiselects = $('select[multiple]');
			multiselects.each(function() {
				$(this).multiselect('deselectAll', false);
				$(this).multiselect('updateButtonText');
			});
			$("#searchForm").render();
			selectList();
		}
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}

		function getDateTimeValue(id) {
			var dateString = document.getElementById(id).value;
			var NICKNAME = document.getElementById("NICKNAME").value;
			var CUST_NAME = document.getElementById("CUST_NAME").value;
			var TELEPHONE_NUM = document.getElementById("TELEPHONE_NUM").value;
			if ((!NICKNAME || NICKNAME === "") && (!CUST_NAME || CUST_NAME === "")  && (!TELEPHONE_NUM || TELEPHONE_NUM === "") && (!dateString || dateString === "")) {
				layer.alert("请选择时间范围！");
				return false;
			}
			return new Date(dateString);
		}

		function downloadExl(val){
			if (getDateTimeValue('beginTime') === false || getDateTimeValue('endTime') === false) return;
			var selectedTexts = $('#COMPLAIN_REASON option:selected').map(function() {
				return $(this).text();
			}).get();
			if(selectedTexts && selectedTexts !=null){
				$('#COMPLAIN_REASON_NAME').val(selectedTexts);
			}
			var beginTime = getDateTimeValue('beginTime');
			var endTime = getDateTimeValue('endTime');

			// 计算两个日期之间的毫秒差并转换为天数
			var diffDays = Math.abs((endTime - beginTime) / (1000 * 60 * 60 * 24));

			// 判断时间差是否超过一年（365天）
			if (diffDays > 365) {
				layer.alert("错误：选择的时间范围不能超过一年！");
				return ; // 阻止表单提交或执行后续操作
			}
			var fullTemplateBtn = document.getElementById('fullTemplateBtn');
			var liteTemplateBtn = document.getElementById('liteTemplateBtn');

			// 禁用按钮
			fullTemplateBtn.disabled = true;
			liteTemplateBtn.disabled = true;
			var option=$("#orgCode option:selected");				
			var PRODUCT_DIVISION=option.text();
			var option1=$("#SERVICE_TYPES option:selected");				
			var SERVICE_TYPE=option1.text();
			if('请选择'==PRODUCT_DIVISION){
				$("#PRODUCT_DIVISION").val('');
			}else{
				$("#PRODUCT_DIVISION").val(PRODUCT_DIVISION);
			}
			if('请选择'==SERVICE_TYPE){
				$("#SERVICE_TYPE").val('');
			}else{
				$("#SERVICE_TYPE").val(SERVICE_TYPE);
			}
			var data = form.getJSONObject("searchForm");
			var last=JSON.stringify(data); //将JSON对象转化为JSON字符
			var SENTIMENT_FROMS =data.SENTIMENT_FROM === undefined?"":JSON.stringify(data.SENTIMENT_FROM);
			var PRODUCT_DIVISIONLIST =data.PRODUCT_DIVISIONS === undefined?"": JSON.stringify(data.PRODUCT_DIVISIONS);
			var IS_DELETElIST =data.IS_DELETE === undefined?"": JSON.stringify(data.IS_DELETE);
			if ('1' == val) {
				location.href = "${ctxPath}/servlet/upload?action=exportAll&type=4&SENTIMENT_FROMS="+SENTIMENT_FROMS+"&PRODUCT_DIVISIONLIST="+PRODUCT_DIVISIONLIST+"&IS_DELETElIST="+IS_DELETElIST+"&" + $("#searchForm").serialize();
			} else {
				location.href = "${ctxPath}/servlet/upload?action=export&type=4&SENTIMENT_FROMS="+SENTIMENT_FROMS+"&PRODUCT_DIVISIONLIST="+PRODUCT_DIVISIONLIST+"&IS_DELETElIST="+IS_DELETElIST+"&" + $("#searchForm").serialize();
			}
			setTimeout(function() {
				orderDeal3.getCache();
			}, 5000);
		}
		function addOrder(){
			var ids = $('input[name="radio"]:checked').val();
			if(''==ids||null==ids){
				layer.alert("请选择行!",{icon:2,time:1000});
				return false;
			}
			
			var val1=ids.split(";")[1];
			var val=ids.split(";")[0];
			if('01'==val1){
				 popup.layerShow({type:2,title:'正向舆情工单',area:['880px','600px'],offset:'20px'},"${ctxPath}/pages/order/order-archive-one.jsp",{id:val,type:'88',insert:'99'});
			}else{
				popup.openTab("${ctxPath}/pages/order/order-deal-type.jsp","舆情工单",{id:val,type:'88',insert:'99'});

			} 
		}
		//小眼睛功能
		function showDetail(id,name,phone,phone2){
			showDetailCommon({
				"model":"sentiment",
				"url":"/sentiment/pages/order/ordertype-list3.jsp",
				"action":"acces",
				"describe":"工单处理[全部]数据，看客户[{{name}}]敏感信息：[用户号码1：{{phone}},用户号码2：{{phone2}}]"},id,name,null,phone,phone2);
		}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>