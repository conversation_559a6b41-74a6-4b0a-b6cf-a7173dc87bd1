<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>工单回访</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form id="editRevForm" data-mars="orderInf.orderRevisit"  method="post"  autocomplete="off" data-mars-prefix="" >
               <input type="hidden" name="rev.ID" value="${param.ID}">
               <input type="hidden" name="rev.ORDER_ID" value="${param.orderId}">
                  <div class="info-title">
				  		<p>回访归档</p>
				  </div>
                  <table class="table  table-vzebra mt-10" >
	                    <tbody>
	                         <tr>
	                                <td >回访渠道类型</td>
			                        <td><input type="text" name="rev.CHANNEL" maxlength="50"   id="" class="form-control input-sm"></td>

              						<td  >回访类别</td>
			                       	 <td><input type="text" name="rev.CATEGORY" maxlength="50"   id="" class="form-control input-sm"></td>

              						<td  >回访项</td>
			                     	 <td><input type="text" name="rev.ITEM" maxlength="50"   id="" class="form-control input-sm"></td>
              			     </tr>
		                     <tr>
		                            <td >回访项结果</td>
			                        <td><input type="text" name="rev.ITEM_RESULT" maxlength="50"   id="" class="form-control input-sm"></td>
			                        <td >是否需要回访</td>
			                        <td>
			                            <select name="rev.NEED_VISIT"   data-mars="order.info"  class="form-control input-sm">
					               					                    		<option value="">请选择</option>
					               
              						    </select>
              						</td>
			                        <td >回访时间</td>
			                        <td><input type="text" name="rev.VISIT_TIME"   id="" class="form-control input-sm Wdate" onclick="WdatePicker()"></td>
		                     </tr>
			                 <tr>     
			                        <td >是否虚假反馈</td>
			                        <td>
			                            <select name="rev.IS_REAL"  data-mars="order.info"  class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						 </td>
			                        <td >是否已删帖</td>
			                        <td>
			                            <select name="rev.IS_DELETED"  data-mars="comm.getDict('IS_DELETE_CARD')"  class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						 </td>
		                            <td >是否补贴</td>
			                        <td>
			                            <select name="rev.IS_SUPPLEMENT"   data-mars="comm.getDict('IS_SUPPLEMENT_CARD')" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                 </tr>
			                 <tr>
			                        <td >满意度</td>
			                        <td>
			                            <select name="rev.SATISFACTION"    class="form-control input-sm" data-mars="order.action">
					                    		<option value="">请选择</option>
              						    </select>
              						 </td>
			                         <td >回访结果</td>
				                     <td colspan="3">
				                        <textarea rows="3" maxlength="500"   class="form-control input-sm" name="rev.VISIT_RESULT"></textarea>
				                     </td>
		                      </tr>
		                        
			              </tbody>
			      </table>
			      <p class="text-c mt-40">
				   		<button class="btn btn-sm btn-primary"  id="revButton" type="button" onclick=" orderDistr.ajaxSubmitForm()">保存</button>
				  </p> 
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<!-- 			<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
 -->	
	<script type="text/javascript">	
	   jQuery.namespace("orderDistr");
	   orderDistr.id='${param.ID}';

	   $(function(){
		/*    if(''== orderDistr.id||null== orderDistr.id){
				layer.alert("该工单没有回访信息！",{icon:2});
				$("#revButton").attr("disabled",true);
			} */
		   $("#editRevForm").render();
	   });
	   orderDistr.ajaxSubmitForm = function(){
	 		if(form.validate("editRevForm")){		
	 			orderDistr.insertData(); 	
			};
	 	}
	   orderDistr.insertData = function() {
			var data = form.getJSONObject("editRevForm");
	 		ajax.remoteCall("${ctxPath}/servlet/orderInf?action=UpOrederRev",data,function(result) { 
				//debugger;
					if(result.state == 1){
						layer.alert(result.msg,{icon: 1,time:1000},function(){
						});
/* 						$("#revButton").attr("disabled",true);
 */					
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }

	 	function isAre(){
	 		$.ajax({
				url:"${ctxPath}/servlet/orderInf?query=isAre",
				data:{"id":orderPro.id},
				async:false,
				success:function(result) { 
					if(result.state == 1){
						if(result.data==0){
							layer.alert("该工单没有派发！",{icon:2});
							$("#revButton").attr("disabled",true);
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>