<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>短信</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table  table-edit table-vzebra mt-10" >
	                   <tbody>
		                     <tr>
			                        <td width="40px" class="required">手机号码</td>
			                        <td width="320px">
<!-- 			                        <input type="text" name="RECEIVER"  data-rules="required" id="RECEIVERIp" class="form-control input-sm">
 -->			                         <textarea data-rules="required"class="form-control input-sm" name="RECEIVER" id="RECEIVERIp"  ></textarea>
			                        </td>			             
		                     </tr>
		                     <tr>
		                           <td class="required">内容</td>
		                           <td>
		                              <textarea  name="CONTENT" id="CONTENTSMS" data-rules="required"  class="form-control input-sm" rows="8"></textarea>
		                           
										<p><span id="text-count">500</span>/500</p>
		                           </td>
		                     </tr>
		                    
	                    </tbody>
	               </table>
				   <p class="text-c mt-40">
					    <button class="btn btn-sm btn-primary"  style="width:80px" type="button" onclick=" msg.ajaxSubmitForm()">发送</button>
				   </p>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">	
	   jQuery.namespace("msg");
	  // msg.sendData='${param.sendData}';
   		var josnData=sendData;
    	josnData.URL=josnData.URL.replace(/\></g,'\&');;

   		msg.orgCode=josnData.orgCode;
   		msg.prodName=josnData.prodName;
   		msg.smsId=josnData.smsId;
   		msg.branchCode=josnData.DEPARTMENT;
   		msg.upgrade='${param.upgrade}';

    	var sentimeId=josnData.sentimeId;
    	var subOrderId=josnData.subOrderId;
	   $(function(){
		   smsCount();
		   //匹配发送短信的人员号码 旧
		   // getUserInfo();
		   getUserInfoNew();

	   });
	   function getUserInfoNew(){
		   var params = {
			   upgradeType: $("#upgradeType2").val(),
			   upgradeTypes: $("#upgradeType2").val(),
			   orgCode: $("#orgCode").val(),
			   branchCode: $("#branchCode").val(),
			   areaCode: $("#areaCode").val(),
			   prodCode: $("#prodCode").val(),
		   };
		   console.log("短信参数params:",params);
		   let req = {
			   controls:["Contacts.getMsgNoticeList"],
			   params:params
		   }
		   ajax.remoteCall("/sentiment/webcall",req,function(result){
			   var list = result['Contacts.getMsgNoticeList'];
			   console.log("短信list:",list);
			   $('#RECEIVERIp').val(list.data);
		   });
	   }
	   msg.ajaxSubmitForm = function(){
			if(form.validate("editForm")){		
				msg.insertData(); 	
			};
		}
	   msg.insertData = function() {
		   	var content=$("#CONTENTSMS").val();
		   	if(content.length>500){
		   		layer.alert('超过500字,无法发送!',{icon:2});
		   		return false;
		   	}
		   	if(subOrderId==null||subOrderId==''){
		 		layer.alert('没有工单单号,无法发送',{icon:2,time:1000});
		 		return false;
		 	}
		   	 var iphList="";
		  	 var ago1=$("#RECEIVERIp").val().split(',');		  	
	 		 var myreg=/^[1][0-9][0-9]{9}$/; 	 		 
	 		 for(var i=0;i<ago1.length;i++){
	 		  var nstr = ago1[i].replace(/\([^\)]*\)/g,"");
	 			 iphList+=nstr+',';
		          if (!myreg.test(nstr)) {
						layer.msg("号码格式不正确:"+nstr, {icon : 2});
		              return false;  
		          }
	 		 }
 
			var data = form.getJSONObject("editForm");
			data.ORDER_ID=subOrderId;
			data.iphList=iphList;
			//var index = parent.layer.getFrameIndex(window.name);
			ajax.remoteCall("${ctxPath}/servlet/order?action=sendSms",data,function(result) { 
					if(result.state == 1){
						layer.alert(result.msg,{icon: 1},function(){
							layer.closeAll();
						});
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }
	 //匹配发送短信的人员号码(旧)
	function getUserInfo(){
		$.ajax({
			url:"${ctxPath}/servlet/order?query=SendUserInfo",
			data:{"branchCode":msg.branchCode,"prodName":msg.prodName,"sendType":"短信","orgCode":msg.orgCode,"upgrade":msg.upgrade},
			async:false,
			success:function(result) { 
				if(result.state == 1){	
					var mg=result.data.toString();
					if('1'==mg){
						layer.alert(result.msg,{icon: 1});
						return false;
					}
					mg=Trim(mg,'g');
					$("#RECEIVERIp").val((mg.replace(/\[|]/g,'')));
					//list.timeTd=result.msg;
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
	});
	}
	 function Trim(str,is_global)
	  {
	   var result;
	   result = str.replace(/(^\s+)|(\s+$)/g,"");
	   if(is_global.toLowerCase()=="g")
	   {
	    result = result.replace(/\s/g,"");
	    }
	   return result;
	}
	 var countAjax='';
	 function orderInfo(){
		 
			$.ajax({
				url:"${ctxPath}/servlet/order?query=getOrderPos1",
				data:{"id":sentimeId},
				async:false,
				success:function(result) { 
					if(result.state == 1){
						var ret = result.data;
						for(var i=0;i<ret.length;i++){
							var ob=ret[i];
							countAjax=ob.CONTENT.replace(/(^\s*)/g,"");
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 function orderInfo1(){
			$.ajax({
				url:"${ctxPath}/servlet/order?query=getOrderPos2",
				data:{"id":subOrderId},
				async:false,
				success:function(result) { 
					if(result.state == 1){
						countAjax = result.data;
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 function smsCount(){
		 $.ajax({
				url:"${ctxPath}/servlet/order?query=smsContent",
				data:{"id":msg.smsId},
				async:false,
				success:function(result) {
					if(result.state == 1){
						if(josnData.TYPEEMAIL=='99'||'yes'==deal.logeType){
							orderInfo1();
						}else{
							orderInfo();
						}
						var reData=result.data;
						 if(undefined==countAjax){
							 countAjax='';
						 }
						 reData=reData.replace(/CONTENT/g,countAjax);
						if(josnData["TELEPHONE_NUM2"]){
							josnData["TELEPHONE_NUM"] += ','+josnData["TELEPHONE_NUM2"]
						}
						for(var i in josnData){
							 reData=reData.replace(i,josnData[i]);							
						} 
						var newData=reData;	
			   			//var count=newData.substring(0, 500);
			   			$("#CONTENTSMS").val(newData);
				        $("#text-count").text(newData.length);

					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 $("#CONTENTSMS").on("input propertychange", function() {
	        var $this = $(this),
	            _val = $this.val();
	    /*         count = "";
	         if (_val.length > 500) {
	            $this.val(_val.substring(0, 500));
	        }
	        count = 500 - $this.val().length;  */
		   	var content=$("#CONTENTSMS").val();

	        $("#text-count").text(content.length);
	 });
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>