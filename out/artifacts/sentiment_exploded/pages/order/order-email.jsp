<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>邮件</title>
		<style>
			.bootstrap-tagsinput{width:100%}
		</style>
	<script type="text/javascript" src="${ctxPath}/static/js/ueditor/ueditor.config.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/ueditor/ueditor.all.js"> </script>
    <link href="${ctxPath}/static/js/ueditor/third-party/codemirror/codemirror.css" type="text/css" rel="stylesheet">
    <link href="${ctxPath}/static/js/ueditor/themes/default/css/ueditor.css" type="text/css" rel="stylesheet">
    <link href="${ctxPath}/static/js/ueditor/themes/iframe.css" type="text/css" rel="stylesheet">
    <script type="text/javascript" src="${ctxPath}/static/js/ueditor/third-party/codemirror/codemirror.js"> </script>
    <script type="text/javascript" src="${ctxPath}/static/js/ueditor/third-party/zeroclipboard/ZeroClipboard.js"> </script>
    <script type="text/javascript"  src="${ctxPath}/static/js/ueditor/lang/zh-cn/zh-cn.js"></script>
</EasyTag:override>
<EasyTag:override name="content">

		<form id="editEmailForm" method="post"  autocomplete="off" onsubmit="return false">
					 <input type="hidden" name="busiId" id="busiId"/>
					 <input type="hidden" name="" id="COMPLAINT_SOURCE_URL"/>
			<div class="text-c" style="margin-top:1px;float: right;display: inline-block;padding: 0 0 0 5px" >
				<button class="btn btn-sm btn-primary  pull-right"  style="width:80px;height: 36px" type="button" onclick="orderEmail.send()">发送</button>
				<!-- <button class="btn btn-sm btn-primary  pull-right"  style="width:80px" type="button" onclick="orderEmail.ajaxSubmitForm()">发送</button> -->
			</div>
			<div class="text-c" style="margin-top:1px;float: right;display: inline-block; padding: 0 5px 0 5px;">
				<button class="btn btn-sm btn-primary  pull-right"  style="width:80px;height: 36px" type="button" onclick="orderEmail.addEmail()">添加邮件人</button>
				<!-- <button class="btn btn-sm btn-primary  pull-right"  style="width:80px" type="button" onclick="orderEmail.ajaxSubmitForm()">发送</button> -->
			</div>
			<div class="input-group" style="margin-top:1px;float: right;display: inline-block">
				<span>升级类型</span>
				<div id="zxServiceRequire"  style="display: inline-block;height: 36px;" >
					<div class="block">
						<el-cascader
								v-model="zxServiceRequire"
								:options="options"
								:props="props"
								:show-all-levels="false"
								collapse-tags
								style="width:100%"
						></el-cascader>
					</div>
				</div>
			</div>
			<%--分中心  data-mars="comm.getBranchCode('CS006')"--%>
			<div class="input-group width-18">
				<span class="input-group-addon" style="width:30%">分中心</span>
				<select class="form-control input-sm width-70"
						id="branchCode1"  name="branchName1"  multiple="multiple">
				</select>
			</div>

			 <table class="table table-edit table-vzebra mt-10" >
                   <tbody>
                  			 <tr>
		                        <td width="50px" class="required">线上收件人</td>
		                        <td width="240px">
			                      <textarea  class="form-control input-sm" name="mip" id="mip"  ></textarea>

		                        </td>
		                     </tr>
	                      	<tr>
		                        <td width="50px" class="required">收件人</td>
		                        <td width="240px">
<!-- 			                      <input type="text" name="RECEIVER"  id="RECEIVER" data-rules="required" id="context" class="form-control input-sm">
 -->			                      <textarea class="form-control input-sm" name="RECEIVER" id="RECEIVER"  ></textarea>

		                        </td>
		                     </tr>
		                      <tr>
			                        <td>抄送人</td>
			                        <td>
<!-- 			                        <input type="text" name="COPY"  id="COPY" data-rules="" class="form-control input-sm">
 -->			                        <textarea class="form-control input-sm" name="COPY" id="COPY"  ></textarea>

			                        </td>
		                     </tr>
		                      <tr>
			                        <td class="required">主题</td>
			                        <td><input type="text" name="TOPIC"  data-rules="required" id="TOPIC" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                           <td class="required">内容</td>
		                           <td>
		                           	 <div>
	    <script id="editor" type="text/plain" style="width:100%;height:300px;"></script>
	</div>

		                           </td>
		                     </tr>
		                       <tr>
	                     	   <td>附件</td>
	                     	   <td>
	                                <button class="btn btn-sm btn-default" type="button" style="width:60px" onclick="emailTemp_uploadFile()">上传</button>
	                                <span id ="uef_tip" style="color:red"></span>
	                           </td>
	                     </tr>
                    </tbody>
               </table>

		</form>
</EasyTag:override>

<EasyTag:override name="script">

	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type ="text/javascript" src = "/neworder/static/js/common.js" ></script>
	<script type="text/javascript" src="/neworder/static/js/comm/commOrder.js"></script>
	<script type="text/javascript" src="/neworder/static/js/comm/commServiceTree.js"></script>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	<script src="/online/static/plugins/layui/layui.all.js" type="text/javascript"></script>
	<script src="/online/static/js/cus.verify.js" type="text/javascript"></script>
	<link rel="stylesheet" href="/neworder/static/css/element-ui.css">
	<script type="text/javascript" src="/neworder/static/js/vue.min.js"></script>
	<script src="/neworder/static/js/elementui/element-ui.js"></script>

	<script type="text/javascript">

		var Main = new Vue({
			el: '#zxServiceRequire',
			data() {
				return {
					props: { multiple: true ,emitPath:false},
					options: [],
					zxServiceRequire: ['${param.upgrade}']
				};
			},
			methods: {
				setZxServiceRequire(){
					ajax.remoteCall("/sentiment/servlet/comm?action=GetConsultationType2",null,result => {
						this.options = result.data;
					})

				}
			},
			created() {
				this.setZxServiceRequire()
			}

		})


    	jQuery.namespace("orderEmail");

		orderEmail.multiSetting = {
			buttonWidth: '100%',
			allSelectedText: "全部",
			nonSelectedText:"--请选择--",
			nSelectedText:"个被选中",
			selectAllNumber:false,
			maxHeight:350,
			includeSelectAllOption: true,
			selectAllText:'全选',
			enableFiltering: true
		};

       //orderEmail.sendData='${param.sendData}';
    	var josnData=sendData;
    	//josnData.TITLE=josnData.TITLE.replace(/`@`/g,"'");
    	//josnData.URL=josnData.URL.replace(/\></g,'\&');;
    	josnData.CONTENT=getOrderInfo().replace(/\n/g, "<br>");
    	josnData.URL="";

    	$("#TOPIC").val(josnData.TITLE);
    	orderEmail.orgCode=josnData.orgCode;
    	orderEmail.buyType=josnData.buyType;
    	orderEmail.prodName=josnData.prodName;
    	orderEmail.upgrade='${param.upgrade}';
    	orderEmail.branchCode=josnData.DEPARTMENT;
    	orderEmail.emailId=josnData.emailId;
    	var sentimeId=josnData.sentimeId;
    	var subOrderId=josnData.subOrderId;
   		var busiId ='';
    	$(function(){
    		//分中心渲染
			if ($("#orgCode").val() != "") {
				console.log("orgCode:",$("#orgCode").val());
				$("#branchCode1").data("mars", "comm.getBranchCode('" + $("#orgCode").val() + "')");
				$("#branchCode1").render({success : function(result){
					$('#branchCode1').multiselect(orderEmail.multiSetting);
					// var option1=$("#branchCode option:selected");
					// $("#branchCode1").data('select2', $("#branchCode").val());
				}});
			}
			showScreen(orderEmail.orgCode,orderEmail.buyType,orderEmail.branchCode);
    		getBusiId();
    		emailCount();
			//匹配发送邮件的人员号码  (旧)
			// getUserInfo();
			//匹配发送邮件的人员号码  (新)
			getUserInfoNew(orderEmail.upgrade,null);
    	});
		//匹配发送邮件的人员号码  (新)
		function getUserInfoNew(upgradeType1,branchCode1){
			var params = {}
			if (branchCode1 === null){
				 params = {
					upgradeType: upgradeType1,
					upgradeTypes: upgradeType1,
					orgCode: $("#orgCode").val(),
					branchCode: $("#branchCode").val(),
					areaCode: $("#areaCode").val(),
					prodCode: $("#prodCode").val(),
				};
			}else {
				 params = {
					upgradeType: upgradeType1,
					upgradeTypes: upgradeType1,
					orgCode: $("#orgCode").val(),
					branchCode: branchCode1,
					areaCode: $("#areaCode").val(),
					prodCode: $("#prodCode").val(),
				};
			}

			console.log("邮件参数params:",params);
			let req = {
				//","Contacts.getEmailNoticeList('3')
				controls:["Contacts.getEmailNoticeList('3')","Contacts.getEmailNoticeList('1')","Contacts.getEmailNoticeList('2')"],
				params:params
			}
			ajax.remoteCall("/sentiment/webcall",req,function(result){
				var noticeList = result["Contacts.getEmailNoticeList('3')"]; //收件人
				console.log("noticeList:",noticeList);
				var copyList = result["Contacts.getEmailNoticeList('1')"]; //抄送人
				console.log("copyList:",copyList);
				var mipList = result["Contacts.getEmailNoticeList('2')"]; //线上收件人
				console.log("mipList:",mipList);
				var downline= $('input[name="downline"]:checked').val();
				if(downline==1){
					$("#RECEIVER").val(noticeList.data);
					$("#COPY").val(copyList.data);
				}
				var online= $('input[name="online"]:checked').val();
				if(online==1){
					$("#mip").val(mipList.data);
				}
			});
		}

		//匹配发送邮件的人员号码  (旧)
        function getUserInfo(){
		$.ajax({
			url:"${ctxPath}/servlet/order?query=SendUserInfo",
			data:{"branchCode":orderEmail.branchCode,"prodName":orderEmail.prodName,"sendType":"邮件","orgCode":orderEmail.orgCode,"upgrade":orderEmail.upgrade},
			async:false,
			success:function(result) {
				var mg=result.data.sendName+"";
				var mn=result.data.ccName+"";
				var mip=result.data.mip+"";
				if('1'!=result.state){
					layer.alert(result.msg,{icon: 5});
					return false;
				}
				mg=Trim(mg,'g');
				mn1=Trim(mn,'g');
				mip=Trim(mip,'g');
				if(result.state == 1){
					 var downline= $('input[name="downline"]:checked').val();
					 if(downline==1){
						 $("#RECEIVER").val(mg.replace(/\[|]/g,''));
						 $("#COPY").val(mn1.replace(/\[|]/g,''));
					 }

					 var online= $('input[name="online"]:checked').val();
					 if(online==1){
						 $("#mip").val(mip.replace(/\[|]/g,''));
					 }

					//$("#email").val(email.replace(/\[|]/g,''));
					//orderEmail.EMAIL_TO=email.replace(/\[|]/g,'');
					//list.timeTd=result.msg;
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
	});
	}
	 function Trim(str,is_global)
	  {
	   var result;
	   result = str.replace(/(^\s+)|(\s+$)/g,"");
	   if(is_global.toLowerCase()=="g")
	   {
	    result = result.replace(/\s/g,"");
	    }
	   return result;
	}
	 function emailCount(){
		 $.ajax({
				url:"${ctxPath}/servlet/order?query=emailContent",
				data:{"id":orderEmail.emailId},
				async:false,
				success:function(result) {
					if(result.state == 1){
						/* if(josnData.TYPEEMAIL=='99'||'yes'==deal.logeType){
							orderInfo1();
						}else{
							orderInfo();
						} */
						var reData=result.data;
						if(josnData["TELEPHONE_NUM2"]){
							josnData["TELEPHONE_NUM"] += ','+josnData["TELEPHONE_NUM2"]
						}
						for(var i in josnData){
							 reData=reData.replace(i,josnData[i]);
						}
						//var newData=reData.replace(/CONTENT/g,countAjax);
						getComplaintContent();
						var newData=reData.replace("升级说明：","升级说明："+"<br>    "+getComplaintContent());
						 newData=newData.replace("升级说明:","升级说明:"+"<br>    "+getComplaintContent());
						var ue = UE.getEditor('editor');
						ue.ready(function() {//编辑器初始化完成再赋值
							ue.setContent(newData);  //赋值给UEditor
						});


					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});

	 	}

	 orderEmail.ajaxSubmitForm=function(online){
			 if($("#TOPIC").val()!=""){
				 orderEmail.insertData(online);
			 }else{
				 layer.alert("主题不能为空");
					return;
			 }
		}

	orderEmail.addEmail=function (){
		//获取升级类型
		if(Main.zxServiceRequire.length == 0){
			layer.alert("请选择升级类型");
			return;
		}
		var ids = Main.zxServiceRequire.join(",");
		console.log("ids:",ids)
		//获取分中心编码
		var codes = $("#branchCode1").val();
		if (codes !== null){
			codes = codes.join(",");
		}
		console.log("codes:",codes)
		getUserInfoNew(ids,codes)
	}

	 orderEmail.send=function(){
		 if($("#TOPIC").val()==""){
			 layer.alert("主题不能为空");
				return;
		 }
		 var online= $('input[name="online"]:checked').val();
		 var downline= $('input[name="downline"]:checked').val();
			if(online=='0'&&downline=='0'){
				layer.alert("线上和线下升级不能都为否");
				return;
			}
			var data={}
			if(online==1){
				orderEmail.online(data)
			}else if(downline==1){
				orderEmail.ajaxSubmitForm(null)
			}
			if($("#sentimentType").val()=='-1'){
				//SENTIMENT_FROM字典

				if($("#dataSource").val()!=""&&($("#TELEPHONE_NUM").val()!=""||$("#TELEPHONE_NUM2").val()!="")){
					var data={
							dataSource:$("#dataSource").val(),
							telephone1:$("#TELEPHONE_NUM").val(),
							telephone2:$("#TELEPHONE_NUM2").val(),
					}
					<%--ajax.remoteCall("${ctxPath}/servlet/comm?query=orderCustLabel",data,function(result) {--%>
					<%--	if(result.state == 1){--%>

					<%--	}else{--%>

					<%--	}--%>
					<%--})--%>
				}

			}
	 }
	 orderEmail.online=function(data){

		  data.online={}
		  data.FOLLOW_UP_ACCOUNT=emailTemp_getEmailMip('code')
		  data.FOLLOW_UP_NAME=emailTemp_getEmailMip('name');
		  data.FOLLOW_UP_EMAIL=emailTemp_getEmailMip('email');
		  data.COMPLAINT_UPGRADE_TYPE='2'//投诉升级类型 1投诉2舆情
		  data.COMPLAINT_SOURCE_NO=$("#ID").val();//投诉来源单号--
		  data.COMPLAINT_SUBJECT=$("#TOPIC").val();//主题
		  data.COMPLAINT_CONTENT='';// $("#COMPLAINT_CONTENT").val();
		  if( $("#COMPLAIN_POINT").val()!=''){
			  data.COMPLAINT_FOCUS=	 $("#COMPLAIN_POINT").find("option:selected").text();//投诉焦点  投诉焦点分析
		  }else{
			  data.COMPLAINT_FOCUS=	 "";
		  }
		  data.CALL_TIMES="";//来电次数
		  data.UPGRADE_TIMES="";//升级次数
		  data.COMPLAINT_TIME=$("#CREATE_TIME").val()//投诉时间
		  data.FANS_NUM=$("#FANS_COUNT").val();//粉丝数量
		  getComplaintSourceUrl();
		  data.COMPLAINT_SOURCE_URL=$("#COMPLAINT_SOURCE_URL").val()//投诉原文链接**
		  data.COMPLAINT_SOURCE_TEXT=''//$("#CONTENT").val()//投诉原文内容**
		  data.PUBLIC_OPINION_TEXT=''//$("#REPLENISH_CONTENT").val()//舆情私信内容**
		  data.CUSTOMER_NAME=$("#CUST_NAME").val();//用户姓名
		  data.CUSTOMER_TEL1=$("#TELEPHONE_NUM").val();//用户电话1
		  data.CUSTOMER_TEL2=$("#TELEPHONE_NUM2").val();//用户电话2
		  data.CUSTOMER_LEVEL="";//用户级别
		  data.CUSTOMER_AREA_CODE=$("#areaCode").val();//用户所在区域编码
		  data.CUSTOMER_AREA_NAME=$("#areaName").val();//用户所在区域名称
		  data.CUSTOMER_ADDRESS=$("#customerAddress").val();//用户地址
		  data.SERVICE_ORDER_NO="";//服务单号
		  data.PROD_CODE=$("#prodCode").val();//产品小类编码
		  data.PROD_NAME=$("#prodName").val();//产品小类名称
		  data.ORG_CODE=$("#orgCode").val();//主体
		  if($("#orgCode").val()!=""){
			  data.ORG_CODE=orderDetail.getOrgCode($("#orgCode").val())
		  }
		  data.FIRST_CALL_TIME=""//首次来电日期
		  data.LAST_CALL_TIME=""//最后一次来电日期
		  data.BRANCH_CODE=$("#branchCode").val()//分中心编码
		  data.BRANCH_NAME=$("#branchName").val()//分中心
		  data.UNIT_CODE=""//网点编码
		  data.UNIT_NAME=""//网点名称
		  data.UNIT_FEEDBACK_DESC="";//网点反馈说明
		  data.COUNTERFEIT_DESC=""//虚假封单说明
		  data.BUY_STORE_NAME=$("#BUY_STORE_NAME").val();//购买店铺名称
		  data.ELECTRICITY_ORDER_NO=$("#E_BUSINESS_NUM").val();//电商订单号
		  data.UNIFY_EXPLAIN_INFO="";	//统一口径
		  data.PROFESSIONAL_OPTION=""//专家坐席处理意见
		  data.COMPLAINT_ACCEPT_PERSON=$("#PROCESS_NAME").val();//投诉受理人
		  data.FOLLOW_UP_TIME=""//
			  popup.layerShow({
					type : 2,
					title : "升级确认",
					offset : '100px',
					area : [ '98%', '89%' ],offset:'20px',shade:0,maxmin: true
				}, "sentiment_on_line.jsp", data);
		}
	 orderEmail.insertData = function(online) {

		    var content= UE.getEditor('editor').getContent();
		    console.info(content)
		    var TOPIC=$("#TOPIC").val();
		    if(TOPIC.length>200){
		    	layer.alert('主题过长,无法发送',{icon:2,time:1000});
		 		return false;
		    }
		   	if(subOrderId==null||subOrderId==''){
		 		layer.alert('没有工单单号,无法发送',{icon:2,time:1000});
		 		return false;
		 	}
		   	if(content==null||content==''){
		 		layer.alert('发送内容为空',{icon:2,time:1000});
		 		return false;
		 	}

			var data = form.getJSONObject("editEmailForm");
		  	 var email1="";
		  	 var email2="";
		  	var reg = '';
		  	 var receiver=$("#RECEIVER").val().split(',');
		  	 var copy=$("#COPY").val().split(',');
		  	 if(''!=$("#RECEIVER").val()&&null!=$("#RECEIVER").val()){
		  		for(var i=0;i<receiver.length;i++){
			 		  var nstr = receiver[i].replace(/\([^\)]*\)/g,"");
				  	  //reg = /^([0-9A-Za-z\-_\.]+)@([0-9a-z]+\.[a-z]{2,3}(\.[a-z]{2})?)$/g;
				  		if(nstr.indexOf(".com")<0){
				  			if(nstr.indexOf(".co")>0){
				  				layer.msg("线下邮箱格式不正确", {icon : 2});
					              return false;
					  		}
				  		};
			 			 if (nstr.indexOf("@")<0) {
								layer.msg("线下邮箱格式不正确", {icon : 2});
				              return false;
				          }
			 			 email1+=nstr+';';
			 		 }
		  	 }

		  	if(''!=$("#COPY").val()&&null!=$("#COPY").val()){
		  		 for(var i=0;i<copy.length;i++){
			 		  var nstr = copy[i].replace(/\([^\)]*\)/g,"");
			 			 email2+=nstr+';';
			 			if(nstr.indexOf(".com")<0){
				  			if(nstr.indexOf(".co")>0){
				  				layer.msg("抄送邮箱格式不正确", {icon : 2});
					              return false;
					  		}
				  		};
			 			 if (nstr.indexOf("@")<0) {
								layer.msg("抄送邮箱格式不正确", {icon : 2});
				              return false;
				          }
			 		 }
		  	}
		  	var mip=$("#mip").val().split(',');
		  	if(''!=$("#mip").val()&&null!=$("#mip").val()){
		  		 for(var i=0;i<mip.length;i++){
			 		  var nstr = mip[i].replace(/\([^\)]*\)/g,"");
			 			if(nstr.indexOf(".com")<0){
				  			if(nstr.indexOf(".co")>0){
				  				layer.msg("线上邮箱格式不正确", {icon : 2});
					              return false;
					  		}
				  		};
			 			 if (nstr.indexOf("@")<0) {
								layer.msg("线上邮箱格式不正确", {icon : 2});
				              return false;
				          }
			 		 }
		  	}
		  	 var on= $('input[name="online"]:checked').val();
			 var down= $('input[name="downline"]:checked').val();
	 		data.ago1=email1.length>0?email1.substring(0,email1.length-1):"";
	 	    data.ago2=email2.length>0?email2.substring(0,email2.length-1):"";
		 	data.CONTENT=content;
		 	data.ORDER_ID=subOrderId;
		 	data.online=online;
		 	data.down=down;
		 	data.on=on;

		 	//添加电话号码1，电话号码2，数据来源字段
		 	data.TELEPHONE_NUM = sendData.TELEPHONE_NUM;
		 	data.TELEPHONE_NUM2 = sendData.TELEPHONE_NUM2;
		 	data.dataSource = sendData.dataSource;
			data.dataSourceText = sendData.dataSourceText;
			data.sentimentTypeText = sendData.sentimentTypeText;
		 	data.addition = sendData.addition;
		 	data.sentimentInfoId = sendData.sentimentInfoId;
			ajax.remoteCall("${ctxPath}/servlet/order?action=NewSendEmail",data,function(result) {
					if(result.state == 1){
						try {
							if(typeof orderDetail !== 'undefined' && typeof orderDetail.setData === 'function') {
								orderDetail.setData();
							}
							if(typeof toGvocSentiment === 'function') {
								toGvocSentiment();
							}
						} catch(e) {
							console.log('Some methods were not found:', e);
						}
				    	$("#busiId").val('');
						busiId=result.data;
						popup.layerClose();
						layer.alert(result.msg,{icon: 1},function(){
							layer.closeAll();
						});
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }
	 var countAjax='';
	 function orderInfo(){
			$.ajax({
				url:"${ctxPath}/servlet/order?query=getOrderPos1",
				data:{"id":sentimeId},
				async:false,
				success:function(result) {
					if(result.state == 1){
						var ret = result.data;
						for(var i=0;i<ret.length;i++){
							var ob=ret[i];
							countAjax=ob.CONTENT.replace(/(^\s*)/g,"");
							/* console.log(JSON.stringify(result)); */
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 function orderInfo1(){
			$.ajax({
				url:"${ctxPath}/servlet/order?query=getOrderPos2",
				data:{"id":subOrderId},
				async:false,
				success:function(result) {
					if(result.state == 1){
						countAjax = result.data;
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 function getOrderInfo(){
		 return $("#REPLENISH_CONTENT").val();
	 }
	 function getComplaintContent(){
		 return $("#COMPLAINT_CONTENT").val()
	 }
	 function emailTemp_uploadFile(){
	    	var busiType = "email";
	    	var requestType= "upload"
	    	popup.layerShow({
				type : 2,
				title : "文件管理",
				offset : '100px',
				area : [ '800px', '50%' ]
			}, "/attachment/servlet/attachment?action=attachment", {busiId:busiId,requestType:requestType,busiType:busiType});
	  }

	  function setBusiId(busiId){
	    	$("#busiId").val(busiId);
	    	$("#uef_tip").html("上传成功~")
	  }
	  function getBusiId(){
		  $.ajax({
				url:"${ctxPath}/servlet/order?query=GetOrderCode",
				data:{},
				async:false,
				success:function(result) {
					if(result.state == 1){
						//busiId=result.msg;
						busiId= result.msg;
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	  }
		function showScreen(val,val1,val2){
			$.ajax({
				url:"${ctxPath}/servlet/screen?query=seleInfo",
				data:{"PRODUCT_DIVISION":val,"BUY_CHANNEL":val1,BRANCHNAME:val2},
				async:false,
				success:function(result) {
					if(result.state == 1){
						if(''!=result.data){
							layer.alert(result.data,{icon:1});
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
		}

		   function emailTemp_getEmailMip(type){
			   var name = "";
			   var code = "";
			   var email = "";
			   var list = $("#mip").val().split(',');
			   if(list != "" && list.length > 0){
				   for(var k in list){
					   var value = list[k];
					   name += value.substring(value.indexOf("(")+1,value.indexOf("<")) + ";";
					   code+= value.substring(value.indexOf("<")+1,value.indexOf(">")) + ";";
					   email += value.substring(0,value.indexOf("(")) + ";";
				   }
			   }
			   if(type=="name"){
				   if(name != ""){
					   name = name.substring(0,name.length-1)
				   }
				   return name
			   } if(type=="email"){
				   if(email != ""){
					   email = email.substring(0,email.length-1)
				   }
				   return email
			   }else{
				   if(code != ""){
					   code = code.substring(0,code.length-1)
				   }
				   return code
			   }
		   }
		   function  getSelectText(id){
			   if($("#"+id).val()!=""){
				   return $("#"+id).find("option:selected").text();
			   }else{
				   return "";
			   }

		   }
		   function setOnline(online){
			   orderEmail.ajaxSubmitForm(online);
			  }
		   function getComplaintSourceUrl(){
			   if($("#ID").val()!=""){
					var data={};
			 		data.id=$("#ID").val();
			 		ajax.remoteCall("${ctxPath}/servlet/order?query=getOrderbySentimentId",data,function(result){
			 			   var url ="";
							if(result.state == 1){
								var ret = result.data;
			 					if(ret.length!=0){
			 						url=ret[0].URL;
			 					}
							}
							$("#COMPLAINT_SOURCE_URL").val(url);
			 			},{async:false});
			   }

		 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>