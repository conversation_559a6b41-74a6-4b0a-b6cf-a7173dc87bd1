<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情工单派发</title>
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
<link rel="stylesheet" type="text/css" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css">
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editDetailForm"  method="post"  autocomplete="off" data-mars-prefix="" >
<%--				<input type="hidden" name="order.ID" id="ID" value="${param.id}">--%>
				<input type="hidden" name="order.URL" id="URL">
				<input type="hidden" name="order.DISTRIBUTED_TIME">
				<input type="hidden" name="POSTING_TIME" id="POSTING_TIME">
				<input type="hidden" name="order.ACCOUNT_AREA" id="ACCOUNT_AREA">
				<input type="hidden" name="order.CREATE_TIME"  id="CREATE_TIME" >
				<input type="hidden" name="" data-mars="orderInf.orderRevisit">
					<div class="info-title">
			  		   <p>用户信息</p>
			      </div>
				  <table class="table table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px" class="required" >网络名</td>
			                        <td width="160px"><input type="text" data-rules="required"  maxlength="60" name="order.NICKNAME"   id="nickname" class="form-control input-sm" maxlength="5"></td>
			                        <td width="50px">用户姓名</td>
			                      	<td width="160px"><input type="text"  maxlength="10" name="order.CUST_NAME"  id="CUST_NAME" class="form-control input-sm"></td>

			                      	   <td >电话号码1</td>
			                        <td>
			                            <div class="input-group">
										     <input type="text" name="order.TELEPHONE_NUM"   id="TELEPHONE_NUM" class="form-control input-sm" onchange="rulesPhone(this)">
										     <span class="input-group-addon" style="cursor:pointer" onclick="tell(1)" ><i class="glyphicon glyphicon-earphone"></i></span>
										</div>
			                         </td>
			                        <td>电话号码2</td>
			                        <td>
			                            <div class="input-group">
										     <input type="text" name="order.TELEPHONE_NUM2"  id="TELEPHONE_NUM2" class="form-control input-sm" onchange="rulesPhone(this)">
										     <span class="input-group-addon" style="cursor:pointer" onclick="tell(2)" ><i class="glyphicon glyphicon-earphone"></i></span>
										</div>
			                        </td>
			                 </tr>
		                     <tr>
		                           	<td width="50px">电话区号</td>
			                      	<td width="160px">
			                      		<div class="input-group  new-input-group">
					                             <input type="number" name="order.PHONE_CODE" id="areaNum" class="form-control input-sm" v-model="areaNum" v-bind:disabled = "custFormDisable">
					                             <span class="input-group-addon" id="enterArea" onclick="phoneCode()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                          </div>

			                      	</td>
			                        <td>区域</td>
			                      	<td>
			                      		<input type="hidden" name="order.CUST_CODE" id="areaCode" v-model="areaCode" class="form-control input-sm" readonly="readonly">
			                      		<input type="text" name="order.CUST_AREA" id="areaName" v-model="areaName" v-bind:disabled = "custFormDisable" class="form-control input-sm">

			                      	</td>
			                      	   <td >产品主体</td>
			                      <td>
					                    <select  name="order.PRODUCT_DIVISION_CODE" id ="orgCode"   class="form-control input-sm"  data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" onchange="changeOrg(this.value)">
					                    			                                 <option value="">请选择</option>

					                     </select></td>
			                        <td >是否有客户资料</td>
			                        <td>
			                            <select  class="form-control input-sm"  data-cust-context-path="/yq_common" name="order.EXIST_CUST" data-cust-mars="dict.getDictList('PUSH_STATIS')">
			                                 <option value="">请选择</option>
			                            </select>
              						</td>
			                 </tr>
			                 <tr>

			                        <td>详细地址</td>
			                        <td colspan="5"><input type="text" name="order.CUST_ADDRESS"  maxlength="50"  id="customerAddress" class="form-control input-sm"></td>
								 <td>舆情工单号</td>
								 <td colspan="5"><input name="order.ID" id="ID" value="${param.id}" readonly="readonly" class="form-control input-sm"></td>
							 </tr>
		               </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>产品信息</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
                         <tbody>
			                 <tr>
			                        <td  width="50px" >产品品牌</td>
			                        <td  width="160px">
			                            <input type="hidden" name="order.BRAND_CODE"  id="brandCode"  >
					                      	  	<input type="text"  name="order.PRODUCT_BRAND" id ="brandName"   readonly="readonly" class="form-control input-sm">
					                      </td>
              						<td  width="50px" >产品品类</td>
			                        <td  width="160px">
			                            <div class="input-group input-group-sm new-input-group">
					                          	 <input type="hidden" name="order.PROD_CODE" id="prodCode"  >
					                             <input type="text"  name="order.PRODUCT_CATEGORY" id="prodName"  class="form-control input-sm" >
					                             <span class="input-group-addon" onclick="orderDetail.productType()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                          </div>
              						</td>
              					    <td  width="50px" >产品型号</td>
			                        <td  width="160px">
											<div class="input-group input-group-sm new-input-group">
					                          	 <input type="hidden" name="order.PRODUCT_CODE" id="productCode"  class="form-control input-sm">
					                             <input type="text"  maxlength="20"   name="order.PRODUCT_TYPE" id="productModel"  class="form-control input-sm">
					                             <span class="input-group-addon" onclick="orderDetail.queryProduct()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                         </div>              						</td>
					                           <td  width="50px">购买渠道</td>
			                       <td width="160px">
			                       		<select  class="form-control input-sm" id="BUY_CHANNEL" data-cust-context-path="/yq_common" name="order.BUY_CHANNEL" data-cust-mars="dict.getDictList('SENTIMENT_BUY_CHANNEL')" >
			                                 <option value="">请选择</option>
			                            </select>
			                         </td>
		                     </tr>
		                     <tr>

			                        <td>电商订单号</td>
			                        <td><input type="text"  maxlength="100" name="order.E_BUSINESS_NUM" id="E_BUSINESS_NUM" class="form-control input-sm"></td>
			                        <td>购买日期</td>
			                        <td><input type="text" name="order.BUY_DATE" id="BUY_DATE" class="form-control input-sm"  onclick="WdatePicker()"></td>
			               <td>购买店铺</td>
			                        <td colspan="5"><input type="text" name="order.BUY_STORE_NAME" id="BUY_STORE_NAME" class="form-control input-sm" maxlength="30"></td>
			                 </tr>
			           </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>舆情信息</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
			          <tbody>
		                     <tr>
		                            <td  width="50px" >舆情类别</td>
			                        <td  width="160px">
			                            <select  id="sentimentType" class="form-control input-sm"  data-cust-context-path="/yq_common" name="order.SENTIMENT_TYPE" data-cust-mars="dict.getDictList('SENTIMENT_TYPE_ORDER')">
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                        <td  width="50px" >数据来源</td>
			                        <td  width="160px">
<%--										<div class="input-group">--%>
											<select name="order.SENTIMENT_FROM" id="SENTIMENT_FROM" class="form-control"
													style="width:100%;">
											</select>
<%--										</div>--%>
<%--			                            <select id="dataSource"  class="form-control input-sm" data-cust-context-path="/yq_common" name="order.SENTIMENT_FROM" data-cust-mars="dict.getDictList('SENTIMENT_FROM')">--%>
<%--			                                 <option value="">请选择</option>--%>
<%--			                            </select>--%>
              						</td>
              						<td  width="50px">评论量</td>
			                        <td  width="160px"><input type="text" name="order.REPLY_COUNT" class="form-control input-sm" ></td>
			                 <td width="50px">粉丝量</td>
			                        <td width="160px"><input type="text" name="order.FANS_COUNT" id="FANS_COUNT" class="form-control input-sm" ></td>
			                 </tr>
			                 <tr>

			                        <td>阅读量</td>
			                        <td><input type="text" name="order.HITS_COUNT" class="form-control input-sm" ></td>
			                        <td>转载量</td>
			                        <td><input type="text" name="order.FORWARD_COUNT" class="form-control input-sm" ></td>
									<td >@关键用户或媒体</td>
			                        <td colspan="3"><input type="text"  name="order.CALL_MEDIA" class="form-control input-sm" maxlength="30"></td>
			                 </tr>
							 <tr>
								 <td>工单渠道来源</td>
								 <td  width="160px">
									 <select  class="form-control input-sm"  name="order.DATA_SOURCES" id="DATA_SOURCES" disabled >
										 <option value="">请选择</option>
										 <option value="01">系统爬取</option>
										 <option value="02">手工建单</option>
										 <option value="03">系统&手工</option>
									 </select>
								 </td>
							 </tr>
			          </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>投诉诉求</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
			          <tbody>
		                     <tr>
              						<td  width="50px" >负向级别</td>
			                        <td  width="160px">
			                             <select  class="form-control input-sm" id="NEGATIVE_LEVEL" data-cust-context-path="/yq_common" name="order.NEGATIVE_LEVEL" data-cust-mars="dict.getDictList('SENTIMENT_NEGATIVE_LEVEL')">
			                                 <option value="">请选择</option>
			                            </select>
              						</td>
			                        <td  width="50px" class="required">服务请求</td>
			                        <td  width="160px">
			                            <select name="SERVICE_TYPES" data-rules="required" id="SERVICE_TYPE" data-mars="order.serviceType" class="form-control input-sm" onchange="changRequest(this.value)">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                        <td  width="50px" class="required">投诉原因</td>
			                        <td  width="160px">
			                            <select name="COMPLAIN_REASONS" id="COMPLAIN_REASON"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm" onchange="changReason(this.value)">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              						<td class="required" width="50px">投诉类别</td>
			                        <td width="160px">
			                            <select name="COMPLAIN_TYPES" id="COMPLAIN_TYPE"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm" onchange="changComplain(this.value)">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              			     </tr>
		                     <tr>

			                        <td class="required">投诉焦点分析</td>
			                        <td>
			                            <select name="COMPLAIN_POINTS" id="COMPLAIN_POINT"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm" >
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     <td>投诉主题</td>
			                        <td  colspan="5"><input type="text" name="order.COMPLIAN_TOPIC" id="TITLE" maxlength="100" class="form-control input-sm" maxlength="200"></td>
		                     </tr>
		                     <tr>
			                         <td >升级说明</td>
				                     <td colspan="8">
				                        <textarea rows="3"   class="form-control input-sm" name="order.COMPLAINT_CONTENT" maxlength="4000" id="COMPLAINT_CONTENT"></textarea>
				                     </td>
		                     </tr>
		                     <tr>
			                         <td >投诉原文</td>
				                     <td colspan="8">
				                        <textarea rows="8"   class="form-control input-sm" name="order.CONTENT" maxlength="4000" id="CONTENT"></textarea>
				                     </td>
		                     </tr>
		                     <tr>
			                         <td >补充</td>
				                     <td colspan="8">
				                        <textarea rows="5"   class="form-control input-sm" name="" maxlength="4000" id="REPLENISH_CONTENT"></textarea>
				                     </td>
		                     </tr>
		              </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>舆情处理信息</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
			          <tbody>
			                <tr>
		                            <td  width="50px">工单状态</td>
			                        <td  width="160px">
			                            <select  class="form-control input-sm" disabled="disabled" data-cust-context-path="/yq_common" name="order.STATUS" data-cust-mars="dict.getDictList('SENTIMENT_ORDER_STATUS')">
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                        <td  width="50px">受理操作员</td>
			                        <td  width="160px">
			                        <input type="text" name="order.CREATE_NAME" readonly="readonly" id="CREATE_NAME"  maxlength="10" class="form-control input-sm" ></td>
			                        <td  width="50px">处理操作员</td>
			                        <td  width="160px">
			                        <input type="text" name="order.PROCESS_NAME"  readonly="readonly" id="PROCESS_NAME"  maxlength="10" class="form-control input-sm" ></td>
		                   			  <td  width="50px">回帖时间</td>
			                        <td width="160px"><input type="text"  name="order.REPLAY_TIME" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
		                   			  </tr>
		                     <tr>

			                        <td >分中心</td>
			                        <td> <select  class="form-control input-sm" name="DEPARTMENTS" id = "branchCode" data-mars="comm.getBranchCode">
			                                 <option value="">请选择</option>
                                      </select></td>

			                        <td >是否升级</td>
			                        <td>
			                           <select   class="form-control input-sm" data-cust-context-path="/yq_common" name="order.NEED_UPGRADE" id="NEED_UPGRADE" data-cust-mars="dict.getDictList('RED_BLOCK_IS_CHECK')">
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
		                      <td>邮件模板</td>
			                        <td>
			                            <select name="order.email" data-mars="order.emailModel" id="email" class="form-control input-sm" onchange="reDoing()">
					               		   <option value="">请选择</option>
              						    </select>
			                        </td>
			                        <td>短信模板</td>
			                        <td>
			                            <select name="order.sms" data-mars="order.smsModel" id="sms" class="form-control input-sm" onchange="reDoing1()">
					               		   <option value="">请选择</option>
              						    </select>
			                        </td>

		                     </tr>
		                          <tr>
		                     <td>升级类型分类</td>
			                        <td  width="160px">
			                        <select   name="order.upgradeType1" data-mars="order.upgradeType1" id="upgradeType1"  class="form-control input-sm" onchange="upgradeTypeSe(this.value)" >
              						   			                                 <option value="">请选择</option>

              						    </select>
              						    </td>
              					 <td>升级类型</td>
			                        <td  width="160px">
			                         <select name="order.upgradeType2" data-mars="order.upgradeType2" id="upgradeType2" class="form-control input-sm" onchange="upgradeTypeSe1()">
              								                                 <option value="">请选择</option>

              					</select>
              					</td>
              					<td width="160px">是否线上升级</td>
			                    <td >
					                <label class="radio-inline">
					                      <input type="radio" value="1" name="online" <%=  request.getParameter("online") == null || "1".equals( request.getParameter("online")) ? "checked" : "" %>> 是
									</label>
									<label class="checkbox-inline">
										<input type="radio" value="0" name="online" <%=  request.getParameter("online") != null &&  request.getParameter("online").equals("0") ? "checked" : "" %>> 否
									</label>
			                    </td>
			                    <td width="160px">是否线下升级</td>
			                    <td >
			                        <label class="radio-inline">
					                      <input type="radio" value="1" name="downline" <%=  request.getParameter("downline") != null &&  request.getParameter("downline").equals("1") ? "checked" : "" %>> 是
									</label>
									<label class="checkbox-inline">
										<input type="radio" value="0" name="downline" <%=  request.getParameter("downline") == null || "0".equals( request.getParameter("downline")) ? "checked" : "" %> > 否
									</label>
							    </td>
		                     </tr>
			                 <tr>
								 <div class="uploadGvoc" >
			                     <td>是否上报舆情</td>
			                     <td >
			                        <label class="radio-inline">
					                      <input type="radio" value="Y" name="isUpload" > 是
									</label> 
									<label class="checkbox-inline"> 
										<input type="radio" value="N" name="isUpload" checked > 否
									</label>	                  
							     </td>
								 </div>
								 <td  width="50px">投诉升级单号</td>
								 <td  width="160px">
									 <input type="text" name="order.COMPLAINT_UPGRADE_NO"  readonly="readonly" id="COMPLAINT_UPGRADE_NO"  maxlength="20" class="form-control input-sm"  oninput="orderDetail.checkComplaintNumber()">
								 </td>
								 <td class="processing-status">运中处理状态</td>
								 <td class="processing-status">
									 <select id="PROCESSING_STATUS" class="form-control input-sm" disabled
											 name="order.PROCESSING_STATUS" data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('COMPLAINT_UPGRADE_STATUS')">
										 <option value="">请选择</option>
									 </select>
								 </td>
			                   </tr>
							<tr hidden="hidden">
								<td  width="50px"  class="processing-opinion">运中处理意见</td>
								<td colspan="6">
                                    <textarea readonly="readonly" id="PROCEESING_OPINION" class="form-control input-sm" name="order.PROCEESING_OPINION" rows="3"
											  maxlength="1000" onchange="this.value=this.value.substring(0, 1000)"
											  onkeydown="this.value=this.value.substring(0, 1000)"
											  onkeyup="this.value=this.value.substring(0, 1000)">
                                    </textarea>
								</td>
							</tr>
		                     <tr>
		                     <td>互动概要</td>
                                <td colspan="6">
                                    <textarea id="communication" class="form-control input-sm" name="order.COMMUNICATION" rows="3"
                                              maxlength="1000" onchange="this.value=this.value.substring(0, 1000)"
                                              onkeydown="this.value=this.value.substring(0, 1000)"
                                              onkeyup="this.value=this.value.substring(0, 1000)">
                                    </textarea>

                                </td>
                                <td>
                                    <button class="btn btn-sm btn-success mr-50" type="button"
                                            onclick="orderDetail.dialogList()">话术查询
                                    </button>
                                </td>
		                     </tr>
	                    </tbody>
	                  </table>
				      <p class="text-c mt-40" style="position: relative">
					   		<button class="btn btn-sm btn-success ml-20" id="staging"  type="button" onclick="orderDetail.ajaxSubmitForm('01')">工单暂存</button>
					   		<button class="btn btn-sm btn-primary"  id="sendEmail" type="button" onclick="$('#Email').click()">邮件升级</button>
					   		<button class="btn btn-sm btn-primary ml-20" id="sendMsg"  type="button" onclick="$('#Message').click()">短信报送</button>
					   		<button class="btn btn-sm btn-primary  ml-20"  type="button" onclick="$('#Telphone').click()">电话外拨</button>
					   		<button class="btn btn-sm btn-success ml-20"  type="button" onclick="orderDetail.distribute()">售后工单</button>
<!-- 					   		<button class="btn btn-sm btn-success ml-20"  type="button" onclick="closeAll()">关闭</button>
 -->					   		<button class="btn btn-sm btn-success ml-20"  id="addOrder" type="button"  style="display:none;" onclick="orderDetail.ajaxSubmitForm('01')">追加舆情</button>
						  <button style="position: absolute;right: 0" class="btn btn-sm btn-danger ml-20" id="save" type="button" onclick="orderDetail.ajaxSubmitForm('05')" style="float: right;">工单存档</button>

					  </p>
		</form>
</EasyTag:override>

<EasyTag:override name="script">
		<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	<script type="text/javascript">
		var _orderInfo;

	   jQuery.namespace("orderDetail");
	   requreLib.setplugs("wdate");
	   orderDetail.orderInfos;
	   orderDetail.type='${param.type}';
	   orderDetail.orderId='${param.orderId}';
	   orderDetail.orderCode='${param.id}';
	   orderDetail.orderCodeOver='';
	   orderDetail.distribute = function(){
		   layer.confirm('确认派发售后工单?',

		            {
		              btn: ['确定','取消'] //按钮

		            },

		            function(index){
		            	orderDetail.distributes();
		              return true;
		            },
		            function(index){
		                layer.msg('已取消！', {icon: 1});
		                return false;
		            }
		        );
	   }
	   orderDetail.distributes = function(){

		   if(orderDetail.orderCode==null||orderDetail.orderCode==''){
		 		layer.alert('请先保存工单',{icon:2,time:1000});
		 		return false;
		 	}

		   orderDetail.orderCodeOver=orderDetail.orderCode;
			var param ={};
		    var contactOrderVO ={};
		   	var contactCallRecordVO ={};
		 	var contactUserRequireVOListTd ={};
			var contactUserRequireVOList={};
			var myArray=new Array();
			var contactProductInfoVO={};
	 		var orgCode=orderDetail.getOrgCode($("#orgCode").val());

			param.updateOrSubmit='submit';

			contactOrderVO.customerLevel='10';
			contactOrderVO.customerType='10';
			contactOrderVO.otherModuleName='04';
			contactOrderVO.otherModuleId=orderDetail.orderCode;
		 	contactOrderVO.contactTypeCode='20';//接入方式
			contactOrderVO.areaNum=$("#areaNum").val();
			contactOrderVO.areaCode=$("#areaCode").val();
			contactOrderVO.areaName=$("#areaName").val();
			contactOrderVO.customerName=$("#CUST_NAME").val();
			contactOrderVO.serviceCustomerName=$("#CUST_NAME").val();
			contactOrderVO.customerMobilephone1=$("#TELEPHONE_NUM").val();
			contactOrderVO.customerMobilephone2=$("#TELEPHONE_NUM2").val();
			contactOrderVO.serviceCustomerMobilephone1=$("#TELEPHONE_NUM").val();
			contactOrderVO.serviceCustomerMobilephone2=$("#TELEPHONE_NUM2").val();
			contactOrderVO.customerPhone=$("#TELEPHONE_NUM").val();
			contactOrderVO.customerAddress=$("#customerAddress").val();
			contactOrderVO.orgCode=orgCode;
			contactOrderVO.isHurry=false;
			contactOrderVO.manualUpgradeFlag='N';
			param.contactOrderVO=contactOrderVO;
			param.contactCallRecordVO=contactCallRecordVO;

			contactUserRequireVOListTd.contactOrderBuyDate=$("#BUY_DATE").val();
			contactUserRequireVOListTd.orgCode=orgCode;
			contactUserRequireVOListTd.brandCode=$("#brandCode").val();
			contactUserRequireVOListTd.brandName=$("#brandName").val();
			contactUserRequireVOListTd.prodCode=$("#prodCode").val();
			contactUserRequireVOListTd.prodName=$("#prodName").val();
			contactUserRequireVOListTd.productCode=$("#productCode").val();
			contactUserRequireVOListTd.productModel=$("#productModel").val();
			contactUserRequireVOListTd.productAmount='1';
			contactUserRequireVOListTd.contactOrderProductUse='10';
			contactUserRequireVOListTd.contactOrderBuyChannel='10';
			contactUserRequireVOListTd.disposeType='11';//处理方式
			contactUserRequireVOListTd.urgentLevel=$("#NEGATIVE_LEVEL").val();	;//紧急程度
			contactUserRequireVOListTd.complaintLevel='13';
			contactUserRequireVOListTd.branchCode=$("#branchCode").val();
			contactUserRequireVOListTd.branchName=$("#branchName").val();
			var TITLE=$("#TITLE").val();
 		 	var complaints=$("#CONTENT").val().split("\n");
 		 	var url=complaints[3].replace("原文链接:","");
 		 	var complaintsCon=complaints[4].replace("原文内容:","");
 		 	contactUserRequireVOListTd.contactOrderServiceDescribe=TITLE+url+complaintsCon;

			var val=$("#SERVICE_TYPE").val();
		 	var val1=$("#COMPLAIN_REASON").val();
		 	var val2=$("#COMPLAIN_TYPE").val();
		 	var val3=$("#COMPLAIN_POINT").val();
		 	var val4=$("#SENTIMENT_FROM").val();
		 	bindingInfo(val,val1,val2,val3,val4);
		 	if(''!=bindingData){
			 	var bindingJson=JSON.parse(bindingData);
			 	contactUserRequireVOListTd.contactOrderSerItem2Name=bindingJson.contactOrderSerItem2Name;
			 	contactUserRequireVOListTd.contactOrderSerItem2Code=bindingJson.contactOrderSerItem2Code;
			 	contactUserRequireVOListTd.contactOrderSerItem1Name=bindingJson.contactOrderSerItem1Name;
			 	contactUserRequireVOListTd.contactOrderSerItem1Code=bindingJson.contactOrderSerItem1Code;
			 	contactUserRequireVOListTd.contactOrderServTypeCode=bindingJson.contactOrderServTypeCode;//一级code
			 	contactUserRequireVOListTd.contactOrderServTypeName=bindingJson.contactOrderServTypeName;//一级name
			 	contactUserRequireVOListTd.serviceMainTypeCode=bindingJson.serviceMainTypeCode;
			 	contactUserRequireVOListTd.serviceMainTypeName=bindingJson.serviceMainTypeName;
			 	contactUserRequireVOListTd.serviceSubTypeCode=bindingJson.serviceSubTypeCode;
			 	contactUserRequireVOListTd.serviceSubTypeName=bindingJson.serviceSubTypeName;
				contactUserRequireVOListTd.contactOrderServiceDesc=TITLE+url+complaintsCon;

		 	}else{
		 		alertChange();
		 		return false
		 	}
			contactProductInfoVO.areaCode=$("#areaCode").val();
			contactProductInfoVO.areaName=$("#areaNum").val();
			contactProductInfoVO.brandCode=$("#brandCode").val();
			contactProductInfoVO.brandName=$("#brandName").val();
			contactProductInfoVO.contactOrderProductUse='10';
			contactProductInfoVO.orgCode=orgCode;
			contactProductInfoVO.prodCode=$("#prodCode").val();
			contactProductInfoVO.prodName=$("#prodName").val();
			contactProductInfoVO.productAmount='1';
			contactProductInfoVO.machineAddress='广东广州';
			contactProductInfoVO.productCode=$("#productCode").val();
			contactProductInfoVO.productModel=$("#productModel").val();
		 	contactProductInfoVO.contactOrderBuyChannel=$("#BUY_CHANNEL").val();

 		 	contactUserRequireVOListTd.contactProductInfoVO=contactProductInfoVO;
			myArray.push(contactUserRequireVOListTd);
			param.ecmOrderVO ={};
			param.contactUserRequireVOList=myArray;
			console.info(param);
			//popup.layerShow({type:2,title:'派发工单',area:['860px','700px'],offset:'1px'},"/neworder/pages/access/order-new.jsp?type=handle",{data:data});
			ajax.remoteCall("/neworder/servlet/contact?action=submitOrder",param,function(result) {
		 		  if(result.state == 1){
						layer.msg(result.msg,{icon: 1},function(){
							//popup.closeTab();
						});
						sentimentOrder();
				  }else{
						layer.alert(result.msg,{icon: 5});
						//orderpopup.btnDisable(true);
				  }
	 		});
	   }

	/*    $(function(){
		   if(orderDetail.type==1){
			   $("#addBtn").show();
		   }
	   }) */
	   $(function(){
		   orderDetail.multiSetting2 = {
			   multiple: false,
			   buttonWidth: '220px',
			   allSelectedText: "全部",
			   nonSelectedText: "--请选择--",
			   nSelectedText: "个被选中",
			   selectAllNumber: false,
			   maxHeight: 350,
			   includeSelectAllOption: false,
			   selectAllText: '全选',
			   enableFiltering: true,
			   single: true

		   };
		$('#areaNum').bind('keyup',function(event){
		        if(event.keyCode == "13")
		        {
		        	$('#enterArea').click();

		        }
		   });
		   $("#editDetailForm").render({success : function(result){
				   var data = {"params":{},"controls":["dataInfo.sentimentChannelList"]}
				   ajax.remoteCall("${ctxPath}/webcall",data,function(result2) {
					   var dataInfo= result2['dataInfo.sentimentChannelList']['data'];
					   var selectElement = $('#SENTIMENT_FROM');
					   var option = $("<option>").val('').text("--请选择--");
					   selectElement.append(option);
					   // 遍历 Map 对象并生成选项
					   dataInfo.forEach(function(item) {
						   var option = $('<option></option>')
								   .attr('value', item.value)
								   .text(item.text);
						   selectElement.append(option);
					   });
					   selectElement.multiselect(orderDetail.multiSetting2);
				   });
			 $("#editDetailForm").data("mars","order.orderType");
			 $("#editDetailForm").render({success : function(result){
				 if('99'== orderDetail.type){
					   $("#addOrder").show();
					  	orderInfo();

				   };
					/*if('88'!= orderDetail.type){
					  	orderInfo();
				   }; */
				   if('99'==deal.insert){
					   orderDetail.orderCode='';
					   getNewOrderId();
				   };
				  //orderInfo();
				var dataInfo = result["order.orderType"]['data'];
				var PRODUCT_DIVISION = dataInfo['order.PRODUCT_DIVISION'];
				var SERVICE_TYPE = dataInfo['order.SERVICE_TYPE'];
				var COMPLAIN_REASON = dataInfo['order.COMPLAIN_REASON'];
				var COMPLAIN_TYPE = dataInfo['order.COMPLAIN_TYPE'];
				var COMPLAIN_POINT = dataInfo['order.COMPLAIN_POINT'];
				var DEPARTMENT = dataInfo['order.DEPARTMENT'];
				var PROCESSING_STATUS = dataInfo['order.PROCESSING_STATUS'];
				var PROCEESING_OPINION = dataInfo['order.PROCEESING_OPINION'];
				var COMPLAINT_UPGRADE_NO = dataInfo['order.COMPLAINT_UPGRADE_NO'];
				var NEED_UPGRADE = dataInfo['order.NEED_UPGRADE'];
				var STATUS = dataInfo['order.STATUS'];
				var SENTIMENT_FROM = dataInfo['order.SENTIMENT_FROM'];

				var email = dataInfo['order.EMAIL'];//
				var sms = dataInfo['order.SMS'];
				var upgradeType1 = dataInfo['order.UPGRADETYPE1'];
				var upgradeType2 = dataInfo['order.UPGRADETYPE2'];
				orderDetail.checkComplaintNumber();
				console.log("SENTIMENT_FROM:"+SENTIMENT_FROM)
				$("#SENTIMENT_FROM").val(SENTIMENT_FROM)
				 $("#SENTIMENT_FROM").multiselect("destroy").multiselect({
					 // 自定义参数，按自己需求定义
					 multiple: false,
					 buttonWidth: '110px',
					 allSelectedText: "全部",
					 nonSelectedText: "--请选择--",
					 nSelectedText: "个被选中",
					 selectAllNumber: false,
					 maxHeight: 350,
					 includeSelectAllOption: false,
					 selectAllText: '全选',
					 enableFiltering: true,
					 single: true
				 });
				$('#email option').each(function(index) {
					var val = $(this).val();
					if (email == val) {
						$(this).prop('selected', 'selected');
					}
				})
				$('#sms option').each(function(index) {
					var val = $(this).val();
					if (sms == val) {
						$(this).prop('selected', 'selected');
					}
				})
				$('#upgradeType1 option').each(function(index) {
					var val = $(this).val();
					if (upgradeType1 == val) {
						$(this).prop('selected', 'selected');
					}
				})
				upgradeTypeSe(upgradeType1,upgradeType2);


				if('02'==NEED_UPGRADE){
					$("#sendEmail").attr("disabled",true);
					$("#sendMsg").attr("disabled",true);
				}
				if ('05'==STATUS){
					$("#sendEmail").attr("disabled",true);
				}
				$('#orgCode option').each(function(index) {
					var text = $(this).text();
					if (text == PRODUCT_DIVISION) {
						$(this).prop('selected', 'selected');
					}
				})
				var isupload='N';
				if(dataInfo['order.SENTIMENT_TYPE']!=null&&dataInfo['order.SENTIMENT_TYPE']=="-1"){
					isupload="Y";
				}else{
					$(".uploadGvoc").addClass("hide");
				}
				if(dataInfo['order.IS_UPLOAD']!=null&&dataInfo['order.IS_UPLOAD']!=""){
					isupload=dataInfo['order.IS_UPLOAD'];
				}
				$('input[name="isUpload"][value="'+isupload+'"]').prop('checked', true);
				$('#SERVICE_TYPE option').each(function(index) {
					var text = $(this).text();
					var val=$(this).val();
					if (text == SERVICE_TYPE) {
						$(this).prop('selected', 'selected');
						$("#COMPLAIN_REASON").render({data:{id:val},success : function(result){
							$('#COMPLAIN_REASON option').each(function(index) {
								var text = $(this).text();
								var val=$(this).val();
								if (text == COMPLAIN_REASON) {
									$(this).prop('selected', 'selected');
									$("#COMPLAIN_TYPE").render({data:{id:val},success : function(result){
										$('#COMPLAIN_TYPE option').each(function(index) {
											var text = $(this).text();
											var val=$(this).val();
											if (text == COMPLAIN_TYPE) {
												$(this).prop('selected', 'selected');
												$("#COMPLAIN_POINT").render({data:{id:val},success : function(result){
													$('#COMPLAIN_POINT option').each(function(index) {
														var text = $(this).text();
														if (text == COMPLAIN_POINT) {
															$(this).prop('selected', 'selected');
															var obj=$("#orgCode").val();
															$("#branchCode").render({data:{"orgCode":obj},success : function(result){
																$('#branchCode option').each(function(index) {
																	var text = $(this).text();
																	if (text == DEPARTMENT) {
																		$(this).prop('selected', 'selected');
																	}
																});
															}});
														}
													});
												}});

											}
										});
									}});

								}
							});
						}});
					}
				});

					 // 若存在GPT提取的购买渠道，进行替换
					 var BUY_CHANNEL = dataInfo['order.BUY_CHANNEL'];
					 if (BUY_CHANNEL) {
						 $('#BUY_CHANNEL option').each(function () {
							 if ($(this).text() === BUY_CHANNEL) {
								 $(this).prop('selected', 'selected');
							 }
						 });
					 }
					 orderDetail.setData();
			 }});
		}
		   });


	/*  $("#sendEmail").attr("disabled",true);
	$("#sendMsg").attr("disabled",true);  */

});
		orderDetail.checkComplaintNumber = function() {
			const complaintNumber = $('#COMPLAINT_UPGRADE_NO').val().trim();
			const processingStatusElements = $('.processing-status');

			if (complaintNumber === '') {
				processingStatusElements.hide();
			} else {
				processingStatusElements.show();
			}
		}
	   orderDetail.productType = function(){
		   	popup.layerShow({type:2,title:'产品品类',shadeClose:false,area:['860px','700px'],offset:'20px'},"/neworder/pages/access/product-type.jsp?orgCode=orgCode&callback=2&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName");
		   	//$('#orgCode').click();
	   }
	 	orderDetail.queryProduct = function(){
	   		var brandCode= $("#brandCode").val();
	   		var orgCode= $("#orgCode").val();
	   		var prodCode= $("#prodCode").val();
	   		var productModel= $("#productModel").val();
	   		if(orgCode==null || orgCode==''){
	   			layer.alert("请先选择产品主体！");
	   			return false;
	   		}
	   		var param = encodeURI("brandCode="+brandCode+"&orgCode="+orgCode+"&prodCode="+prodCode+"&productModel="+productModel+"&productModelId=productModel&productCodeId=productCode");
		   	popup.layerShow({type:2,title:'产品型号查询',shadeClose:false,area:['860px','650px'],offset:'20px'},"/neworder/pages/access/product-list.jsp?"+param);
	   	}
	 	function changRequest(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_REASON").render({data:{id:val}});
	 	}
	 	function changReason(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_TYPE").render({data:{id:val}});
	 	}
	 	function changComplain(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_POINT").render({data:{id:val}});
	 	}

	 	function changeOrg(obj){
	 		var obj1=$("#areaCode").val();
	    	$("#branchCode").render({data:{"orgCode":obj},success:function(result){
	    		changeBranch(obj,obj1);
	    	}});
	 	}

	 	orderDetail.ajaxSubmitForm = function(val){
	 		if($("#nickname").val()==""){
	 			layer.msg("网络名不能为空", {icon : 2});
	              return false;
	 		}
	 		if(val=='05'){
				if (!form.validate("editDetailForm")){
					return false;
				}
	 		}
	 		if(val=='01'){
			   	var content=$("#CONTENT").val().length;

				if (5000<content){
					layer.msg("投诉原文过长,无法提交", {icon : 2});
					return false;
				}
	 		}
	 		var ago=$("#TELEPHONE_NUM").val();
	 		var ago1=$("#TELEPHONE_NUM2").val();
	 		 var myreg=/^[1][0-9]{10}$/;
	 		 var myreg1=/^0\d{2,3}-?\d{7,8}$/;
	 		  if(ago!=''&&ago!=null){
	        	  if (!myreg.test(ago)&&!myreg1.test(ago)) {
						layer.msg("号码1格式不正确", {icon : 2});
		              return false;
		          }
	          }
	          if(ago1!=''&&ago1!=null){
	        	  if (!myreg.test(ago1)&&!myreg1.test(ago1)) {
						layer.msg("号码2格式不正确", {icon : 2});
		              return false;
		          }
	          }

	          orderDetail.insertData (val);
		}
		orderDetail.setData = function(){
			var data = form.getJSONObject("editDetailForm");
			var option=$("#orgCode option:selected");
			var PRODUCT_DIVISION=option.text();
			if('请选择'==PRODUCT_DIVISION){
				PRODUCT_DIVISION='';

			}
			var IP_LOCATION='';
			var LIKE_COUNT='';
			var RELEASE_ACCOUNT='';
			var NEGATIVE_WORDS='';
			var url='';
			var complaintsCon='';
			var complaints=$("#CONTENT").val().split("\n");
			var SENTIMENT_FROM_NAME =$("#SENTIMENT_FROM option:selected").text();
			if(complaints!=null&&complaints.length>0){
				for(var i=0;i<complaints.length;i++){
					if(complaints[i].indexOf("IP属地：")!=-1 ){
						const str = complaints[i];
						const match = str.match(/IP属地[:：]\s*(.+)/);
						IP_LOCATION = match ? match[1] : '';
					}
					if(complaints[i].indexOf("点赞量：")!=-1){
						const str = complaints[i];
						const match = str.match(/点赞量[:：]\s*(\d+)/);
						LIKE_COUNT = match ? match[1] : '';
					}
					if(complaints[i].indexOf(SENTIMENT_FROM_NAME+"号：")!=-1){
						var name = SENTIMENT_FROM_NAME+"号";
						const str = complaints[i];
						const match = str.match(new RegExp(`${name}[:：]\\s*(\\d+)`));
						RELEASE_ACCOUNT = match ? match[1] : '';
					}
					if(complaints[i].indexOf("负面词:")!=-1){
						const str = complaints[i];
						const match = str.match(/负面词[:：]\s*(.+)/);
						NEGATIVE_WORDS = match ? match[1] : '';
					}
					if (complaints[i].indexOf("原文链接:")!=-1){
						const str = complaints[i];
						const match = str.match(/原文链接[:：]\s*(.+)/);
						url = match ? match[1] : '';
					}
					if (complaints[i].indexOf("原文内容:")!=-1){
						const str = complaints[i];
						const match = str.match(/原文内容[:：]\s*(\d+)/);
						complaintsCon = match ? match[1] : '';
					}
				}
				data.url=url;
				data.complaintsCon=complaintsCon;
			}

			var option1=$("#branchCode option:selected");
			var DEPARTMENT=option1.text();

			var option2=$("#SERVICE_TYPE option:selected");
			var SERVICE_TYPE=option2.text();
			var option3=$("#COMPLAIN_REASON option:selected");
			var COMPLAIN_REASON=option3.text();
			var option4=$("#COMPLAIN_TYPE option:selected");
			var COMPLAIN_TYPE=option4.text();
			var option5=$("#COMPLAIN_POINT option:selected");
			var COMPLAIN_POINT=option5.text();
			var REPLENISH_CONTENT=$("#REPLENISH_CONTENT").val();
			var BRANCH_NAME = $("#branchCode option:selected").text();

			var SERVICE_TYPE_NAME = $("#SERVICE_TYPE option:selected").text();
			var UPGRADETYPE1_NAME = $("#upgradeType1 option:selected").text();
			var UPGRADETYPE2_NAME = $("#upgradeType2 option:selected").text();

			data.PRODUCT_DIVISION=checkParams(PRODUCT_DIVISION);
			data.DEPARTMENT=checkParams(DEPARTMENT);
			data.SERVICE_TYPE=checkParams(SERVICE_TYPE);
			data.COMPLAIN_REASON=checkParams(COMPLAIN_REASON);
			data.COMPLAIN_TYPE=checkParams(COMPLAIN_TYPE);
			data.COMPLAIN_POINT=checkParams(COMPLAIN_POINT);
			data.REPLENISH_CONTENT = checkParams(REPLENISH_CONTENT);
			data.BRANCH_NAME = checkParams(BRANCH_NAME);
			data.SENTIMENT_FROM_NAME = checkParams(SENTIMENT_FROM_NAME);
			data.SERVICE_TYPE_NAME = checkParams(SERVICE_TYPE_NAME);
			data.UPGRADETYPE1_NAME = checkParams(UPGRADETYPE1_NAME);
			data.UPGRADETYPE2_NAME = checkParams(UPGRADETYPE2_NAME);
			data.IP_LOCATION=checkParams(IP_LOCATION);
			data.LIKE_COUNT=checkParams(LIKE_COUNT);
			data.RELEASE_ACCOUNT=checkParams(RELEASE_ACCOUNT);
			data.NEGATIVE_WORDS=checkParams(NEGATIVE_WORDS);
			data.orderId=orderDetail.orderCode;
			data.SENTIMENT_ID=orderDetail.orderId;
			data.insert=deal.insert;
			$("#PROCESS_NAME").val();
			var isupload=$('input[name="isUpload"]:checked').val();
			data.isupload=isupload;
			orderDetail.orderInfos = data;
		}
		function checkParams(val){
			if(val=='请选择'){
				return '';
			}
			return val;
		}
	 	orderDetail.insertData = function(val) {
	 		/* if (!form.validate("editDetailForm")){
				return false;
			} */
			var data = form.getJSONObject("editDetailForm");
			var option=$("#orgCode option:selected");
			var PRODUCT_DIVISION=option.text();
			if('请选择'==PRODUCT_DIVISION){
				PRODUCT_DIVISION='';

			}
			var DATA_SOURCES = $("#DATA_SOURCES").val();
	 		var option1=$("#branchCode option:selected");
			var DEPARTMENT=option1.text();

	 		var option2=$("#SERVICE_TYPE option:selected");
			var SERVICE_TYPE=option2.text();
	 		var option3=$("#COMPLAIN_REASON option:selected");
			var COMPLAIN_REASON=option3.text();
	 		var option4=$("#COMPLAIN_TYPE option:selected");
			var COMPLAIN_TYPE=option4.text();
	 		var option5=$("#COMPLAIN_POINT option:selected");
			var COMPLAIN_POINT=option5.text();
	 		var REPLENISH_CONTENT=$("#REPLENISH_CONTENT").val();
			var BRANCH_NAME = $("#branchCode option:selected").text();
			var SENTIMENT_FROM_NAME =$("#SENTIMENT_FROM option:selected").text();
			var SERVICE_TYPE_NAME = $("#SERVICE_TYPE option:selected").text();
			var UPGRADETYPE1_NAME = $("#upgradeType1 option:selected").text();
			var UPGRADETYPE2_NAME = $("#upgradeType2 option:selected").text();


			data.PRODUCT_DIVISION=PRODUCT_DIVISION;
			data.DEPARTMENT=DEPARTMENT;
			data.SERVICE_TYPE=SERVICE_TYPE;
			data.COMPLAIN_REASON=COMPLAIN_REASON;
			data.COMPLAIN_TYPE=COMPLAIN_TYPE;
			data.COMPLAIN_POINT=COMPLAIN_POINT;
			data.REPLENISH_CONTENT = REPLENISH_CONTENT;
			data.BRANCH_NAME = BRANCH_NAME;
			data.SENTIMENT_FROM_NAME = SENTIMENT_FROM_NAME;
			data.SERVICE_TYPE_NAME = SERVICE_TYPE_NAME;
			data.UPGRADETYPE1_NAME = UPGRADETYPE1_NAME;
			data.UPGRADETYPE2_NAME = UPGRADETYPE2_NAME;
			if('01'==val){
				upgradeTypeSe1();
			}
			if('05'==val){
				bury_management_confirm();
			}
			data.STATUS=val;
			data.orderId=orderDetail.orderCode;
			data.SENTIMENT_ID=orderDetail.orderId;
			data.insert=deal.insert;
			data.DATA_SOURCES = DATA_SOURCES;
			$("#PROCESS_NAME").val();
			var isupload=$('input[name="isUpload"]:checked').val();
			data.isupload=isupload;
			var index = parent.layer.getFrameIndex(window.name);
			orderDetail.setData();
			ajax.remoteCall("${ctxPath}/servlet/order?action=upOrderNeg",data,function(result) {
				//debugger;
					if(result.state == 1){
						if(val == '05'){
							toGvocSentiment();
						}
						deal.insert='1';
						layer.alert(result.msg,{icon: 1,time:1000},function(){
						});
						/* $("#save").attr("disabled",true);
						$("#staging").attr("disabled",true);
						if('01'==$("#NEED_UPGRADE").val()){
							$("#sendEmail").attr("disabled",false);
							$("#sendMsg").attr("disabled",false);
						}else{
							$("#sendEmail").attr("disabled",true);
							$("#sendMsg").attr("disabled",true);
						} */
						//window.parent.location.reload();
						deal.insert='';
						setTimeout(function(){
							   var frames=$(parent.document.getElementById('pageLoader')).find('iframe');
							    for(var i=0; i<frames.length; i++){
							    	var item=frames[i];
							    	if(/ordertype-list2/.test(item.src)||/ordertype-list3/.test(item.src)||/orderdeal-list/.test(item.src)||/orderdeal-list3/.test(item.src)||/orderdeal-list2/.test(item.src)||/deal-list/.test(item.src)){
							    		item.contentWindow.selectList('1');
							    		//item.contentWindow.location.reload();
							    	}
							    }
						},1000);
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }
	 	 orderDetail.getOrgCode=function(orgCode){
		 		ajax.remoteCall("/neworder/servlet/comm?action=GetOrgCode&orgCode="+orgCode,
		 				{orgCode:orgCode},function(result){
					if(result.state =="1"){
						orgCode=result.data;
					}
				},{async:false});
		 		return orgCode
		 	}
	 	 function userName(){
		 		var option=$("#orgCode option:selected");
				var DEPARTMENT=option.text();
		 		var option1=$("#branchCode option:selected");
				var SUB_CENTER=option1.text();
		 		$.ajax({
					url:"${ctxPath}/servlet/order?query=getUserName",
					data:{"DEPARTMENT":DEPARTMENT,"SUB_CENTER":SUB_CENTER},
					async:false,
					success:function(result) {
						if(result.state == 1){
							userName=result.msg;
							userId=result.data;
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
			});
		 	}
	 	function orderInfo(){
	 		var data={};
	 		data.id=orderDetail.orderId;
	 		// 获取页面当前 DATA_SOURCES 值
	 		var currentDataSource = $("#DATA_SOURCES").val() || "";
	 		ajax.remoteCall("${ctxPath}/servlet/order?query=getOrderPos",data,function(result) {
					if(result.state == 1){
						var ret = result.data;
						var str=$("#CONTENT").val();
						if(ret.length!=0){
							_orderInfo=ret[0];
						}

						// 用于记录所有返回的 DATA_SOURCES 值
						var dataSourcesSet = new Set();
						
						for(var i=0;i<ret.length;i++){
							var ob=ret[i];
							var content=ob.CONTENT.replace(/(^\s*)/g,"");
							/* console.log(JSON.stringify(result)); */
							str+="网络名: "+ob.CREATER+"\n";
							str+="来源: "+ob.SENTIMENT_FROM+"\n";
							str+="发布时间: "+ob.CREATE_TIME+"\n";
							str+="原文链接: "+ob.URL+"\n";
							str+="原文内容:"+content+"\n\r";
							
							// 收集 DATA_SOURCES 值
							if(ob.DATA_SOURCES) {
								dataSourcesSet.add(ob.DATA_SOURCES);
							}
						}
						
						// 比较 DATA_SOURCES 值
						if(dataSourcesSet.size > 0) {
							var allSame = true;
							var firstValue = Array.from(dataSourcesSet)[0];
							
							// 检查是否所有值都相同
							dataSourcesSet.forEach(function(value) {
								if(value !== firstValue) {
									allSame = false;
								}
							});
							
							// 如果值不同或与当前页面值不同，设置为 "03"
							if(!allSame || (currentDataSource && firstValue !== currentDataSource)) {
								$("#DATA_SOURCES").val("03");
							}
						}
						
						$("#CONTENT").html(str);
					} else {
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
		function bury_management_confirm(){
			if (top.collectEvent) {
					top.collectEvent("click_public_opinion_management_confirm", {
						status:1,
						inputTokens:0,
						outputTokens:0,
						opType:-1,
						opTotalTime:0,
						opTimeEfficiencyValue:"350s",
						opEfficiencyValue:1,
					});
			}
		}
		function phoneCode(){
	 		var areaNum =$("#areaNum").val();
	 		popup.layerShow({type:2,title:'电话区号',shadeClose:false,area:['860px','660px'],offset:'20px'},"/neworder/pages/access/phone-code.jsp?callback=2&areaCodeId=areaCode&areaNameId=areaName&areaNumId=areaNum&areaNum="+areaNum);
	 		$("#customerAddress").val($("#areaName").val());

		}


		var bindingData='';
		function bindingInfo(val,val1,val2,val3,val4){
			$.ajax({
				url:"${ctxPath}/servlet/binding?query=bindingInfo",
				data:{"SERVICE_TYPE":val,"COMPLAIN_REASON":val1,"COMPLAIN_TYPE":val2,"COMPLAIN_POINT":val3,"DATASOURCE":val4},
				async:false,
				success:function(result) {
					if(result.state == 1){
						//console.info(result.data)
						bindingData= result.data;
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	}
		function closeAll(){
			var index = parent.layer.getFrameIndex(window.name);
			parent.layer.close(index);
			window.parent.location.reload();

		}

		function alertChange(){
			layer.confirm('诉求信息未填写,是否派单?',

		            {
		              btn: ['确定','取消'], //按钮
		              time:5000

		            },

		            function(index){
		            	var dataList ={};
		     		    var data={};
		     		   	var customerVO={};
		     		 	var requireVO={};
		    	 		var orgCode=orderDetail.getOrgCode($("#orgCode").val());

		     		 	customerVO.areaNum=$("#areaNum").val();
		     		 	customerVO.areaCode=$("#areaCode").val();
		     		 	customerVO.areaName=$("#areaName").val();
		     		 	customerVO.customerName=$("#CUST_NAME").val();
		     		 	customerVO.customerAddress=$("#customerAddress").val();
		     		 	customerVO.serviceCustomerName=$("#CUST_NAME").val();

		     		 	customerVO.customerPhone=$("#TELEPHONE_NUM").val();
		     		 	customerVO.serviceCustomerMobilephone1=$("#TELEPHONE_NUM").val();
		     		 	customerVO.serviceCustomerMobilephone2=$("#TELEPHONE_NUM2").val();
		     		 	customerVO.customerMobilephone1=$("#TELEPHONE_NUM").val();
		     		 	customerVO.customerMobilephone2=$("#TELEPHONE_NUM2").val();

		     		 	customerVO.contactTypeCode='20';
		     		 	customerVO.customerLevel='10';
		     		 	customerVO.customerType='10';

		     		 	requireVO.brandCode=$("#brandCode").val();
		     		 	requireVO.brandName=$("#brandName").val();
		     		 	requireVO.prodCode=$("#prodCode").val();
		     		 	requireVO.prodName=$("#prodName").val();
		     		 	requireVO.productModel=$("#productModel").val();
		     		 	requireVO.productCode=$("#productCode").val();
		     		 	requireVO.urgentLevel=$("#NEGATIVE_LEVEL").val();
		     		 	requireVO.disposeType='11';
		     		 	requireVO.orgCode=orgCode;
		     		 	requireVO.contactOrderBuyDate=$("#BUY_DATE").val();
		      			var TITLE=$("#TITLE").val();
		     		 	var complaints=$("#CONTENT").val().split("\n");
		     		 	var url=complaints[3].replace("原文链接:","");
		     		 	var complaintsCon=complaints[4].replace("原文内容:","");

		     		 	requireVO.contactOrderServiceDescribe=TITLE+url+complaintsCon;
		     		 	requireVO.contactOrderBuyChannel=$("#BUY_CHANNEL").val();
		     		 	requireVO.contactOrderServTypeCode='TS';

		     		   	data.customerVO=customerVO;
		     		   	data.requireVO=requireVO;
		     		 	dataList.moduleName ='04';
		     		 	dataList.moduleId=orderDetail.orderCode;
		     		 	orderDetail.orderCodeOver=orderDetail.orderCode;
		     		 	dataList.type='other';
		     		 	dataList.data=encodeURIComponent(JSON.stringify(data));
						dataList.customerTel=$("#TELEPHONE_NUM").val();
						console.log(dataList,"dataList")
						popup.openTab("/neworder/pages/access/order-new.jsp","派发工单",dataList);
						//dataList.customerTel=$("#TELEPHONE_NUM").val();
		     			//popup.layerShow({type:2,title:'派发工单',area:['860px','700px'],offset:'1px'},"/neworder/pages/access/order-new.jsp?type=handle",{data:data});
						// if(!top.pageControl || !top.pageControl.getEnv){
						// 	popup.openTab("/neworder/pages/access/order-new.jsp","派发工单",dataList);
						// }else {
						// 	dataList.callId = Math.random().toString(36).slice(2,9);
						// 	dataList.title = "派发工单";
						// 	top.pageControl.openVoiceOrder(dataList)
						// }
		              return true;
		            },
		            function(index){
		                layer.msg('已取消！', {icon: 1});
		                return false;
		            }
		        );
		}
		function areaNumCallbackFunc(){
	 		$("#customerAddress").val($("#areaName").val());

			var obj1=$("#areaCode").val();
			var obj=$("#orgCode").val();
			changeBranch(obj,obj1);
		}

		function changeBranch(orgCode,regoinCode){
			if(orgCode!=''&&regoinCode!=''){
				var data = {};
				data.regoinCode = regoinCode ;
				data.orgCode =  getOrgCodeByCC(orgCode);
				ajax.remoteCall("/neworder/servlet/comm?query=getBranchByRegion",data,function(result){
					if(result.state == 1){
						var list = result.data;
						if(list && list.length >0 ){
							$('#branchCode option').each(function(index) {
								var val = $(this).val();
								if (val == list[0].branchCode) {
									$(this).prop('selected', 'selected');
								}
							})
						}
					}
				});
			}
		}
		function getOrgCodeByCC(orgCode){
	 		ajax.remoteCall("/neworder/servlet/comm?action=GetOrgCode&&orgCode="+orgCode,
	 				{orgCode:orgCode},function(result){
				if(result.state =="1"){
					orgCode=result.data;
				}
			},{async:false});
	 		return orgCode
	 	}
		function reDoing(){
			ago2=true;
		}
		function reDoing1(){
			ago3=true;
		}
		function getNewOrderId(){
		if(''==orderDetail.orderCode){
			$.ajax({
				url:"${ctxPath}/servlet/order?query=GetOrderCode",
				data:{},
				async:false,
				success:function(result) {
					if(result.state == 1){
						 orderDetail.orderCode=result.msg;
						 $("#ID").val(result.msg);
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
		}
	}
		function tell(ago){
			console.info('调用'+ago);
			var valTp='';
			if('1'==ago){
				valTp=$("#TELEPHONE_NUM").val();
			}else{
				valTp=$("#TELEPHONE_NUM2").val();
			}
			console.info('第二'+valTp);

			layer.prompt({formType: 2,value: valTp,title: ['外呼','color:red'],area: ['300px', '30px']} //自定义文本域宽高
			, function(value, index, elem){
				console.info('第二'+valTp);

				top.myCCbar.call(valTp,{},function(result){
					if(result.state=='1'){
						  layer.close(index);
					}else{
						layer.alert("外呼失败",{icon:2});
					}
				})

		});
		}


		function upgradeTypeSe(val,val2){
		$("#upgradeType2").render({data:{"id":val},success:function(result){
			$('#upgradeType2 option').each(function(index) {
				var val = $(this).val();
				if (val == val2) {
					$(this).prop('selected', 'selected');
				}
			})
			}});
		}
		function productSelectDoneFunc(orgCode,brandCode,brandName,prodCode,prodName){
	 		var obj1=$("#areaCode").val();
			$("#branchCode").render({data:{"orgCode":orgCode},success:function(result){
	    		changeBranch(orgCode,obj1);
	    	}});
		}
		function upgradeTypeSe1(){
			ago2=true;
			ago3=true;
		}
		function sentimentOrder(){
			$.ajax({
				url:"${ctxPath}/servlet/order?action=ChangeStatus",
				data:{"id":orderDetail.orderCodeOver},
				async:false,
				success:function(result) {
					if(result.state == 1){
						 var frames=$(parent.document.getElementById('pageLoader')).find('iframe');
						   for(var i=0; i<frames.length; i++){
						    	var item=frames[i];
						    	if(/ordertype-list2/.test(item.src)||/ordertype-list3/.test(item.src)||/orderdeal-list/.test(item.src)||/orderdeal-list3/.test(item.src)||/orderdeal-list2/.test(item.src)||/deal-list/.test(item.src)){
						    		item.contentWindow.selectList('-1');
						    		//break;
						    	}
						    }
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			});
		}
		function  rulesPhone(ths){
			var ago=$(ths).val();
	 		 var myreg=/^[1][0-9]{10}$/;
	 		 var myreg1=/^0\d{2,3}-?\d{7,8}$/;
	 		  if(ago!=''&&ago!=null){
	        	  if (!myreg.test(ago)&&!myreg1.test(ago)) {
						layer.msg("号码格式不正确", {icon : 2});
		          }
	          }
		}

		orderDetail.dialogList = function () {
			popup.layerShow({
				type:2,
				title:'话术查询',
				shadeClose:false,
				area:['1200px','660px'],
				offset:'20px'
			},"${ctxPath}/pages/order/order-sentiment-dialog.jsp");

		}

        orderDetail.callIn = function (content) {
            $('#communication').val(content);
        }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>