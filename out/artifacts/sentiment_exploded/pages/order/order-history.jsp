<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>历史工单查询</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchHistoryForm" class="form-inline" id="searchHistoryForm" onsubmit="return false" data-toggle="">
             	<input type="hidden" name="NICKNAME" value="${param.name}">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
             		     <div class="form-group">
             		           <h5><span class="glyphicon glyphicon-list"></span> 历史工单查询</h5>
             		          <div class="input-group input-group-sm pull-right">
             		                <button class="btn btn-sm btn-success" type="button" onclick="editOrder()">补充工单内容</button>   
             		           </div>
             		     </div>
             		     
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="order.orderTypeList">
                             <thead>
	                         	 <tr> 
	     <th  class="text-c"><input type="radio"  name="radio" value=""></th>                         	     
	                         	      <th>工单状态</th>
								      <th>是否有客户资料</th>
								      <th>登记人</th>
								      <th>网络名</th>
								      <th>投诉主题</th>
								      <th>产品主体</th>
								      <th>产品大类</th>
								      <th>舆情来源</th>
								      <th>工单类型</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                  
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td  class="text-c"><input type="radio"  name="radio" value="{{:ID}},{{dictFUN:STATUS "SENTIMENT_ORDER_STATUS"}},{{dictFUN:ORDER_TYPE "SENTIMENT_ORDER_TYPE"}}"></td>
											<td>{{dictFUN:STATUS "SENTIMENT_ORDER_STATUS"}}</td>
											<td>{{dictFUN:EXIST_CUST "PUSH_STATIS"}}</td>
											<td>{{:PROCESS_NAME}}</td>
											<td>{{:NICKNAME}}</td>
											<td>{{:COMPLIAN_TOPIC}}</td>
											<td>{{:PRODUCT_DIVISION}}</td>
											<td>{{:PRODUCT_CATEGORY}}</td>
											<td>{{call:DATA_SOURCES fn='getDataSources'}}</td>
											<td>{{dictFUN:ORDER_TYPE "SENTIMENT_ORDER_TYPE"}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
	                   <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("history");
		history.orderId='${param.id}'
	    $(function(){
			$("#searchHistoryForm").render();
		})
		function editOrder(){	
	    	
	    	var str = $('input[name="radio"]:checked').val();
	    	if(''==str||undefined==str){
	    		layer.alert('请选择要补充的工单！',{icon:2});
	    		return false;

	    	}
	    	var ids=str.split(",");
	    	var id=ids[0];
	    	var type=ids[1];
	    	var orderType=ids[2];
	    	if('已归档'==type){
	    		layer.alert('该状态工单不能补充！',{icon:2});
	    		return false;
	    	}	    
	    	if('正向工单'==orderType){
				 popup.layerShow({type:2,title:'舆情处理',shadeClose:false,area:['880px','600px'],offset:'20px'},"${ctxPath}/pages/order/order-archive-one.jsp",{id:id,orderId:history.orderId});
			}else{
				popup.openTab("${ctxPath}/pages/order/order-deal-type.jsp","舆情工单",{id:id,orderId:history.orderId,type:'99',addition:1});
			}
			// popup.layerShow({type:1,title:'舆情工单',area:['90%','820px'],offset:'10px'},"${ctxPath}/pages/order/order-deal.jsp",{type:1});
		}
        function getDataSources(val){
			if(val=='01'){
				return '系统爬取';
			}else if (val == '02'){
				return '手工建单';
			}else if (val =='03'){
				return '系统&手工';
			}
			return '';
		}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>