<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>电话通知</title>
</EasyTag:override>
<EasyTag:override name="content">
  <form action="" method="post" name="searchIpForm" class="form-inline" id="searchIpForm" onsubmit="return false" data-mars="">
    <input type="hidden" name="DEPARTMENT" id="DEPARTMENT" >
    <input type="hidden" name="SUB_CENTER" id="SUB_CENTER" >
    <input type="hidden" name="prodCode"  value="${param.prodCode}">
    <input type="hidden" name="branchCode"  value="${param.branchCode}">
    <input type="hidden" name="orgCode"  value="${param.orgCode}">
    <input type="hidden" name="upgradeType"  value="${param.upgradeType}">
    <input type="hidden" name="areaCode"  value="${param.areaCode}">
     <div class="ibox">
         <!--  <div style="margin:30px auto;text-align:center">
		      <div class="input-group ">
			      <span class="input-group-addon">电话</span>	
				  <input type="text" name="iphone" id="iphone" class="form-control input-sm" style="width:160px">
				  <span class="input-group-addon" style="cursor:pointer"><i class="glyphicon glyphicon-earphone"></i></span>
		      </div>
		  </div> -->
	      <div class="ibox-title clearfix" id="divId">
			     <div class="form-group" >
          		     <h5><span class="glyphicon glyphicon-list"></span> 电话本</h5>
          		     <div class="input-group  pull-right mr-10">
          		            <button class="btn btn-sm btn-success" type="button" onclick="orderIphone.reset()"> 重置</button>	             		            
          		      </div>
	         		 <div class="input-group ">
					      <span class="input-group-addon">员工姓名</span>	
						  <input type="text" name="USER_NAME" id="USER_NAME" class="form-control input-sm" style="width:120px">
				     </div>
				     <div class="input-group ">
					      <span class="input-group-addon">电话</span>	
						  <input type="text" name="PHONE_NUM" id="PHONE_NUM" class="form-control input-sm" style="width:100px">
				     </div>
				  <!--    <div class="input-group ">
					      <span class="input-group-addon">主体</span>						
					                    <select  name="orgCodes" id ="orgCodes"   class="form-control input-sm"  data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" onchange="changeOrg(this.value)">
					                          <option value="">请选择</option>
					                     </select> 
				     </div>
				     <div class="input-group ">
					      <span class="input-group-addon">分中心</span>	
							 <select class="form-control input-sm" name="branchCodeS" id ="branchCodeS" data-cust-context-path="/neworder" data-cust-mars="comm.branchCode" style="width: 130px">
                                <option value="">请选择</option>
                           </select>
				     </div> -->
				     <div class="input-group ">
						  <button type="button" class="btn btn-sm btn-default" onclick="orderIphone.searchData()"><span class="glyphicon glyphicon-search"></span> 查询</button>
				     </div>
			  </div>
		 </div>  
          <div class="ibox-content">
        	    <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="setting.userInfoNew">
                    <thead>
                     	 <tr>
                     	<!--     <th>主体</th>
                     	    <th>分中心</th> -->
                     	    <th>联系人</th>
                     	    <th>岗位</th>
                     	    <th>电话号码</th>
                     	    <th>邮箱</th>
                     	 </tr>
                    </thead>
                       <tbody id="dataList">                            	
                       </tbody>    
               </table>
<!-- 											<td>{{:DEPARTMENT}}</td>
											<td>{{:SUB_CENTER}}</td> -->
               
               	 <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
 											<tr>											
											<td>{{:AGENTNAME}}</td>
											<td>{{:AGENTPOSITION}}</td>
                                      		<td><a href="javascript:tell('{{:PHONE}}')">{{:PHONE}}</a></td>
											<td>{{:EMAIL}}</td>
									    </tr>
								    {{/for}}					         
						</script>
						<div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
           </div>
    </div>
  </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">	
	   jQuery.namespace("orderIphone");
	   $(function(){
			$("#searchIpForm").render();
	   })
		 function changeOrg(obj){
	    	$("#branchCodeS").render({data:{"orgCode":obj}});
	 	}
	   orderIphone.reset=function(){
	    	$("#divId select").val("");
	    	$("#USER_NAME").val('');
	    	$("#PHONE_NUM").val('');
	    	$("#DEPARTMENT").val('');
	    	$("#SUB_CENTER").val('');
		};
		orderIphone.searchData=function(){
			var option1=$("#branchCodeS option:selected");				
			var SUB_CENTER=option1.text();			
			 if('--请选择--'==SUB_CENTER){
				 SUB_CENTER='';
			 }
			$("#DEPARTMENT").val(DEPARTMENT);
			$("#SUB_CENTER").val(SUB_CENTER);
			$("#searchIpForm").searchData();
		}
		orderIphone.setIphone=function(val){
			$("#iphone").val(val);
		}
		function tell(val){
			layer.prompt({formType: 2,value: val,title: ['外呼','color:red'],area: ['300px', '30px']} //自定义文本域宽高
			, function(value, index, elem){
				top.myCCbar.call(val,{},function(result){
					if(result.state=='1'){
						  layer.close(index);
					}else{
						layer.alert("外呼失败",{icon:2});
					}
				}) 

		});
		}
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>