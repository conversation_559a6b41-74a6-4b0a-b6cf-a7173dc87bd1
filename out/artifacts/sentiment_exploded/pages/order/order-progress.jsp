<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>处理进展</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form id="editProForm" data-mars="orderInf.orderIndo"  method="post"  autocomplete="off" data-mars-prefix="" >
       <input type="hidden" name="pro.ID" value="${param.ID}">
                      <input type="hidden" name="pro.ORDER_ID" value="${param.orderId}">
       
			      <div class="info-title">
				  		<p style="width: 150px;">舆情处理信息</p>
				  </div>
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
		                            <td width="70px">分中心</td>
			                        <td ><input type="text"  name="pro.NOTICE_CENTER"  maxlength="30"   id="" class="form-control input-sm"></td>
			                      
			                        
			                 </tr>
			                  
			                 <tr>
			                  <td  >处理进展</td>
			                        <td ><input type="text" name="pro.PROGRESS"  maxlength="150"   id="" class="form-control input-sm"></td>
			                 </tr>
			                 <tr>
			                 <td >处理结果反馈</td>
			                        <td ><input type="text" name="pro.PROGRESS_FEEDBACK" maxlength="150"   id="" class="form-control input-sm"></td>
			                 </tr>
	                    </tbody>
                  </table>
                  <div class="info-title mt-20" >
				  	<p style="width: 150px;">售后工单处理进展</p>
				  </div>
                  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px" >接入单</td>
			                        <td width="160px"><input type="text" name="pro.CC_ORDER_NO"  maxlength="50"  id="" class="form-control input-sm"></td>
			                        <td width="50px" >派单时间</td>
			                        <td width="160px"><input type="text" name="pro.SEND_TIME"    id="SEND_TIME" class="form-control input-sm Wdate" "></td>
			                        <td width="50px" >服务网点</td>
			                        <td width="160px"><input type="text" name="pro.SERVICE_BRANCHES" maxlength="50"   id="" class="form-control input-sm"></td>
			                 </tr>
			                 <tr>
			                        <td >接单时间</td>
			                        <td><input type="text" name="pro.RECEIVE_TIME"    id="RECEIVE_TIME" class="form-control input-sm Wdate" "></td>                		                          
			                        <td >首约时间</td>
			                        <td><input type="text" name="pro.FIRST_AGREED_TIME"    id="" class="form-control "></td>
			                        <td >改约时间</td>
			                        <td><input type="text" name="pro.SECOND_AGREED_TIME"    id="" class="form-control "></td>              
			                 </tr>
			                 <tr>
			                        <td >反馈类型</td>
			                        <td><input type="text" name="pro.FEEDBACK_TYPE"  maxlength="50"  id="" class="form-control input-sm"></td>
			                        <td >反馈时间</td>
			                        <td><input type="text" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" name="pro.FEEDBACK_TIME"    id="" class="form-control input-sm Wdate" "></td>                
			                        <td >反馈结果</td>
			                        <td><input type="text" name="pro.FEEDBACK_RESULT"  maxlength="150"  id="" class="form-control input-sm"></td>
			                 </tr> 
			                 <tr>
			                        <td >服务单状态</td>
			                        <td><input type="text" name="pro.CC_ORDER_STATUS"  maxlength="150"  id="" class="form-control input-sm"></td>

			                        <td >一级反馈项目</td>
			                        <td><input type="text" name="pro.FEEDBACK_FIRST_ITEM"  maxlength="50"  id="" class="form-control input-sm"></td>
			                        <td >二级反馈项目</td>
			                        <td><input type="text" name="pro.FEEDBACK_SECOND_ITEM" maxlength="50"   id="" class="form-control input-sm"></td>			                       
			                 </tr>
			                  <tr>
			                        <td >反馈说明</td>
			                        <td colspan="5"><textarea rows="3"   maxlength="150" class="form-control input-sm" name="pro.FEEDBACK_DESC"></textarea></td>
			                 </tr>
			              </tbody>
			      </table>
			      <p class="text-c mt-40">
				   		<button class="btn btn-sm btn-primary ml-20"  type="button" id="proButton" onclick="orderPro.ajaxSubmitForm()">保存进度</button>
				  </p> 
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
<!-- 		<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script> -->

	<script type="text/javascript">
	   jQuery.namespace("orderPro");
	   orderPro.id='${param.ID}';
	   $(function(){
		/* if(''== orderPro.id||null== orderPro.id){
			layer.alert("该工单没有处理进展信息！",{icon:2});
			$("#proButton").attr("disabled",true);
		} */
		$("#editProForm").render();
	});
	   
	   orderPro.ajaxSubmitForm = function(){
	 		if(form.validate("editProForm")){		
	 			orderPro.insertData(); 	
			};
	 	}
	 	orderPro.insertData = function() {
			var data = form.getJSONObject("editProForm");
	 		ajax.remoteCall("${ctxPath}/servlet/orderInf?action=UpOrederPro",data,function(result) { 
				//debugger;
					if(result.state == 1){
						layer.alert(result.msg,{icon: 1,time:1000},function(){
						});
/* 						$("#proButton").attr("disabled",true);
 */					
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }
	 	function isAre(){
	 		$.ajax({
				url:"${ctxPath}/servlet/orderInf?query=isAre",
				data:{"id":orderPro.id},
				async:false,
				success:function(result) { 
					if(result.state == 1){
						if(result.data==0){
							layer.alert("该工单没有派发！",{icon:2});
							$("#proButton").attr("disabled",true);
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>