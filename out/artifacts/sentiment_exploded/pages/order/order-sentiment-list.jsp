<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>舆情话语配置</title>
    <link rel="stylesheet"
          href="/neworder/static/lib/layui/css/layui.css">
    <link rel="stylesheet"
          href="/neworder/static/lib/zTree_v3/css/demo.css">
    <script src="/easitline-static/js/jquery.min.js"></script>
    <script type="text/javascript"
            src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
    <style type="text/css">
        .layui-form-label {
            width: 120px;
        }

        .layui-input-block {
            margin-left: 130px;
        }

        .red {
            color: red;
        }

        .ew-tree-table .ew-tree-pack {
            max-width: 90%;
        }

        .ew-tree-table-td-single > .ew-tree-tips {
            text-overflow: initial !important;
        }

        .ew-tree-tips::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 4px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 4px;
        }

        .ew-tree-tips::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 10px;
            background-color: skyblue;
            background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
        }

        .ew-tree-tips::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #ededed;
            border-radius: 10px;
        }

        .layui-form-item {
            margin-bottom: 5px !important
        }

        .ew-tree-table .ew-tree-pack {
            display: flex !important;
        }

        .ew-tree-table .ew-tree-pack > span {
            height: auto !important;
            line-height: 16px;
            display: inline-block;
            vertical-align: middle;
            white-space: pre-wrap;
        }

        .ew-tree-table-td-single > .ew-tree-tips {
            overflow: visible !important;
            white-space: pre-wrap !important;
        }

    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <body style="padding: 15px;">
    <div class="ibox">
        <div class="ibox-title clearfix">
            <div class="form-group">
                <h5><span class="glyphicon glyphicon-list"></span>舆情话语配置</h5>
            </div>
            <div class="ibox-content">
                <div style="display: flex;margin-bottom: 10px;justify-content: space-between;">
                    <div class="input-group input-group-sm pull-right mr-10">
                        <input type="text" name="searchWord" id="searchWord" class="form-control input-sm"
                               placeholder="请输入关键字..." style="width:150px">
                        <button type="button" class="btn btn-sm btn-default" onclick="sentimentDialog.searchDialog()">
                            <span class="glyphicon glyphicon-search"></span> 搜索
                        </button>
                    </div>
                    <div style="margin-bottom: 10px; text-align: right;">
                        <button type="button" class="btn btn-sm btn-success" onclick="sentimentDialog.expand()">
                            +全部展开
                        </button>
                        <button type="button" class="btn btn-sm btn-success" onclick="sentimentDialog.showBox()">
                            +新增
                        </button>
                        <button type="button" class="btn btn-sm btn-primary" onclick="sentimentDialog.showImport()">
                            +导入
                        </button>
                    </div>

                </div>
                <div class="page-wrapper">
                    <table id="demoTreeTable1"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格操作列 -->
    <script type="text/html" id="demoTreeTableBar1">
        {{# if(d.pid == '0') { }}
        <a class="layui-btn layui-btn-success layui-btn-xs" lay-event="add" lay-html="">新增</a>
        {{# } }}
        <a class="layui-btn layui-btn-normal layui-btn-xs" lay-event="edit" lay-html="">编辑</a>
        <a class="layui-btn layui-btn-danger layui-btn-xs" lay-event="del" lay-html="">删除</a>
    </script>

    <div id="addBox" style="display: none; padding: 20px 20px 0px 0px;">
        <form class="layui-form" action="" lay-filter="TheForm" id="dialogForm">
            <input type="hidden" name="dialog.ID"/>
            <input type="hidden" name="dialog.PARENT_ID" id="dia_pid"/>
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>关键字</label>
                <div class="layui-input-block">
                    <input type="text" name="dialog.KEYWORDS" autocomplete="off" class="layui-input"
                           data-rules="required">
                </div>
            </div>
            <div class="layui-form-item" id="contentBox">
                <label class="layui-form-label"><span class="red">*</span>话术内容</label>
                <div class="layui-input-block">
                    <textarea name="dialog.DIALOGUE_CONTENT" id="content" autocomplete="off" class="layui-input"
                              style="height: 250px"
                              data-rules="required" maxlength="1200"></textarea>
                </div>
            </div>
            <div class="layui-form-item" id="enableBox">
                <label class="layui-form-label"><span class="red">*</span>是否启用</label>
                <div class="layui-input-block">
                    <label class="radio-inline">
                        <input type="radio" name="dialog.ENABLED" value="Y" checked="checked">启用
                    </label>
                    <label class="radio-inline">
                        <input type="radio" name="dialog.ENABLED" value="N">禁用
                    </label>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block"
                     style="text-align: center; margin-left: 0;">
                    <button class="layui-btn layui-btn-warm layui-btn-sm" type="button"
                            onclick="sentimentDialog.addDialog()">提交
                    </button>
                    <button class="layui-btn layui-btn-normal layui-btn-sm" type="button"
                            onclick="sentimentDialog.close()">关闭
                    </button>
                </div>
            </div>
        </form>
    </div>

    <div id="importBox" style="display: none; padding: 20px 20px 0px 0px;">
        <form class="layui-form" action="" lay-filter="importForm" id="importForm" enctype="multipart/form-data">
            <a class="btn btn-sm btn-link" href="JavaScript:sentimentDialog.download()">下载导入模板</a>
            <table class="table table-vzebra" style="margin: 10px">
                <tbody>
                <tr>
                    <td width="60px">Excel文件</td>
                    <td><input class="hidden" type="file" id="file" name=file
                               accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel">
                        <button class="btn btn-xs btn-info" type="button"
                                onclick="$('#file').click()">选择文件
                        </button>
                        <a class="btn btn-sm btn-link" href="JavaScript:void(0)" id="file-text">暂未选择</a>
                    </td>
                </tr>
                </tbody>
            </table>
            <div class="layui-form-item">
                <div class="layui-input-block"
                     style="text-align: center; margin-left: 0;">
                    <button class="layui-btn layui-btn-warm layui-btn-xs" type="button"
                            onclick="sentimentDialog.import()">导入
                    </button>
                    <button class="layui-btn layui-btn-normal layui-btn-xs" type="button"
                            onclick="sentimentDialog.close()">关闭
                    </button>
                </div>
            </div>
        </form>
    </div>

</EasyTag:override>

<EasyTag:override name="script">
    <script src="/neworder/static/lib/layui/layui.js"></script>
    <script src="/neworder/static/lib/layui/treeTable/treeTable.js"></script>
    <script type="text/javascript">
        jQuery.namespace("sentimentDialog");

        // 监听上传文件，显示选中文件名
        $('#file').change(function () {
            if (this.files[0]) {
                $('#file-text').text(this.files[0].name);
            } else {
                $('#file-text').text('暂未选择');
            }
        });

        // 下载导入模板
        sentimentDialog.download = function () {
            location.href = "${ctxPath}/servlet/sentiment/dialog?action=download&type=1";
        }

        // 打开导入框
        sentimentDialog.showImport = function () {
            layer.open({
                type: 1,
                title: '导入话术',
                content: $('#importBox'),
                area: ['450px', '300px']
            });

            $('#file').val('');
        }

        // 导入数据
        sentimentDialog.import = function () {
            if ($("#file").val()) {
                var filePath = $('#file').val().toLowerCase().split(".");
                var fileType = filePath[filePath.length - 1]; //获得文件结尾的类型如 zip rar 这种写法确保是最后的
                if (!(fileType === "xlsx")) {
                    layer.alert('文件格式不符合要求(.xlsx)！')
                    return;
                }
                var formData = new FormData($("#importForm")[0]);
                $.ajax({
                    url: '${ctxPath}/servlet/sentiment/dialog?action=Import',
                    type: 'POST',
                    data: formData,
                    timeout: 0,
                    async: true, cache: false, contentType: false, processData: false,
                    success: function (result) {
                        if (result.state === 1) {
                            layer.msg(result.msg, {icon: 1});
                            layer.closeAll();
                            refresh();
                        } else {
                            layer.alert(result.msg, {icon: 5});
                        }
                    }
                });
            } else {
                layer.alert('请上传文件', {icon: 2});
            }
        }

        sentimentDialog.close = function () {
            layer.closeAll();
        }

        // 展开所有话术
        sentimentDialog.expand = function () {
            insTb.expandAll();
        }

        // 搜索关键词
        let searchKeywords = '';

        // 搜索话术
        sentimentDialog.searchDialog = function () {
            searchKeywords = $('#searchWord').val();
            refresh();
        }

        // 新增话术
        sentimentDialog.addDialog = function () {
            if (!form.validate('#dialogForm')) {
                return;
            }

            if ($('#content').val().length > 1200) {
                layer.alert('话术内容不能大于1200', {icon: 5});
                return;
            }

            let data = form.getJSONObject("dialogForm");

            ajax.remoteCall('${ctxPath}/servlet/sentiment/dialog?action=SaveOrUpdate', data, function (result) {
                if (result.state == 1) {
                    layer.alert(result.msg, {
                        icon: 1,
                        closeBtn: 0,
                        title: "操作成功",
                        btn: ["确定"]
                    }, function () {
                        layer.closeAll();
                        refresh();
                    });

                } else {
                    layer.alert(result.msg, {icon: 5});
                }
            });
        }

        let currentItem;
        var insTb;

        sentimentDialog.showBox = function () {
            $("#dialogForm input[type='text'],input[type='hidden'],textarea").val("");
            $('#dia_pid').val('0');
            layer.open({
                type: 1,
                title: '新增话术分类',
                content: $('#addBox'),
                area: ['700px', '350px']
            });

            // 新增分类不校验话术内容和是否启用必填，并隐藏
            $("#content").removeAttr("data-rules");
            $('#contentBox').css("display", "none");
            $('#enableBox').css("display", "none");
        }

        layui.config({
            base: '/neworder/static/lib/layui/'
        }).extend({
            treeTable: 'treeTable/treeTable'
        }).use(['layer', 'util', 'treeTable', 'form'], function () {
            var $ = layui.jquery;
            var layer = layui.layer;
            var util = layui.util;
            var form = layui.form;
            var treeTable = layui.treeTable;

            // 渲染表格
            insTb = treeTable.render({
                elem: '#demoTreeTable1',
                tree: {
                    arrowType: 'arrow2',
                    idName: 'id',
                    iconIndex: 0,
                    pidName: 'pid',
                    treeDefaultClose: false,
                    isPidData: true // 是否是pid形式数据
                },
                text: {},
                cols: [
                    {
                        field: 'keywords',
                        title: '关键字',
                        width: '20%',
                        templet: function (row) {
                            if (row.keywords.length > 50) {
                                return row.keywords.substring(0, 50) + "..."
                            } else {
                                return row.keywords;
                            }
                        }
                    },
                    {
                        field: 'content',
                        title: '话术',
                        width: '50%',
                        templet: function (row) {
                            if (row.content.length > 200) {
                                return row.content.substring(0, 200)
                            } else {
                                return row.content;
                            }
                        }
                    },
                    {
                        field: 'enabled',
                        title: '是否开启',
                        width: 90,
                        align: 'center',
                        templet: function (row) {
                            if (row.enabled === 'Y') {
                                return '是';
                            } else {
                                return '否';
                            }
                        }
                    },
                    {
                        align: 'center',
                        toolbar: '#demoTreeTableBar1',
                        title: '操作',
                    }
                ],
                reqData: function (data, callback) {
                    ajax.remoteCall('${ctxPath}/servlet/sentiment/dialog?action=DialogList', {keywords: searchKeywords}, function (res) {
                        callback(res.data);
                    });
                },
                style: 'margin-top:0;'
            });

            //监听工具条
            treeTable.on('tool(demoTreeTable1)', function (
                obj) { //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
                var thedata = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）

                if (layEvent === 'add') { //添加
                    currentItem = thedata;
                    form.val('TheForm', {
                        "dialog.ID": '',
                        "dialog.KEYWORDS": '',
                        "dialog.DIALOGUE_CONTENT": ''
                    });

                    $('#dia_pid').val(thedata.id);

                    layer.open({
                        type: 1,
                        title: '新增话术',
                        content: $('#addBox'),
                        area: ['600px', '450px']
                    });

                    // 新增话术校验话术内容必填，并显示
                    $("#content").prop("data-rules", "required");
                    $('#contentBox').css("display", "block");
                    $('#enableBox').css("display", "block");


                } else if (layEvent === 'del') { //删除
                    currentItem = thedata;
                    layer.confirm('确定删除这一行吗？', {
                        btn: ['确定', '取消'],
                        title: "提示"
                    }, function (index) {

                        // 若要删除父话术，确保其下无子话术才能删除
                        if (thedata.children) {
                            layer.msg("当前话术下存在子话术，为了保证数据完整性，无法删除", {icon: 5, time: 1000});
                            return;
                        }

                        var data = {};
                        data.id = thedata.id;
                        ajax.remoteCall("${ctxPath}/servlet/sentiment/dialog?action=Del", data, function (result) {
                            if (result.state == 1) {
                                layer.msg(result.msg, {icon: 1});
                                if (currentItem.pid !== '0') {
                                    refresh();
                                } else {
                                    refresh();
                                }
                            } else {
                                layer.alert(result.msg, {icon: 5});
                            }
                        });

                    });

                } else if (layEvent === 'edit') { //编辑

                    currentItem = thedata;

                    // 如果是父类的编辑，不显示话术内容，否则显示
                    if (currentItem.pid !== '0') {
                        $("#content").prop("data-rules", "required");
                        $('#contentBox').css("display", "block");
                        $('#enableBox').css("display", "block");

                    } else {
                        $("#content").removeAttr("data-rules");
                        $('#contentBox').css("display", "none");
                        $('#enableBox').css("display", "none");
                    }

                    layer.open({
                        type: 1,
                        title: '话术配置',
                        content: $('#addBox'),
                        area: ['600px', '450px']
                    });

                    form.val('TheForm', {
                        "dialog.ID": thedata.id,
                        "dialog.PARENT_ID": thedata.pid,
                        "dialog.KEYWORDS": thedata.keywords,
                        "dialog.DIALOGUE_CONTENT": thedata.content,
                        'dialog.ENABLED': thedata.enabled
                    });

                    form.render();

                }
            });

        });

        function refresh(pId) {
            if (pId && pId != "0") {
                insTb.refresh(pId);
            } else {
                insTb.refresh();
            }
        }


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>