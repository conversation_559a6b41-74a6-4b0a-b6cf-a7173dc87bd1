<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>升级跟进信息</title>
	<style>
	    #upgradeHandleInfoForm th{overflow:hidden;}
	    #upgradeHandleInfoForm td{overflow:hidden;}
	    .w-120{ mix-width: 50px }
	    .w-500{max-width: 600px; mix-width: 100px }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
   <form action="" method="post" name="upgradeHandleInfoForm" class="form-inline table-responsive" id="upgradeHandleInfoForm" onsubmit="return false" data-toggle="">
    <input  class="hidden" name="complaintSourceNo" value="${param.id}" />
    <button class="btn btn-sm btn-success pull-right" id="staging"  type="button" onclick="upgradeHandleInfo.render()">更新</button>
    <div class="ibox-content">
      	<table class="table table-auto table-bordered table-hover table-condensed"
        	data-mars="comm.serachupgradehandleinfo"
        	data-container="#cTab1_dataList" data-template="cTab1_list-template">
       	    <thead>
        			<tr>
        				<th class="text-c">序号</th>
        				<th class="text-c w-120" >跟进人</th>
        				<th class="text-c w-120">跟进时间</th>
        				<th class="text-c w-500">处理意见</th>
        				<!-- <th class="text-c">操作</th> -->
        			</tr>
       		</thead>
       		<tbody id="cTab1_dataList">
			     
			</tbody>
       </table>
       <script id="cTab1_list-template" type="text/x-jsrender">
					{{for list}}
							{{if #getIndex()==0}}
							<tr>
								<td >收件人</td>
								<td colspan="3">{{:followUpName}}</td>
							<tr>
							<tr>
								<td >主题</td>
								<td colspan="3">
									{{if leaderReplyFlag=='Y'}}
										<img alt="" style="height:20px;width:20px" src="${ctxPath}/static/images/important.png">
									{{/if}}
									{{:complaintSubject}}
								</td>
							<tr>
							{{/if}} 
							<tr>
								<td >{{:#index+1}}</td>
								<td>{{:followUpAccount}}</td>
								<td>{{TIME:followUpTime}}</td>
								<td>{{:proceesingOpinion}}</td>
								<!-- <td>{{op:complaintUpgradeHandleNo}}</td> -->
							</tr>
							
					{{/for}}			         
	  </script>
	  </div>
   </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript">
		jQuery.namespace("upgradeHandleInfo");
		 $(function(){
			 $("#upgradeHandleInfoForm").render({});
	     });
		 upgradeHandleInfo.render=function(){
			 $("#upgradeHandleInfoForm").render({})
		 }
		 
		 $.views.converters("TIME", function(val) {
				if(val!=null){
					 return formatDate(val);	
					 }
				return "";
				
			});
		 $.views.converters("op", function(val) {
				if(val!=null){
					 return "<a href='#' onclick='upgradeHandleInfo.update(\""+val+"\")'>补充</a>";	
					 }
				return "";
				
		});
		function formatDate(now) { 
			 var date = new Date(now);
			 if( isNaN(date) ){
				return now;
			 }
			  var YY = date.getFullYear() + '-';
			  var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
			  var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
			  var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
			  var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
			  var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
			  return YY + MM + DD +" "+hh + mm + ss;
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>