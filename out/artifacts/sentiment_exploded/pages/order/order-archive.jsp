<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情归档</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="order.orderInfo"  method="post"  autocomplete="off" data-mars-prefix="" >
			<input type="hidden" name="order.SENTIMENT_ID" value="${param.id}">
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px" >网络名</td>
			                        <td width="160px"><input  maxlength="10" type="text" name="order.NICKNAME"   id="" class="form-control input-sm" ></td>
			                        <td width="50px">联系电话</td>
			                        <td width="160px"><input  type="text" name="order.TELEPHONE_NUM" id="TELEPHONE_NUM"  id="" class="form-control input-sm"></td>
			                        <td width="80px" >是否有客户资料</td>
			                        <td width="180px">
			                            <select  class="form-control input-sm"  data-cust-context-path="/yq_common" name="order.EXIST_CUST" data-cust-mars="dict.getDictList('PUSH_STATIS')"> 
			                                 <option value="">请选择</option>
			                            </select> 
              						</td>
		                     </tr>
		                     <tr>
			                      <!--   <td >性别</td>
			                        <td>
			                            <select  class="form-control input-sm"  data-cust-context-path="/yq_common" name="order.SEX" data-cust-mars="dict.getDictList('SEX')"> 
			                                 <option value="">请选择</option>
			                            </select> 
			                        </td> -->
			                        <td >用户</td>
			                        <td ><input type="text" name="order.CUST_NAME"  maxlength="10" class="form-control input-sm" ></td>
			                        <td >网络监测人员</td>
			                        <td><input type="text" name="order.MONITOR_USER"  maxlength="10"  id="" class="form-control input-sm"></td>
			                        <td >网络监测时间段</td>
			                        <td><input type="text" name="order.MONITOR_TIME"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td >产品主体</td>
			                        <td>
			                          <select  name="order.PRODUCT_DIVISION_CODE" id ="PRODUCT_DIVISION"    class="form-control input-sm" disabled="disabled" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')">
					                              <option value="">请选择</option>
					                          </select></td>
              						<td >产品类别</td>
			                        <td>
			                          <div class="input-group input-group-sm new-input-group">
					                          	 <input type="hidden" name="prodCode" id="prodCode" >
					                             <input type="text"  name="order.PRODUCT_CATEGORY" id="prodName"  class="form-control input-sm" v-bind:disabled = "requireFormDisable">
					                             <span class="input-group-addon" onclick="orderChive.productType()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                          </div>
			                        <td >产品型号</td>
			                        <td> <div class="input-group input-group-sm new-input-group">
					                          	 <input type="hidden" name="productCode" id="productCode"  class="form-control input-sm">
					                             <input type="text"  maxlength="30" name="order.PRODUCT_TYPE" id="productModel"  class="form-control input-sm">
					                             <span class="input-group-addon" onclick="orderChive.queryProduct()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                         </div></td>
		                     </tr>
		                     <tr>
		                            <td >销售公司</td>
			                        <td><input type="text" name="order.ACCOUNT_AREA"   maxlength="30" class="form-control input-sm"></td>
			                        <td>服务亮点</td>
			                        <td>
			                           <select  class="form-control input-sm" data-cust-context-path="/yq_common" name="order.SERVICE_WINDOW" data-cust-mars="dict.getDictList('SENTIMENT_SERVICE_WINDOW')"> 
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                        <td>购买渠道</td>	
			                        <td><select  class="form-control input-sm" data-cust-context-path="/yq_common" name="order.BUY_CHANNEL" data-cust-mars="dict.getDictList('SENTIMENT_BUY_CHANNEL')"> 
			                                 <option value="">请选择</option>
			                            </select></td>
		                     </tr>
		                     <tr>
			                        <td >数据来源</td>
			                        <td>
			                            <select  class="form-control input-sm"  id="dataSource" data-cust-context-path="/yq_common" name="order.SENTIMENT_FROM" data-cust-mars="dict.getDictList('SENTIMENT_FROM')" > 
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                        
		                     </tr>
		                     <tr>
			                         <td >原文内容</td>
				                     <td colspan="5">
				                        <textarea rows="5"  class="form-control input-sm" maxlength="4000" name="order.CONTENT" id="CONTENT"></textarea>
				                     </td>
		                     </tr>
		                     <tr>
			                         <td >总部分析原因</td>
				                     <td colspan="5">
				                        <textarea rows="3" class="form-control input-sm"  maxlength="400"  name="order.ANALYSIS"></textarea>
				                     </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="orderChive.ajaxSubmitForm()">提交归档</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="parent.layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">	
	   jQuery.namespace("orderChive");
	   requreLib.setplugs("wdate");
	  orderChive.id='${param.id}';
	   $(function(){
		   $("#editForm").render({success : function(result){
				var dataInfo = result["order.orderInfo"]['data'];
				var SENTIMENT_FROM = dataInfo['order.SENTIMENT_FROM'];
				$('#dataSource option').each(function(index) {
					var text = $(this).text();
					if (text == SENTIMENT_FROM) {
						$(this).prop('selected', 'selected');
					}
				})
			}}); 
		  orderInfo();
		
		/*  $("#sendEmail").attr("disabled",true);
		$("#sendMsg").attr("disabled",true);  */

	});
		
		orderChive.productType = function(){			
		   	popup.layerShow({type:2,title:'产品品类',shadeClose:false,area:['860px','700px'],offset:'20px'},"/neworder/pages/access/product-type.jsp?orgCode=PRODUCT_DIVISION&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName");
	   	}
	 	orderChive.queryProduct = function(){	 		
	   		var orgCode= $("#PRODUCT_DIVISION").val();
	   		var prodCode= $("#prodCode").val();
	   		var productModel= $("#productModel").val();
	   		var brandCode= $("#brandCode").val();	   			   
	   		if(orgCode==null || orgCode==''){
	   			layer.alert("请先选择产品主体！");
	   			return false;
	   		}

	   		var param = encodeURI("orgCode="+orgCode+"&prodCode="+prodCode+"&productModel="+productModel+"&productModelId=productModel&productCodeId=productCode");
		   	popup.layerShow({type:2,title:'产品型号查询',shadeClose:false,area:['860px','650px'],offset:'20px'},"/neworder/pages/access/product-list.jsp?"+param);
	   	}
	 	orderChive.ajaxSubmitForm = function(){
	 		var ago=$("#TELEPHONE_NUM").val();
	 		var myreg=/^[1][3,4,5,7,8][0-9]{9}$/;  
	 		if(ago!=null&&ago!=''){
	          if (!myreg.test(ago)) {
					layer.msg("号码格式不正确", {icon : 2});
	              return false;  
	          }	 			
	 		}
				if(form.validate("editForm")){		
					orderChive.insertData(); 	
				};
			}
	 	
	 	orderChive.insertData = function() {
				var option=$("#PRODUCT_DIVISION option:selected");				
				var data = form.getJSONObject("editForm");
				var index = parent.layer.getFrameIndex(window.name);
				data.PRODUCT_DIVISION=option.text();
				data.PRODUCT_DIVISION_CODE=option.val();
				//var index = parent.layer.getFrameIndex(window.name);
				ajax.remoteCall("${ctxPath}/servlet/order?action=addOrderPos",data,function(result) { 
					//debugger;
						if(result.state == 1){
							layer.alert(result.msg,{icon: 1,time:1000},function(){
							});
							setTimeout(function () {
								parent.layer.closeAll();
								//parent.orderType1.searchData();
								window.parent.location.reload();
							  }, 1000);
							}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
				);
		 }
	 	function orderInfo(){
	 		var data={};
	 		data.id=orderChive.id;
	 		ajax.remoteCall("${ctxPath}/servlet/order?query=getOrderPos",data,function(result) { 
					if(result.state == 1){
						var ret = result.data;
						var str="";
						for(var i=0;i<ret.length;i++){
							var ob=ret[i];
							var content=ob.CONTENT.replace(/(^\s*)/g,"");
							/* console.log(JSON.stringify(result)); */
							str+="网络名: "+ob.CREATER+"\n";
							str+="来源: "+ob.SENTIMENT_FROM+"\n";
							str+="发布时间: "+ob.CREATE_TIME+"\n";
							str+="原文链接: "+ob.URL+"\n";
							str+="原文内容:"+content+"\n\r";
						}
					$("#CONTENT").html(str);
					
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>