<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情工单派发</title>
	
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
<link rel="stylesheet" type="text/css" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css">
</EasyTag:override>
<EasyTag:override name="content">

			<form id="editDetailForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" >
			
				<input type="hidden" name="order.SENTIMENT_ID" value="${param.id}">			
				<input type="hidden" name="order.DISTRIBUTED_TIME">			
				<input type="hidden" name="order.URL" id="URL">			
				<input type="hidden" name="" id="ID" >			
				<input type="hidden" name="order.CRAWL_TIME" id="CRAWL_TIME">			
				<input type="hidden" name="order.ACCOUNT_AREA" id="ACCOUNT_AREA">			
			      <div class="info-title">
			  		   <p style="width: 60%">用户信息
			  		   <button class="btn btn-sm   pull-right  btn-danger"  type="button" onclick="closeThis()">关闭</button>
			  		   
			  		   </p>
			  		 <!--   <span style="width: 30%" class="full-right">
			  		   </span> -->
			      </div>
	             
				  <table class="table table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px" >网络名</td>
			                        <td width="160px"><input type="text"  maxlength="60" name="order.NICKNAME"   id="nickname" class="form-control input-sm" maxlength="5"></td>
			                        <td width="50px">用户姓名</td>
			                      	<td width="160px"><input type="text"  maxlength="10" name="order.CUST_NAME"  id="CUST_NAME" class="form-control input-sm"></td>
			                      	 <td >电话号码1</td>
			                        <td>
			                            <div class="input-group">
										     <input type="text" name="order.TELEPHONE_NUM"   id="TELEPHONE_NUM" class="form-control input-sm" onchange="rulesPhone(this)">
										     <span class="input-group-addon" style="cursor:pointer" onclick="tell(1)"><i class="glyphicon glyphicon-earphone"></i></span>
										</div>
			                         </td>	
		                          
			                        <td>电话号码2</td>
			                        <td>
			                            <div class="input-group">
										     <input type="text" name="order.TELEPHONE_NUM2"  id="TELEPHONE_NUM2" class="form-control input-sm" onchange="rulesPhone(this)">
										     <span class="input-group-addon" style="cursor:pointer" onclick="tell(2)"><i class="glyphicon glyphicon-earphone"></i></span>
										</div>
			                        </td>		                        
			                 </tr>
		                     <tr>
		                      <td width="50px">电话区号</td>
			                      	<td width="160px">
			                      		<div class="input-group  new-input-group">
					                             <input type="number" name="PHONE_CODE" id="areaNum" class="form-control input-sm" v-model="areaNum" v-bind:disabled = "custFormDisable">
					                             <span class="input-group-addon" id="enterArea" onclick="phoneCode()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                          </div>
			                      	
			                      	</td>
			                      	   <td>区域</td>
			                      	<td>
			                      	 <input type="hidden"  name="order.CUST_CODE" id="areaCode" v-model="areaCode" class="form-control input-sm" readonly="readonly">	                     
			                      	<input type="text" name="order.CUST_AREA" id="areaName" v-model="areaName" v-bind:disabled = "custFormDisable" class="form-control input-sm">                 	
			                      	</td>	
			                     	
			                      	    <td >产品主体</td>
			                      <td>
					                    <select  name="order.PRODUCT_DIVISION_CODE" id ="orgCode"   class="form-control input-sm"  data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" onchange="changeOrg(this.value)">
					                    			                                 <option value="">请选择</option>
					                    
					                     </select></td>       
			                        <td >是否有客户资料</td>
			                        <td>
			                            <select  class="form-control input-sm"  data-cust-context-path="/yq_common" name="order.EXIST_CUST" data-cust-mars="dict.getDictList('PUSH_STATIS')"> 
			                                 <option value="">请选择</option>
			                            </select>
              						</td>	
			                 </tr>
			                 <tr>	  
			                    		                      
			                        <td>详细地址</td>
			                        <td colspan="5">			                        
			                     	  <input type="text" name="order.CUST_ADDRESS" id="customerAddress" v-model="customerAddress" v-bind:disabled = "custFormDisable" class="form-control input-sm">			                        
			                        </td>		                        
		                     </tr>
		               </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>产品信息</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
                         <tbody>
			                 <tr>
			                        <td  width="50px" >产品品牌</td>
			                        <td  width="160px">
			                            <input type="hidden" name="order.BRAND_CODE"  id="brandCode"  >
					                      	  	<input type="text"  name="order.PRODUCT_BRAND" id ="brandName"   readonly="readonly" class="form-control input-sm">
					                      </td>
              						<td  width="50px" >产品品类</td>
			                        <td  width="160px">
			                            <div class="input-group  new-input-group">
					                          	 <input type="hidden" name="order.PROD_CODE" id="prodCode"  >
					                             <input type="text"  name="order.PRODUCT_CATEGORY" id="prodName"  class="form-control input-sm" >
					                             <span class="input-group-addon" onclick="orderDetail.productType()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                          </div>
              						</td>
              					    <td  width="50px" >产品型号</td>
			                        <td  width="200px">
											<div class="input-group  new-input-group">
					                          	 <input type="hidden" name="order.PRODUCT_CODE" id="productCode"  class="form-control input-sm">
					                             <input type="text"  maxlength="20"   name="order.PRODUCT_TYPE" id="productModel"  class="form-control input-sm">
					                             <span class="input-group-addon" onclick="orderDetail.queryProduct()"><i class="glyphicon glyphicon-zoom-in"></i></span>
					                         </div>              						</td>
					                         <td width="50px">购买渠道</td>
			                       <td  width="160px">
			                       		<select  class="form-control input-sm" data-cust-context-path="/yq_common" name="order.BUY_CHANNEL" id="BUY_CHANNEL" data-cust-mars="dict.getDictList('SENTIMENT_BUY_CHANNEL')" > 
			                                 <option value="">请选择</option>
			                            </select>
			                         </td>
		                     </tr>
		                     <tr>
			                        
			                        <td>电商订单号</td>
			                        <td><input type="text"  maxlength="50" name="order.E_BUSINESS_NUM" id="E_BUSINESS_NUM" class="form-control input-sm"></td>
			                        <td>购买日期</td>
			                        <td><input type="text" name="order.BUY_DATE" id="BUY_DATE" class="form-control input-sm"  onclick="WdatePicker()"></td>
			             		   <td>购买店铺</td>
			                        <td colspan="5"><input type="text" name="order.BUY_STORE_NAME" id="BUY_STORE_NAME" class="form-control input-sm" maxlength="30"></td>
			                 </tr>

			           </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>舆情信息</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
			          <tbody>
		                     <tr>
		                            <td  width="50px" >舆情类别</td>
			                        <td  width="160px">
			                            <select  class="form-control input-sm"  data-cust-context-path="/yq_common" name="order.SENTIMENT_TYPE" data-cust-mars="dict.getDictList('SENTIMENT_TYPE_ORDER')"> 
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                        <td  width="50px" >数据来源</td>
			                        <td  width="160px">
										<select name="order.SENTIMENT_FROM" id="SENTIMENT_FROM" class="form-control"
												style="width:100%;">
										</select>
<%--			                            <select id="dataSource"  class="form-control input-sm" data-cust-context-path="/yq_common" name="order.SENTIMENT_FROM" data-cust-mars="dict.getDictList('SENTIMENT_FROM')"> --%>
<%--			                                 <option value="">请选择</option>--%>
<%--			                            </select>--%>
              						</td>
              						<td  width="50px">评论量</td>
			                        <td  width="160px"><input type="text" name="order.REPLY_COUNT" class="form-control input-sm" ></td>
			            		   <td  width="50px">粉丝量</td>
			                        <td width="160px"> <input type="text" name="order.FANS_COUNT" id="FANS_COUNT" class="form-control input-sm" ></td>
			                 </tr>
			                 <tr>
			                        
			                        <td>阅读量</td>
			                        <td><input type="text" name="order.HITS_COUNT" class="form-control input-sm" ></td>
			                        <td>转载量</td>
			                        <td><input type="text" name="order.FORWARD_COUNT" class="form-control input-sm" ></td>
			                <td >@关键用户或媒体</td>
			                        <td colspan="3"><input type="text"  name="order.CALL_MEDIA" class="form-control input-sm" maxlength="30"></td>
			                 </tr>
					  		<tr>
								<td>工单渠道来源</td>
								<td  width="160px">
									<select  class="form-control input-sm"  name="order.DATA_SOURCES" id="DATA_SOURCES"  disabled>
										<option value="">请选择</option>
										<option value="01">系统爬取</option>
										<option value="02">手工建单</option>
										<option value="03">系统&手工</option>
									</select>
								</td>
							</tr>

			          </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>投诉诉求</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
			          <tbody> 
		                     <tr>  
              						<td  width="50px" >负向级别</td>
			                        <td  width="160px">
			                             <select  class="form-control input-sm" id="NEGATIVE_LEVEL"  data-cust-context-path="/yq_common" name="order.NEGATIVE_LEVEL" data-cust-mars="dict.getDictList('SENTIMENT_NEGATIVE_LEVEL')"> 
			                                 <option value="">请选择</option>
			                            </select>
              						</td>
			                        <td  width="50px" class="required">服务请求</td>
			                        <td  width="160px">
			                            <select name="SERVICE_TYPES" data-rules="required" id="SERVICE_TYPE" data-mars="order.serviceType" class="form-control input-sm" onchange="changRequest(this.value)">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                        <td  width="50px" class="required">投诉原因</td>
			                        <td  width="160px">
			                            <select name="COMPLAIN_REASONS" id="COMPLAIN_REASON" data-rules="required"  data-mars="order.complainReason" class="form-control input-sm" onchange="changReason(this.value)">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              						<td class="required"  width="50px">投诉类别</td>
			                        <td width="160px">
			                            <select name="COMPLAIN_TYPES" id="COMPLAIN_TYPE"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm" onchange="changComplain(this.value)">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              			     </tr>
		                     <tr>            						
			                        <td class="required">投诉焦点分析</td>
			                        <td>
			                            <select name="COMPLAIN_POINTS" data-rules="required" id="COMPLAIN_POINT"  data-mars="order.complainReason" class="form-control input-sm" >
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                       <td>投诉主题</td>
			                        <td  colspan="5"><input type="text" name="order.COMPLIAN_TOPIC" id="TITLE" maxlength="100" class="form-control input-sm" maxlength="200"></td>
		                     </tr>
		         	         <tr>
			                         <td >升级说明</td>
				                     <td colspan="8">
				                        <textarea rows="3"   class="form-control input-sm" name="order.COMPLAINT_CONTENT" maxlength="4000" id="COMPLAINT_CONTENT"></textarea>
				                     </td>
		                     </tr>
		                     <tr>
			                         <td >投诉原文</td>
				                     <td colspan="8">
				                        <textarea rows="8"  class="form-control input-sm" name="CONTENT" id="CONTENT"  maxlength="4000"></textarea>
				                     </td>
		                     </tr>
		                      <tr>
			                         <td >补充</td>
				                     <td colspan="8">
				                        <textarea rows="5"   class="form-control input-sm" name="" maxlength="4000" id="REPLENISH_CONTENT"></textarea>
				                     </td>
		                     </tr>
		              </tbody>
                 </table>
                 <div class="info-title mt-10">
			  		  <p>舆情处理信息</p>
			     </div>
			     <table class="table  table-vzebra mt-10" >
			          <tbody>
			                <tr>
		                            <td  width="50px">工单状态</td>
			                        <td  width="160px">
			                            <select  class="form-control input-sm" disabled="disabled" id="STATUS" data-cust-context-path="/yq_common" name="order.STATUS" data-cust-mars="dict.getDictList('SENTIMENT_ORDER_STATUS')"> 
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                        <td  width="50px">受理操作员</td>
			                        <td  width="160px">
			                        <input type="text" name="order.CREATE_NAME" id="CREATE_NAME"  maxlength="10" class="form-control input-sm" readonly="readonly"></td>
			                        <td  width="50px">处理操作员</td>
			                        <td  width="160px">
			                        <input type="text" name="order.PROCESS_NAME"  id="PROCESS_NAME"  maxlength="10" class="form-control input-sm" readonly="readonly"></td>
		                   			   <td  width="50px">回帖时间</td>
			                        <td width="160px"><input type="text"  name="order.REPLAY_TIME" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
		                   			  </tr>
		                     <tr>

			                        <td >分中心</td>
			                        <td> <select  class="form-control input-sm" name="DEPARTMENTS" id = "branchCode" data-mars="comm.getBranchCode" >
			                                 <option value="">请选择</option>
                                      </select></td>

			                        <td >是否升级</td>
			                        <td>
			                           <select   class="form-control input-sm" data-cust-context-path="/yq_common" name="order.NEED_UPGRADE" id="NEED_UPGRADE" data-cust-mars="dict.getDictList('RED_BLOCK_IS_CHECK')">
			                                 <option value="">请选择</option>
			                            </select>
			                        </td>
			                         <td>邮件模板</td>
			                        <td>
			                            <select name="order.email" data-mars="order.emailModel" id="email" class="form-control input-sm" onchange="reDoing()">
					               		   <option value="">请选择</option>
              						    </select>
			                        </td>
		                            <td>短信模板</td>
			                        <td>
			                            <select name="order.sms" data-mars="order.smsModel" id="sms" class="form-control input-sm" onchange="reDoing1()">
					               		   <option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>

		                          <tr>
		                     <td>升级类型分类</td>
			                        <td  width="160px">
			                        <select   name="order.upgradeType1" data-mars="order.upgradeType1" id="upgradeType1"  class="form-control input-sm" onchange="upgradeTypeSe(this.value)" >
              						   			                                 <option value="">请选择</option>

              						    </select>
              						    </td>
              					 <td>升级类型</td>
			                        <td  width="160px">
			                         <select name="order.upgradeType2" data-mars="order.upgradeType2" id="upgradeType2" class="form-control input-sm" onchange="upgradeTypeSe1()">
              								                                 <option value="">请选择</option>

              					</select>
              					</td>
		                     	<td width="160px">是否线上升级</td>
			                    <td >
					                <label class="radio-inline">
										<input type="radio" value="1" name="online" <%=  request.getParameter("online") == null || "1".equals( request.getParameter("online")) ? "checked" : "" %>> 是
									</label>
									<label class="checkbox-inline">
										<input type="radio" value="0" name="online" <%=  request.getParameter("online") != null &&  request.getParameter("online").equals("0") ? "checked" : "" %>> 否
									</label>
			                    </td>
			                    <td width="160px">是否线下升级</td>
			                    <td >
			                        <label class="radio-inline">
										<input type="radio" value="1" name="downline" <%=  request.getParameter("downline") != null &&  request.getParameter("downline").equals("1") ? "checked" : "" %>> 是
									</label>
									<label class="checkbox-inline">
										<input type="radio" value="0" name="downline" <%=  request.getParameter("downline") == null || "0".equals( request.getParameter("downline")) ? "checked" : "" %> > 否
									</label>
							    </td>
		                     </tr>
		                     <tr class="uploadGvoc">
			                     <td>是否上报舆情</td>
			                     <td >
			                        <label class="radio-inline">
					                      <input type="radio" value="Y" name="isUpload" > 是
									</label>
									<label class="checkbox-inline">
										<input type="radio" value="N" name="isUpload" checked > 否
									</label>
							     </td>
			                   </tr>
		                       <tr>
		                     <td>互动概要</td>
			                        <td colspan="6">
			                      <textarea class="form-control input-sm" name="order.COMMUNICATION" id="communication" rows="3" maxlength="1000" onchange="this.value=this.value.substring(0, 1000)" onkeydown="this.value=this.value.substring(0, 1000)" onkeyup="this.value=this.value.substring(0, 1000)"></textarea>

			                        </td>

								   <td>
									   <button class="btn btn-sm btn-success mr-50" type="button"
											   onclick="orderDetail.dialogList()">话术查询
									   </button>
								   </td>
		                     </tr>
	                    </tbody>
	                  </table>
				      <p class="text-c mt-40">
					   		<button class="btn btn-sm btn-success ml-20" id="staging"  type="button" onclick="orderDetail.ajaxSubmitForm('01')">工单暂存</button>
					   		<button class="btn btn-sm btn-primary"  id="sendEmail" type="button" onclick="$('#Email').click()">邮件升级</button>
					   		<button class="btn btn-sm btn-primary ml-20" id="sendMsg"  type="button" onclick="$('#Message').click()">短信报送</button>
					   		<button class="btn btn-sm btn-primary  ml-20"  type="button" onclick="$('#Telphone').click()">电话外拨</button>
					   		<button class="btn btn-sm btn-success ml-20"  type="button" onclick="orderDetail.distribute()">售后工单</button>
						   	<button class="btn btn-sm btn-danger ml-20" style="float: right;"  id="save" type="button" onclick="orderDetail.ajaxSubmitForm('05')">工单存档</button>
					   		<button class="btn btn-sm btn-success ml-20" style="display:none" id="addBtn" type="button" onclick="">追加舆情</button>
					   		<button class="btn btn-sm btn-danger ml-20"  type="button" onclick="closeThis()">关闭</button>

					  </p>
		</form>
</EasyTag:override>

<EasyTag:override name="script">
		<script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">

	   jQuery.namespace("orderDetail");
	   requreLib.setplugs("wdate");
	   orderDetail.type='${param.type}';
	   orderDetail.orderCode='';
	   orderDetail.orderCodeOver='';
	   orderDetail.id='${param.id}';

	   // 话术查询
	   orderDetail.dialogList = function () {
		   popup.layerShow({
			   type:2,
			   title:'话术查询',
			   shadeClose:false,
			   area:['1200px','660px'],
			   offset:'20px'
		   },"${ctxPath}/pages/order/order-sentiment-dialog.jsp");

	   }

	   orderDetail.callIn = function (content) {
		   $('#communication').val(content);
	   }

	   orderDetail.distribute = function(){
		   layer.confirm('确认派发售后工单?',

		            {
		              btn: ['确定','取消'] //按钮

		            },

		            function(index){
		            	orderDetail.distributes();
		              return true;
		            },
		            function(index){
		                layer.msg('已取消！', {icon: 1});
		                return false;
		            }
		        );
	   }
	   orderDetail.distributes = function(){
		 //  popup.openTab("${ctxPath}/pages/order/order-afterservice.jsp","售后工单",null)
		 	if(orderDetail.orderCode==null||orderDetail.orderCode==''){
		 		layer.alert('请先保存工单',{icon:2,time:1000});
		 		return false;
		 	}
		 	  layer.confirm('确认派发售后工单?',

			            {
			              btn: ['确定','取消'] //按钮

			            },

			            function(index){

			              return true;
			            },
			            function(index){
			                layer.msg('已取消！', {icon: 1});
			                return false;
			            }
			        );
		 	var param ={};
		    var contactOrderVO ={};
		   	var contactCallRecordVO ={};
		 	var contactUserRequireVOListTd ={};
			var contactUserRequireVOList={};
			var myArray=new Array();
			var contactProductInfoVO={};
	 		var orgCode=orderDetail.getOrgCode($("#orgCode").val());
			param.updateOrSubmit='submit';

			contactOrderVO.customerLevel='10';
			contactOrderVO.customerType='10';
			contactOrderVO.otherModuleName='04';
			contactOrderVO.otherModuleId=orderDetail.orderCode;
		 	contactOrderVO.contactTypeCode='20';//接入方式
			contactOrderVO.areaNum=$("#areaNum").val();
			contactOrderVO.areaCode=$("#areaCode").val();
			contactOrderVO.areaName=$("#areaName").val();
			contactOrderVO.customerName=$("#CUST_NAME").val();
			contactOrderVO.serviceCustomerName=$("#CUST_NAME").val();
			contactOrderVO.customerMobilephone1=$("#TELEPHONE_NUM").val();
			contactOrderVO.customerMobilephone2=$("#TELEPHONE_NUM2").val();
			contactOrderVO.serviceCustomerMobilephone1=$("#TELEPHONE_NUM").val();
			contactOrderVO.serviceCustomerMobilephone2=$("#TELEPHONE_NUM2").val();
			contactOrderVO.customerPhone=$("#TELEPHONE_NUM").val();
			contactOrderVO.customerAddress=$("#customerAddress").val();
			contactOrderVO.orgCode=orgCode;
			contactOrderVO.isHurry=false;
			contactOrderVO.manualUpgradeFlag='N';
			param.contactOrderVO=contactOrderVO;

			param.contactCallRecordVO=contactCallRecordVO;
			contactUserRequireVOListTd.contactOrderBuyDate=$("#BUY_DATE").val();
			contactUserRequireVOListTd.orgCode=orgCode;
			contactUserRequireVOListTd.brandCode=$("#brandCode").val();
			contactUserRequireVOListTd.brandName=$("#brandName").val();
			contactUserRequireVOListTd.prodCode=$("#prodCode").val();
			contactUserRequireVOListTd.prodName=$("#prodName").val();
			contactUserRequireVOListTd.productCode=$("#productCode").val();
			contactUserRequireVOListTd.productModel=$("#productModel").val();
			contactUserRequireVOListTd.productAmount='1';
			contactUserRequireVOListTd.contactOrderProductUse='10';
			contactUserRequireVOListTd.contactOrderBuyChannel='10';
			contactUserRequireVOListTd.disposeType='11';//处理方式
			contactUserRequireVOListTd.urgentLevel=$("#NEGATIVE_LEVEL").val();	;//紧急程度
			contactUserRequireVOListTd.complaintLevel='13';
			contactUserRequireVOListTd.branchCode=$("#branchCode").val();
			contactUserRequireVOListTd.branchName=$("#branchName").val();
			var TITLE=$("#TITLE").val();
 		 	var complaints=$("#CONTENT").val().split("\n");
 		 	var url=complaints[3].replace("原文链接:","");
 		 	var complaintsCon=complaints[4].replace("原文内容:","");
 		 	contactUserRequireVOListTd.contactOrderServiceDescribe=TITLE+url+complaintsCon;
			var val=$("#SERVICE_TYPE").val();
		 	var val1=$("#COMPLAIN_REASON").val();
		 	var val2=$("#COMPLAIN_TYPE").val();
		 	var val3=$("#COMPLAIN_POINT").val();
		   var val4=$("#SENTIMENT_FROM").val();
		 	bindingInfo(val,val1,val2,val3,val4);
		 	if(''!=bindingData){
			 	var bindingJson=JSON.parse(bindingData);
			 	contactUserRequireVOListTd.contactOrderSerItem2Name=bindingJson.contactOrderSerItem2Name;
			 	contactUserRequireVOListTd.contactOrderSerItem2Code=bindingJson.contactOrderSerItem2Code;
			 	contactUserRequireVOListTd.contactOrderSerItem1Name=bindingJson.contactOrderSerItem1Name;
			 	contactUserRequireVOListTd.contactOrderSerItem1Code=bindingJson.contactOrderSerItem1Code;
			 	contactUserRequireVOListTd.contactOrderServTypeCode=bindingJson.contactOrderServTypeCode;//一级code
			 	contactUserRequireVOListTd.contactOrderServTypeName=bindingJson.contactOrderServTypeName;//一级name
			 	contactUserRequireVOListTd.serviceMainTypeCode=bindingJson.serviceMainTypeCode;
			 	contactUserRequireVOListTd.serviceMainTypeName=bindingJson.serviceMainTypeName;
			 	contactUserRequireVOListTd.serviceSubTypeCode=bindingJson.serviceSubTypeCode;
			 	contactUserRequireVOListTd.serviceSubTypeName=bindingJson.serviceSubTypeName;
				//contactUserRequireVOListTd.contactOrderServiceDesc=bindingJson.contactOrderSerItem2Name;
				contactUserRequireVOListTd.contactOrderServiceDesc=TITLE+url+complaintsCon;
			 	}else{
			 		alertChange();
			 		return false
			 	}
			contactProductInfoVO.areaCode=$("#areaCode").val();
			contactProductInfoVO.areaName=$("#areaNum").val();
			contactProductInfoVO.brandCode=$("#brandCode").val();
			contactProductInfoVO.brandName=$("#brandName").val();
			contactProductInfoVO.contactOrderProductUse='10';
			contactProductInfoVO.orgCode=orgCode;
			contactProductInfoVO.prodCode=$("#prodCode").val();
			contactProductInfoVO.prodName=$("#prodName").val();
			contactProductInfoVO.productAmount='1';
			contactProductInfoVO.machineAddress='广东广州';
			contactProductInfoVO.productCode=$("#productCode").val();
			contactProductInfoVO.productModel=$("#productModel").val();
 		 	contactProductInfoVO.contactOrderBuyChannel=$("#BUY_CHANNEL").val();
			contactUserRequireVOListTd.contactProductInfoVO=contactProductInfoVO;
			myArray.push(contactUserRequireVOListTd);
			param.contactUserRequireVOList=myArray;
			param.ecmOrderVO ={};
			orderDetail.orderCodeOver=orderDetail.orderCode;
			//popup.layerShow({type:2,title:'派发工单',area:['860px','700px'],offset:'1px'},"/neworder/pages/access/order-new.jsp?type=handle",{data:data});
			ajax.remoteCall("/neworder/servlet/contact?action=submitOrder",param,function(result) {
		 		  if(result.state == 1){
						layer.msg(result.msg,{icon: 1},function(){
							//popup.closeTab();
						});
						sentimentOrder();
				  }else{
						layer.alert(result.msg,{icon: 5});
						//orderpopup.btnDisable(true);
				  }
	 		});
	   }
	   orderDetail.getOrgCode=function(orgCode){
	 		ajax.remoteCall("/neworder/servlet/comm?action=GetOrgCode&orgCode="+orgCode,
	 				{orgCode:orgCode},function(result){
				if(result.state =="1"){
					orgCode=result.data;
				}
			},{async:false});
	 		return orgCode
	 	}
	/*    $(function(){
		   if(orderDetail.type==1){
			   $("#addBtn").show();
		   }
	   }) */

	   $(function(){
		   orderDetail.multiSetting2 = {
			   multiple: false,
			   buttonWidth: '220px',
			   allSelectedText: "全部",
			   nonSelectedText: "--请选择--",
			   nSelectedText: "个被选中",
			   selectAllNumber: false,
			   maxHeight: 350,
			   includeSelectAllOption: false,
			   selectAllText: '全选',
			   enableFiltering: true,
			   single: true

		   };
		   upCurrentStatus();
		   $('#areaNum').bind('keyup',function(event){
		        if(event.keyCode == "13")
		        {
		        	$('#enterArea').click();

		        }
		   });
			$("#editDetailForm").data("mars","order.orderInfos");
		   $("#editDetailForm").render({success : function(result){
				   var dataInfo = result["order.orderInfos"]['data'];
				   var SENTIMENT_FROM_STR = dataInfo['order.SENTIMENT_FROM'];
				   var SENTIMENT_FROM_VALUE;
				   var data = {"params":{},"controls":["dataInfo.sentimentChannelList"]}
				   ajax.remoteCall("${ctxPath}/webcall",data,function(result2) {
					   var dataInfo= result2['dataInfo.sentimentChannelList']['data'];
					   var selectElement = $('#SENTIMENT_FROM');
					   var option = $("<option>").val('').text("--请选择--");
					   selectElement.append(option);
					   // 遍历 Map 对象并生成选项
					   dataInfo.forEach(function(item) {
						   if (item.text === SENTIMENT_FROM_STR){
							   SENTIMENT_FROM_VALUE = item.value;
						   }
						   var option = $('<option></option>')
								   .attr('value', item.value)
								   .text(item.text);
						   selectElement.append(option);
					   });
					   selectElement.multiselect(orderDetail.multiSetting2);
				   });
				  $("#editDetailForm").render({success : function(result){
					  var dataInfo = result["order.orderInfos"]['data'];
						  $("#SENTIMENT_FROM").val(SENTIMENT_FROM_VALUE)
						  $("#SENTIMENT_FROM").multiselect("destroy").multiselect({
							  // 自定义参数，按自己需求定义
							  multiple: false,
							  buttonWidth: '220px',
							  allSelectedText: "全部",
							  nonSelectedText: "--请选择--",
							  nSelectedText: "个被选中",
							  selectAllNumber: false,
							  maxHeight: 350,
							  includeSelectAllOption: false,
							  selectAllText: '全选',
							  enableFiltering: true,
							  single: true
						  });
						var isupload='N';
						if(dataInfo['order.SENTIMENT_TYPE']!=null&&dataInfo['order.SENTIMENT_TYPE']=="-1"){
							isupload="Y";
						}else{
							$(".uploadGvoc").addClass("hide");
						}
						if(dataInfo['order.IS_UPLOAD']!=null&&dataInfo['order.IS_UPLOAD']!=""){
							isupload=dataInfo['order.IS_UPLOAD'];
						}
						$('input[name="isUpload"][value="'+isupload+'"]').prop('checked', true);
						// $('#dataSource option').each(function(index) {
						// 	var text = $(this).text();
						// 	if (text == SENTIMENT_FROM) {
						// 		$(this).prop('selected', 'selected');
						// 	}
						// })

						  // 若存在品类，品牌，填入品类、品牌ID
						  var categoryNo = dataInfo['order.PRODUCT_CATEGORY_NO'];
						  if (categoryNo) {
							  $('prodCode').val(categoryNo);
						  }

						  var brandNo = dataInfo['order.PRODUCT_BRAND_NO'];
						  if (brandNo) {
							  $('brandCode').val(brandNo);
						  }

						  // 若存在GPT提取的购买渠道，进行替换
						  var BUY_CHANNEL = dataInfo['order.BUY_CHANNEL'];
						  if (BUY_CHANNEL) {
							  $('#BUY_CHANNEL option').each(function () {
								  if ($(this).text() === BUY_CHANNEL) {
									  $(this).prop('selected', 'selected');
								  }
							  });
						  }

						  // 存在产品主体，代入产品主体
						  var orgCode = dataInfo['order.CC_ORG_CODE'];
						  if (orgCode) {
							  $('#orgCode').val(orgCode);
							  changeOrg(orgCode)
						  }
						  if('yes'==deal.logeType){
							  $("#STATUS").attr("disabled",false);
						  }else{
							  orderInfo();
						  }
						  orderDetail.setData();
				  } });
				}});


			   if('yes'==deal.logeType){
				  $("#STATUS").attr("disabled",false);
			   }else{
				   orderInfo();
			   }

			/*  $("#sendEmail").attr("disabled",true);
			$("#sendMsg").attr("disabled",true);  */


		});
	   orderDetail.productType = function(){
		   	popup.layerShow({type:2,title:'产品品类',shadeClose:false,area:['860px','700px'],offset:'20px'},"/neworder/pages/access/product-type.jsp?orgCode=orgCode&callback=2&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName");
		   	//$('#orgCode').click();
	   }
	 	orderDetail.queryProduct = function(){
	   		var brandCode= $("#brandCode").val();
	   		var orgCode= $("#orgCode").val();
	   		var prodCode= $("#prodCode").val();
	   		var productModel= $("#productModel").val();
	   		if(orgCode==null || orgCode==''){
	   			layer.alert("请先选择产品主体！");
	   			return false;
	   		}
	   		var param = encodeURI("brandCode="+brandCode+"&orgCode="+orgCode+"&prodCode="+prodCode+"&productModel="+productModel+"&productModelId=productModel&productCodeId=productCode");
		   	popup.layerShow({type:2,title:'产品型号查询',shadeClose:false,area:['860px','650px'],offset:'20px'},"/neworder/pages/access/product-list.jsp?"+param);
	   	}
	 	function changRequest(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_REASON").render({data:{id:val}});
	 	}
	 	function changReason(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_TYPE").render({data:{id:val}});
	 	}
	 	function changComplain(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_POINT").render({data:{id:val}});
	 	}

		 // 产品主体改变时，选择中心
	 	function changeOrg(obj){
			var obj1=$("#areaCode").val();
	    	$("#branchCode").render({data:{"orgCode":obj},success:function(result){
	    		changeBranch(obj,obj1);
	    	}});


	 	}
	 	function userName(){
	 		var option=$("#orgCode option:selected");
			var DEPARTMENT=option.text();
	 		var option1=$("#branchCode option:selected");
			var SUB_CENTER=option1.text();
	 		$.ajax({
				url:"${ctxPath}/servlet/order?query=getUserName",
				data:{"DEPARTMENT":DEPARTMENT,"SUB_CENTER":SUB_CENTER},
				async:false,
				success:function(result) {
					if(result.state == 1){
						$("#CREATE_NAME").val(result.msg);
						$("#userId").val(result.data);
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 	orderDetail.ajaxSubmitForm = function(val){
	 		if($("#nickname").val()==""){
	 			layer.msg("网络名不能为空", {icon : 2});
	              return false;
	 		}
	 		if(val=='05'){
				if (!form.validate("editDetailForm")){
					return false;
				}
	 		}
	 	  	var ago=$("#TELEPHONE_NUM").val();
	 		var ago1=$("#TELEPHONE_NUM2").val();
	 		 var myreg=/^[1][0-9]{10}$/;
	 		 var myreg1=/^0\d{2,3}-?\d{7,8}$/;

	 		if(ago!=''&&ago!=null){
	        	  if (!myreg.test(ago)&&!myreg1.test(ago)) {
					layer.msg("号码1格式不正确", {icon : 2});
	              return false;
	          }
	         }
	          if(ago1!=''&&ago1!=null){
	        	  if (!myreg.test(ago1)&&!myreg1.test(ago1)) {
						layer.msg("号码2格式不正确", {icon : 2});
		              return false;
		          }
	          }
	          orderDetail.insertData (val);

		}

	   orderDetail.setData = function(){
		   var data = form.getJSONObject("editDetailForm");
		   var option=$("#orgCode option:selected");
		   var PRODUCT_DIVISION=option.text();
		   if('请选择'==PRODUCT_DIVISION){
			   PRODUCT_DIVISION='';

		   }
		   var IP_LOCATION='';
		   var LIKE_COUNT='';
		   var RELEASE_ACCOUNT='';
		   var NEGATIVE_WORDS='';
		   var url='';
		   var complaintsCon='';
		   var complaints=$("#CONTENT").val().split("\n");
		   var SENTIMENT_FROM_NAME =$("#SENTIMENT_FROM option:selected").text();
		   if(complaints!=null&&complaints.length>0){
			   for(var i=0;i<complaints.length;i++){
				   if(complaints[i].indexOf("IP属地：")!=-1 ){
					   const str = complaints[i];
					   const match = str.match(/IP属地[:：]\s*(.+)/);
					   IP_LOCATION = match ? match[1] : '';
				   }
				   if(complaints[i].indexOf("点赞量：")!=-1){
					   const str = complaints[i];
					   const match = str.match(/点赞量[:：]\s*(\d+)/);
					   LIKE_COUNT = match ? match[1] : '';
				   }
				   if(complaints[i].indexOf(SENTIMENT_FROM_NAME+"号：")!=-1){
					   var name = SENTIMENT_FROM_NAME+"号";
					   const str = complaints[i];
					   const match = str.match(new RegExp(`${name}[:：]\\s*(\\d+)`));
					   RELEASE_ACCOUNT = match ? match[1] : '';
				   }
				   if(complaints[i].indexOf("负面词:")!=-1){
					   const str = complaints[i];
					   const match = str.match(/负面词[:：]\s*(.+)/);
					   NEGATIVE_WORDS = match ? match[1] : '';
				   }
				   if (complaints[i].indexOf("原文链接:")!=-1){
					   const str = complaints[i];
					   const match = str.match(/原文链接[:：]\s*(.+)/);
					   url = match ? match[1] : '';
				   }
				   if (complaints[i].indexOf("原文内容:")!=-1){
					   const str = complaints[i];
					   const match = str.match(/原文内容[:：]\s*(\d+)/);
					   complaintsCon = match ? match[1] : '';
				   }
			   }
			   data.url=url;
			   data.complaintsCon=complaintsCon;
		   }

		   var option1=$("#branchCode option:selected");
		   var DEPARTMENT=option1.text();

		   var option2=$("#SERVICE_TYPE option:selected");
		   var SERVICE_TYPE=option2.text();
		   var option3=$("#COMPLAIN_REASON option:selected");
		   var COMPLAIN_REASON=option3.text();
		   var option4=$("#COMPLAIN_TYPE option:selected");
		   var COMPLAIN_TYPE=option4.text();
		   var option5=$("#COMPLAIN_POINT option:selected");
		   var COMPLAIN_POINT=option5.text();
		   var REPLENISH_CONTENT=$("#REPLENISH_CONTENT").val();
		   var BRANCH_NAME = $("#branchCode option:selected").text();

		   var SERVICE_TYPE_NAME = $("#SERVICE_TYPE option:selected").text();
		   var UPGRADETYPE1_NAME = $("#upgradeType1 option:selected").text();
		   var UPGRADETYPE2_NAME = $("#upgradeType2 option:selected").text();

		   data.PRODUCT_DIVISION=checkParams(PRODUCT_DIVISION);
		   data.DEPARTMENT=checkParams(DEPARTMENT);
		   data.SERVICE_TYPE=checkParams(SERVICE_TYPE);
		   data.COMPLAIN_REASON=checkParams(COMPLAIN_REASON);
		   data.COMPLAIN_TYPE=checkParams(COMPLAIN_TYPE);
		   data.COMPLAIN_POINT=checkParams(COMPLAIN_POINT);
		   data.REPLENISH_CONTENT = checkParams(REPLENISH_CONTENT);
		   data.BRANCH_NAME = checkParams(BRANCH_NAME);
		   data.SENTIMENT_FROM_NAME = checkParams(SENTIMENT_FROM_NAME);
		   data.SERVICE_TYPE_NAME = checkParams(SERVICE_TYPE_NAME);
		   data.UPGRADETYPE1_NAME = checkParams(UPGRADETYPE1_NAME);
		   data.UPGRADETYPE2_NAME = checkParams(UPGRADETYPE2_NAME);
		   data.IP_LOCATION=checkParams(IP_LOCATION);
		   data.LIKE_COUNT=checkParams(LIKE_COUNT);
		   data.RELEASE_ACCOUNT=checkParams(RELEASE_ACCOUNT);
		   data.NEGATIVE_WORDS=checkParams(NEGATIVE_WORDS);
		   data.orderId=orderDetail.orderCode;
		   data.SENTIMENT_ID=orderDetail.orderId;
		   data.insert=deal.insert;
		   $("#PROCESS_NAME").val();
		   var isupload=$('input[name="isUpload"]:checked').val();
		   data.isupload=isupload;
		   orderDetail.orderInfos = data;
	   }
	   function checkParams(val){
		   if(val=='请选择'){
			   return '';
		   }
		   return val;
	   }

	 	orderDetail.insertData = function(val) {
	 		var order
	 		if(''==orderDetail.orderCode){
	 			$.ajax({
					url:"${ctxPath}/servlet/order?query=GetOrderCode",
					data:{},
					async:false,
					success:function(result) {
						if(result.state == 1){
							 orderDetail.orderCode=result.msg;
						}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
			});
	 		}

			var data = form.getJSONObject("editDetailForm");
			var option=$("#orgCode option:selected");
			var PRODUCT_DIVISION=option.text();
			if('请选择'==PRODUCT_DIVISION){
				PRODUCT_DIVISION='';

			}
			var dataSources = $("#DATA_SOURCES").val();
	 		var option1=$("#branchCode option:selected");
			var DEPARTMENT=option1.text();

	 		var option2=$("#SERVICE_TYPE option:selected");
			var SERVICE_TYPE=option2.text();
	 		var option3=$("#COMPLAIN_REASON option:selected");
			var COMPLAIN_REASON=option3.text();
	 		var option4=$("#COMPLAIN_TYPE option:selected");
			var COMPLAIN_TYPE=option4.text();
	 		var option5=$("#COMPLAIN_POINT option:selected");
			var COMPLAIN_POINT=option5.text();
			data.PRODUCT_DIVISION=PRODUCT_DIVISION;
			data.DEPARTMENT=DEPARTMENT;
			data.SERVICE_TYPE=SERVICE_TYPE;
			data.COMPLAIN_REASON=COMPLAIN_REASON;
			data.COMPLAIN_TYPE=COMPLAIN_TYPE;
			data.COMPLAIN_POINT=COMPLAIN_POINT;
			data.STATUS=val;
			data.ID=orderDetail.orderCode;
			data.DATA_SOURCES = dataSources;
			$("#PROCESS_NAME").val();
			if('01'==val){
				upgradeTypeSe1();
			}
			if('05'==val){
				bury_management_confirm();
			}
			data.STATUS=val;
			data.orderId=orderDetail.orderCode;
			data.SENTIMENT_ID=orderDetail.orderId;
			data.insert=deal.insert;
			data.DATA_SOURCES = dataSources;
			var isupload=$('input[name="isUpload"]:checked').val();
			data.isupload=isupload;
			//var index = parent.layer.getFrameIndex(window.name);
			orderDetail.setData();
			ajax.remoteCall("${ctxPath}/servlet/order?action=addOrderNeg",data,function(result) {
				//debugger;
					if(result.state == 1){
						if(val == '05' || val == '01'){
							toGvocSentiment();
						}
						debugger
						$("#ID").val(result.msg);
						layer.alert("操作成功",{icon: 1,time:1000},function(){
						});
					/*  	$("#save").attr("disabled",true);
						$("#staging").attr("disabled",true);
						if('01'==$("#NEED_UPGRADE").val()){
							$("#sendEmail").attr("disabled",false);
							$("#sendMsg").attr("disabled",false);
						}  */
						setTimeout(function(){
							   var frames=$(parent.document.getElementById('pageLoader')).find('iframe');
							    for(var i=0; i<frames.length; i++){
							    	var item=frames[i];
							    	if(/deal-list/.test(item.src)){
							    		//item.contentWindow.location.reload();
							    		item.contentWindow.selectList('1');
							    	}
							    }
						},1000);
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }

	 	function orderInfo(){
	 		var data={};
	 		data.id=orderDetail.id;
	 		ajax.remoteCall("${ctxPath}/servlet/order?query=getOrderPos",data,function(result) {
				//debugger;
					if(result.state == 1){
						var ret = result.data;
	 					var str="";
						var dataSourcesSet = new Set();
						for(var i=0;i<ret.length;i++){
							var ob=ret[i];
							var content=ob.CONTENT.replace(/(^\s*)/g,"");
							/* console.log(JSON.stringify(result)); */
							str+="网络名: "+ob.CREATER+"\n";
							str+="来源: "+ob.SENTIMENT_FROM+"\n";
							str+="发布时间: "+ob.CREATE_TIME+"\n";
							str+="原文链接: "+ob.URL+"\n";
							str+="原文内容:"+content+"\n\r";
							// 收集 DATA_SOURCES 值
							if(ob.DATA_SOURCES) {
								dataSourcesSet.add(ob.DATA_SOURCES);
							}
						}

						// 比较 DATA_SOURCES 值
						if(dataSourcesSet.size > 0) {
							var allSame = true;
							var firstValue = Array.from(dataSourcesSet)[0];

							// 检查是否所有值都相同
							dataSourcesSet.forEach(function(value) {
								if(value !== firstValue) {
									allSame = false;
								}
							});

							// 如果值不同或与当前页面值不同，设置为 "03"
							if(!allSame) {
								$("#DATA_SOURCES").val("03");
							}else{
								$("#DATA_SOURCES").val(firstValue);
							}
						}

					$("#CONTENT").html(str);
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}

	   function bury_management_confirm(){
		   if (top.collectEvent) {
			   top.collectEvent("click_public_opinion_management_confirm", {
				   status:1,
				   inputTokens:0,
				   outputTokens:0,
				   opType:-1,
				   opTotalTime:0,
				   opTimeEfficiencyValue:"350s",
				   opEfficiencyValue:1,
			   });
		   }
	   }

	function phoneCode(){
	 		var areaNum =$("#areaNum").val();
	 		popup.layerShow({type:2,title:'电话区号',shadeClose:false,area:['860px','660px'],offset:'20px'},"/neworder/pages/access/phone-code.jsp?callback=2&areaCodeId=areaCode&areaNameId=areaName&areaNumId=areaNum&areaNum="+areaNum);
		}


	var bindingData='';
	function bindingInfo(val,val1,val2,val3,val4){
			$.ajax({
				url:"${ctxPath}/servlet/binding?query=bindingInfo",
				data:{"SERVICE_TYPE":val,"COMPLAIN_REASON":val1,"COMPLAIN_TYPE":val2,"COMPLAIN_POINT":val3,"DATASOURCE":val4},
				async:false,
				success:function(result) {
					if(result.state == 1){
						bindingData= result.data;
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	}
	function closeAll(){
		var index = parent.layer.getFrameIndex(window.name);
		parent.layer.close(index);
		window.parent.location.reload();

	}
	function alertChange(){
		layer.confirm('诉求信息未填写,是否派单?',

	            {
	              btn: ['确定','取消'], //按钮
	              time:5000

	            },

	            function(index){
	            	var dataList ={};
	     		    var data ={};
	     		   	var customerVO ={};
	     		 	var requireVO ={};
	    	 		var orgCode=orderDetail.getOrgCode($("#orgCode").val());

	     		 	customerVO.areaNum=$("#areaNum").val();
	     		 	customerVO.areaCode=$("#areaCode").val();
	     		 	customerVO.areaName=$("#areaName").val();
	     		 	customerVO.customerName=$("#CUST_NAME").val();
	     		 	customerVO.customerAddress=$("#customerAddress").val();
	     		 	customerVO.serviceCustomerName=$("#CUST_NAME").val();
	     		 	customerVO.customerPhone=$("#TELEPHONE_NUM").val();
	     		 	customerVO.serviceCustomerMobilephone1=$("#TELEPHONE_NUM").val();
	     		 	customerVO.serviceCustomerMobilephone2=$("#TELEPHONE_NUM2").val();
	     		 	customerVO.customerMobilephone1=$("#TELEPHONE_NUM").val();
	     		 	customerVO.customerMobilephone2=$("#TELEPHONE_NUM2").val();
	     		 	customerVO.contactTypeCode='20';
	     		 	customerVO.customerLevel='10';
	     		 	customerVO.customerType='10';

	     		 	requireVO.urgentLevel=$("#NEGATIVE_LEVEL").val();
	     		 	requireVO.disposeType='11';
	     		 	requireVO.brandCode=$("#brandCode").val();
	     		 	requireVO.brandName=$("#brandName").val();
	     		 	requireVO.prodCode=$("#prodCode").val();
	     		 	requireVO.prodName=$("#prodName").val();
	     		 	requireVO.productModel=$("#productModel").val();
	     		 	requireVO.productCode=$("#productCode").val();
	     		 	requireVO.orgCode=orgCode;
	     		 	requireVO.contactOrderBuyDate=$("#BUY_DATE").val();
	     			var TITLE=$("#TITLE").val();
	     		 	var complaints=$("#CONTENT").val().split("\n");
	     		 	var url=complaints[3].replace("原文链接:","");
	     		 	var complaintsCon=complaints[4].replace("原文内容:","");
	     		 	requireVO.contactOrderServiceDescribe=TITLE+url+complaintsCon;

	     		 	requireVO.contactOrderBuyChannel=$("#BUY_CHANNEL").val();
	     		 	requireVO.contactOrderServTypeCode='TS';
	     		   	data.customerVO=customerVO;
	     		   	data.requireVO=requireVO;
	     		 	dataList.moduleName ='04';
	     		 	dataList.moduleId=orderDetail.orderCode;
	     		 	dataList.type='other';
	     		 	dataList.data=encodeURIComponent(JSON.stringify(data));
	     		 	dataList.customerTel=$("#TELEPHONE_NUM").val();
	     		 	orderDetail.orderCodeOver=orderDetail.orderCode;
					console.log(dataList,"dataList")
					popup.openTab("/neworder/pages/access/order-new.jsp","派发工单",dataList);
					// popup.openTab("/neworder/pages/access/order-new.jsp","派发工单",dataList);
					// if(!top.pageControl || !top.pageControl.getEnv){
					// 	popup.openTab("/neworder/pages/access/order-new.jsp","派发工单",dataList);
					// }else {
					// 	dataList.callId = Math.random().toString(36).slice(2,9);
					// 	dataList.title = "派发工单";
					// 	top.pageControl.openVoiceOrder(dataList)
					// }
	              return true;
	            },
	            function(index){
	                layer.msg('已取消！', {icon: 1});
	                return false;
	            }
	        );
	}
	function areaNumCallbackFunc(){
 		$("#customerAddress").val($("#areaName").val());

		var obj1=$("#areaCode").val();
		var obj=$("#orgCode").val();
		changeBranch(obj,obj1);
	}

	function changeBranch(orgCode,regoinCode){
		if(orgCode!=''&&regoinCode!=''){
			var data = {};
			data.regoinCode = regoinCode ;
			data.orgCode =  getOrgCodeByCC(orgCode);
			ajax.remoteCall("/neworder/servlet/comm?query=getBranchByRegion",data,function(result){
				if(result.state == 1){
					var list = result.data;
					if(list && list.length >0 ){
						$('#branchCode option').each(function(index) {
							var val = $(this).val();
							if (val == list[0].branchCode) {
								$(this).prop('selected', 'selected');
							}
						});
					}
				}
			});
		}
	}
	function getOrgCodeByCC(orgCode){
 		ajax.remoteCall("/neworder/servlet/comm?action=GetOrgCode&&orgCode="+orgCode,
 				{orgCode:orgCode},function(result){
			if(result.state =="1"){
				orgCode=result.data;
			}
		},{async:false});
 		return orgCode
 	}
	function reDoing(){
		ago2=true;
	}
	function reDoing1(){
		ago3=true;
	}

 	function upCurrentStatus() {
	 	$.ajax({
			url:"${ctxPath}/servlet/order?query=UpStatus",
			data:{"id":orderDetail.id},
			async:false,
			success:function(result) {
			}
	});
}
 	function closeThis(){
 		$.ajax({
			url:"${ctxPath}/servlet/order?query=UpStatus1",
			data:{"id":orderDetail.id},
			async:false,
			success:function(result) {
			}
	});
 		popup.closeTab();

 	}
	function tell(ago){
		console.info('调用'+ago);
		var valTp='';
		if('1'==ago){
			valTp=$("#TELEPHONE_NUM").val();
		}else{
			valTp=$("#TELEPHONE_NUM2").val();
		}
		console.info('第一'+valTp);
		layer.prompt({formType: 2,value: valTp,title: ['外呼','color:red'],area: ['300px', '30px']} //自定义文本域宽高
		, function(value, index, elem){
			console.info('第二'+valTp);
			top.myCCbar.call(valTp,{},function(result){
				if(result.state=='1'){
					  layer.close(index);
				}else{
					layer.alert("外呼失败",{icon:2});
				}
			})

	});
	}

	function upgradeTypeSe(val){
	$("#upgradeType2").render({data:{"id":val},success:function(result){

		}});
	}
	function productSelectDoneFunc(orgCode,brandCode,brandName,prodCode,prodName){
 		var obj1=$("#areaCode").val();
		$("#branchCode").render({data:{"orgCode":orgCode},success:function(result){
    		changeBranch(orgCode,obj1);
    	}});
	}
	function sentimentOrder(){
		$.ajax({
			url:"${ctxPath}/servlet/order?action=ChangeStatus",
			data:{"id":orderDetail.orderCodeOver},
			async:false,
			success:function(result) {
				if(result.state == 1){
					 var frames=$(parent.document.getElementById('pageLoader')).find('iframe');
					   for(var i=0; i<frames.length; i++){
					    	var item=frames[i];
					    	if(/ordertype-list2/.test(item.src)||/ordertype-list3/.test(item.src)||/orderdeal-list/.test(item.src)||/orderdeal-list3/.test(item.src)||/orderdeal-list2/.test(item.src)||/deal-list/.test(item.src)){
					    		item.contentWindow.selectList('-1');
					    		//break;
					    	}
					    }
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
	});
	}
	function upgradeTypeSe1(){
		ago2=true;
		ago3=true;
	}
	function  rulesPhone(ths){
		var ago=$(ths).val();
 		 var myreg=/^[1][0-9]{10}$/;
 		 var myreg1=/^0\d{2,3}-?\d{7,8}$/;
 		  if(ago!=''&&ago!=null){
        	  if (!myreg.test(ago)&&!myreg1.test(ago)) {
					layer.msg("号码格式不正确", {icon : 2});
	          }
          }
	}
 	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>