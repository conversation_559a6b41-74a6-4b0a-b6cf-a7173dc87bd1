<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>舆情话语配置</title>
    <link rel="stylesheet"
          href="/neworder/static/lib/layui/css/layui.css">
    <link rel="stylesheet"
          href="/neworder/static/lib/zTree_v3/css/demo.css">
    <script src="/easitline-static/js/jquery.min.js"></script>
    <script type="text/javascript"
            src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
    <style type="text/css">
        .layui-form-label {
            width: 120px;
        }

        .layui-input-block {
            margin-left: 130px;
        }

        .red {
            color: red;
        }

        .ew-tree-table .ew-tree-pack {
            max-width: 90%;
        }

        .ew-tree-table-td-single > .ew-tree-tips {
            text-overflow: initial !important;
        }

        .ew-tree-tips::-webkit-scrollbar {
            /*滚动条整体样式*/
            width: 4px;
            /*高宽分别对应横竖滚动条的尺寸*/
            height: 4px;
        }

        .ew-tree-tips::-webkit-scrollbar-thumb {
            /*滚动条里面小方块*/
            border-radius: 10px;
            background-color: skyblue;
            background-image: -webkit-linear-gradient(45deg, rgba(255, 255, 255, 0.2) 25%, transparent 25%, transparent 50%, rgba(255, 255, 255, 0.2) 50%,
            rgba(255, 255, 255, 0.2) 75%, transparent 75%, transparent);
        }

        .ew-tree-tips::-webkit-scrollbar-track {
            /*滚动条里面轨道*/
            box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
            background: #ededed;
            border-radius: 10px;
        }

        .layui-form-item {
            margin-bottom: 5px !important
        }
        .ew-tree-table .ew-tree-pack {
            display: flex !important;
        }

        .ew-tree-table .ew-tree-pack > span {
            height: auto !important;
            line-height: 16px;
            display: inline-block;
            vertical-align: middle;
            white-space: pre-wrap;
        }

        .ew-tree-table-td-single > .ew-tree-tips {
            overflow: visible !important;
            white-space: pre-wrap !important;
        }


    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <body style="padding: 15px;">
    <div class="ibox">
        <div class="ibox-title clearfix">
            <div class="form-group">
                <h5><span class="glyphicon glyphicon-list"></span>舆情话语配置</h5>
            </div>
            <div class="ibox-content">
                <div style="display: flex;margin-bottom: 10px;justify-content: space-between;">
                    <div class="input-group input-group-sm pull-right mr-10">
                        <input type="text" name="searchWord" id="searchWord" class="form-control input-sm"
                               placeholder="请输入关键字..." style="width:150px">
                        <button type="button" class="btn btn-sm btn-default" onclick="sentimentDialog.searchDialog()">
                            <span class="glyphicon glyphicon-search"></span> 搜索
                        </button>
                    </div>
                    <div style="margin-bottom: 10px; text-align: right;">
                        <button type="button" class="btn btn-sm btn-success" onclick="sentimentDialog.expand()">
                            +全部展开
                        </button>
                    </div>

                </div>
                <div class="page-wrapper">
                    <table id="demoTreeTable1"></table>
                </div>
            </div>
        </div>
    </div>

    <!-- 表格操作列 -->
    <script type="text/html" id="demoTreeTableBar1">
        {{# if(d.pid != '0') { }}
        <a class="layui-btn layui-btn-success layui-btn-xs" lay-event="view" lay-html="">查看</a>
        <a class="layui-btn layui-btn-warm layui-btn-xs" lay-event="callOut" lay-html="">代入</a>
        {{# } }}
    </script>

    <div id="showBox" style="display: none; padding: 20px 20px 0px 0px;">
        <form class="layui-form" action="" lay-filter="TheForm" id="dialogForm">
            <div class="layui-form-item">
                <label class="layui-form-label"><span class="red">*</span>关键字</label>
                <div class="layui-input-block">
                    <textarea type="text" name="keywords" autocomplete="off" class="layui-input"
                              data-rules="required" disabled>
                    </textarea>
                </div>
            </div>
            <div class="layui-form-item" id="contentBox">
                <label class="layui-form-label"><span class="red">*</span>话术内容</label>
                <div class="layui-input-block">
                    <textarea name="dialog.DIALOGUE_CONTENT" id="content" autocomplete="off" class="layui-input"
                              style="height: 250px"
                              data-rules="required" maxlength="1200" disabled></textarea>
                </div>
            </div>
            <div class="layui-form-item">
                <div class="layui-input-block"
                     style="text-align: center; margin-left: 0;">
                    <button class="layui-btn layui-btn-normal layui-btn-sm" type="button"
                            onclick="sentimentDialog.close()">关闭
                    </button>
                </div>
            </div>
        </form>
    </div>

</EasyTag:override>

<EasyTag:override name="script">
    <script src="/neworder/static/lib/layui/layui.js"></script>
    <script src="/neworder/static/lib/layui/treeTable/treeTable.js"></script>
    <script type="text/javascript">
        jQuery.namespace("sentimentDialog");

        sentimentDialog.close = function () {
            layer.closeAll();
        }

        // 展开所有话术
        sentimentDialog.expand = function () {
            insTb.expandAll();
        }

        // 搜索关键词
        let searchKeywords = '';

        // 搜索话术
        sentimentDialog.searchDialog = function () {
            searchKeywords = $('#searchWord').val();
            refresh();
        }

        // 代入话术
        sentimentDialog.callOut = function (content) {
            window.parent.orderDetail.callIn(content);
        }

        let currentItem;
        var insTb;

        layui.config({
            base: '/neworder/static/lib/layui/'
        }).extend({
            treeTable: 'treeTable/treeTable'
        }).use(['layer', 'util', 'treeTable', 'form'], function () {
            var $ = layui.jquery;
            var layer = layui.layer;
            var util = layui.util;
            var form = layui.form;
            var treeTable = layui.treeTable;

            // 渲染表格
            insTb = treeTable.render({
                elem: '#demoTreeTable1',
                tree: {
                    arrowType: 'arrow2',
                    idName: 'id',
                    iconIndex: 0,
                    pidName: 'pid',
                    treeDefaultClose: false,
                    isPidData: true // 是否是pid形式数据
                },
                text: {},
                cols: [
                    {
                        field: 'keywords',
                        title: '关键字',
                        width: '20%',
                        templet: function (row) {
                            if (row.keywords.length > 50) {
                                return row.keywords.substring(0, 50) + "..."
                            } else {
                                return row.keywords;
                            }
                        }
                    },
                    {
                        field: 'content',
                        title: '话术',
                        width: '55%',
                        templet: function (row) {
                            if (row.content.length > 200) {
                                return row.content.substring(0, 200)
                            } else {
                                return row.content;
                            }
                        }
                    },
                    {
                        field: 'enabled',
                        title: '是否开启',
                        width: 90,
                        align: 'center',
                        templet: function (row) {
                            if (row.enabled === 'Y') {
                                return '是';
                            } else {
                                return '否';
                            }
                        }
                    },
                    {
                        align: 'center',
                        toolbar: '#demoTreeTableBar1',
                        title: '操作',
                    }
                ],
                reqData: function (data, callback) {
                    ajax.remoteCall('${ctxPath}/servlet/sentiment/dialog?action=DialogList', {keywords: searchKeywords, enabled: 'Y'}, function (res) {
                        callback(res.data);
                    });
                },
                style: 'margin-top:0;'
            });

            //监听工具条
            treeTable.on('tool(demoTreeTable1)', function (
                obj) { //注：tool 是工具条事件名，test 是 table 原始容器的属性 lay-filter="对应的值"
                var thedata = obj.data; //获得当前行数据
                var layEvent = obj.event; //获得 lay-event 对应的值（也可以是表头的 event 参数对应的值）
                var tr = obj.tr; //获得当前行 tr 的 DOM 对象（如果有的话）

                if (layEvent === 'callOut') {
                    currentItem = thedata;
                    if (thedata.enabled === 'N') {
                        layer.msg('该话术暂未启用', {icon: 1});
                        return;
                    }
                    sentimentDialog.callOut(thedata.content);

                } else if (layEvent === 'view') { //添加
                    currentItem = thedata;
                    form.val('TheForm', {
                        "keywords": currentItem.keywords,
                        "dialog.DIALOGUE_CONTENT": currentItem.content
                    });

                    layer.open({
                        type: 1,
                        title: '查看话术',
                        content: $('#showBox'),
                        area: ['600px', '450px']
                    });

                }
            });

        });

        function refresh(pId) {
            if (pId && pId != "0") {
                insTb.refresh(pId);
            } else {
                insTb.refresh();
            }
        }


    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>