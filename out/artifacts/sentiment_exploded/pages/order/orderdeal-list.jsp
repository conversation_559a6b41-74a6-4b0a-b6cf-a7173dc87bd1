<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>工单处理</title>
	<style>
	     .dropdown-icon>li>.addon{margin-top:-0.7em}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 工单查询</h5>
	             		       <div class="input-group  pull-right">
	             		            <button class="btn btn-sm btn-success" type="button" onclick=""><span class="glyphicon glyphicon-export"></span> 导出全模板</button>	             		            
	             		       </div>
	             		       <div class="input-group  pull-right mr-10">
	             		            <button class="btn btn-sm btn-success" type="button" onclick=""><span class="glyphicon glyphicon-export"></span> 导出精简版</button>
	             		       </div>
	             		       <div class="input-group  pull-right mr-10">
	             		            <button class="btn btn-sm btn-success" type="button" onclick=""> +新建工单</button>	             		            
	             		       </div>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             		 <div class="form-group">
             		           <div class="input-group ">
								      <span class="input-group-addon">网络名</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:120px">
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">用户名</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:120px">
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">服务请求</span>	
									  <select class="form-control input-sm" name="" data-mars="" onchange="">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">联系电话</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:90px">
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">数据来源</span>	
									  <select class="form-control input-sm" name="" data-mars="" onchange="">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick=""><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							   <div class="input-group  pull-right">
								 	<button type="button" id="moreBtn" class="btn btn-sm btn-link" onclick="toggleMore()" title="展开"> 展开 <span class="glyphicon glyphicon glyphicon-menu-up"></span>&nbsp;</button>
							  </div>
						  </div>
						  <div class="form-group moreSearch" style="display:none">
						    
             		           <div class="input-group ">
								      <span class="input-group-addon">产品主体</span>	
									  <select class="form-control input-sm" name="" data-mars="" onchange="">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							     <div class="input-group ">
								      <span class="input-group-addon">产品类别</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:120px">
									  <span class="input-group-addon" style="cursor:pointer">请选择</span>	
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">工单状态</span>	
									  <select class="form-control input-sm" name="" data-mars="" onchange="">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">受理操作员</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:90px">
							   </div>
							  <div class="input-group ">
								      <span class="input-group-addon">处理操作员</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:90px">
							   </div>
						   </div>
						   <div class="form-group  moreSearch" style="display:none">
						     
							   <div class="input-group ">
             		              <span class="input-group-addon">创建日期</span>	
								  <input type="text" name="beginTime" id="beginTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:128px">
             		              <span class="input-group-addon">-</span>	
								  <input type="text" name="endTime" id="endTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:128px">
							  </div>
							  <div class="input-group ">
								      <span class="input-group-addon">是否有客户资料</span>	
									  <select class="form-control input-sm" name="" data-mars="" onchange="">
                                          <option value="">是</option>
                                      </select>
							   </div>
							   <div class="input-group ">
								   <span class="input-group-addon">舆情工单号</span>
								   <input type="text" id="ID" name="ID" class="form-control input-sm" style="width:120px">
							   </div>
							   <div class="input-group ">
								   <span class="input-group-addon">升级投诉工单号</span>
								   <input type="text" id="COMPLAINT_UPGRADE_NO" name="COMPLAINT_UPGRADE_NO" class="form-control input-sm" style="width:120px">
							   </div>
							 
							</div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="">
                             <thead>
	                         	 <tr>
	                         	      <th>网络名</th>
								      <th>原文链接</th>
								      <th>是否有客户资料</th>
								      <th>用户</th>
								      <th>联系电话1</th>
								      <th>联系电话2</th>
								      <th>产品主体</th>
								      <th>产品大类</th>
								      <th>投诉原因</th>
								      <th>工单状态</th>
								      <th>受理操作员</th>
								      <th>工单类型</th>
								      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                  <tr>
                                      <td><a  href="javascript:orderDeal.editData()">张天元4</a></td>
                                      <td>http://weibo.com/5...</td>
                                      <td>是</td>
                                      <td>刘海丽</td>
                                      <td>17872643287</td>
                                      <td>美美家园</td>
                                      <td>美的空调</td>
                                      <td>服务原因</td>
                                      <td>已暂存</td>
                                      <td>已暂存</td>
                                      <td>1246</td>
								      <td>负向舆情</td>
								      <td><a  href="javascript:orderDeal.editData()">处理</a></td>
                                  </tr>
                                   <tr>
                                      <td><a  href="javascript:orderDeal.editData()">张天元5</a></td>
                                      <td>http://weibo.com/6...</td>
                                      <td>是</td>
                                      <td>刘海丽</td>
                                      <td>17872643287</td>
                                      <td>美美家园</td>
                                      <td>美的空调</td>
                                      <td>服务原因</td>
                                      <td>已暂存</td>
                                      <td>已暂存</td>
                                      <td>1246</td>
                                      <td>正向舆情</td>
                                      <td><a  href="javascript:orderDeal.editData()">处理</a></td>
                                  </tr>
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("orderDeal");
	    
		requreLib.setplugs("wdate")
	
        orderDeal.editData = function(){
			popup.layerShow({type:1,title:'舆情处理',shadeClose:false,area:['90%','820px'],offset:'10px'},"${ctxPath}/pages/order/order-deal.jsp",null);		
		}
		 
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon-menu-down glyphicon glyphicon-menu-up")
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>