<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>升级跟进信息</title>
    <style>
        #orderAdditionForm th {
            overflow: hidden;
        }

        #orderAdditionForm td {
            overflow: hidden;
        }

        .w-120 {
            mix-width: 50px
        }

        .w-500 {
            max-width: 600px;
            mix-width: 100px
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="orderAdditionForm" class="form-inline table-responsive" id="orderAdditionForm"
          onsubmit="return false" data-toggle="">
        <input class="hidden" name="ORDER_ID" value="${param.id}"/>
        <div class="input-group  pull-right" style="padding-left: 10px; margin-bottom: 5px">
            <button class="btn btn-sm btn-success" style="margin-right: 10px" id="staging" type="button" onclick="orderAddition.render()">
                更新
            </button>
            <button id="downButton" class="btn btn-sm btn-success" type="button" onclick="orderAddition.downloadExl()">
                <span class="glyphicon glyphicon-export"></span> 导出
            </button>
        </div>
        <div class="ibox-content" style="padding: 0px">
            <table class="table table-auto table-bordered table-hover table-condensed"
                   data-mars="orderInf.orderAddition"
                   data-container="#cTab1_dataList" data-template="cTab1_list-template">
                <thead>
                <tr>
                    <th class="text-c">序号</th>
                    <th class="text-c w-120">舆情工单号</th>
                    <th class="text-c w-120">发帖时间</th>
                    <th class="text-c w-500">原文链接</th>
                    <th class="text-c w-500">升级处理方式</th>
                    <th class="text-c w-500">处理操作员</th>
                    <th class="text-c w-500">升级类型分类</th>
                    <!-- <th class="text-c">操作</th> -->
                </tr>
                </thead>
                <tbody id="cTab1_dataList">

                </tbody>
            </table>
            <script id="cTab1_list-template" type="text/x-jsrender">
                {{for list}}
                   <tr>
                      <td style="width:220px;max-width:220px;text-align: center;">{{:#index+1}}</td>
                      <td style="width:220px;max-width:220px;text-align: center;" title="{{:ORDER_ID}}">{{:ORDER_ID}}</td>
                      <td style="width:220px;max-width:220px;text-align: center;" title="{{:DISTRIBUTED_TIME}}">{{:DISTRIBUTED_TIME}}</td>
                      <td style="width:220px;max-width:220px;text-align: center;" title="{{:URL}}">{{:URL}}</td>
                      <td style="width:220px;max-width:220px;text-align: center;">{{dictFUN:UPGRADE_METHOD "UPGRADE_METHOD_TYPE"}}</td>
                      <td style="width:220px;max-width:220px;text-align: center;" title="{{:CREATE_ACC}}">{{:CREATE_ACC}}</td>
                      <td style="width:220px;max-width:220px;text-align: center;">{{custValFun:UPGRADETYPE1 "order.upgradeType1"}}</td>
                   </tr>
                {{/for}}
            </script>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript">
        jQuery.namespace("orderAddition");
        var orderId = ${param.id}
        $(function () {
            $("#orderAdditionForm").render({});
        });
        requreLib.setplugs("wdate")
        orderAddition.render = function () {
            orderAddition.getCache();
            $("#orderAdditionForm").render({})
        }

        $.views.converters("TIME", function (val) {
            if (val != null) {
                return formatDate(val);
            }
            return "";

        });
        function formatDate(now) {
            var date = new Date(now);
            if (isNaN(date)) {
                return now;
            }
            var YY = date.getFullYear() + '-';
            var MM = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1) + '-';
            var DD = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate());
            var hh = (date.getHours() < 10 ? '0' + date.getHours() : date.getHours()) + ':';
            var mm = (date.getMinutes() < 10 ? '0' + date.getMinutes() : date.getMinutes()) + ':';
            var ss = (date.getSeconds() < 10 ? '0' + date.getSeconds() : date.getSeconds());
            return YY + MM + DD + " " + hh + mm + ss;
        }
        orderAddition.downloadExl = function (){
            var downButton = document.getElementById('downButton');
            // 禁用按钮
            downButton.disabled = true;
            location.href = "${ctxPath}/servlet/upload?action=exportAddition&type=addition&ORDER_ID="+orderId;
            setTimeout(function () {
                orderAddition.getCache();
            }, 5000);
        }

        let intervalId = null;

        orderAddition.getCache = function () {
            var data = {
                type: "addition",
            }
            ajax.remoteCall("${ctxPath}/servlet/upload?action=cache", data, function (result) {
                if (result.state !== 1) {
                    if (!intervalId) { // 确保只设置一次定时器
                        intervalId = setInterval(orderAddition.getCache, 10000);
                    }
                    var downButton = document.getElementById('downButton');
                    // 禁用按钮
                    downButton.disabled = true;
                } else {
                    clearInterval(intervalId);
                    var downButton = document.getElementById('downButton');
                    // 取消禁用按钮
                    downButton.disabled = false;
                }
            }, {loading: false});
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>