<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>新建舆情</title>
	<style>
		.think-container {
			position: absolute;
			top: 100%;
			left: 0;
			width: 100%;
			max-height: 200px;
			overflow-y: auto;
			border: 1px solid #ddd;
			border-top: none;
			background: white;
			z-index: 1000;
			box-shadow: 0 2px 5px rgba(0,0,0,0.2);
		}
		.think-item {
			padding: 8px 12px;
			cursor: pointer;
			border-bottom: 1px solid #eee;
		}
		.think-item:hover {
			background-color: #f5f5f5;
		}
		.think-item:last-child {
			border-bottom: none;
		}
		.url-input-container {
			position: relative;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px" >标题</td>
			                        <td width="200px"><input type="text" name="TITLE"   id="" class="form-control input-sm" value="人工搜索"></td>
			                        <td width="50px"  class="required">网络名</td>
			                        <td width="200px"><input type="text" name="CREATER" data-rules="required" id="" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td  class="required">原文链接</td>
			                        <td>
			                        	<div class="url-input-container">
			                        		<input type="text" name="URL"  data-rules="required" id="urlInput" class="form-control input-sm" 
			                        			onfocus="orderNew.showUrlOptions()" onblur="orderNew.hideUrlOptions()" 
			                        			oninput="orderNew.filterUrlOptions()">
			                        		<div class="think-container" id="urlThinkContainer" style="display: none;">
			                        			<div class="think-item" onclick="orderNew.selectUrl('美的号私信投诉，无链接')">美的号私信投诉，无链接</div>
			                        			<div class="think-item" onclick="orderNew.selectUrl('小天鹅号私信投诉，无链接')">小天鹅号私信投诉，无链接</div>
			                        			<div class="think-item" onclick="orderNew.selectUrl('COLMO号私信投诉，无链接')">COLMO号私信投诉，无链接</div>
			                        			<div class="think-item" onclick="orderNew.selectUrl('东芝号私信投诉，无链接')">东芝号私信投诉，无链接</div>
			                        			<div class="think-item" onclick="orderNew.selectUrl('市监局投诉')">市监局投诉</div>
			                        		</div>
			                        	</div>
			                        </td>
			                        <td  class="required">发布时间</td>
			                        <td><input type="text" name="CREATE_TIME" data-rules="required" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="CREATE_TIME" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>

			                        <td  class="required">数据来源</td>
			                        <td>
										<div class="input-group">
											<select name="SENTIMENT_FROM" id="SENTIMENT_FROM" class="form-control"
													style="width:150px;">
											</select>
										</div>
<%--			                            <select name="SENTIMENT_FROM" id="SENTIMENT_FROM" data-rules="required" class="form-control input-sm" data-mars="dataInfo.sentimentFrom">--%>
<%--					                    		<option value="">请选择</option>--%>
<%--              						    </select>--%>
              						</td>
			                        <td  >搜索关键字</td>
			                        <td><input type="text" name="SEARCH_KEYWORD" class="form-control input-sm"></td>
		                     </tr>
		                      <tr>

			                        <td  class="required">舆情类型</td>
			                        <td>
			                            <select name="SENTIMENT_TYPE"  data-rules="required" class="form-control input-sm" data-mars="dataInfo.findDictCode">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                        <td  >事业部</td>
			                        <td>
			                            <select name="CODE" id="CODE"  class="form-control input-sm" data-mars="dataInfo.sentimentDept">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                     <tr>

			                         <td  class="required">原文内容</td>
				                     <td colspan="3">
				                        <textarea rows="5" class="form-control input-sm" name="CONTENT" data-rules="required"></textarea>
				                     </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="orderNew.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="parent.layer.closeAll();">关闭</button>
				    </div>
		</form>
</EasyTag:override>

<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	<script type="text/javascript">
	   jQuery.namespace("orderNew");
	   $(function(){
		   orderNew.multiSetting2 = {
			   multiple: false,
			   buttonWidth: '220px',
			   allSelectedText: "全部",
			   nonSelectedText: "--请选择--",
			   nSelectedText: "个被选中",
			   selectAllNumber: false,
			   maxHeight: 350,
			   includeSelectAllOption: false,
			   selectAllText: '全选',
			   enableFiltering: true,
			   single: true

		   };

		   var data = {"params":{},"controls":["dataInfo.sentimentChannelList"]}
		   ajax.remoteCall("${ctxPath}/webcall",data,function(result2) {
			   var dataInfo= result2['dataInfo.sentimentChannelList']['data'];
			   var selectElement = $('#SENTIMENT_FROM');
			   var option = $("<option>").val('').text("--请选择--");
			   selectElement.append(option);
			   // 遍历 Map 对象并生成选项
			   dataInfo.forEach(function(item) {
				   var option = $('<option></option>')
						   .attr('value', item.value)
						   .text(item.text);
				   selectElement.append(option);
			   });
			   selectElement.multiselect(orderNew.multiSetting2);
			   $("#SENTIMENT_FROM").multiselect("destroy").multiselect({
				   // 自定义参数，按自己需求定义
				   multiple: false,
				   buttonWidth: '110px',
				   allSelectedText: "全部",
				   nonSelectedText: "--请选择--",
				   nSelectedText: "个被选中",
				   selectAllNumber: false,
				   maxHeight: 350,
				   includeSelectAllOption: false,
				   selectAllText: '全选',
				   enableFiltering: true,
				   single: true
			   });
		   });
		   $("#editForm").render();
	   });
	   orderNew.ajaxSubmitForm = function(){
		   var time=$("#CREATE_TIME").val();
		   var time1=get_nowDateTime();
		   if(time>time1){
			  layer.alert("发帖时间不能大于当前时间",{icon:2});
			  return false;
		   }
			if(form.validate("editForm")){
				orderNew.insertData();
			};
		}
		
	   // URL下拉选择相关函数
	   orderNew.showUrlOptions = function() {
		   $("#urlThinkContainer").show();
	   };
	   
	   orderNew.hideUrlOptions = function() {
		   // 延迟隐藏，给点击事件时间
		   setTimeout(function() {
			   $("#urlThinkContainer").hide();
		   }, 200);
	   };
	   
	   orderNew.selectUrl = function(url) {
		   $("#urlInput").val(url);
		   $("#urlThinkContainer").hide();
	   };
	   
	   orderNew.filterUrlOptions = function() {
		   var input = $("#urlInput").val().toLowerCase();
		   $("#urlThinkContainer .think-item").each(function() {
			   var text = $(this).text().toLowerCase();
			   if (text.indexOf(input) >= 0) {
				   $(this).show();
			   } else {
				   $(this).hide();
			   }
		   });
	   };
	   orderNew.insertData = function() {
			var data = form.getJSONObject("editForm");
			var option=$("#CODE option:selected");
			data.codeName=option.text();
			var option1=$("#SENTIMENT_FROM option:selected");
			data.typeName=option1.text();
			//var index = parent.layer.getFrameIndex(window.name);
			ajax.remoteCall("${ctxPath}/servlet/order?query=addOrder",data,function(result) {
				//debugger;
					if(result.state == 1){
						layer.alert(result.msg,{icon: 1},function(){
							parent.layer.closeAll();
							parent.selectList();
						});
						}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 }
		function get_nowDateTime(){
			var date = new Date();
			var day = date.getDate() < 10 ? "0" + date.getDate():date.getDate();
			var month = (date.getMonth() + 1) > 9 ? (date.getMonth() + 1) : "0"	+ (date.getMonth() + 1);
			var hour = date.getHours() <10? "0"+ date.getHours():date.getHours();
			var minute = date.getMinutes() <10 ? "0"+date.getMinutes():date.getMinutes();
			var second = date.getSeconds() <10 ? "0"+ date.getSeconds():date.getSeconds();
			return date.getFullYear() + "-" + month + "-"  + day + " " + hour + ":" + minute+ ":" + second;
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>