<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>售后工单</title>
	<style>
	    .blank-page{padding: 20px 40px;overflow-y: hidden;border: solid 1px #cccccc; background-color: #ffffff;margin: 20px auto;box-shadow: -2px -2px 7px #cccccc;width:890px;margin-bottom: 100px}
        .blank-page .header-title{text-align:center;color:#333;font-size:19px}
        .blank-page .p-title{border-bottom:1px solid #cccccc;font-size:16px;color:#555;margin-top:30px;margin-bottom:15px}
        .blank-page .p-title>span{border-bottom:2px solid #00a0f0;padding:2px 6px}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     <div class="blank-page">
     	<form id="editForm" class="form-horizontal" method="post"  autocomplete="off" data-mars-prefix="ent.">
		<h4 class="header-title">售后工单</h4>
		<p class="p-title"><span>用户信息</span></p>
		<table class="table table-vzebra">
	        <tbody>
	            <tr>
                    <td width="90px" class="required">用户信息</td>
                    <td><input type="text"  name=""  class="form-control input-sm"></td>
                    <td width="90px" class="required">来电号码</td>
                    <td><input type="text"  name=""  class="form-control input-sm"></td>
                    <td width="90px" class="required">用户类型</td>
                    <td>
                        <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
                    </td>
	            </tr>
	            <tr>
	                <td>用户级别</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                <td>电话区号</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>电话号码1</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	            </tr>
	            <tr>
	                <td>电话号码2</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>电话号码3</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>区域</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	            </tr>
	            <tr>
	                <td>用户地址</td>
	                <td colspan="5"><input type="text"  name=""  class="form-control input-sm"></td>
	            </tr>
	            <tr>
	                <td>服务联系人</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>服务联系电话</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>接入方式</td>
	                <td>
	                    <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
                    </td>
	            </tr>
	           
	        </tbody>
	    </table>
	    <p class="p-title"><span>用户需求</span></p>
	    <table class="table table-vzebra" data-mars-prefix="entRes.">
	        <tbody>
	             <tr>
	                <td  width="90px">产品主体</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                <td  width="90px">产品品牌</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td  width="90px">产品品类</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	            </tr>
	            <tr>
	                <td>产品型号</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>产品数量</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>产品用途</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	            </tr>
	            <tr>  
	                <td>购买渠道</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                <td>销售单位</td>
	                <td colspan="3"><input type="text"  name=""  class="form-control input-sm"></td>
	             </tr>
	             <tr>  
	                <td>来源单据号</td>
	                <td>
	                   <input type="text"  name=""  class="form-control input-sm">
	                </td>
	                <td>服务请求</td>
	                <td colspan="3"><input type="text"  name=""  class="form-control input-sm"></td>
	             </tr>
	             <tr>  
	                <td>故障代码</td>
	                <td>
	                   <input type="text"  name=""  class="form-control input-sm">
	                </td>
	                <td>用户指定网点</td>
	                <td><input type="text"  name=""  class="form-control input-sm"></td>
	                <td>紧急程度</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	             </tr>
	             <tr>  
	                <td>服务描述</td>
	                <td colspan="5">
	                   <textarea  name="" rows="3" class="form-control input-sm"></textarea>
	                </td>
	             </tr>
	             <tr>  
	                <td>投诉等级</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                <td>预约场景</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                <td>预约日期</td>
	                <td>
	                   <input type="text"  name=""  class="form-control input-sm Wdate" onclick="WdatePicker()">
	                </td>
	             </tr>
	             <tr>  
	                <td>预约时间段</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                <td>处理方式</td>
	                <td>
	                   <select class="form-control input-sm" data-rules="required" data-mars="" name="" style="width:160px">
                                <option value="">请选择</option>
                     	</select>
	                </td>
	                 <td>通知分中心</td>
	                <td>
	                   <input type="text"  name=""  class="form-control input-sm">
	                </td>
	             </tr>
	             <tr>  
	                <td>备注</td>
	                <td colspan="5">
	                   <textarea  name="" rows="3" class="form-control input-sm"></textarea>
	                </td>
	             </tr>
	        </tbody>
	    </table>
	   
	    <p class="text-c" style="margin-top:50px">
		      <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick=""> 保 存 </button>
		      <button type="button" class="btn btn-primary btn-sm ml-20" style="width: 80px" onclick=""> 暂存 </button>
		      <button type="button" class="btn btn-primary btn-sm ml-20" style="width: 80px" onclick=""> 复制 </button>
		</p>   
		
	    </form>
    </div> 
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script>
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>