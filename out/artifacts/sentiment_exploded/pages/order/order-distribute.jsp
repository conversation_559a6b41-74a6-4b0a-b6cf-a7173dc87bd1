<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情工单派发</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px">网络名</td>
			                        <td width="160px"><input type="text" name=""  data-rules="" id="" class="form-control input-sm"></td>
			                        <td width="50px">联系电话1</td>
			                        <td width="160px"><input type="text" name="" data-rules="" id="" class="form-control input-sm"></td>
			                        <td width="50px">联系电话2</td>
			                        <td width="160px"><input type="text" name="" data-rules="" id="" class="form-control input-sm"></td>
			                 </tr>
		                     <tr>
			                        <td>用户</td>
			                      	<td><input type="text" name=""  id="" class="form-control input-sm"></td>			                      
			                        <td>详细地址</td>
			                        <td><input type="text" name=""  id="" class="form-control input-sm"></td>
			                        <td>是否有客户资料</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
		                     </tr>
		                     <tr>
			                        <td>产品主体</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              						<td>产品类别</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
			                        <td>舆情类别</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                      <tr>
			                        <td>数据来源</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              						<td>负向级别</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
			                        <td>服务请求</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td>投诉原因</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
              						<td>投诉类别</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
			                        <td>投诉焦点分析</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                     <tr>
			                        <td>购买渠道</td>
			                        <td><input type="text" name="" class="form-control input-sm"></td>
			                        <td>电商订单号</td>
			                        <td><input type="text" name="" class="form-control input-sm"></td>
			                        <td>派工分中心</td>
			                        <td><input type="text" name="" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td>关键用户或媒体</td>
			                        <td><input type="text" name="" class="form-control input-sm" ></td>
			                        <td>粉丝量</td>
			                        <td><input type="text" name="" class="form-control input-sm" ></td>
			                        <td>工单状态</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                     <tr>
		                            <td>排查用户单位</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                        <td>受理操作员</td>
			                        <td><input type="text" name="" class="form-control input-sm" ></td>
			                        <td>处理操作员</td>
			                        <td><input type="text" name="" class="form-control input-sm" ></td>
		                     </tr>
		                     <tr>
			                        <td>投诉主题</td>
			                        <td colspan="5"><input type="text" name="" class="form-control input-sm" ></td>
		                     </tr>
		                     <tr>
			                         <td>投诉原文</td>
				                     <td colspan="5">
				                        <textarea rows="5" class="form-control input-sm" name=""></textarea>
				                     </td>
		                     </tr>
		                     <tr>
			                        <td>互动概要</td>
			                        <td colspan="5"><input type="text" name="" class="form-control input-sm" ></td>
		                     </tr>
		                     <tr>
			                        <td>处理方案</td>
			                        <td colspan="5"><input type="text" name="" class="form-control input-sm" ></td>
		                     </tr>
		                     <tr>
		                            <td>回帖时间</td>
			                        <td><input type="text" name="" class="form-control input-sm"  onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})"></td>
		                            <td>是否升级</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
			                        <td>邮件模板</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
	                    </tbody>
	                  </table>
				      <p class="text-c mt-40">
					   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="orderDistr.email()">编辑邮件</button>
					   		<button class="btn btn-sm btn-primary ml-20"  type="button" onclick="orderDistr.message()">编辑短信</button>
					   		<button class="btn btn-sm btn-success ml-20"  type="button" onclick="">工单存档</button>
					   		<button class="btn btn-sm btn-success ml-20"  type="button" onclick="">工单暂存</button>
					   		<button class="btn btn-sm btn-success ml-20"  type="button" onclick="orderDistr.distribute()">售后工单</button>
					  </p> 
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
	   jQuery.namespace("orderDistr");
	   requreLib.setplugs("wdate");
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>