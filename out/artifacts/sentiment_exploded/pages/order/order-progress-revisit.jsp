<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>处理进展</title>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="editProForm" data-mars="orderInf.orderIndo" method="post" autocomplete="off" data-mars-prefix="">
        <input type="hidden" id="pro_ID" name="pro.ID" value="${param.proId}">
        <input type="hidden" name="pro.ORDER_ID" value="${param.orderId}">


        <div class="info-title">
            <p style="width: 150px;">舆情处理信息</p>
        </div>
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td width="70px">分中心</td>
                <td><input type="text" name="pro.NOTICE_CENTER" maxlength="30" id="" class="form-control input-sm"></td>


            </tr>

            <tr>
                <td>处理进展</td>
                <td><input type="text" name="pro.PROGRESS" maxlength="150" id="" class="form-control input-sm"></td>
            </tr>
            <tr>
                <td>处理结果反馈</td>
                <td><input type="text" name="pro.PROGRESS_FEEDBACK" maxlength="150" id="" class="form-control input-sm">
                </td>
            </tr>
            </tbody>
        </table>
        <div class="info-title mt-20">
            <p style="width: 150px;">售后工单处理进展</p>
        </div>
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td width="50px">接入单</td>
                <td width="160px"><input type="text" name="pro.CC_ORDER_NO" maxlength="50" id=""
                                         class="form-control input-sm"></td>
                <td width="50px">派单时间</td>
                <td width="160px"><input type="text" name="pro.SEND_TIME" id="SEND_TIME"
                                         class="form-control input-sm Wdate" ">
                </td>
                <td width="50px">服务网点</td>
                <td width="160px"><input type="text" name="pro.SERVICE_BRANCHES" maxlength="50" id=""
                                         class="form-control input-sm"></td>
            </tr>
            <tr>
                <td>接单时间</td>
                <td><input type="text" name="pro.RECEIVE_TIME" id="RECEIVE_TIME" class="form-control input-sm Wdate" ">
                </td>
                <td>首约时间</td>
                <td><input type="text" name="pro.FIRST_AGREED_TIME" id="" class="form-control "></td>
                <td>改约时间</td>
                <td><input type="text" name="pro.SECOND_AGREED_TIME" id="" class="form-control "></td>
            </tr>
            <tr>
                <td>反馈类型</td>
                <td><input type="text" name="pro.FEEDBACK_TYPE" maxlength="50" id="" class="form-control input-sm"></td>
                <td>反馈时间</td>
                <td><input type="text" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" name="pro.FEEDBACK_TIME"
                           id="" class="form-control input-sm Wdate" ">
                </td>
                <td>反馈结果</td>
                <td><input type="text" name="pro.FEEDBACK_RESULT" maxlength="150" id="" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td>服务单状态</td>
                <td><input type="text" name="pro.CC_ORDER_STATUS" maxlength="150" id="" class="form-control input-sm">
                </td>

                <td>一级反馈项目</td>
                <td><input type="text" name="pro.FEEDBACK_FIRST_ITEM" maxlength="50" id=""
                           class="form-control input-sm"></td>
                <td>二级反馈项目</td>
                <td><input type="text" name="pro.FEEDBACK_SECOND_ITEM" maxlength="50" id=""
                           class="form-control input-sm"></td>
            </tr>
            <tr>
                <td>反馈说明</td>
                <td colspan="5"><textarea rows="3" maxlength="150" class="form-control input-sm"
                                          name="pro.FEEDBACK_DESC"></textarea></td>
            </tr>
            </tbody>
        </table>
    </form>
    <form id="editRevForm" data-mars="orderInf.orderRevisit" method="post" autocomplete="off" data-mars-prefix="">
        <input type="hidden" id="rev_ID" name="rev.ID" value="${param.revId}">
        <input type="hidden" name="rev.ORDER_ID" value="${param.orderId}">
        <div class="info-title">
            <p>投诉处理进展</p>
        </div>
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td>回访渠道类型</td>
                <td><input type="text" name="rev.CHANNEL" maxlength="50" id="" class="form-control input-sm"></td>

                <td>回访类别</td>
                <td><input type="text" name="rev.CATEGORY" maxlength="50" id="" class="form-control input-sm"></td>

                <td>回访项</td>
                <td><input type="text" name="rev.ITEM" maxlength="50" id="" class="form-control input-sm"></td>
            </tr>
            <tr>
                <td>回访项结果</td>
                <td><input type="text" name="rev.ITEM_RESULT" maxlength="50" id="" class="form-control input-sm"></td>
                <td>满意度</td>
                <td>
                    <select name="rev.SATISFACTION" class="form-control input-sm" data-mars="order.action">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td>回访时间</td>
                <td><input type="text" name="rev.VISIT_TIME" id="" class="form-control input-sm Wdate"
                           onclick="WdatePicker()"></td>
            </tr>
            <tr>
                <td>闭环结果</td>
                <td>
                    <select name="rev.OFF_RESULT" id="OFF_RESULT" class="form-control input-sm" data-mars="comm.getDict('SENTIMENT_OFF_RESULT')" onclick="orderProRev.updateRequiredAsterisks()">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td  id="isDeletedLabel">是否删帖</td>
                <td>
                    <select name="rev.IS_DELETED" data-mars="comm.getDict('IS_DELETE_CARD')" id="isDeleteCard" class="form-control input-sm" onchange="orderProRev.onchange()" >
                        <option value="">请选择</option>
                    </select>
                </td>
                <td>是否补帖</td>
                <td>
                    <select name="rev.IS_SUPPLEMENT" data-mars="comm.getDict('IS_SUPPLEMENT_CARD')" class="form-control input-sm">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td id="closedReasonLabel">已闭环原因</td>
                <td>
                    <select name="rev.OFF_REASON_CLOSED" class="form-control input-sm" data-mars="comm.getDict('SENTIMENT_OFF_REASON_CLOSED')">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td id="closedTimeLabel">闭环时间</td>
                <td>
                    <input type="text" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" name="rev.OFF_TIME"
                           id="OFF_TIME" class="form-control input-sm Wdate">
                </td>
                <td>是否需要沟通删帖</td>
                <td>
                    <select name="rev.IS_TALK_DELETED" data-mars="order.info" class="form-control input-sm">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td id="unclosedReasonLabel">未闭环原因</td>
                <td>
                    <select name="rev.OFF_REASON_UNCLOSED" class="form-control input-sm" data-mars="comm.getDict('SENTIMENT_OFF_REASON_UNCLOSED')">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td class="noDeleteReasonDiv" id="noDeleteReasonDiv">未删帖原因</td>
                <td class="noDeleteReasonDiv">
                    <select name="rev.NO_DELETE_REASON" id="noDeleteReason" class="form-control input-sm" data-mars="comm.getDict('NO_DELETE_REASON')">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td class="noDeleteReasonDiv">未删帖原因描述</td>
                <td colspan="5" class="noDeleteReasonDiv">
                    <textarea rows="3" maxlength="200" class="form-control input-sm" name="rev.NO_DELETE_REASON_DESC"></textarea>
                </td>
            </tr>
            <tr>
                <td>沟通描述</td>
                <td colspan="5">
                    <textarea rows="3" maxlength="500" class="form-control input-sm" name="rev.COMMUNICATION_DESCRIPTION"></textarea>
                </td>
            </tr>
            <tr>
                <td>回访结果</td>
                <td colspan="5">
                    <textarea rows="3" maxlength="500" class="form-control input-sm" name="rev.VISIT_RESULT"></textarea>
                </td>
            </tr>
            </tbody>
        </table>
        <p class="text-c mt-40">
            <button class="btn btn-sm btn-primary" id="revButton" type="button"
                    onclick=" orderProRev.ajaxSubmitForm()">保存
            </button>
        </p>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <!-- <script type="text/javascript" src="/yq_common/static/js/yq/extends.js"></script> -->

    <script type="text/javascript">
        jQuery.namespace("orderProRev");
        orderProRev.proId = '${param.proId}';
        orderProRev.revId = '${param.revId}';
        orderProRev.isLoading = true;
        orderProRev.orderInfos;
        orderProRev.isOffTime  = false;
        orderProRev.isOffResult  = false;
        $(function () {
            /* if(''== orderPro.id||null== orderPro.id){
                layer.alert("该工单没有处理进展信息！",{icon:2});
                $("#proButton").attr("disabled",true);
            } */
            $("#editProForm").render();
            $("#editRevForm").render({success : function(result){
                    var data = result['orderInf.orderRevisit'].data;
                    if(data && data['rev.OFF_TIME'] !=''){
                        orderProRev.isOffTime= true;
                    }


                    orderProRev.updateRequiredAsterisks();
                    orderProRev.setData();
                    orderProRev.onchange();
                    var offTime = $("#OFF_TIME").val();
                    if(offTime && offTime != null&& offTime != ''){
                        $("#OFF_TIME").attr("disabled",true);
                    }
                    if(data && data['rev.OFF_RESULT'] !='' && data['rev.OFF_RESULT'] == '1'){
                        orderProRev.isOffResult= true;
                        $("#OFF_RESULT").attr("disabled",true);
                    }
                }
            });

        });
        orderProRev.setData = function(){
            var data = form.getJSONObject("editRevForm");
            var offTime = $('#OFF_TIME').val();
            data['rev.OFF_TIME'] = offTime;
            orderProRev.orderInfos = data;
        }
        orderProRev.onchange = function () {
            var isDeleteCard = $("#isDeleteCard").val();
            //var nodeDeletedReason = $("select[name='rev.NO_DELETE_REASON']");
            var noDeleteReasonDiv = $("#noDeleteReasonDiv");
            noDeleteReasonDiv.css('color', '');
            if(isDeleteCard && isDeleteCard == 'N') {
                $(".noDeleteReasonDiv").show();
                noDeleteReasonDiv.css('color', 'red');
            } else {
                $(".noDeleteReasonDiv").hide();
            }
        }
        orderProRev.updateRequiredAsterisks = function () {
            var offResult = $("select[name='rev.OFF_RESULT']").val();
            var offTime = $("input[name='rev.OFF_TIME']");
            var offReasonClosed = $("select[name='rev.OFF_REASON_CLOSED']");
            var offReasonUnclosed = $("select[name='rev.OFF_REASON_UNCLOSED']");
            var isDeleted = $("select[name='rev.IS_DELETED']");

            // 获取所有需要改变样式的元素
            var closedReasonLabel = $("#closedReasonLabel");
            var closedTimeLabel = $("#closedTimeLabel");
            var unclosedReasonLabel = $("#unclosedReasonLabel");
            var isDeletedLabel = $("#isDeletedLabel");

            // 移除所有红色文本样式
            closedReasonLabel.css('color', '');
            closedTimeLabel.css('color', '');
            unclosedReasonLabel.css('color', '');
            isDeletedLabel.css('color', '');

            // 根据闭环结果更新文本颜色
            if (offResult === '1') { // 已闭环
                offReasonUnclosed.attr('disabled', true);
                offReasonUnclosed.val('')
                offReasonClosed.attr('disabled', false)
                closedReasonLabel.css('color', 'red');
                closedTimeLabel.css('color', 'red');
                isDeletedLabel.css('color', 'red');
                var offTimev = $('#OFF_TIME').val();
                if(offTimev == '' || offTimev==null){
                    var nowTime = getNowTime();
                    $('#OFF_TIME').val(nowTime);
                }
                offTime.attr('disabled', false)

            } else if (offResult === '0') { // 未闭环
                offReasonUnclosed.attr('disabled', false);
                offReasonClosed.attr('disabled', true)
                offReasonClosed.val('')
                offTime.attr('disabled', true)
                offTime.val('')
                unclosedReasonLabel.css('color', 'red');
                isDeletedLabel.css('color', 'red');
                var offTimev = $('#OFF_TIME').val();
                if(offTimev =='' || offTimev == null){
                    $('#OFF_TIME').val('');
                }
            }else if (offResult === '') { // 未选择
                offReasonClosed.attr('disabled', true)
                offReasonUnclosed.attr('disabled', true)
                offTime.attr('disabled', true)
                offReasonClosed.val('')
                offReasonUnclosed.val('')
                offTime.val('')
                isDeletedLabel.val('')
                var offTimev = $('#OFF_TIME').val();
                if(offTimev =='' || offTimev==null){
                    $('#OFF_TIME').val('');
                }
            }
        }

        // 初始化时调用一次
        // $(document).ready(function() {
        //     orderProRev.updateRequiredAsterisks();
        // });

        // 监听选择框变化
        $("select[name='rev.OFF_RESULT']").on('change', function() {
            orderProRev.updateRequiredAsterisks();
        });

        orderProRev.ajaxSubmitForm = function () {
            if (orderProRev.validateOffResult()){
                if (form.validate("editProForm")) {
                    orderProRev.insertProData();
                }
                if (form.validate("editRevForm")) {
                    orderProRev.insertRevData();
                }
            }
        }
        orderProRev.insertProData = function () {
            var data = form.getJSONObject("editProForm");
            console.log('insertProData:' + JSON.stringify(data))
            ajax.remoteCall("${ctxPath}/servlet/orderInf?action=UpOrederPro", data, function (result) {
                    //debugger;
                    if (result.state == 1) {
                        $("#pro_ID").val(result.data["ID"])
                        /* 						$("#proButton").attr("disabled",true);
                         */
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            );
        }

        orderProRev.insertRevData = function () {
            var data = form.getJSONObject("editRevForm");
            var offResult = $("select[name='rev.OFF_RESULT']").val();
            var offTime = $("#OFF_TIME").val();
            data['rev.OFF_TIME'] = offTime;
            data['rev.OFF_RESULT'] = offResult;
            orderProRev.setData();
            console.log('insertRevData:' + JSON.stringify(data))
            ajax.remoteCall("${ctxPath}/servlet/orderInf?action=UpOrederRev", data, function (result) {
                    //debugger;
                    if (result.state == 1) {
                        orderDetail.setData();
                        toGvocSentiment();
                        $("#rev_ID").val(result.data["ID"])
                            layer.alert(result.msg, {icon: 1, time: 1000}, function () {
                        });
                        /* 						$("#revButton").attr("disabled",true);
                         */
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            );
        }

        orderProRev.validateOffResult =function () {
            var offResult = $("select[name='rev.OFF_RESULT']").val();
            var offTime = $("input[name='rev.OFF_TIME']").val();
            var offReasonClosed = $("select[name='rev.OFF_REASON_CLOSED']").val();
            var offReasonUnclosed = $("select[name='rev.OFF_REASON_UNCLOSED']").val();
            var isDeleted = $("select[name='rev.IS_DELETED']").val();
            if (offResult === '1') {
                if (!isDeleted){
                    layer.alert("是否删帖必填！", {icon: 5});
                    return false;
                }
                if (!offTime || !offReasonClosed) {
                    layer.alert("已闭环时，闭环时间和已闭环原因必须填写！", {icon: 5});
                    return false;
                }
            } else if (offResult === '0') {
                if (!isDeleted){
                    layer.alert("是否删帖必填！", {icon: 5});
                    return false;
                }
                if (!offReasonUnclosed) {
                    layer.alert("未闭环时，未闭环原因必须填写！", {icon: 5});
                    return false;
                }
            }
            if(isDeleted && isDeleted=='N'){
                var noDeleteReason = $("select[name='rev.NO_DELETE_REASON']").val();
                if(noDeleteReason=='' || noDeleteReason ==null){
                    layer.alert("未删帖时,未删帖原因必须填写!", {icon: 5});
                    return false;
                }
            }
            return true;
        }

        function isAre() {
            $.ajax({
                url: "${ctxPath}/servlet/orderInf?query=isAre",
                data: {"id": orderProRev.id},
                async: false,
                success: function (result) {
                    if (result.state == 1) {
                        if (result.data == 0) {
                            layer.alert("该工单没有派发！", {icon: 2});
                            $("#proButton").attr("disabled", true);
                        }
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            });
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>