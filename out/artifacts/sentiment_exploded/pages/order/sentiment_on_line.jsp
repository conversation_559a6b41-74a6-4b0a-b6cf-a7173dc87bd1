<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>线上升级</title>
	<style>
		.hidden {display: none;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">

			<form id="onlineForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" style="height:  300px" >
			<div >
				  <input type="text" value="${param.COMPLAINT_UPGRADE_HANDLE_NO }" id="COMPLAINT_UPGRADE_HANDLE_NO"  name="complaintUpgradeHandleNo" class="form-control input-sm hidden"  readonly="readonly"><!--投诉受理人  -->
				  <input type="text" value="${param.ORG_CODE }" id="orgCode"  name="orgCode" class="form-control input-sm hidden"  readonly="readonly"><!--主体-->
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
	                    	<tr>
                    		   	<td >投诉升级类型</td>
		                        <td><input type="text" value="${param.COMPLAINT_UPGRADE_TYPE }" id="COMPLAINT_UPGRADE_TYPE"  name="complaintUpgradeType" class="form-control input-sm" readonly="readonly"></td>
                    		    <td >投诉来源单号</td>
		                        <td><input type="text" value="${param.COMPLAINT_SOURCE_NO }" id="COMPLAINT_SOURCE_NO"  name="complaintSourceNo" class="form-control input-sm" readonly="readonly"></td>
		                        <td >投诉焦点</td>
		                        <td colspan="3"><input  type="text" value="${param.COMPLAINT_FOCUS }" id="COMPLAINT_FOCUS"  name="complaintFocus" class="form-control input-sm" ></td>
		                        
	                    	</tr>
	                    	<tr>
		                    	<td >MIP账号</td>
		                        	
		                        <td colspan="3">
		                        	<input value="${param.FOLLOW_UP_NAME }" type="text" id="FOLLOW_UP_NAME"  name="followUpName" class="form-control input-sm" readonly="readonly">
		                        	<!--MIP账号code  -->
		                        	<input value="${param.FOLLOW_UP_ACCOUNT }" type="text" id="FOLLOW_UP_ACCOUNT"  name="followUpAccount" class="form-control input-sm hidden"  readonly="readonly">
		                       	<td >邮箱</td>
		                        <td colspan="3">
		                        	<input value="${param.FOLLOW_UP_EMAIL }" type="text" id="FOLLOW_UP_EMAIL"  name="followUpEmail" class="form-control input-sm"  readonly="readonly">		                        	
		                        </td>
		                    </tr>
		                    <tr>
		                      	<td>投诉主题</td>
		                        <td colspan="7"><input value="${param.COMPLAINT_SUBJECT}" type="text" id="COMPLAINT_SUBJECT"  name="complaintSubject" class="form-control input-sm" readonly="readonly"></td>
		                    <tr>
		                    <tr>
		                    	<td class="required">投诉概括</td>
					            <td colspan="7" ><textarea  class="form-control input-sm" name="complaintContent" id ="COMPLAINT_CONTENT"  rows="3" placeholder=""></textarea></td>
		                    </tr>
		                    <tr>
		                  		<td >来电次数</td>
		                        <td><input type="text" value="${param.CALL_TIMES}" id="CALL_TIMES"  name="callTimes" class="form-control input-sm" readonly="readonly"></td>
                    		    <td >升级次数</td>
		                        <td><input type="text" value="${param.UPGRADE_TIMES}" id="UPGRADE_TIMES"  name="upgradeTimes" class="form-control input-sm" readonly="readonly"></td>
                    		    <td >投诉时间</td>
		                        <td><input type="text" value="${param.COMPLAINT_TIME}" id="COMPLAINT_TIME"  name="complaintTime" class="form-control input-sm" readonly="readonly"></td>
		                        <td >分中心名称</td>
		                        <td>
			                        <input type="text" value="${param.BRANCH_NAME}" id="BRANCH_NAME"  name="branchName" class="form-control input-sm " >
			                        <!--分中心编码 -->
			                        <input type="text" value="${param.BRANCH_CODE}" id="BRANCH_CODE"  name="branchCode" class="form-control input-sm hidden" readonly="readonly">
		                        </td>
	                        </tr>
		                    <tr>
		                  		<td >用户姓名</td>
		                        <td><input type="text" value="${param.CUSTOMER_NAME}" id="CUSTOMER_NAME"  name="customerName" class="form-control input-sm" readonly="readonly"></td>
                    		    <td >用户电话1</td>
		                        <td><input type="text" value="${param.CUSTOMER_TEL1}" id="CUSTOMER_TEL1"  name="customerTel1" class="form-control input-sm" readonly="readonly"></td>
                    		    <td >用户电话2</td>
		                        <td><input type="text" value="${param.CUSTOMER_TEL2}" id="CUSTOMER_TEL2"  name="customerTel2" class="form-control input-sm" readonly="readonly"></td>
		                        <td >用户级别</td>
		                        <td><input type="text" value="${param.CUSTOMER_LEVEL}" id="CUSTOMER_LEVEL"  name="customerLevel" class="form-control input-sm" readonly="readonly"></td>
	                        </tr>
		                    <tr>
		                  		<td >用户所在区域编码</td>
		                        <td><input type="text" value="${param.CUSTOMER_AREA_CODE}" id="CUSTOMER_AREA_CODE"  name="customerAreaCode" class="form-control input-sm" readonly="readonly" ></td>
                    		    <td >用户所在区域名称</td>
		                        <td><input type="text" value="${param.CUSTOMER_AREA_NAME}" id="CUSTOMER_AREA_NAME"  name="customerAreaName" class="form-control input-sm" readonly="readonly"></td>
		                        <td >用户地址</td>
		                        <td colspan="3"><input value="${param.CUSTOMER_ADDRESS}" type="text" id="CUSTOMER_ADDRESS"  name="customerAddress" class="form-control input-sm" readonly="readonly" ></td>
	                        </tr>
		                    <tr>
		                  		<td >服务单号</td>
		                        <td ><input type="text" value="${param.SERVICE_ORDER_NO}" id="SERVICE_ORDER_NO"  name="serviceOrderNo" class="form-control input-sm" ></td>
		                  		<td >产品小类</td>
		                        <td>
			                        <input type="text" value="${param.PROD_NAME}" id="PROD_NAME"  name="prodName" class="form-control input-sm " readonly="readonly" >
			                        <!--产品小类名称 -->
			                        <input type="text" value="${param.PROD_CODE}" id="PROD_CODE"  name="prodCode" class="form-control input-sm hidden" readonly="readonly" >
		                        </td>
                    		    <td >首次来电日期</td>
		                        <td><input type="text" value="${param.FIRST_CALL_TIME}" id=FIRST_CALL_TIME  name="firstCallTime" class="form-control input-sm" ></td>
                    		    <td >最后一次来电日期</td>
		                        <td><input type="text" value="${param.LAST_CALL_TIME}" id="LAST_CALL_TIME"  name="lastCallTime" class="form-control input-sm" ></td>
	                        </tr>
		                    <tr>
		                  		<td >电商订单号</td>
		                        <td><input type="text" value="${param.ELECTRICITY_ORDER_NO}" id="ELECTRICITY_ORDER_NO"  name="electricityOrderNo" class="form-control input-sm " >
		                  		
		                       	<td >网点名称</td>
		                        <td>
		                        	<input type="text" value="${param.UNIT_NAME}" id="UNIT_NAME"  name="unitName" class="form-control input-sm" >
		                        	<!--网点名编码 -->
			                        <input type="text" value="${param.UNIT_CODE}" id="UNIT_CODE"  name="unitCode" class="form-control input-sm hidden" readonly="readonly">
		                        </td>                        
                    		  
                    		    <td >网点反馈说明</td>
		                        <td colspan="3"><input type="text" value="${param.UNIT_FEEDBACK_DESC}" id="UNIT_FEEDBACK_DESC"  name="unitFeedbackDesc" class="form-control input-sm" ></td>
	                        </tr>
		                    <tr>
		                     	<td >购买店铺名称</td>
		                        <td><input type="text" value="${param.BUY_STORE_NAME}" id=BUY_STORE_NAME  name="buyStoreName" class="form-control input-sm" ></td>
		                    
		                     	
		                  		<td >虚假封单说明</td>
		                        <td colspan="5">
			                        <input type="text" value="${param.COUNTERFEIT_DESC}" id="COUNTERFEIT_DESC"  name="counterfeitDesc" class="form-control input-sm " >
		                        </td>
							</tr>
		                    <tr>
		                  		
	                        </tr>
	                        <tr>
	                        	<td >统一口径</td>
					            <td colspan="3"><textarea  class="form-control input-sm" name="unifyExplainInfo" id ="UNIFY_EXPLAIN_INFO"  rows="3" placeholder="">${param.UNIFY_EXPLAIN_INFO}</textarea></td>
	                        	<td >专家坐席处理意见</td>
					            <td colspan="3"><textarea  class="form-control input-sm" name="professionalOption" id ="PROFESSIONAL_OPTION"  rows="3" placeholder="">${param.PROFESSIONAL_OPTION}</textarea></td>
							</tr>	  
							<tr>
		                    	<td class="required">投诉原文内容</td>
					            <td colspan="3"><textarea    class="form-control input-sm" name="complaintSourceText" id ="COMPLAINT_SOURCE_TEXT"  rows="5" placeholder=""></textarea></td>
					            <td >补充内容</td>
		                        <td colspan="3"><textarea    class="form-control input-sm" name="publicOpinionText" id ="PUBLIC_OPINION_TEXT"  rows="5" placeholder=""></textarea></td>
					          </tr>  
		                     <tr>
		                     	<td >粉丝数量</td>
		                       	<td><input type="text" value="${param.FANS_NUM}" id="FANS_NUM"  name="fansNum" class="form-control input-sm" ></td>
		                     
		                    	<td >处理人</td>
		                       	<td><input type="text" value="${param.COMPLAINT_ACCEPT_PERSON }" readonly="readonly" id="COMPLAINT_ACCEPT_PERSON"  name="complainAcceptPerson" class="form-control input-sm"  ><!--投诉受理人  --></td>
		                       	<td >首次投诉原文链接</td>
		                       	<td colspan="3"><input type="text" value="${param.COMPLAINT_SOURCE_URL }" readonly="readonly" id="COMPLAINT_SOURCE_URL"  name="complaintSourceUrl" class="form-control input-sm"  ><!--投诉受理人  --></td>
		                       	
		 		             </tr>  
		                     		                     
							 <%-- <tr>
		                  		<td >投诉原文链接</td>
		                        <td><input type="text" value="${param.COMPLAINT_SOURCE_URL}" id="COMPLAINT_SOURCE_URL"  name="complaintSourceUrl" class="form-control input-sm" ></td>
                    		    <td >投诉原文内容</td>
		                        <td><input type="text" value="${param.COMPLAINT_SOURCE_TEXT}" id="COMPLAINT_SOURCE_TEXT"  name="complaintSourceText" class="form-control input-sm" ></td>
                    		    <td >舆情私信内容</td>
		                        <td><input type="text" value="${param.PUBLIC_OPINION_TEXT}" id="PUBLIC_OPINION_TEXT"  name="publicOpinionText" class="form-control input-sm" ></td>
		                       	                        </tr>    --%>                 
	                    </tbody>
                 </table>
                 <div class="text-c">
			   		<button class="btn btn-sm btn-primary"  type="button" onclick="online.send()">确认</button>
			   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="popup.layerClose();">取消</button>
		    </div>
            </div>
            
		</form>		
		
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	
	<script type="text/javascript">
		jQuery.namespace("online");
	     requreLib.setplugs("wdate")
		  $(function(){
			  $("#COMPLAINT_CONTENT").val( window.parent.$("#COMPLAINT_CONTENT").val());
			  $("#COMPLAINT_SOURCE_TEXT").val(window.parent.$("#CONTENT").val())
			  $("#PUBLIC_OPINION_TEXT").val(window.parent.$("#REPLENISH_CONTENT").val())
			  if($("#COMPLAINT_TIME").val()==""){
				  $("#COMPLAINT_TIME").val(getFormatDate());
			  }
			  $("#COMPLAINT_TIME").val(formatDate($("#COMPLAINT_TIME").val()));
			  $("#FIRST_CALL_TIME").val(formatDate($("#FIRST_CALL_TIME").val()));
			  $("#LAST_CALL_TIME").val(formatDate($("#LAST_CALL_TIME").val()));
				  
	     })
	     function formatDate(str) {
			return str.replace(
			  /(?<=\/|-|\.|:|\b|T)\d{1}(?=\/|-|\.|:|\b|T)/g,
			  function ($1) {
			    return "0" + $1;
			  }
			);}
	     online.send=function(){
	    	/*  if($("#COMPLAINT_TIME").val()==""){
    			 layer.msg("无法获取投诉时间，请暂存工单后再进行升级", {icon : 2});
    			 return
	    	 } */
	    	 if(rulesPhone()){
	    		 if($("#COMPLAINT_CONTENT").val().length>1000){
	    			 layer.msg("投诉概括过长：当前长度"+$("#COMPLAINT_CONTENT").val().length, {icon : 2});
	    			 return
	    		 }
	    		 /* if($("#COMPLAINT_SOURCE_TEXT").val().length>1000){
	    			 layer.msg("投诉原文过长：当前长度"+$("#COMPLAINT_CONTENT").val().length, {icon : 2});
	    		 }
	    		 if($("#PUBLIC_OPINION_TEXT").val().length>1000){
	    			 layer.msg("补充过长：当前长度"+$("#COMPLAINT_CONTENT").val().length, {icon : 2});
	    		 } */
	    		 var data = form.getJSONObject("onlineForm");
				 data.type='${param.type }';


				 const parentWindow = window.parent;       // 父窗口对象
				 const parent$ = parentWindow.$;           // 父窗口的 jQuery（如果存在）
				 const parentDeal = parentWindow.deal;     // 父窗口的 deal 对象

				 setTimeout((win, $, deal) => {
					 try {
						 // 检查父窗口是否仍然可用（防止 iframe 关闭后报错）
						 if (win && !win.closed) {
							 if ($ && typeof $ === "function") {
								 $("#tab-1").load("${ctxPath}/pages/order/order-detail-type.jsp?type="+deal.type+"&id="+deal.id+"&orderId="+deal.orderId + "&_t=" + Date.now());

							 }
						 }
					 } catch (e) {
						 console.error("父窗口操作失败:", e);
					 }
				 }, 5000, parentWindow, parent$, parentDeal);

				 window.parent.setOnline(data);
			}
	     }
	     function  rulesPhone(){
				var ago=$("#CUSTOMER_TEL1").val();
		 		 var myreg=/^[1][0-9]{10}$/;
		 		 var myreg1=/^0\d{2,3}-?\d{7,8}$/;
		 		  if(ago!=''&&ago!=null){
		        	  if (!myreg.test(ago)&&!myreg1.test(ago)) {
							layer.msg("号码1格式不正确", {icon : 2});
							return false
			          }   
		          }
		 		 var ago=$("#CUSTOMER_TEL2").val();
		 		 var myreg=/^[1][0-9]{10}$/;
		 		 var myreg1=/^0\d{2,3}-?\d{7,8}$/;
		 		  if(ago!=''&&ago!=null){
		        	  if (!myreg.test(ago)&&!myreg1.test(ago)) {
							layer.msg("号码2格式不正确", {icon : 2});
							return false
			          }   
		          }
		 		 return true
			}
	     function getFormatDate() {
	    	    var date = new Date();
	    	    var month = date.getMonth() + 1;
	    	    var strDate = date.getDate();
	    	    var hours = date.getHours();
	    	    var minutes = date.getMinutes();
	    	    var seconds = date.getSeconds();
	    	    if (month >= 1 && month <= 9) {
	    	        month = "0" + month;
	    	    }
	    	    if (strDate >= 0 && strDate <= 9) {
	    	        strDate = "0" + strDate;
	    	    }
	    	    if (hours >= 0 && hours <= 9) {
	    	        hours = "0" + hours;
	    	    }
	    	    if (minutes >= 0 && minutes <= 9) {
	    	        minutes = "0" + minutes;
	    	    }
	    	    if (seconds >= 0 && seconds <= 9) {
	    	        seconds = "0" + seconds;
	    	    }
	    	    return date.getFullYear() + "-" + month + "-" + strDate
	    	        + " " + date.getHours() + ":" + minutes + ":" + seconds;
	    	}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>