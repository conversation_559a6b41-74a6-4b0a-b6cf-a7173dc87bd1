<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情工单派发</title>
	<style>
	     .info-title p:before {
		    content: '';
		    position: absolute;
		    background-color: #337ab7;
		    left: -1px;
		    height: 15px;
		    width: 3px;
		    top: 50%;
		    margin-top: -5px;
		 }
		.info-title p {
		    border-bottom: none;
		    position: relative;
		    height: 35px
		 }
	</style>
</EasyTag:override>
<EasyTag:override name="content">
    <div id="pageLoader" class="ibox-panel">
       <div class="ibox-panel-title clearfix">
	       <%-- <ul class="page-links ">
	            <li class="cur">
	                <a data-type="page" id="page-link1" data-href="${ctxPath}/pages/order/order-detail.jsp?type=${param.type}&id=${param.id}"">
	                    <span>舆情工单</span>
	                </a>
	            </li>
	            <li>
	                <a data-type="page" id="page-link2" data-href="${ctxPath}/pages/order/order-progress.jsp">
	                    <span>处理进展</span>
	                </a>
	            </li>
	            <li>
	                <a data-type="page" id="page-link3" data-href="${ctxPath}/pages/order/order-revisit.jsp">
	                    <span>回访</span>
	                </a>
	            </li>
	            <li>
	                <a data-type="page" id="page-link4" data-href="${ctxPath}/pages/order/order-email.jsp">
	                    <span>邮件</span>
	                </a>
	            </li>
	            <li>
	                <a data-type="page" id="page-link5" data-href="${ctxPath}/pages/order/order-message.jsp">
	                    <span>短信</span>
	                </a>
	            </li>
	            <li>
	                <a data-type="page" id="page-link6" data-href="${ctxPath}/pages/order/order-telphone.jsp">
	                    <span>电话通知</span>
	                </a>
	            </li>
	        </ul> --%>
	        <ul class="nav nav-tabs new-nav-tabs" role="tablist">
				    <li role="presentation" class="active"><a href="#tab-1" aria-controls="tab-1" role="tab" data-toggle="tab" >舆情工单</a></li>
<!-- 				    <li role="presentation"><a href="#tab-2" aria-controls="tab-2" role="tab" id="progress" data-toggle="tab" onclick="orderProgress()">处理进展</a></li>
				    <li role="presentation"><a href="#tab-3" aria-controls="tab-3" role="tab" id="revisit"  data-toggle="tab" onclick="orderRevisit()">回访</a></li> -->
				    <li role="presentation"><a href="#tab-4" aria-controls="tab-4" role="tab" data-toggle="tab" id="Email" onclick="orderEmail()">邮件</a></li>
				    <li role="presentation"><a href="#tab-5" aria-controls="tab-5" role="tab" data-toggle="tab" id="Message" onclick="orderMessage()">短信</a></li>
				    <li role="presentation"><a href="#tab-6" aria-controls="tab-6" role="tab" data-toggle="tab" id="Telphone" onclick="orderTelphone()">电话通知</a></li>
			</ul>
			<div class="tab-content" style="padding: 15px">
				<div role="tabpanel" class="tab-pane active" id="tab-1"></div>
		<!-- 		<div role="tabpanel" class="tab-pane active" id="tab-2"></div>
				<div role="tabpanel" class="tab-pane active" id="tab-3"></div> -->
				<div role="tabpanel" class="tab-pane active" id="tab-4"></div>
				<div role="tabpanel" class="tab-pane active" id="tab-5"></div>
				<div role="tabpanel" class="tab-pane active" id="tab-6"></div>
			</div>

		</div>
       <div class="ibox-panel-content"></div>
    </div>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("deal");
		deal.type='${param.type}';
		deal.id='${param.id}';
		deal.logeType='${param.logeType}';
		var ago=true;
		var ago1=true;
		var ago2=true;
		var ago3=true;
		var ago4=true;
		var ago5=true;
		var userId='';
		var userName='';
		var	sendData={};

	     $(function(){
	    	 changOne();
	    	/*  if('yes'==deal.logeType){
	    		 $("#progress").hide();
	    		 $("#revisit").hide();
	    	 } */
	     })
/* 	 function orderProgress(){
			var orderCode=$("#orderCode").val();
	    	 if(ago){	    		 
	    		 $("#tab-2").load("${ctxPath}/pages/order/order-progress.jsp");
	    		 ago=false;
	    	 }
	    	 if(''!=orderCode&&null!=orderCode){
		    	 $("#tab-2").load("${ctxPath}/pages/order/order-progress.jsp?orderCode="+orderCode);
			 }
	     }
		 function orderRevisit(){
			 if(ago1){
	    	 $("#tab-3").load("${ctxPath}/pages/order/order-revisit.jsp");
	    	 ago1=false; 
			 }
	     }  */
		 function orderEmail(){
			 getSendData();
			var buyType=$("#BUY_CHANNEL").val();
			var option1=$("#orgCode option:selected");	
			var orgCode=option1.text();
			var option2=$("#branchCode option:selected");				
			var branchCode=option2.text();
			 sendData.emailId=$("#email").val();
			 sendData.prodName=$("#prodName").val();//品类
			 if('请选择'==branchCode){
				 branchCode='';
			 }
			 if('请选择'==orgCode){
				 orgCode='';
			 }
			 var upgrade=$("#upgradeType2").val();

			/* if(''==upgrade){
				layer.alert('请选择升级类型',{icon:2});
			} */
			 //sendData.upgrade=upgrade;
			upgrade=encodeURI(upgrade).replace(/\+/g,'%2B');
			 sendData.DEPARTMENT=branchCode;//分中心
			 sendData.orgCode=orgCode;
			 sendData.buyType=buyType;
			 var strData = encodeURI(JSON.stringify(sendData));	

			 if(ago2){
	    	 $("#tab-4").load("${ctxPath}/pages/order/order-email.jsp?upgrade="+upgrade);
	    	 ago2=false; 
			 }
			
	     }
		 function orderMessage(){
			 getSendData();
				var option1=$("#orgCode option:selected");	
				var orgCode=option1.text();
				var option2=$("#branchCode option:selected");				
				var branchCode=option2.text();
				 sendData.smsId=$("#sms").val();
				 sendData.prodName=$("#prodName").val();
				 if('请选择'==branchCode){
					 branchCode='';
				 }
				 if('请选择'==orgCode){
					 orgCode='';
				 }
				 sendData.DEPARTMENT=branchCode;
				 sendData.orgCode=orgCode;
				 var upgrade=$("#upgradeType2").val();

				/* if(''==upgrade){
					layer.alert('请选择升级类型',{icon:2});
				} */
				// sendData.upgrade=upgrade;
				upgrade=encodeURI(upgrade).replace(/\+/g,'%2B');

				 var strData = encodeURI(JSON.stringify(sendData));		
			
				 
			 if(ago3){
	    	 $("#tab-5").load("${ctxPath}/pages/order/order-message.jsp?upgrade="+upgrade);
	    	 ago3=false;
			 }
			
	     }
		 function orderTelphone(){
				var prodName=$("#prodName").val();//品类
				var option2=$("#branchCode option:selected");
				var branchCode=option2.text();
				 if('请选择'==branchCode){
					 branchCode='';
				 }
				var option1=$("#orgCode option:selected");
				var orgCode=option1.text();
				 if('请选择'==orgCode){
					 orgCode='';
				 }
				 var upgrade=$("#upgradeType2").val();

				/*  if(''==upgrade){
						layer.alert('请选择升级类型',{icon:2});
					} */
					upgrade=encodeURI(upgrade).replace(/\+/g,'%2B');

		    	 $("#tab-6").load("${ctxPath}/pages/order/order-telphone.jsp?prodName="+prodName+"&branchCode="+branchCode+"&orgCode="+orgCode+"&upgrade="+upgrade);
				/*  if(ago4){
			    	 ago4=false;
				 } */
		     }
		function getSendData(){
			// var val=$("#TITLE").val();
			// val=val.replace(/\(/g,'\(').replace(/\)/g,'\)').replace(/\{/g,'\{').replace(/\}/g,'\}').replace(/\'/g,"`@`").replace(/\"/g,'\\\"');
				sendData.TITLE=$("#TITLE").val();
			sendData.sentimeId=orderDetail.id;
			var val=$("#URL").val();
			val=val.replace(/\&/g,'\><');
			sendData.URL=val;
			sendData.FANS_COUNT=$("#FANS_COUNT").val();
			sendData.FORWARD_COUNT=$('input[name="order.FORWARD_COUNT"]').val();
			sendData.HITS_COUNT=$('input[name="order.HITS_COUNT"]').val();
			var sentimentFromOption = $("#SENTIMENT_FROM").val(); // 获取当前选中的 value（例如 "29"）
			sendData.SENTIMENT_FROM = $("#SENTIMENT_FROM option[value='" + sentimentFromOption + "']").text();
			sendData.subOrderId=orderDetail.orderCode;
			//sendData.ACCOUNT_AREA=$("#ACCOUNT_AREA").val();
			sendData.ACCOUNT_AREA=$("#customerAddress").val();
			sendData.CUST_NAME=$("#CUST_NAME").val();
			sendData.TELEPHONE_NUM=$("#TELEPHONE_NUM").val();
			sendData.CUST_ADDRESS=$("#customerAddress").val();
			sendData.BUY_STORE_NAME=$("#BUY_STORE_NAME").val();
			sendData.E_BUSINESS_NUM=$("#E_BUSINESS_NUM").val();
		}
		function changOne(){
			if(ago5){
		    	 $("#tab-1").load("${ctxPath}/pages/order/order-detail.jsp?type="+deal.type+"&id="+deal.id+"");
		    	 ago5=false;
			}
		}
		function toGvocSentiment(){
			var isLoading = false; // 默认为false
			var orderInfos = {};

			// 安全检查 - 检查orderProRev是否存在
			try {
				if (typeof orderProRev !== 'undefined' && orderProRev !== null) {
					isLoading = orderProRev.isLoading;
					if (isLoading) {
						orderInfos = orderProRev.orderInfos || {};
					}
				}
			} catch (e) {
				console.log("orderProRev未定义，将使用API获取数据");
				isLoading = false;
			}

			if(isLoading){
				// 如果已加载，使用orderProRev中的数据
				var data = $.extend({}, orderDetail.orderInfos, orderInfos);
				console.log('同步舆情工单入参:' + JSON.stringify(data));
				ajax.remoteCall("${ctxPath}/servlet/order?action=GvocSentiment", data, function (result) {
					if (result.state == 1) {
						// 成功处理
					} else {
						//layer.alert(result.msg, {icon: 5});
					}
				});
			} else {
				// 如果未加载或发生错误，从API获取数据
				var data = {"params":{"rev.ID":deal.revId},"controls":["orderInf.orderRevisit"]};
				ajax.remoteCall("${ctxPath}/webcall", data, function(result) {
					orderInfos = {};
					if (result && result['orderInf.orderRevisit'] && result['orderInf.orderRevisit']['data']) {
						var dataInfo = result['orderInf.orderRevisit']['data'];
						orderInfos["rev.OFF_RESULT"] = dataInfo["rev.OFF_RESULT"];
						orderInfos["rev.IS_DELETED"] = dataInfo["rev.IS_DELETED"];
						orderInfos["rev.IS_SUPPLEMENT"] = dataInfo["rev.IS_SUPPLEMENT"];
						orderInfos["rev.NO_DELETE_REASON"] = dataInfo["rev.NO_DELETE_REASON"];
						orderInfos["rev.COMMUNICATION_DESCRIPTION"] = dataInfo["rev.COMMUNICATION_DESCRIPTION"];
						orderInfos["rev.VISIT_TIME"] = dataInfo["rev.VISIT_TIME"];
						orderInfos["rev.OFF_REASON_CLOSED"] = dataInfo["rev.OFF_REASON_CLOSED"];
						orderInfos["rev.OFF_REASON_UNCLOSED"] = dataInfo["rev.OFF_REASON_UNCLOSED"];
						orderInfos["rev.OFF_TIME"] = dataInfo["rev.OFF_TIME"];
						orderInfos["rev.VISIT_RESULT"] = dataInfo["rev.VISIT_RESULT"];
					}

					var finalData = $.extend({}, orderDetail.orderInfos, orderInfos);
					console.log('同步舆情工单入参:' + JSON.stringify(finalData));
					ajax.remoteCall("${ctxPath}/servlet/order?action=GvocSentiment", finalData, function (result) {
						if (result.state == 1) {
							// 成功处理
						} else {
							//layer.alert(result.msg, {icon: 5});
						}
					});
				}, {async:false});
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>