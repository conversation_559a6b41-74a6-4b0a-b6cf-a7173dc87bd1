<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>关键字</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table table-edit table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="60px">大数据关键字</td>
			                        <td width="200px"><input type="text" name=""  data-rules="" id="" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                     		
			                        <td>事业部</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
		                     </tr>
		                      <tr>
			                        <td>渠道</td>
			                        <td>
			                            <select name="" data-mars="" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                     <tr>
		                         
			                         <td>备注</td>
				                     <td colspan="3">
				                        <textarea rows="3" class="form-control input-sm" name=""></textarea>
				                     </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	   jQuery.namespace("");
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>