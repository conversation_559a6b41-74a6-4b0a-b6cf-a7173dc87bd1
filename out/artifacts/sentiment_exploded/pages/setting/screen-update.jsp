<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>更新</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="screenUpform" data-mars="screen.screenOne" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
		<input type="hidden" name="scr.ID" id="ID" value="${param.id}">
		  <table class="table table-edit table-vzebra">
                 <tbody>
                     <tr>
                         <td class="required" >主体</td>
                         <td > <select  name="orgCode" id ="orgCode"  style="width:140px"  class="form-control input-sm"  data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" onchange="changeOrg(this.value)">
					                          <option value="">请选择</option>
					                     </select> </td>
                         
                     </tr>
                       <tr>
                      <td  >分中心</td>
			            <td> <select  class="form-control input-sm"  name="branchCode" id = "branchCode" style="width:200px" data-mars="comm.getBranchCode" >
			                 <option value="">请选择</option>
                         </select></td>
                     </tr>
                  	<tr>
                  	<td >购买渠道</td>
                         <td><select  class="form-control input-sm" style="width:140px"   data-cust-context-path="/yq_common" name="scr.BUY_CHANNEL" data-cust-mars="dict.getDictList('SENTIMENT_BUY_CHANNEL')"> 
							<option value="">请选择</option>
						</select></td>
                  	</tr>
                      <tr>
                         <td>提示信息</td>
                         <td>
                            <textarea class="form-control input-sm" name="scr.PROMPT_INFO" rows="3" maxlength="200" onchange="this.value=this.value.substring(0, 200)" onkeydown="this.value=this.value.substring(0, 200)" onkeyup="this.value=this.value.substring(0, 200)"></textarea>
                         </td>
                     </tr>
                 </tbody>
	      </table>
		  <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="screenUp.ajaxSubmitForm()">保存</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="parent.layer.closeAll();">关闭</button>
		  </div>
	</form>				
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	jQuery.namespace("screenUp");
	 $(function(){
			$("#screenUpform").render({success:function(result){
				var li= result["screen.screenOne"]['data']['scr.PRODUCT_DIVISION'];
				var lt= result["screen.screenOne"]['data']['scr.BRANCHNAME'];
				$("#orgCode").render({success:function(result1){
					var id='';
					$('#orgCode option').each(function(index) {
						var text = $(this).text();
						if (text == li) {
							id=$(this).val();
							$(this).prop('selected', 'selected');
						}
					});
					$("#branchCode").render({data:{"orgCode":id},success:function(result){
						$('#branchCode option').each(function(index){
							var text = $(this).text();
							if (text == lt) {
								id=$(this).val();
								$(this).prop('selected', 'selected');
							}
						});
			    	}});
				
				}});
				
			}});
	   })
	screenUp.ajaxSubmitForm = function(){
		if(form.validate("screenUpform")){		
			screenUp.insertData(); 	
		};
	}
	screenUp.insertData = function() {
		var data = form.getJSONObject("screenUpform");
		var option=$("#orgCode option:selected");
		var option1=$("#branchCode option:selected");
		var teBranch=option1.text();
		if('请选择'==teBranch){
			teBranch='';
		}
		data.PRODUCT_DIVISION=option.text();
		data.BRANCHNAME=teBranch;
		ajax.remoteCall("${ctxPath}/servlet/screen?action=Up",data,function(result) { 
			//debugger;
				if(result.state == 1){
					parent.layer.closeAll();
					parent.screen(); 
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
	function changeOrg(obj){
    	$("#branchCode").render({data:{"orgCode":obj},success:function(result){
    	}});  
		
 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>