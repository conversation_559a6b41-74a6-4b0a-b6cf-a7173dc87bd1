<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>导入</title>
</EasyTag:override>
<EasyTag:override name="content">
		<form id="uploadForm" data-mars="" data-pk="" method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
			                 <tr>
			                      <td width="50px">文件</td>
			                      <td>
			                        	 <input type="file" id="file" name="file"  onchange="" class="hidden" accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel">
			                        	 <button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()"> 选择文件  </button>
			                        	 <a class="btn btn-sm btn-link" href="return false" onclick="javascript:upload.download()" target="_blank">下载导入模板</a>
			                        </td>
		                     </tr>
		                 <!--     <tr>
			                      <td>备注</td>
			                      <td>
			                          <textarea rows="3"  class="form-control input-sm"></textarea>
			                      </td>
			                 </tr> -->
	                    </tbody>
	                </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="upload.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	jQuery.namespace("upload");
	upload.download=function(){
		 location.href ="${ctxPath}/pages/sentiment.xlsx";
	}
	upload.ajaxSubmitForm=function() {
		var files = $('#file').val();
		if(''==files||undefined==files){
			layer.msg("上传内容为空", {icon : 2});
			return false;
		}
			var data = form.getJSONObject("uploadForm");
			var last=JSON.stringify(data); //将JSON对象转化为JSON字符
			$("#uploadForm").attr("enctype","multipart/form-data");
			var formData = new FormData($("#uploadForm")[0]); 
			$.ajax({  
		          url: '${ctxPath}/servlet/setting?action=Upload',  
		          type: 'POST',  
		          data: formData,
		          async: true,cache: false,contentType: false,processData: false,  
		          success: function (result) {
		        	  $("#uploadForm").removeAttr("enctype");
		        	  if(result.state == 1){
  						layer.alert(result.msg,{icon: 1,closeBtn:0,time: 1000},function(){
  						});
  						setTimeout(function () {
							layer.closeAll();
							userinfo();
						  }, 1000);
  						}else{
  						layer.alert(result.msg,{icon: 5});
  					}
  			
		          }
		     }); 
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>