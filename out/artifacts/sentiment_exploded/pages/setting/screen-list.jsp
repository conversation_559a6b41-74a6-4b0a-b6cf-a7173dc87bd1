<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情弹屏信息配置</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="screenForm" class="form-inline" id="screenForm" onsubmit="return false" data-toggle="">
       <input type="hidden" name="PRODUCT_DIVISION" id="PRODUCT_DIVISION" >          	
             	<div class="ibox">
             		<div class="ibox-title clearfix">
             		     <div class="form-group">
             		          <h5><span class="glyphicon glyphicon-list"></span> 舆情弹屏信息配置</h5>
            		          
             		          <div class="input-group  pull-right mr-10">
             		               <button class="btn btn-sm btn-success" type="button" onclick="screen.addUser()"> +添加</button>
             		          </div>
             		     </div>
             		     <hr style="margin:5px -15px">
						 <div class="form-group">
	             		       <div class="input-group ">
								      <span class="input-group-addon">提示信息</span>	
									  <input type="text" name="PROMPT_INFO" class="form-control input-sm" style="width:100px">
							   </div>
							   
							   <div class="input-group ">
								      <span class="input-group-addon">主体</span>	
									   <select  name="orgCodes" id ="orgCodes"   class="form-control input-sm"  data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" onchange="changeOrg(this.value)">
					                          <option value="">请选择</option>
					                     </select> 
							   </div>
							   <div class="input-group ">
						<span class="input-group-addon">购买渠道</span> <select  class="form-control input-sm" style="width:140px"   data-cust-context-path="/yq_common" name="BUY_CHANNEL" data-cust-mars="dict.getDictList('SENTIMENT_BUY_CHANNEL')"> 
							<option value="">请选择</option>
						</select>
					</div>  
							   <div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="screen()"><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
	             		 </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="screen.screenList">
                             <thead>
	                         	 <tr>
	                         	      <th  style="text-align: center;" width="5%">序号</th>
								      <th  style="text-align: center;" width="10%">主体</th>
								      <th  style="text-align: center;" width="10%">分中心</th>
								      <th  style="text-align: center;" width="10%">购买渠道</th>
								      <th  style="text-align: center;" width="35%">提示信息</th>
								      <th  style="text-align: center;" width="10%">创建人账号</th>
								      <th  style="text-align: center;" width="10%">操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                              
                             </tbody>
		                 </table>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:#index+1}}</td>
											<td>{{:PRODUCT_DIVISION}}</td>
											<td>{{:BRANCHNAME}}</td>
											<td>{{dictFUN:BUY_CHANNEL "SENTIMENT_BUY_CHANNEL"}}</td>
											<td>{{:PROMPT_INFO}}</td>
											<td>{{:CREATE_ACC}}</td>
											  <td>
                                          <a href="javascript:screen.editUser('{{:ID}}')" >修改</a>&nbsp;
                                          <a href="javascript:;screen.dele('{{:ID}}')" >删除</a>
                                      </td>
									    </tr>
								   {{/for}}					         
							 </script>
	                  <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("screen");
		   $(function(){
				$("#screenForm").render();
		   })
		screen.addUser = function(){
			 popup.layerShow({type:2,title:'新增',offset:'20px',shadeClose:false,area:['480px','360px']},"${ctxPath}/pages/setting/screen-edit.jsp",null);
		}
        
		screen.editUser = function(val){
			 popup.layerShow({type:2,title:'编辑',offset:'20px',shadeClose:false,area:['480px','360px']},"${ctxPath}/pages/setting/screen-update.jsp",{id:val});
		}
		
				
		function screen(){
			var option=$("#orgCodes option:selected");				
			var DEPARTMENT=option.text();
			if('请选择'!=DEPARTMENT){
				$("#PRODUCT_DIVISION").val(DEPARTMENT);
			}
			
			$("#screenForm").searchData();
		}
		screen.dele = function(val){
			 layer.confirm('确认删除?', 
			            
			            {
			              btn: ['确定','取消'] //按钮
			    
			            }, 
			            
			            function(index){
			       	var data={};
			data.id=val;
			ajax.remoteCall("${ctxPath}/servlet/screen?action=Dele",data,function(result) { 
			    				//debugger;
			    					if(result.state == 1){
			    						layer.alert(result.msg,{icon: 1,closeBtn:0,time: 1000},function(){
			    						});
			    						screen();
			    						}else{
			    						layer.alert(result.msg,{icon: 5});
			    					}
			    				}
			    			);
			              return true;
			            },
			            function(index){
			                layer.msg('已取消！', {icon: 1});
			                return false;
			            }
			        );
	 }
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>