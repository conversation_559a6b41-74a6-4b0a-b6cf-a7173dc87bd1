<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>配置详情</title>
    <style>
		.info-title p:before {
		    content: '';
		    position: absolute;
		    background-color: #337ab7;
		    left: -1px;
		    height: 15px;
		    width: 3px;
		    top: 50%;
		    margin-top: -5px;
		 }
		.info-title p {
		    border-bottom: none;
		    position: relative;
		    height: 35px
		 }
		 .tab-page{margin-right:15px;margin-left:15px;padding:15px;background:#fff}
		 .tab-page .tab-content{padding:15px}
	</style>
</EasyTag:override>
<EasyTag:override name="content">		     
			            <form id="searchPlanInfoForm_3"  data-mars="" method="post"  >
		           	<div class="ibox">
			            
			             <input type="hidden"  name="ID" value="${param.id}">		
				            <table class="table table-auto table-bordered table-hover table-condensed text-c" id="tableHead_1" data-mars="setting.configInfo" >
					        	    <thead>
						        			<tr>
						        				<th class="text-c" width="20%">事业部</th>
						        				<th class="text-c"  width="20%">分中心</th>
						        				<th class="text-c"  width="20%">品类</th>						     
						        				<th class="text-c"  width="20%">发送类型</th>						     
						        				<th class="text-c"  width="20%">升级类型</th>						     
										        	</tr>
					        		</thead>
					        		<tbody id="dataList_1">
									    
								    </tbody>
						    </table>
						    </div>									 
						</form>					
</EasyTag:override>

<EasyTag:override name="script">
<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>

	<script type="text/javascript">
	jQuery.namespace("planremindInfo");
	    $(function(){
	    	 $("#searchPlanInfoForm_3").render({success:function(result){
	    		 debugger;
	  		 		var li_1=result["setting.configInfo"]["data"]; 
	  		 			var str="";
	  		 			var ago1="";
	  		 			var ago2="";
	  		 			var ago3="";
	  		 			var ago4="";
	  		 			var ago5="";
	  		 			for(var i=0;i<li_1.length;i++){
		  		 			var val=li_1[i].SEND_PARAM;
	  		 				if('01'==val||'04'==val){
	  		 					ago1+=li_1[i].SEND_VALUE+"<br/>";
	  		 				}else if('02'==val){
	  		 					ago2+=li_1[i].SEND_VALUE+"<br/>";
	  		 				}else if('03'==val){
	  		 					ago3+=li_1[i].SEND_VALUE+"<br/>";
	  		 				}else if('05'==val){
	  		 					ago4+=li_1[i].SEND_VALUE+"<br/>";
	  		 				} else if('06'==val){
	  		 					ago5+=li_1[i].SEND_VALUE+"<br/>";
	  		 				}
	  		 				
	  		 			}
	  		 				str+="<tr>";		 	
			 				str+="<td>"+ago1+"</td>";
			 				str+="<td>"+ago3+"</td>";
			 				str+="<td>"+ago2+"</td>";
			 				str+="<td>"+ago4+"</td>";
			 				str+="<td>"+ago5+"</td>";
			 				str+="</tr>";
				$("#dataList_1").html(str);
	  		 	 }}); 
	    })
  		
  		 	
  		 	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>