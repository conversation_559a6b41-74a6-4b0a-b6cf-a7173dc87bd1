<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>常用语管理</title>
	<style type="text/css">
		.labelListDiv{height: 32px;line-height: 32px;padding: 0px 10px;font-size: 13px;}
		.labelListDiv.active{background-color:#f8f8f8}
		.labelListDiv a{text-decoration: none;color: #666;}
		.ibox-content table tr td label+label{margin-left:10px}
    </style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="searchForm" name="searchForm" class="form-inline">
			<div class="row">
			<div  style="background-color: #fff;margin-left: 15px;width: 20%;float: left;height: 530px;">
				<div style="height: 52px;line-height: 52px;padding: 0px 15px;border-bottom: 1px solid #eee;">
				    <span class="glyphicon glyphicon-home"></span> 常用语目录
				    <button style="float: right;margin-top: 11px;" type="button" class="btn btn-sm btn-default " onclick="word.addCatalog()">+新增目录</button>	
				</div>
			    <div data-template="labelList-template" id="labelTableHead" data-mars="">
			        <div class="clearfix labelListDiv active">
						<div class="pull-left"><a href="javascript:void(0)" onclick="">空调类</a></div>                                     
						<div class="pull-right">
			                <a onclick="word.editCatalog()" href="javascript:void(0)" title = "修改">修改</a> -
			                <a onclick="" href="javascript:void(0)" title = "删除">删除</a>
						</div>                                         
				    </div>
				    <div class="clearfix labelListDiv">
						<div class="pull-left"><a href="javascript:void(0)" onclick="">冰箱类</a></div>                                     
						<div class="pull-right">
			                <a onclick="word.editCatalog()" href="javascript:void(0)" title = "修改">修改</a> -
			                <a onclick="" href="javascript:void(0)" title = "删除">删除</a>
						</div>                                         
				    </div>
			    </div>
			</div>
			<script id="labelList-template" type="text/x-jsrender">
								   {{for  data}}
										<div class="clearfix labelListDiv">
											<div class="pull-left"><a href="javascript:void(0)" onclick="">{{:}}</a></div>                                     
											<div width="80px" class="pull-right">
								                <a onclick="" href="javascript:void(0)" title = "修改">修改</a> -
								                <a onclick="" href="javascript:void(0)" title = "删除">删除</a>
											</div>                                         
									    </div>
								    {{/for}}					         
			</script>
			<div style="height: 450px;width: 76%;float: left;margin-left: 15px;">
				<div class="ibox">
						<div class="ibox-title clearfix" >
						   <div class="form-group">
							    <h5><span class="glyphicon glyphicon-list"></span> 常用语</h5>
							    <div class="input-group input-group-sm pull-right">
							        <button  class="btn btn-sm btn-success" type="button" onclick="word.addWord()"> +新增</button>
							    </div>
								<div class="input-group input-group-sm ml-8">
			             		    <span class="input-group-addon">标题</span>
			             		     <input type="text" name="" class="form-control input-sm" style="width:120px">
								</div>
								   <div class="input-group input-group-sm">
			             		    <span class="input-group-addon">内容</span>
			             		     <input type="text" name="" class="form-control input-sm" style="width:160px">
								</div>
								<div class="input-group input-group-sm">
			             		     <span class="input-group-addon">启用状态</span>
			             		     <select name="" class="form-control input-sm">
			             		          <option>启用</option>
			             		     </select>
								</div>
								<div class="input-group input-group-sm">
								    <button  class="btn btn-sm btn-default" type="button" onclick=""><span class="glyphicon glyphicon-search"></span> 查询</button>
								</div>
							</div>
						</div>
					 	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="">
                             <thead>
	                         	 <tr>
								      <th>序号</th>
								      <th>标题</th>
								      <th>内容</th>
								      <th>启用状态</th>
								      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                   <tr>
										<td>1</td>    
										<td>欢迎语</td>                                      
										<td>欢迎(´･ω･`)4如家酒店见风使舵福克斯孔明灯发</td>         
                                        <td>启用</td>      
										<td> 
											<a  href="javascript:void(0)"  onclick="word.editWord()">修改</a>&nbsp;
											<a  href="javascript:void(0)"  onclick="">删除</a>&nbsp;
										</td>
								    </tr>
                             </tbody>
		                 </table>
                         <script id="list-template" type="text/x-jsrender">
								   {{for  list}}
										<tr>
											<td>{{:}}</td>                                         
											<td>{{:}}</td>         
                                            <td>{{:}}</td>                                         
											<td>{{:}}</td>      
                                            <td>{{:}}</td>                                   
											<td> 
												<a  href="javascript:void(0)"  onclick="">修改</a>&nbsp;
												<a  href="javascript:void(0)"  onclick="">删除</a>&nbsp;
											</td>
									    </tr>
								    {{/for}}					         
					     </script>
	              	</div> 
                      	 
                     <div class="row paginate" id="page">
                     	<jsp:include page="/pages/common/pagination.jsp"/>
                     </div> 
					 </div>
				</div>
			</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("word");
		
		$(function(){
			$(".labelListDiv").find("a").click(function(){
				var obj = $(this);
				$(obj).parent().parent().addClass("active");
				$(obj).parent().parent().siblings().removeClass("active");
			});
		})
	
		word.addCatalog=function(){
		    popup.layerShow({type:1,title:'新增目录',offset:'20px',area:['340px','180px']},"${ctxPath}/pages/setting/word-catalog-edit.jsp",null);
		}
		
		word.editCatalog=function(){
		    popup.layerShow({type:1,title:'编辑目录',offset:'20px',area:['340px','180px']},"${ctxPath}/pages/setting/word-catalog-edit.jsp",null);
		}
		
		word.addWord=function(){
		    popup.layerShow({type:1,title:'新增常用语',offset:'20px',area:['460px','340px']},"${ctxPath}/pages/setting/welcomeword-edit.jsp",null);
		}
		
		word.editWord=function(){
		    popup.layerShow({type:1,title:'编辑常用语',offset:'20px',area:['460px','340px']},"${ctxPath}/pages/setting/welcomeword-edit.jsp",null);
		}
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>