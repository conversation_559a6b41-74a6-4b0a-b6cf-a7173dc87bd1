<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>发送配置</title>
	<link rel="stylesheet" href="${ctxPath}/static/lib/zTree_v3/css/zTreeStyle/zTreeStyle.css" type="text/css">
	<script type="text/javascript" src="${ctxPath}/static/lib/zTree_v3/js/jquery.ztree.core.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/lib/zTree_v3/js/jquery.ztree.excheck.js"></script>
	<style>
	    .ztree .line{border:none}
	</style>
</EasyTag:override>
<EasyTag:override name="content"> 
<form id="configForm" data-mars=""  method="post"  autocomplete="off"  >
	<input type="hidden" name="sysCode" id="sysCode">
	<input type="hidden" name="category" id="category">
	<input type="hidden" name="channal" id="channal">
	<input type="hidden" name="CONTACT_ID" value="${param.id}">
    <div class="row">
        <div class="col-md-4 col-xs-4">
            <div class="slimScrollDiv" id="slimScrollDiv" >
			     <div class="ztree"  id="ztree1"  > </div>
		     </div>
        </div>
        <div class="col-md-3 col-xs-3">
              <div class="slimScrollDiv" id="slimScrollDiv1">
			     <div class="ztree"  id="ztree2" > </div>
		     </div>
        </div>
        <div class="col-md-5 col-xs-5">
              <div class="slimScrollDiv" id="slimScrollDiv2">
			     <div class="ztree"  id="ztree3" > </div>
		     </div>
        </div>
    </div>
    <div>
        <span class="mr-15">选择无分中心人员事业部</span>
            <select  name="orgCodes" id ="orgCodes"   class="form-control input-sm"   
             data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" style="width: 180px;"  multiple="multiple" >
			</select> 
    </div>
    <div class="mt-15">
        <span class="mr-10" style="padding-left:28px">发送方式</span>
   	    <label class="checkbox checkbox-info checkbox-inline">
              <input type="checkbox" value="电话" name="sendMethod" > <span> 电话</span>
       	</label>
       	<label class="checkbox checkbox-info checkbox-inline">
              <input type="checkbox" value="邮件" name="sendMethod" > <span> 邮件</span>
        </label>
       	<label class="checkbox checkbox-info checkbox-inline">
              <input type="checkbox" value="短信" name="sendMethod" > <span> 短信</span>
       	</label>
    </div>
	<div class="layer-foot text-c">
	   		<button class="btn btn-sm btn-primary"  type="button" onclick="userConfig.ajaxSubmitForm()">保存</button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="parent.layer.closeAll();">关闭</button>
    </div>
		</form>	
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
<link rel="stylesheet" type="text/css" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css">

	<script type="text/javascript">
	jQuery.namespace("userConfig");
	userConfig.id='${param.id}'
		  $(document).ready(function(){

					requreLib.setplugs('slimscroll,ztree',function(){					
					 $('#slimScrollDiv').slimScroll({  
			                height: '330px',
			                color: '#000000'
			        });
					   $.fn.zTree.init($("#ztree1"), setting,null);  
			            setTimeout(function(){  
			               // expandAll("ztree1");  
			            },1000);//延迟加载  	
				});					         
					requreLib.setplugs('slimscroll,ztree',function(){					
					 $('#slimScrollDiv2').slimScroll({  
			                height: '330px',
			                color: '#000000'
			        });
					   $.fn.zTree.init($("#ztree3"), setting3,null);  
			            setTimeout(function(){  
			               // expandAll("ztree1");  
			            },1000);//延迟加载  	
				});					         
					requreLib.setplugs('slimscroll,ztree',function(){					
					 $('#slimScrollDiv1').slimScroll({  
			                height: '330px',
			                color: '#000000'
			        });
					   $.fn.zTree.init($("#ztree2"), setting2,null);  
			            setTimeout(function(){  
			                //expandAll1("ztree2");  
			            },1000);//延迟加载  	
				});	
	
	        }); 
	$(function(){			
		$("#orgCodes").render({success : function(result){
			  changConfig();
			 mulSelect2('orgCodes');
		 }});
	});
		 var setting = { 
			check:{
				enable:true
				} ,
            async: {  
                enable: true,  
                url:'${ctxPath}/servlet/setting?action=getTree',  
                autoParam:["id"],  
                otherParam:{"config":userConfig.id},  
               // dataFilter: filter,  
                type: "get"  
            },  
            callback: {  
               // beforeAsync: beforeAsync,  
                onAsyncSuccess: onAsyncSuccess1 
            }           
        };  
		 var setting3 = { 
			check:{
				enable:true
				} ,
            async: {  
                enable: true,  
                url:'${ctxPath}/servlet/setting?action=getTree3',  
                autoParam:["id"],  
                otherParam:{"config":userConfig.id},  
               // dataFilter: filter,  
                type: "get"  
            },  
            callback: {  
               // beforeAsync: beforeAsync,  
               onAsyncSuccess: onAsyncSuccess3
            }           
        };  
  
		 var setting2 = { 
			check:{
				enable:true
				} ,
            async: {  
                enable: true,  
                url:'${ctxPath}/servlet/setting?action=getTree2',  
                autoParam:["id"],  
                otherParam:{"config":userConfig.id},  
               // dataFilter: filter,  
                type: "get"  
            },  
            callback: {  
               /*  beforeAsync: beforeAsync1,  */
                onAsyncSuccess: onAsyncSuccess2   
            }           
        };  
      /*   function filter(treeId, parentNode, childNodes) {  
            if (!childNodes) return null;  
            for (var i=0, l=childNodes.length; i<l; i++) {  
                childNodes[i].name = childNodes[i].name.replace(/\.n/g, '.');  
            }  
            return childNodes;  
        }  
  
        function beforeAsync() {  
            curAsyncCount++;  
        }  
          
        function onAsyncSuccess(event, treeId, treeNode, msg) {  
            curAsyncCount--;  
            if (curStatus == "expand") {  
                expandNodes(treeNode.children);  
            } else if (curStatus == "async") {  
                asyncNodes(treeNode.children);  
            }  
  
            if (curAsyncCount <= 0) {  
                curStatus = "";  
            }  
        }  
  
        var curStatus = "init", curAsyncCount = 0, goAsync = false;  
        function expandAll() {  
            if (!check()) {  
                return;  
            }  
            var zTree = $.fn.zTree.getZTreeObj("ztree1");  
            expandNodes(zTree.getNodes());  
            if (!goAsync) {  
                curStatus = "";  
            }  
        }  
        function expandNodes(nodes) {  
            if (!nodes) return;  
            curStatus = "expand";  
            var zTree = $.fn.zTree.getZTreeObj("ztree1");  
            for (var i=0, l=nodes.length; i<l; i++) {  
                zTree.expandNode(nodes[i], true, false, false);//展开节点就会调用后台查询子节点  
                if (nodes[i].isParent && nodes[i].zAsync) {  
                    expandNodes(nodes[i].children);//递归  
                } else {  
                    goAsync = true;  
                }  
            }  
        }  
  
        function check() {  
            if (curAsyncCount > 0) {  
                return false;  
            }  
            return true;  
        }  
        //分割线
        function beforeAsync1() {  
            curAsyncCount1++;  
        }  
          
        function onAsyncSuccess1(event, treeId, treeNode, msg) {  
            curAsyncCount1--;  
            if (curStatus1 == "expand") {  
                expandNodes1(treeNode.children);  
            } else if (curStatus1 == "async") {  
                asyncNodes1(treeNode.children);  
            }  
  
            if (curAsyncCount1 <= 0) {  
                curStatus1 = "";  
            }  
        }  
  
        var curStatus1 = "init", curAsyncCount1 = 0, goAsync1 = false;  
        function expandAll1() {  
            if (!check1()) {  
                return;  
            }  
            var zTree = $.fn.zTree.getZTreeObj("ztree2");  
            expandNodes1(zTree.getNodes());  
            if (!goAsync1) {  
                curStatus1 = "";  
            }  
        }  
        function expandNodes1(nodes) {  
            if (!nodes) return;  
            curStatus1 = "expand";  
            var zTree = $.fn.zTree.getZTreeObj("ztree2");  
            for (var i=0, l=nodes.length; i<l; i++) {  
                zTree.expandNode(nodes[i], true, false, false);//展开节点就会调用后台查询子节点  
                if (nodes[i].isParent && nodes[i].zAsync) {  
                    expandNodes1(nodes[i].children);//递归  
                } else {  
                    goAsync1 = true;  
                }  
            }  
        }  
  
        function check1() {  
            if (curAsyncCount1 > 0) {  
                return false;  
            }  
            return true;  
        } */
        userConfig.ajaxSubmitForm=function(){
      	  var treeObj=$.fn.zTree.getZTreeObj("ztree1")
 	   		  nodes=treeObj.getCheckedNodes(true)
     		  var vm="";  
     		  var vc="";  
		 	  for(var i=0;i<nodes.length;i++){
		  	  if(true!=nodes[i].isParent){
		  		  	vm+=nodes[i].name + ";"; 
		       	  }else{
		       		vc+=nodes[i].name + ";";
		       	  }
		    } 
      	  var treeObj=$.fn.zTree.getZTreeObj("ztree3")
 	   		  nodes=treeObj.getCheckedNodes(true)
     		  var upgradeType="";  
		 	  for(var i=0;i<nodes.length;i++){
		  	  if(true!=nodes[i].isParent){
		  		upgradeType+=nodes[i].code + ";"; 
		       	  }
		    } 
    		
      	  var treeObj=$.fn.zTree.getZTreeObj("ztree2")
 	   		  nodes=treeObj.getCheckedNodes(true)
     		  var vd="";  
		 	  for(var i=0;i<nodes.length;i++){
		  	  if(true!=nodes[i].isParent){
		  		  vd+=nodes[i].name + ";"; 
		       	  }
		    } 
    		
    		var cont = '';
    		$("input:checkbox[name='sendMethod']:checked").each(function(){
    			cont+=$(this).text()+";"; 
            });
    	/* 	if(vm==''){
    			layer.alert("请选择分中心！",{icon:2});
    			return false;
    		} */
    	/* 	if(vd==''){
    			layer.alert("请选择品类！",{icon:2});
    			return false;
    		} */
    		if(cont==''){
    			layer.alert("请选择发送方式！",{icon:2});
    			return false;
    		}
    		var orgStr='';
    		$("#orgCodes option:selected").each(function(){
    			orgStr+=$(this).text()+";"; 
            });
    		var data = form.getJSONObject("configForm");
    		data.sysCode=vm;
    		data.category=vd;
    		data.channal=vc;
    		data.upgradeType=upgradeType;
    		data.orgStr=orgStr;
     		 ajax.remoteCall("${ctxPath}/servlet/config?action=addConfig",data,function(result) { 
    			//debugger;
    				if(result.state == 1){
    					layer.alert(result.msg,{icon: 1,time:1000},function(){
    					});
    					setTimeout(function () {
    						parent.layer.closeAll();
    						parent.userinfo(-1);
    					  }, 1000);
    					}else{
    					layer.alert(result.msg,{icon: 5});
    				}
    			}
    		);  
      }
      function changConfig(){
    	  var data={};
    	  data.id=userConfig.id;
    	  ajax.remoteCall("${ctxPath}/servlet/config?action=infoData",data,function(result) { 
    				if(result.state == 1){
    					var ret = result.data;
    					if(''!=ret){
    						var valData;
    						if(''!=ret.SEND_PARAM1){
    	    					 valData=ret.SEND_PARAM1.split(";");
    						}
    						if(''!=ret.SEND_PARAM2&&undefined!=ret.SEND_PARAM2){
    							var  valData1=ret.SEND_PARAM2.split(";"); 
	    							$("#orgCodes option").each(function(index){
	        	    					for(var i=0;i<valData1.length;i++){
	    	    							var text=$(this).text();
	    	    							if(text==valData1[i]){
	    	    								$(this).prop('selected','selected');
	    	    								break;
	    	    							}
	        	    					}
	        						});
	        						$('#orgCodes').multiselect('rebuild');
	        			            $('#orgCodes').multiselect('refresh');
    						}
	    					//initradio('ifDanger',ret.SEND_PARAM);
	    					for(var i=0;i<valData.length;i++){
	    						initradio('sendMethod',valData[i]);
	    					}
    					
    						
    					}
    					}else{
    					layer.alert(result.msg,{icon: 5});
    				}
    			}
    		);  
      }
      function initradio(rName,rValue){
          var rObj = document.getElementsByName(rName);

          for(var i = 0;i < rObj.length;i++){
              if(rObj[i].value == rValue){
                  rObj[i].checked =  'checked';
              }
          }
      }
      function mulSelect2(id){
			$('#'+id).multiselect({
				 buttonWidth: '180px',
				 allSelectedText: "全部",
				 nonSelectedText:"全部",
				 nSelectedText:"个被选中",
				 selectAllNumber:false,
				 maxHeight:350,
				 onChange:function(option,checked,select){
					 var sel_id =option.val();
					 if(sel_id==""){
						 var inps=option.parent().children();
						 var index=[];
						 for(var i=1; i<inps.length; i++){
							 index.push(inps[i].value);
						 }
						 if(checked==true){
							 $('#'+id).multiselect('select',index);
							 //alert("option="+sel_id+",checked="+checked); 
						 }else{
							 $('#'+id).multiselect('deselect',index);
							 //alert("option="+sel_id+",checked="+checked); 
						 }
					 }
				 }
			 });
		}
      function onAsyncSuccess1(event, treeId, treeNode, msg) {  
        var treeObj=$.fn.zTree.getZTreeObj("ztree1")
  		  nodes=treeObj.getCheckedNodes(true)
        for(var j=0;j<nodes.length;j++){
        	var node = treeObj.getNodeByParam("id",nodes[j].id );
        	//treeObj.selectNode(node,true);//指定选中ID的节点
			treeObj.expandNode(node, true, false);//指定选中ID节点展开

	    } 

      } 
      function onAsyncSuccess3(event, treeId, treeNode, msg) {  
        var treeObj=$.fn.zTree.getZTreeObj("ztree3")
  		  nodes=treeObj.getCheckedNodes(true)
        for(var j=0;j<nodes.length;j++){
        	var node = treeObj.getNodeByParam("id",nodes[j].id );
        	//treeObj.selectNode(node,true);//指定选中ID的节点
			treeObj.expandNode(node, true, false);//指定选中ID节点展开

	    } 

      } 
      function onAsyncSuccess2(event, treeId, treeNode, msg) {  
        var treeObj=$.fn.zTree.getZTreeObj("ztree2")
  		  nodes=treeObj.getCheckedNodes(true)
        for(var j=0;j<nodes.length;j++){
        	var node = treeObj.getNodeByParam("id",nodes[j].id );
        	//treeObj.selectNode(node,true);//指定选中ID的节点
			treeObj.expandNode(node, true, false);//指定选中ID节点展开

	    } 

      } 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>