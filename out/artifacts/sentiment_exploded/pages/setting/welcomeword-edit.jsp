<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>关键字</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars=""  method="post"  autocomplete="off" data-mars-prefix="" >
				  <table class="table table-edit table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px">常用语目录</td>
			                        <td width="220px"><input type="text" name=""  readonly="readonly" id="" class="form-control input-sm" value="空调类"></td>
		                     </tr>
		                     <tr>
		                     		
			                        <td>标题</td>
			                        <td><input type="text" name="" id="" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                         
			                         <td>内容</td>
				                     <td colspan="3">
				                        <textarea rows="3" class="form-control input-sm" name=""></textarea>
				                     </td>
		                     </tr>
		                      <tr>
			                        <td>是否启用</td>
			                        <td>
			                            <label class="radio-inline">
				                        	<input type="radio" value="" checked="checked" name="ifuse"> 启用
				                        </label>
				                        <label class="radio-inline">
				                        	<input type="radio" value=""  name="ifuse"> 不启用
				                        </label>
			                        </td>
		                     </tr>
		                     
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	   jQuery.namespace("");
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>