<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>用户信息管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
<!--        <input type="hidden" name="DEPARTMENT" id="DEPARTMENT" >          	
 -->              
			 <input type="hidden" id="changePage" name="changePage">                        	
 					<div class="ibox">
             		<div class="ibox-title clearfix">
             		     <div class="form-group">
             		          <h5><span class="glyphicon glyphicon-list"></span> 用户信息管理</h5>
            		          <div class="input-group  pull-right mr-10">
             		               <button class="btn btn-sm btn-success" type="button" onclick="userinfo.importData()"><span class="glyphicon glyphicon-import"></span> 导入</button>
             		          </div>
             		          <div class="input-group  pull-right mr-10">
             		               <button class="btn btn-sm btn-success" type="button" onclick="userinfo.addUser()"> +添加</button>
             		          </div>
             		     </div>
             		     <hr style="margin:5px -15px">
						 <div class="form-group">
	             		       <div class="input-group ">
								      <span class="input-group-addon">处理人</span>	
									  <input type="text" name="USER_NAME" class="form-control input-sm" style="width:100px">
							   </div>
							   <div class="input-group ">
								      <span class="input-group-addon">电话号码</span>	
									  <input type="text" name="PHONE_NUM" class="form-control input-sm" style="width:110px">
							   </div> 
							   <div class="input-group ">
								      <span class="input-group-addon">邮箱地址</span>	
									  <input type="text" name="EMAIL" class="form-control input-sm" style="width:120px">
							   </div>  
							  <!--  <div class="input-group ">
								      <span class="input-group-addon">事业部</span>	
									   <select  name="orgCodes" id ="orgCodes"   class="form-control input-sm"  data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" onchange="changeOrg(this.value)">
					                          <option value="">请选择</option>
					                     </select> 
							   </div> -->  
							   <div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="userinfo()"><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							   <div class="input-group ">
								    <button type="button" class="btn btn-sm btn-danger" onclick="deleS()">删除</button>
							  </div>
	             		 </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed"  id="tableHead" data-mars="setting.userInfoTd">
                             <thead>
	                         	 <tr>
	                         	 	<th class="text-c"><input type="checkbox" onchange="checkAll($(this))"/></th>	                         	 
	                         	      <th>序号</th>
								      <th>岗位</th>
								  <!--     <th>事业部</th>
								      <th>分中心</th> -->
								      <th>处理人</th>
								      <th>电话号码</th>
								      <th>邮箱地址</th>
								      <th>MIP</th>
								      <th>发送/抄送</th>
								      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                              
                             </tbody>
		                 </table>
<!-- 											<td>{{:DEPARTMENT}}</td>
											<td>{{:SUB_CENTER}}</td> -->
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c"><input type="checkbox" data-id="{{:ID}}"></td>
											<td>{{:#index+1}}</td>
											<td>{{dictFUN:POSITION "SENTIMENT_CONTACT_POSITION"}}</td>
											<td>{{:USER_NAME}}</td>
											<td>{{:PHONE_NUM}}</td>
											<td>{{:EMAIL}}</td>
											<td>{{:MIP}}</td>
											<td>{{dictFUN:CONTACT_TYPE "SENTIMENT_PROCESS_SEND_TYPE"}}</td>
											  <td>
                                          <a href="javascript:userinfo.editUser('{{:ID}}')" >修改</a>&nbsp;
                                          <a href="javascript:userinfo.config('{{:ID}}')">发送配置</a>&nbsp;
                                          <a href="javascript:;userinfo.configInfo('{{:ID}}')" >配置详情</a>
                                          <a href="javascript:;userinfo.dele('{{:ID}}')" >删除</a>
                                      </td>
									    </tr>
								   {{/for}}					         
							 </script>
	                  <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("userinfo");
		   $(function(){
				$("#searchForm").render();
		   })
		userinfo.addUser = function(){
			 popup.layerShow({type:2,title:'新增用户',offset:'20px',shadeClose:false,area:['480px','400px']},"${ctxPath}/pages/setting/userinfo-edit.jsp",null);
		}
        
		userinfo.editUser = function(val){
			 popup.layerShow({type:2,title:'编辑用户',offset:'20px',shadeClose:false,area:['480px','400px']},"${ctxPath}/pages/setting/userinfo-update.jsp",{id:val});
		}
		
		
		
		userinfo.importData = function(){
		    popup.layerShow({type:1,title:'导入用户',area:['420px','290px'],shadeClose:false,offset:'20px'},"${ctxPath}/pages/setting/keyword-import.jsp",null);
		   		     
		}
		
		userinfo.config = function(val){
			popup.layerShow({type:2,title:'发送配置',offset:'20px',shadeClose:false,area:['870px','560px']},"${ctxPath}/pages/setting/send-config.jsp?time="+Math.round(Math.random()),{id:val});
		}
		userinfo.configInfo = function(val){
			popup.layerShow({type:2,title:'配置详情',offset:'20px',shadeClose:false,area:['670px','560px']},"${ctxPath}/pages/setting/send-configInfo.jsp",{id:val});
		}
		function userinfo(count){
			/* var option=$("#orgCodes option:selected");				
			var DEPARTMENT=option.text();
			$("#DEPARTMENT").val(DEPARTMENT); */
			if(''!=count&&undefined!=count){
				var totalRowV= $(".totalRowV").val()-count;
				var totalRow= $(".totalRow").html()-count;
				 $(".totalRowV").val(totalRowV);
				 $(".totalRow").html(totalRow)
				
				var num= $("input[name='pageIndex']").val();
				$("#changePage").val(num);
				$("#searchForm").searchData({success:function(result){
					$("#changePage").val('');

				}});
				
			}else if('-1'==count){
				var num= $("input[name='pageIndex']").val();
				$("#changePage").val(num);
				$("#searchForm").searchData({success:function(result){
					$("#changePage").val('');

				}});
			}else{
				$("#searchForm").searchData({success:function(result){
					$("#changePage").val('');

				}});
			}
			
		}
		userinfo.dele = function(val){
			 layer.confirm('确认删除?', 
			            
			            {
			              btn: ['确定','取消'] //按钮
			    
			            }, 
			            
			            function(index){
			       	var data={};
			data.id=val;
			ajax.remoteCall("${ctxPath}/servlet/setting?action=deleUser",data,function(result) { 
			    				//debugger;
			    					if(result.state == 1){
			    						layer.alert(result.msg,{icon: 1,closeBtn:0,time: 1000},function(){
			    						});
			    						userinfo(1);
			    						}else{
			    						layer.alert(result.msg,{icon: 5});
			    					}
			    				}
			    			);
			              return true;
			            },
			            function(index){
			                layer.msg('已取消！', {icon: 1});
			                return false;
			            }
			        );
	 }
		function checkAll(obj){
			$("#dataList").find("input:checkbox").prop('checked', obj.is(':checked'));
		}	
		function deleS(){
			 var checkedRecord = $("#dataList").find("input[type='checkbox']:checked");
				if(checkedRecord.length<1){ 			
					layer.alert('请选择需要删除的行！',{icon:2});
					return false;
				}
			 layer.confirm('确认删除?', 
			            
			            {
			              btn: ['确定','取消'] //按钮
			    
			            }, 
			            
			            function(index){
			            	  var checkedRecord = $("#dataList").find("input[type='checkbox']:checked");
					            var arr=new Array();
								var data={};
								var leng=checkedRecord.length;
								for(var i=0;i<leng;i++){
									arr.push($(checkedRecord[i]).attr("data-id"));
								}
								data.ids=arr;
			ajax.remoteCall("${ctxPath}/servlet/setting?action=deleUsers",data,function(result) { 
			    				//debugger;
			    					if(result.state == 1){
			    						layer.alert(result.msg,{icon: 1,closeBtn:0,time: 1000},function(){
			    						});
			    						userinfo(leng);
			    						
			    						
			    						}else{
			    						layer.alert(result.msg,{icon: 5});
			    					}
			    				}
			    			);
			              return true;
			            },
			            function(index){
			                layer.msg('已取消！', {icon: 1});
			                return false;
			            }
			        );
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>