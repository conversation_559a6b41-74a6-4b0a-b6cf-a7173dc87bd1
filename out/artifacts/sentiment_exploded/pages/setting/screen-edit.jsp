<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>新增</title>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="screenAddform" data-mars="" style="margin-top: 10px;"  autocomplete="off" data-mars-prefix="">
		  <table class="table table-edit table-vzebra">
                 <tbody>
                     <tr>
                         <td class="required" >主体</td>
                         <td > <select  name="orgCode" data-rules="required" id ="orgCode"  style="width:200px"  class="form-control input-sm"  data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" onchange="changeOrg(this.value)">
					                          <option value="">请选择</option>
					                     </select> </td>
                         
                     </tr>
                     <tr>
                      <td  >分中心</td>
			            <td> <select  class="form-control input-sm"  name="branchCode" id = "branchCode" style="width:200px" data-mars="comm.getBranchCode" >
			                 <option value="">请选择</option>
                         </select></td>
                     </tr>
                  	<tr>
                  	<td >购买渠道</td>
                         <td><select  class="form-control input-sm"  style="width:200px"   data-cust-context-path="/yq_common" name="BUY_CHANNEL" data-cust-mars="dict.getDictList('SENTIMENT_BUY_CHANNEL')"> 
							<option value="">请选择</option>
						</select></td>
                  	</tr>
                      <tr>
                         <td class="required">提示信息</td>
                         <td>
                            <textarea class="form-control input-sm" data-rules="required" name="PROMPT_INFO" rows="3" maxlength="200" onchange="this.value=this.value.substring(0, 200)" onkeydown="this.value=this.value.substring(0, 200)" onkeyup="this.value=this.value.substring(0, 200)"></textarea>
                         </td>
                     </tr>
                 </tbody>
	      </table>
		  <div class="layer-foot text-c">
		   		<button class="btn btn-sm btn-primary"  type="button" id="submit-form" onclick="screenEdit.ajaxSubmitForm()">保存</button>
		   		<button class="btn btn-sm btn-default ml-20"  type="button" onclick="parent.layer.closeAll();">关闭</button>
		  </div>
	</form>				
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
	jQuery.namespace("screenEdit");
	 $(function(){
			$("#screenAddform").render();
	   })
	screenEdit.ajaxSubmitForm = function(){
		if(form.validate("screenAddform")){		
			screenEdit.insertData(); 	
		};
	}
	screenEdit.insertData = function() {
		var data = form.getJSONObject("screenAddform");
		var option=$("#orgCode option:selected");
		var option1=$("#branchCode option:selected");
		var teBranch=option1.text();
		if('请选择'==teBranch){
			teBranch='';
		}
		data.PRODUCT_DIVISION=option.text();
		data.BRANCHNAME=teBranch;
		 ajax.remoteCall("${ctxPath}/servlet/screen?action=add",data,function(result) { 
			//debugger;
				if(result.state == 1){
					parent.layer.closeAll();
					parent.screen(); 
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		); 
 }
 	function changeOrg(obj){
    	$("#branchCode").render({data:{"orgCode":obj},success:function(result){
    	}});  
		
 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>