<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>用户信息</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editUserUpForm" data-mars="setting.userRecode"  method="post"  autocomplete="off"  >
				<input type="hidden" name="user.ID" value="${param.id}">
				  <table class="table table-edit table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td width="50px" class="required">处理人</td>
			                        <td width="220px"><input type="text" name="user.USER_NAME" data-rules="required"  maxlength="10"  class="form-control input-sm"></td>
		                     </tr>
		                  <!--    <tr>
		                     		
			                        <td class="required">事业部</td>
			                        <td>
			                             <select  name="DEPARTMENTTD" id ="DEPARTMENTTD"   data-rules="required" class="form-control input-sm"  data-cust-context-path="/neworder" data-cust-mars="comm.sysCode('ORG_CODE')" onchange="changeOrg(this.value)">
					                          <option value="">请选择</option>
					                     </select> 
              						</td>
		                     </tr>
		                      <tr>
		                     		
			                        <td class="required">分中心</td>
			                        <td>
							 <select class="form-control input-sm" name="SUB_CENTERTD" id ="SUB_CENTERTD" data-rules="required" data-cust-context-path="/neworder" data-cust-mars="comm.branchCode" style="width: 130px">
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
		                     </tr> -->
		                      <tr>
		                     		
			                        <td class="required">发送/抄送</td>
			                        <td>
									<select id="CONTACT_TYPE"  class="form-control input-sm" data-rules="required" data-cust-context-path="/yq_common" name="user.CONTACT_TYPE" data-cust-mars="dict.getDictList('SENTIMENT_PROCESS_SEND_TYPE')"> 
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
		                     </tr>
		                       <tr>
		                     		
			                        <td >岗位</td>
			                        <td>
									<select id="POSITION"  class="form-control input-sm"  data-cust-context-path="/yq_common" name="user.POSITION" data-cust-mars="dict.getDictList('SENTIMENT_CONTACT_POSITION')"> 
					                    		<option value="">请选择</option>
              						    </select>
              						</td>
		                     </tr>
		                      <tr>
			                        <td class="required">电话号码</td>
			                        <td><input type="text" name="user.PHONE_NUM"  data-rules="required" id="PHONE_NUM" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td class="required">邮箱地址</td>
			                        <td><input type="text" name="user.EMAIL"  data-rules="required" id="EMAIL" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
			                        <td >MIP</td>
			                        <td><input type="text" name="user.MIP"  id="MIP" class="form-control input-sm"></td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="userUp.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20" type="button"
							id="backbut" onclick="parent.layer.closeAll();">关闭</button>	
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	   jQuery.namespace("userUp");
	   $(function(){
			$("#editUserUpForm").render({success:function(result){
				var dataInfo = result["setting.userRecode"]['data'];
				var DEPARTMENT=dataInfo['user.DEPARTMENT'];
				var SUB_CENTER=dataInfo['user.SUB_CENTER'];
				$('#DEPARTMENTTD option').each(function(index) {
					var text = $(this).text();
					var val = $(this).val();
					if (text == DEPARTMENT) {
						$(this).prop('selected', 'selected');
				    	$("#SUB_CENTERTD").render({data:{"orgCode":val},success : function(result){
				    		$('#SUB_CENTERTD option').each(function(index) {
								var text = $(this).text();
								if (text == SUB_CENTER) {
									$(this).prop('selected', 'selected');
								}
							})
				    	}});
					}
				})
			}});
	   })
	   function changeOrg(obj){
	    	$("#SUB_CENTERTD").render({data:{"orgCode":obj}});
	 	}
	   userUp.ajaxSubmitForm = function(){
	 			var ago=$("#PHONE_NUM").val();
	 			var ago1=$("#EMAIL").val();
				var myreg = /^[1][0-9]{10}$/;
	 		     var reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
	          if (!myreg.test(ago)) {
					layer.msg("号码格式不正确", {icon : 2});
	              return false;  
	          }
	         /*  if (!reg.test(ago1)) {
					layer.msg("邮箱格式不正确", {icon : 2});
	              return false;  
	          } */
				if(form.validate("editUserUpForm")){		
					userUp.insertData(); 	
				};
			}
	 	
	   userUp.insertData = function() {
				var data = form.getJSONObject("editUserUpForm");
		/* 		var option=$("#DEPARTMENTTD option:selected");				
				var option1=$("#SUB_CENTERTD option:selected");
				data.DEPARTMENT=option.text();
				data.SUB_CENTER=option1.text(); */
				 ajax.remoteCall("${ctxPath}/servlet/setting?action=UpUser",data,function(result) { 
					//debugger;
						if(result.state == 1){
							layer.alert(result.msg,{icon: 1},function(){
								parent.layer.closeAll();
								parent.userinfo(-1);
							});

							}else{
							layer.alert(result.msg,{icon: 5});
						}
					}
				); 
		 }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>