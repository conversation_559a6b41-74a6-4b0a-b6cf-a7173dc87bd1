<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>关键字管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
						       <h5><span class="glyphicon glyphicon-list"></span> 关键字管理</h5>
	             		       <div class="input-group input-group-sm">
								      <span class="input-group-addon">关键字</span>	
									  <input type="text" name="" class="form-control input-sm" style="width:160px">
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">事业部</span>	
									  <select name="" class="form-control input-sm" >
									       <option>请选择</option>
									  </select>
							   </div>  
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick=""><span class="glyphicon glyphicon-search"></span> 查询</button>
							   </div>
							   <div class="input-group input-group-sm pull-right">
             		               <button class="btn btn-sm btn-success" type="button" onclick="keyword.addKeyword()"> +添加</button>
             		           </div>
             		           <div class="input-group input-group-sm pull-right mr-10">
             		               <button class="btn btn-sm btn-success" type="button" onclick="keyword.importData()"><span class="glyphicon glyphicon-import"></span> 导入</button>
             		            </div>
             		            <div class="input-group input-group-sm pull-right mr-10">
             		               <button class="btn btn-sm btn-success" type="button" onclick=""><span class="glyphicon glyphicon-export"></span> 导出</button>
             		            </div>
	             		 </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="">
                             <thead>
	                         	 <tr>
	                         	      <th>大数据查询关键字</th>
								      <th>事业部</th>
								      <th>渠道</th>
								      <th>备注</th>
								      <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                                  <tr>
                                      <td>美的空调机</td>
                                      <td>家用空凋</td>
                                      <td></td>
                                      <td></td>
                                      <td>
                                          <a href="javascript:;" onclick="keyword.editKeyword()">修改</a>&nbsp;
                                          <a href="javascript:;">删除</a>
                                      </td>
                                  </tr>
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("keyword");
	    
		keyword.addKeyword = function(){
		    popup.layerShow({type:1,title:'添加关键字',area:['460px','360px'],offset:'20px'},"${ctxPath}/pages/setting/keyword-edit.jsp",null);
		   		     
		}
		
		keyword.editKeyword = function(){
		    popup.layerShow({type:1,title:'修改关键字',area:['460px','360px'],offset:'20px'},"${ctxPath}/pages/setting/keyword-edit.jsp",null);
		   		     
		}
		
		keyword.importData = function(){
		    popup.layerShow({type:1,title:'导入关键字',area:['420px','290px'],offset:'20px'},"${ctxPath}/pages/setting/keyword-import.jsp",null);
		   		     
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>