<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>对应关系配置</title>

	<style>
	    .ztree .line{border:none}
	</style>
</EasyTag:override>
<EasyTag:override name="content"> 
<form id="bindingForm" data-mars=""  method="post"  autocomplete="off"  >
   
    <div class="mt-15">
    <div class="form-group" style="margin-left :18px">
		<h5><span class="required"></span> 请选择舆情服务请求(必填)</h5>								
		</div>
   	    <label class="checkbox checkbox-info checkbox-inline">
              <select name="SERVICE_TYPE" data-rules="required" id="SERVICE_TYPE" data-mars="order.serviceType" class="form-control input-sm" onchange="changRequest(this.value)" style="width: 130px">
				<option value="">请选择</option>
               </select>
       	</label>
   	    <label class="checkbox checkbox-info checkbox-inline">
               <select name="COMPLAIN_REASON" id="COMPLAIN_REASON"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm" onchange="changReason(this.value)"style="width: 130px">
					                    		<option value="">请选择</option>
              						    </select>
       	</label>
   	    <label class="checkbox checkbox-info checkbox-inline">
              <select name="COMPLAIN_TYPE" id="COMPLAIN_TYPE"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm" onchange="changComplain(this.value)"style="width: 130px">
					                    		<option value="">请选择</option>
              						    </select>
       	</label>
   	    <label class="checkbox checkbox-info checkbox-inline">
               <select name="COMPLAIN_POINT" id="COMPLAIN_POINT"  data-rules="required" data-mars="order.complainReason" class="form-control input-sm"style="width: 130px" onchange="bindingSelect()">
					                    		<option value="">请选择</option>
              						    </select>
       	</label>
       <div class="mt-3" style="margin-left :18px">
        <span class="required">数据来源(必填)</span>
              <select id="DATASOURCE"  data-rules="required" style="width:  130px;" class="form-control input-sm" data-cust-context-path="/yq_common" name="DATASOURCE" data-cust-mars="dict.getDictList('SENTIMENT_FROM')" onchange="bindingSelect()"> 
			     <option value="">请选择</option>
			      </select>     
    </div>  
    </div>
        <div class="col-md-5">
        <div class="form-group" >
			<h5><span class=" "></span> 请选择售后工单服务请求</h5>								
		</div>
           <div id="serMenuContent" class="menuContent">
		    		<input type="hidden" id = "serviceRequireTreeHidden" data-mars= "comm.serviceRequire" />
		    		<input type="hidden" id = "serviceRequireId" />
		         	<ul id="serviceRequireTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
		    	</div>
	<div class="input-group ">
	   		<button class="btn btn-sm btn-primary"  type="button" onclick="binding.ajaxSubmitForm()">绑定</button>
    </div>
        </div>
		</form>	
</EasyTag:override>

<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css" />
	<script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
	<script type="text/javascript">
	jQuery.namespace("binding");
	var bindingData='';
	var level2='';
	var	level3='';
	  $(function(){
			$("#bindingForm").render({success :function(result){
				requreLib.setplugs('slimscroll,ztree',function(){					
					 $('#serMenuContent').slimScroll({  
			                height: '400px',
			                color: '#000000'
			        });
					
				});	
				 srTree.nodes = result["comm.serviceRequire"].data;
    			$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes); 
			}});
		

	   })
	   function changRequest(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_REASON").render({data:{id:val}});
	 	}
	 	function changReason(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_TYPE").render({data:{id:val}});
	 	}
	 	function changComplain(val){
	 		//$("#COMPLAIN_REASON").empty();
			$("#COMPLAIN_POINT").render({data:{id:val}});		
 		}
	 	function bindingSelect(){
	 		
	 		isAre();
	 		$("#serviceRequireTreeHidden").render({success :function(result){
				var level1=bindingData.contactOrderServTypeCode;
				 srTree.nodes = result["comm.serviceRequire"].data;
				 for(var i=0,len=srTree.nodes.length; i<len;i++){
					 if(level1== srTree.nodes[i].id){						 
						 srTree.nodes[i].checked=true; 
						 break;
					 }
				 }
				// console.info(srTree.nodes);

    			$.fn.zTree.init($("#serviceRequireTree"), srTree.setting,srTree.nodes); 
			}});
	 	}
	 	/**
	 	 * 服务请求树
	 	 */
	 	var srTree = {
	 			nodes : {},
	 			setting : {},
	 			sr_data : {
	 					simpleData: {
	 			            enable: true,
	 						idKey: "id",
	 						pIdKey: "pId",
	 						rootPId: 0
	 				     },
	 				     key: {
	 				        name: "name"
	 				     }
	 				}
	 			
	 	}
	    srTree.setting = {
	    		data : srTree.sr_data,	    		
	    		check: {
	                enable: true,
	                chkStyle: "radio",  //单选框
	                radioType: "all"   //对所有节点设置单选
	            },
	    		callback :{

	    			onCheck : zTreeSrOnclick

	    		},
	    		async : {
	    			enable:true,
	    	    	type:"post",
	    	    	url:"/sentiment/servlet/comm?query=serviceRequire2Level",
	    	    	autoParam:["id","level"],
	    	    	otherParam:["orgCode",function() { return $("#orgCode").val() },"level2",function() { return level2 },"level3",function() { return level3 }]
	    		}
	    }
	 
		function zTreeSrOnclick(event,treeId,treeNode){
	 		$('#serviceRequireId').val(treeNode.id);
	    	$('#serviceRequireId').data('serviceRequire', treeNode);
		}

	 	binding.ajaxSubmitForm=function(){
	 		isAre();
	 		if(!form.validate("bindingForm")){
	 			return false;
	 		}
	 		var serviceRequireId = $('#serviceRequireId').val();
	 		var serviceRequire = $('#serviceRequireId').data('serviceRequire');
	 		if(serviceRequireId==''){
	 			layer.alert("请选择售后工单服务请求",{icon:2});
	 	 		return false;
	 		}
	 		var jsonDara={};
	 	 	var data= serviceRequire.getPath();
	 	 	if(data.length<3||serviceRequire==''){
	 	 	
	 	 	}else{
	 		 	jsonDara.contactOrderSerItem2Name=data[2].name//三级
		 	 	jsonDara.contactOrderSerItem2Code=data[2].id//三级
		 	 	jsonDara.contactOrderSerItem1Name=data[1].name//二级
		 	 	jsonDara.contactOrderSerItem1Code=data[1].id//二级
		 	 	jsonDara.contactOrderServTypeCode=data[0].id//一级	 	 	
		 	 	jsonDara.contactOrderServTypeName=data[0].name//一级	 	 	
		 	 	jsonDara.serviceMainTypeCode=data[2].serviceMainTypeCode//业务类型
		 	 	jsonDara.serviceMainTypeName=data[2].serviceMainTypeName//业务类型
		 	 	jsonDara.serviceSubTypeCode=data[2].serviceMainTypeCode//业务类型
		 	 	jsonDara.serviceSubTypeName=data[2].serviceMainTypeName//业务类型
	 	 	}
	 
    		  var data = form.getJSONObject("bindingForm");
	 	 	data.SH_SERVICE_CODE=JSON.stringify(jsonDara);
	    	  ajax.remoteCall("${ctxPath}/servlet/binding?action=binding",data,function(result) { 
	    				if(result.state == 1){
	    					layer.alert(result.msg,{icon:1},function(){
	    						layer.closeAll();
		    					location.reload();
	    					});
	    					}else{
	    					layer.alert(result.msg,{icon: 5});
	    				}
	    			}
	    		);  
	 	 	
	 	}
	 	function isAre(){
	 		var SERVICE_TYPE=$("#SERVICE_TYPE").val();
	 		var COMPLAIN_REASON=$("#COMPLAIN_REASON").val();
	 		var COMPLAIN_TYPE=$("#COMPLAIN_TYPE").val();
	 		var COMPLAIN_POINT=$("#COMPLAIN_POINT").val();
	 		var DATASOURCE=$("#DATASOURCE").val();
	 		$.ajax({
	 			url:'${ctxPath}/servlet/binding?query=isAre',
	 			data:{"DATASOURCE":DATASOURCE,"SERVICE_TYPE":SERVICE_TYPE,"COMPLAIN_REASON":COMPLAIN_REASON,"COMPLAIN_TYPE":COMPLAIN_TYPE,"COMPLAIN_POINT":COMPLAIN_POINT},
	 			async:false,
	 			success:function(result){
	 				console.info(result)
	 				if(result.state == 1){
	 					if(''==result.data){
	 						bindingData='';
	 					}else{
		 					bindingData = JSON.parse(result.data);
		 					level2=bindingData.contactOrderSerItem1Code;
		 					level3=bindingData.contactOrderSerItem2Code;
	 					}
	 					
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	
	 			}
	 		});
	 	}
	  
	   
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>