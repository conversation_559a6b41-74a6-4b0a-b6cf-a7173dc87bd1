<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>通讯录维护</title>
    <style>
        #menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        .el-cascader-menu__wrap {
            height: 400px !important;
        }

        .el-cascader-node {
            height: 24px !important;;
            line-height: 24px !important;;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false"
          data-toggle="">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5><span class="glyphicon glyphicon-list"></span> 通讯录维护</h5>
                </div>
                <hr style="margin:5px -15px">
                <div class="form-group">
                    <div class="input-group">
                        <span class="input-group-addon">报送人姓名</span>
                        <input name="agentName" class="form-control input-sm" style="width: 150px">
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">电话</span>
                        <input class="form-control input-sm" style="width: 150px" name="phone">
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">升级类型分类</span>
                        <select name="super_upgrade_type" data-mars="Contacts.upgradeType1" id="upgradeType1"
                                class="form-control input-sm" onchange="upgradeTypeSe(this.value)">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">升级类型</span>
                        <select name="upgrade_type" data-mars="Contacts.upgradeType2" id="upgradeType2"
                                class="form-control input-sm" onchange="upgradeTypeSe1(this.value)">
                            <option value="">请选择</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">主体</span>
                        <select name="org_code" id="org_code" onchange="loadBranchCode(this)"
                                data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')"
                                class="form-control input-sm" style="width: 150px">
                            <option value="">请选择</option>
                            <option value="-">空</option>
                        </select>
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">分中心</span>
                        <select class="form-control input-sm" style="width: 150px" theme="bootstrap" name="branch_code"
                                id="branch_code">
                        </select>
                    </div>

                    <div class="input-group">
                        <span class="input-group-addon">品类</span>
                        <input class="hidden" name="prod_code" id="prod_code">
                        <input class="form-control input-sm" style="width:110px" name="prod_name"
                               onchange="cleanVal(this,'prod_code')" id="proCodeShow"
                               onclick="showMenu(this,'menuContent')"/>
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">岗位</span>
                        <input class="form-control input-sm"
                               style="width: 150px" name="AGENTPOSITION">
                    </div>
                    <div class="input-group">
                        <span class="input-group-addon">备注</span>
                        <input class="form-control input-sm"
                               style="width: 150px" name="BACKUP">
                    </div>
                </div>
                <div class="form-group">
                    <div class="input-group input-group-sm pull-right mr-10">
                        <button type="button" class="btn btn-sm btn-default" onclick="contacts.reload()"><span
                                class="glyphicon glyphicon-search"></span> 搜索
                        </button>
                    </div>
                    <EasyTag:res resId="sentiment_compl_admin">
                        <div class="input-group input-group-sm pull-left mr-10">
                            <button type="button" class="btn btn-sm btn-success" onclick="contacts.addData()"> +新增
                            </button>
                        </div>
                        <div class="input-group input-group-sm pull-left mr-10">
                            <button type="button" class="btn btn-sm btn-success" onclick="downloadExl()"><span
                                    class="glyphicon glyphicon-export"></span>导出
                            </button>
                        </div>
                        <div class="input-group input-group-sm pull-left mr-10">
                            <button type="button" class="btn btn-sm btn-success" onclick="contacts.importData()"><span
                                    class="glyphicon glyphicon-export"></span>导入
                            </button>
                        </div>
                        <div class="input-group input-group-sm pull-left mr-10">
                            <button type="button" class="btn btn-sm btn-success" onclick="contacts.dels()"><span
                                    class="glyphicon glyphicon-export"></span>删除
                            </button>
                        </div>
                        <div class="input-group input-group-sm pull-left mr-10">
                            <button type="button" class="btn btn-sm btn-success" onclick="contacts.delSearch()"><span
                                    class="glyphicon glyphicon-export"></span>条件删除
                            </button>
                        </div>
                    </EasyTag:res>
                </div>
            </div>
            <div class="ibox-content">
                <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10"
                       id="tableHead" data-mars="Contacts.list">
                    <thead>
                    <tr>
                        <th class="text-c">
                            <label class="checkbox checkbox-success">
                                <input class="checkAll" type="checkbox"
                                       onchange="checkAll('dataList',$(this))"><span></span>
                            </label>
                        </th>
                        <th class="text-c">升级类型</th>
<%--                        <th class="text-c">运中编号</th>--%>
<%--                        <th class="text-c">网点</th>--%>
                        <th class="text-c">主体</th>
                        <th class="text-c">分中心</th>
                        <th class="text-c">品类</th>
                        <th class="text-c">区域</th>
                        <th class="text-c">报送人姓名</th>
                        <th class="text-c">备注</th>
                        <th class="text-c">岗位</th>
                        <th class="text-c">MIP账号</th>
                        <th class="text-c">电话</th>
                        <th class="text-c">邮箱</th>
                        <th class="text-c">电话通知</th>
                        <th class="text-c">短信通知</th>
                        <th class="text-c">邮件通知</th>
                        <th class="text-c">是否抄送</th>
                        <th class="text-c">MIP通知</th>
                        <th class="text-c">操作</th>
                    </tr>
                    </thead>
                    <tbody id="dataList">
                    </tbody>
                </table>
                <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox" data-code="{{:ORG_CODE}}" data-id="{{:ID}}" ><span></span></label></td>
											<td>{{:UPGRADE_NAME}}</td>
<%--											<td>{{:SITE_CODE}}</td>--%>
<%--											<td>{{:SITE_NAME}}</td>--%>
											<td>{{:ORG_NAME}}</td>
											<td>{{:BRANCH_NAME}}</td>
											<td>{{:PROD_NAME}}</td>
											<td>{{:AREANAME}}</td>
											<td>{{:AGENTNAME}}</td>
											<td>{{:BACKUP}}</td>
											<td>{{:AGENTPOSITION}}</td>
											<td>{{:MIP_CODE}}</td>
											<td>{{:PHONE}}</td>
											<td>{{:EMAIL}}</td>
											<td>{{dictFUN:IS_CALL 'SF_YN'}}</td>
											<td>{{dictFUN:IS_MSG 'SF_YN'}}</td>
											<td>{{dictFUN:IS_MAIL 'SF_YN'}}</td>
											<td>{{dictFUN:IS_COPY 'SF_YN'}}</td>
											<td>{{dictFUN:IS_MIP 'SF_YN'}}</td>
											<td>
				<EasyTag:res resId="sentiment_compl_admin">
                    <a href="javascript:contacts.editData('{{:ID}}','{{:SUPER_UPGRADE_TYPE}}','{{:UPGRADE_TYPE}}','{{:ORG_CODE}}')">编辑</a>&nbsp;
                    <a href="javascript:contacts.del('{{:ID}}');">删除</a>
                </EasyTag:res>
											</td>
									    </tr>
								   {{/for}}


                </script>

                <div class="row paginate" id="page">
                    <jsp:include page="/pages/common/pagination.jsp">
                        <jsp:param value="25" name="pageSize"/>
                    </jsp:include>
                </div>
            </div>
        </div>
        <div id="menuContent" class="menuContent">
            <input type="hidden" id="proCodeTreeHidden" data-mars="comm.productCode"/>
            <ul id="proCodeTree" class="ztree" style="margin-top:0; width:100%; height:auto;"></ul>
        </div>
    </form>

</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <link type="text/css" rel="stylesheet"
          href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css"/>
    <script type="text/javascript"
            src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
    <link href="/sentiment/static/css/element-ui.css" rel="stylesheet">
    <script type="text/javascript" src="/sentiment/static/js/vue.min.js"></script>
    <script type="text/javascript" src="/sentiment/static/js/elementui/element-ui.js"></script>

    <script type="text/javascript">
        jQuery.namespace("contacts");
        $(function () {
            contacts.multiSetting = {
                multiple: false,
                buttonWidth: '220px',
                allSelectedText: "全部",
                nonSelectedText: "--请选择--",
                nSelectedText: "个被选中",
                selectAllNumber: false,
                maxHeight: 350,
                includeSelectAllOption: true,
                selectAllText: '全选',
                enableFiltering: true

            };
            $('#branch_code').multiselect(contacts.multiSetting);
            $("#searchForm").render({});
        });
        contacts.addData = function () {
            popup.layerShow({
                type: 2,
                title: '新增维护',
                area: ['600px', '650px'],
                offset: '20px',
                shadeClose: false
            }, "${ctxPath}/pages/template/contacts-edit.jsp");
        }
        contacts.editData = function (id,super_upgrade_type,upgrade_type,code) {
            popup.layerShow({
                type: 2,
                title: '编辑维护',
                area: ['600px', '650px'],
                offset: '20px',
                shadeClose: false
            }, "${ctxPath}/pages/template/contacts-edit.jsp", {ID: id,SUPER_UPGRADE_TYPE: super_upgrade_type,UPGRADE_TYPE: upgrade_type,ORG_CODE: code});
        }
        contacts.editDatas = function (ids) {
            popup.layerShow({
                type: 2,
                title: '编辑维护',
                area: ['600px', '650px'],
                offset: '20px',
                shadeClose: false
            }, "${ctxPath}/pages/template/contacts-edit.jsp", {IDS: ids + ""});
        }
        contacts.delSearch = function () {
            if (confirm("确认要删除吗？请检查检索的条件......")) {
                var data = {
                    phone: $("input[name='phone']").val(),
                    AGENTPOSITION: $("input[name='AGENTPOSITION']").val(),
                    BACKUP: $("input[name='BACKUP']").val(),
                    upgrade_type: $("#upgradeType2").find("option:selected").val(),
                    org_code: $("#org_code").val(),
                    branch_code: $("#branch_code").val(),
                    prod_code: $("#prod_code").val(),
                    agentName: $("input[name='agentName']").val(),
                    prod_name: $("#proCodeShow").val(),
                }
                console.log("data:",data);
                ajax.remoteCall("${ctxPath}/servlet/Contacts?action=DeleteBySearch", data, function (result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1});
                        contacts.reload();
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                });
            }
        }
        contacts.dels = function () {
            var ids = $("#dataList").find("input[type='checkbox']:checked");
            if (ids.length < 1) {
                alert('请选择需要删除的选项！');
                return;
            } else {
                var arr = new Array();
                for (var i = 0; i < ids.length; i++) {
                    arr.push($(ids[i]).attr("data-id"));
                }
                if (confirm("确认要删除吗？")) {
                    ajax.remoteCall("${ctxPath}/servlet/Contacts?action=delete", {ids: arr}, function (result) {
                        if (result.state == 1) {
                            layer.msg(result.msg, {icon: 1});
                            contacts.reload();
                        } else {
                            layer.alert(result.msg, {icon: 5});
                        }
                    });
                }
            }
        }

        contacts.del = function (id) {
            var ids = [id];
            if (confirm("确认要删除吗？")) {
                ajax.remoteCall("${ctxPath}/servlet/Contacts?action=delete", {ids: ids}, function (result) {
                    if (result.state == 1) {
                        layer.msg(result.msg, {icon: 1});
                        contacts.reload();
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                });
            }
        }

        jQuery.namespace("contact");
        var pcNodes;
        var pcSetting = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "/sentiment/servlet/comm?query=proCode2Level",
                autoParam: ["id", "level"]
            }
        };
        var srNodes;
        var srSetting = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "/sentiment/servlet/comm?query=serviceRequire2Level2",
                autoParam: ["id", "level"],
                otherParam: ["orgCode", function () {
                    return $("#orgCode").val()
                }]
            }
        };

        $(function () {
            $("#searchForm").render({
                success: function (result) {
                    if (result["comm.productCode"]) {
                        pcNodes = result["comm.productCode"].data;
                    }
                    if (result["comm.serviceRequire"]) {
                        srNodes = result["comm.serviceRequire"].data;
                    }
                    $.fn.zTree.init($("#proCodeTree"), pcSetting, pcNodes);
                }
            });
        });

        contact.changeOrg = function (obj) {
            $("#proCode").val('');
            $("#proCodeShow").val('');
            obj = contact.getOrgCode(obj)
            $("#menuContent").render({
                data: {"orgCode": obj}, success: function (result) {
                    pcNodes = result["comm.productCode"].data;
                    $.fn.zTree.init($("#proCodeTree"), pcSetting, pcNodes);
                }
            });
        }
        //CC主体转主体
        contact.getOrgCode = function (orgCode) {
            ajax.remoteCall("/sentiment/servlet/comm?action=GetOrgCode&orgCode=" + orgCode, {orgCode: orgCode}, function (result) {
                if (result.state == "1") {
                    orgCode = result.data;
                }
            }, {async: false});
            return orgCode

        }

        function onBodyDownPc(event) {
            if (!(event.target.id == "menuContent" || event.target.id == "proCodeShow" || $(event.target).parents("#menuContent").length > 0)) {
                hideMenu('menuContent');
            }
        }

        //点击事件
        function zTreeOnclick(event, treeId, treeNode) {
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            } else {
                $("#prod_code").val(treeNode.id);
                $("#proCodeShow").val(treeNode.name);
                hideMenu('menuContent');
            }
        }

        //显示菜单
        function showMenu(obj, treeId) {
            var leftPx = $(obj).offset().left;
            var topPx = $(obj).offset().top;
            var heightPx = $(obj).height() + $(obj).innerHeight() / 2;
            $("#" + treeId).css({left: leftPx, top: topPx + heightPx}).slideDown("fast");
            $("body").bind("mousedown", onBodyDownPc);
        }

        //隐藏菜单
        function hideMenu(divId) {
            $("#" + divId).fadeOut("fast");
            $("body").unbind("mousedown", onBodyDownPc);
        }

        contacts.reload = function () {
            $("#searchForm").searchData();
        }

        function loadBranchCode(data) {
            /* 	$("#branch_code").data("mars", "comm.getBranchCode('"+data.value+"')");
                $("#branch_code").render(); */

            var sel2 = $("#branch_code").data('select2');
            $("#branch_code").data({
                "mars": "comm.getBranchCode('" + data.value + "')",
            });

            $("#branch_code").multiselect('destroy');
            $("#branch_code").empty();
            $("#branch_code").empty();
            var option = $("<option>").val('').text("--请选择--");
            var option1 = $("<option>").val('-').text("空");
            $("#branch_code").append(option);
            $("#branch_code").append(option1);
            $("#branch_code").render({
                success: function (result) {
                    $('#branch_code').multiselect(contacts.multiSetting
                    )

                }
            });
            $("#branch_code").data('select2', sel2);
        }

        function downloadExl() {
            location.href = "${ctxPath}/servlet/Contacts?action=Export&"
                + $("#searchForm").serialize();
        }

        contacts.updates = function () {
            var ids = $("#searchForm").find("input[type='checkbox']:checked");
            if (ids.length < 1) {
                alert('请选择需要修改的收件人！');
            } else {
                var newids = "";
                for (var i = 0; i < ids.length; i++) {
                    newids = newids == "" ? $(ids[i]).attr("data-id") : newids + ";" + $(ids[i]).attr("data-id");
                }
                contacts.editDatas(newids);
            }
        }
        contacts.importData = function () {
            popup.layerShow({
                type: 1,
                title: "通讯录名单导入",
                offset: '20px',
                area: ['420px', '200px']
            }, "${ctxPath}/pages/template/contacts-import.jsp", null);
        }
        //升级类型
        function upgradeTypeSe(val,val2){
            console.log("upgradeType1:",val);
            $("#upgradeType2").render({data:{"code":val},success:function(result){
                    $('#upgradeType2 option').each(function(index) {
                        var val = $(this).val();
                        if (val == val2) {
                            $(this).prop('selected', 'selected');
                        }
                    })
                }});
        }
        function upgradeTypeSe1(val){
            console.log("upgradeType2:",val);
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>