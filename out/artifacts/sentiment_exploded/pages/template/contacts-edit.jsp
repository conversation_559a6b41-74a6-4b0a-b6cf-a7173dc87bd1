<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>通讯录维护</title>
    <style>
        #menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 255px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }

        .el-cascader-menu__wrap {
            height: 400px !important;
        }

        .el-cascader-node {
            height: 24px !important;;
            line-height: 24px !important;;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="editForm" data-mars="Contacts.edit" method="post" autocomplete="off" data-mars-prefix="Contacts.">
        <input class="hidden" name="Contacts.ID" value="${param.ID }">
        <input class="hidden" id="ids" name="Contacts.IDS" value="${param.IDS }">
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td>舆情升级类型</td>
                <td>
                    <select name="Contacts.SUPER_UPGRADE_TYPE" data-mars="Contacts.upgradeType1" id="upgradeType1"
                            class="form-control input-sm" onchange="upgradeTypeSe(this.value)">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td>升级类型名称</td>
                <td>
                    <input type="text" id="upgrade_name" name="Contacts.UPGRADE_NAME" class="hidden">
                    <select name="Contacts.UPGRADE_TYPE" data-mars="Contacts.upgradeType2" id="upgradeType2"
                            class="form-control input-sm" onchange="upgradeTypeSe1(this.value)">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>主体</td>
                <td>
                    <input type="text" id="org_name" name="Contacts.ORG_NAME" class="hidden">
                    <select name="Contacts.ORG_CODE" id="orgCode"
                            onchange="loadBranchCode(this); contacts.changeOrg(this.value)"
                            data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')"
                            class="form-control input-sm">
                        <option value="">请选择</option>
                    </select>
                </td>
                <td>分中心</td>
                <td>
                    <input type="text" id="branch_name" name="Contacts.BRANCH_NAME" class="hidden">
                    <select name="Contacts.BRANCH_CODE" id="branch_code" class="form-control input-sm">
                        <option value="">请选择</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>品类</td>
                <td>
                    <input type="text" id="prodCode" name="Contacts.PROD_CODE" class="hidden">
                    <input class="form-control input-sm" id="prodName" name="Contacts.PROD_NAME"
                           onclick="showMenu(this,'menuContent')"/>
                </td>
            </tr>
            <tr>
                <td class="required">员工姓名</td>
                <td>
                    <input type="text" name="Contacts.AGENTNAME" data-rules="required" class="form-control input-sm">
                </td>
                <td class="required">岗位</td>
                <td><input type="text" name="Contacts.AGENTPOSITION" data-rules="required" class="form-control input-sm"></td>
            </tr>
            <tr>
                <td>备注</td>
                <td colspan="3">
                    <textarea class="form-control input-sm" name="Contacts.BACKUP"
                              class="form-control input-sm"></textarea>
                </td>
            </tr>
            <tr>
                <td class="required">电话</td>
                <td><input type="text" name="Contacts.PHONE" data-rules="required" class="form-control input-sm"></td>
                <td class="required">邮箱</td>
                <td><input type="text" name="Contacts.EMAIL" data-rules="required" class="form-control input-sm"></td>
            </tr>
            <tr>
                <td>MIP账号</td>
                <td><input type="text" name="Contacts.MIP_CODE" class="form-control input-sm"></td>
                <td></td>
                <td></td>
            </tr>
<%--            <tr>--%>
<%--                <td>运中编号</td>--%>
<%--                <td>--%>
<%--                    <input type="text" name="Contacts.SITE_CODE" class="form-control input-sm">--%>
<%--                </td>--%>
<%--                <td>网点</td>--%>
<%--                <td>--%>
<%--                    <input type="text" name="Contacts.SITE_NAME" class="form-control input-sm">--%>
<%--                </td>--%>
<%--            </tr>--%>
            <tr>
                <td>区域编码</td>
                <td>
                    <input type="text" id="areaCode" name="Contacts.AREACODE" class="form-control input-sm">
                </td>
                <td>区域名称</td>
                <td>
                    <input type="text" id="areaName" name="Contacts.AREANAME" class="form-control input-sm">
                </td>
            </tr>
            <tr>
                <td class="required">电话通知</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_CALL" value="Y" checked="checked">
                        是</label>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_CALL" value="N"> 否</label>
                </td>
                <td class="required">短信通知</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_MSG" value="Y" checked="checked">
                        是</label>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_MSG" value="N"> 否</label>
                </td>
            </tr>
            <tr>
                <td class="required">邮件通知</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_MAIL" value="Y" checked="checked">
                        是</label>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_MAIL" value="N"> 否</label>
                </td>
                <td>MIP通知</td>
                <td>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_MIP" value="Y" checked="checked">
                        是</label>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_MIP" value="N"> 否</label>
                </td>
            </tr>
            <tr>
                <td>是否抄送</td>
                <td colspan="1">
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_COPY" value="Y" checked="checked">
                        是</label>
                    <label class="radio-inline"><input type="radio" name="Contacts.IS_COPY" value="N"> 否</label>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="contacts.ajaxSubmitForm()">保存</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose()">取消</button>
        </div>
        <div id="menuContent" class="menuContent">
            <input type="hidden" id="proCodeTreeHidden" data-mars="comm.productCode"/>
            <ul id="proCodeTree" class="ztree" style="margin-top:0; width:90%; height:auto;"></ul>
        </div>
    </form>

</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <link href="/sentiment/static/css/element-ui.css" rel="stylesheet">
    <script type="text/javascript" src="/sentiment/static/js/vue.min.js"></script>
    <script type="text/javascript" src="/sentiment/static/js/elementui/element-ui.js"></script>
    <script type="text/javascript">
        jQuery.namespace("contacts");
        var pcNodes;
        var pcSetting = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "/sentiment/servlet/comm?query=proCode2Level",
                autoParam: ["id", "level"],
                otherParam: ["orgCode", function () {
                    return $("#orgCode").val()
                }]
            }
        };
        var srNodes;
        var srSetting = {
            data: {
                simpleData: {
                    enable: true,
                    idKey: "id",
                    pIdKey: "pId",
                    rootPId: 0
                },
                key: {
                    name: "name"
                }
            },
            callback: {
                onClick: zTreeOnclick
            },
            async: {
                enable: true,
                type: "post",
                url: "/sentiment/servlet/comm?query=serviceRequire2Level2",
                autoParam: ["id", "level"],
                otherParam: ["orgCode", function () {
                    return $("#orgCode").val()
                }]
            }
        };

        $(function () {
            if ('${param.ORG_CODE}' != "") { //分中心渲染
                $("#branch_code").data("mars", "comm.getBranchCode('${param.ORG_CODE}')");
                $("#branch_code").render();
            }
            $("#editForm").render({
                success: function (result) {
                    if ('${param.SUPER_UPGRADE_TYPE}' != ''){
                        console.log("SUPER_UPGRADE_TYPE：",${param.SUPER_UPGRADE_TYPE})
                        $("#upgradeType1").val('${param.SUPER_UPGRADE_TYPE}')
                    }
                    if ('${param.UPGRADE_TYPE}' != ''){
                        console.log("UPGRADE_TYPE：",'${param.UPGRADE_TYPE}')
                        upgradeTypeSe('${param.SUPER_UPGRADE_TYPE}','${param.UPGRADE_TYPE}')
                    }
                    if (result["comm.productCode"]) {
                        pcNodes = result["comm.productCode"].data;
                    }
                    if (result["comm.serviceRequire"]) {
                        srNodes = result["comm.serviceRequire"].data;
                    }
                    $.fn.zTree.init($("#proCodeTree"), pcSetting, pcNodes);
                }
            });
        });


        contacts.changeOrg = function (obj) {
            $("#prodCode").val('');
            $("#prodName").val('');
            obj = contacts.getOrgCode(obj)
            $("#menuContent").render({
                data: {"orgCode": obj}, success: function (result) {
                    pcNodes = result["comm.productCode"].data;
                    $.fn.zTree.init($("#proCodeTree"), pcSetting, pcNodes);
                }
            });
        }
        //CC主体转主体
        contacts.getOrgCode = function (orgCode) {
            ajax.remoteCall("/sentiment/servlet/comm?action=GetOrgCode&orgCode=" + orgCode, {orgCode: orgCode}, function (result) {
                if (result.state == "1") {
                    orgCode = result.data;
                }
            }, {async: false});
            return orgCode

        }

        function onBodyDownPc(event) {
            if (!(event.target.id == "menuContent" || event.target.id == "prodCode" || $(event.target).parents("#menuContent").length > 0)) {
                hideMenu('menuContent');
            }
        }

        //点击事件
        function zTreeOnclick(event, treeId, treeNode) {
            if (treeNode.isParent) {
                var ztreeObj = $.fn.zTree.getZTreeObj(treeId);
                ztreeObj.expandNode(treeNode);
            } else {
                $("#prodCode").val(treeNode.id);
                $("#prodName").val(treeNode.name);
                hideMenu('menuContent');
            }
        }

        //品类 显示菜单 旧
        function showMenu(obj, treeId) {
            var leftPx = $(obj).offset().left;
            var topPx = $(obj).offset().top;
            var heightPx = $(obj).height() + $(obj).innerHeight() / 2;
            $("#" + treeId).css({left: leftPx, top: topPx + heightPx}).slideDown("fast");
            $("body").bind("mousedown", onBodyDownPc);
        }

        //品类 显示菜单 新
        function showMenuNew() {
            popup.layerShow({type: 2, title: '产品品类', area: ['860px', '700px'], offset: '20px', shadeClose: false},
                "/neworder/pages/access/product-type.jsp?ccOrgCode=true&orgCode=orgCode&brandCode=brandCode&brandName=brandName&prodCode=prodCode&prodName=prodName" +
                "&productName=&org=");
        }

        //隐藏菜单
        function hideMenu(divId) {
            $("#" + divId).fadeOut("fast");
            $("body").unbind("mousedown", onBodyDownPc);
        }

        function loadBranchCode(data) {
            $("#branch_code").data("mars", "comm.getBranchCode('" + data.value + "')");
            $("#branch_code").render();
        }

        //升级类型
        function upgradeTypeSe(val, val2) {
            console.log("upgradeType1:", val);
            $("#upgradeType2").render({
                data: {"id": val}, success: function (result) {
                    $('#upgradeType2 option').each(function (index) {
                        var val = $(this).val();
                        if (val == val2) {
                            $(this).prop('selected', 'selected');
                        }
                    })
                }
            });
        }

        //单纯打印
        function upgradeTypeSe1(val) {
            console.log("upgradeType2:", val);
        }

        contacts.ajaxSubmitForm = function () {
            //升级类型校验
            // if ($("#upgradeType1").val() == "" || $("#upgradeType2").val() == "") {
            //     layer.alert("请确定升级类型", {icon: 5});
            //     return;
            // }
            //主体校验
            // if ($("#orgCode").val() == "" || $("#branch_code").val() == "") {
            //     layer.alert("请选择主体或分中心", {icon: 5});
            //     return;
            // }
            //品类校验
            // if ($("#prodCode").val() == "" || $("#prodName").val() == "") {
            //     layer.alert("请选择品类类型", {icon: 5});
            //     return;
            // }
            if (!form.validate("#editForm")) {
                return;
            }
            if ($("#upgrade_type").find("option:selected").val() != "") {
                $("#upgrade_name").val($("#upgrade_type").find("option:selected").text());
            } else {
                $("#upgrade_name").val("");
            }
            if ($("#orgCode").find("option:selected").val() != "") {
                $("#org_name").val($("#orgCode").find("option:selected").text())
            } else {
                $("#org_name").val("");
            }
            if ($("#branch_code").find("option:selected").val() != "") {
                $("#branch_name").val($("#branch_code").find("option:selected").text())
            } else {
                $("#branch_name").val("");
            }
            //加上参数：父级升级类型编码  升级类型编码 升级类型名称
            if ($("#upgradeType2").find("option:selected").val() != "") {
                $("#upgrade_name").val($("#upgradeType2").find("option:selected").text())
            } else {
                $("#upgrade_name").val("");
            }
            var data = form.getJSONObject("editForm");
            console.log("data:",data)
            ajax.remoteCall("${ctxPath}/servlet/Contacts?action=save", data, function (result) {
                    if (result.state == 1) {
                        window.parent.layer.closeAll();
                        window.parent.layer.msg(result.msg, {icon: 1});
                        window.parent.contacts.reload();

                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            );
        }

    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>