<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>

<EasyTag:override name="content">
    <form action=""  id="importForm" method="post" name="uploadForm" class="form-inline" id="uploadForm" enctype="multipart/form-data">
        <table class="table  table-vzebra mt-10">
            <tbody>
            <tr>
                <td width="60px">Excel文件</td>
                <td>
                    <input  type="file" id="file" name=file accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel">
<%--                    <button class="btn btn-xs btn-info" type="button"--%>
<%--                            onclick="$('#file').click()">选择文件</button>--%>
                    <a class="btn btn-sm btn-link" href="JavaScript:exc.download()"  >下载导入模板</a>
                </td>
            </tr>
            </tbody>
        </table>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button"
                    onclick="exc.upload()">保存</button>
            <button class="btn btn-sm btn-default ml-20" type="button"
                    id="backbut" onclick="layer.closeAll();">关闭</button>
        </div>
    </form><EasyTag:override name="head">
    <title>名单导入</title>
</EasyTag:override>
</EasyTag:override>
<EasyTag:override name="script">
    <script type="text/javascript">
        requreLib.setplugs('wdate');
        jQuery.namespace("exc");
        exc.download=function(){
            location.href ="${ctxPath}/servlet/Contacts?action=download";
        }
        exc.upload = function() {
            if($("#file").val()!=""){
                $("#importForm").attr("enctype","multipart/form-data");
                var formData = new FormData($("#importForm")[0]);
                var index = layer.load(1, {
                    shade: [0.1,'#fff'] //0.1透明度的白色背景
                });
                $.ajax({
                    url: '${ctxPath}/servlet/Contacts?action=upload',
                    type: 'POST',
                    data: formData,
                    async: true,cache: false,contentType: false,processData: false,
                    success: function (result) {
                        $("#importForm").removeAttr("enctype");
                        if(result.state == 1){
                            layer.close(index);
                            layer.closeAll();
                            layer.msg(result.msg,{icon: 1});
                            redblock.searchData();
                            reload();
                        }else{
                            layer.close(index);
                            layer.open({
                                type: 1,
                                skin: 'layui-layer-rim', //加上边框
                                area: ['650px', '550px'], //宽高
                                content: result.msg
                            });
                        }
                    }
                });
            }else{
                alert('请上传文件');
            }

        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp"%>