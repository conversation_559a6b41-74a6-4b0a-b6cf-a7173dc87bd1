<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业管理</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 企业管理</h5>
             		           <div class="input-group input-group-sm">
								      <span class="input-group-addon">企业名称</span>	
									  <input type="text" name="entName" class="form-control input-sm" style="width:140px">
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">Petra资源</span>	
									  <select class="form-control input-sm" name="petraId" data-mars="ent.petraResDictAll" onchange="Ent.searchData()">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">MARS资源</span>	
									  <select class="form-control input-sm" name="marsId" data-mars="ent.marsResDict" onchange="Ent.searchData()">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">数据库资源</span>	
									  <select class="form-control input-sm" name="schemaId" data-mars="ent.schemaResDictAll" onchange="Ent.searchData()">
                                          <option value="">请选择</option>
                                      </select>
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="Ent.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="ent.list">
                             <thead>
	                         	 <tr>
	                         	      <th class="text-c">企业ID</th>
								      <th class="text-c">企业代码</th>
								      <th class="text-l">企业名称</th>
								      <th class="text-c">坐席数</th>
								      <th class="text-c">话机数</th>
								      <th class="text-c">号码数</th>
								      <th class="text-c">联系人</th>
								      <th class="text-c">联系电话</th>
								      <th class="text-c">Petra资源</th>
								      <th class="text-c">MARS资源</th>
								      <th class="text-c">数据库资源</th>
								      <th class="text-c">状态</th>
								      <th class="text-c">开户时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:ENT_ID}}</td>
											<td>{{:ENT_CODE}}</td>
											<td  class="text-l"> <a href="javascript:void(0)" onclick="Ent.entInfo('{{:ENT_NAME}}','{{:ENT_ID}}','{{:ENT_CODE}}')">{{:ENT_NAME}}</a></td>
											<td>{{:AGENT_COUNT}}</td>
											<td>{{:PHONG_COUNT}}</td>
											<td>{{:PREFIX_COUNT}}</td>
											<td>{{:LINKMAN}}</td>
                                            <td>{{:LINKPHONE}}</td>
											<td>{{getText:PETRA_ID 'petraId'}}</td>
											<td>{{getText:MARS_ID 'marsId'}}</td>
											<td>{{getText:SCHEMA_ID 'schemaId'}}</td>
											<td>{{entStateFun:ENT_STATE}}</td>
											<td>{{cutText:CREATE_TIME 12 ''}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp">
	                     			<jsp:param value="25" name="pageSize"/>
	                     		</jsp:include>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		$.views.converters("entStateFun", function(val) {
			if(val == 0){
				return "<span class='label label-success'>正常</span>";
			}else if(val == 1){
				return "<span class='label label-warning'>暂停</span>";
			}else if(val == 2){
				return "<span class='label label-primary'>待审核</span>";
			}else if(val == 9){
				return "<span class='label label-danger'>销户</span>";
			}
		});
		
		jQuery.namespace("Ent");
		
		Ent.searchData=function(){
			$("#searchForm").searchData();
		}
		Ent.entInfo = function(entName,entId,entCode){
			popup.openTab('${ctxPath}/pages/ent/ent-info.jsp',entName,{entId:entId,entCode:entCode,entName:entName});
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>