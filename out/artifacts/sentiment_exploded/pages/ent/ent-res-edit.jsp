<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业资源</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="ent.getEntRes" data-pk="${param.entId}" method="post"  autocomplete="off" data-mars-prefix="entRes.">
			      <input type="hidden" name="entRes.ENT_ID" value="${param.entId}">
				  <table class="table table-edit table-vzebra">
	                    <tbody>
			                   <tr>
			                        <td class="required" style="width: 80px">Petra节点</td>
			                        <td>
			                        	<div class="input-group input-group-sm">
			                        	    <select class="form-control input-sm" name="entRes.PETRA_ID" data-rules="required" data-mars="ent.petraResDictAll">
			                                	<option value="">请选择</option>
			                                </select>			                            
			                        	    <span class="input-group-addon" onclick="EntResEdit.petraList()" style="cursor: pointer;">查看详情</span>
			                        	</div>
			                            
			                        </td>
		                     </tr>	
		                      <tr>
			                        <td class="required">MARS节点</td>
			                        <td>
			                            <div class="input-group input-group-sm">
				                            <select class="form-control input-sm" name="entRes.MARS_ID" data-rules="required" data-mars="ent.marsResDict">
				                                <option value="" selected="selected">请选择</option>
				                            </select>
				                            <span class="input-group-addon" onclick="EntResEdit.marsList()" style="cursor: pointer;">查看详情</span>
			                            </div>
			                        </td>
		                     </tr>	
		                      <tr>
			                        <td class="required">数据库节点</td>
			                        <td>
			                        	<div class="input-group input-group-sm">
				                            <select class="form-control input-sm" disabled="disabled" name="entRes.SCHEMA_ID" data-rules="required" data-mars="ent.schemaResDictAll">
				                                <option value="">请选择</option>
				                            </select>
				                            <span class="input-group-addon" onclick="EntResEdit.schemaList()" style="cursor: pointer;">查看详情</span>
			                        	</div>
			                        </td>
		                     </tr>	
		                      <tr>
			                        <td class="required">中继资源数</td>
			                        <td><input type="number" name="entRes.CALL_LICENSE" id="callLicense" data-rules="required|digits" class="form-control input-sm"></td>
			                 </tr>	
		                      <tr>
			                        <td class="required">智能外呼中继资源数</td>
			                        <td><input type="number" name="entRes.AUTO_CALL_LICENSE" id="autoCallLicense" data-rules="required|digits" class="form-control input-sm"></td>
			                 </tr> 
			                  <tr>
			                        <td class="required">坐席工号数</td>
			                        <td><input type="number" name="entRes.USER_LICENSE" data-rules="required|digits" class="form-control input-sm"></td>
			                 </tr>  
		                      <tr>
			                        <td class="required">话机数</td>
			                        <td><input type="number" name="entRes.AGENT_LICENSE" data-rules="required|digits" class="form-control input-sm"></td>
			                 </tr> 
		                      <tr>
			                        <td class="required">话机前缀</td>
			                        <td><input type="text" name="entRes.PHONE_PREFIX" data-rules="required|digits" class="form-control input-sm"></td>
			                 </tr>                    
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="EntResEdit.updateData()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
	jQuery.namespace("EntResEdit");
	
	EntResEdit.entId='${param.entId}';
	$(function(){
		$("#editForm").render();    
	});

	EntResEdit.updateData = function(){
		if(form.validate("#editForm")){
			/* var userLicense = $("input[name='entRes.USER_LICENSE']").val()*1;
			var agentLicense = $("input[name='entRes.AGENT_LICENSE']").val()*1;
			if(userLicense < agentLicense){
				layer.alert("坐席工号数不能小于话机数",{icon: 5});
				return false;
			} */
			
			if($("#callLicense").val()*1 < $("#autoCallLicense").val()*1){
				layer.alert("智能外呼占用中继资数必须小于中继资源数",{icon: 5});
				return;
			}
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/ent?action=updateRes",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						Ent.searchEntInfo();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	}
	
	EntResEdit.schemaList = function(){
		 popup.layerShow({type:2,title:'数据库资源列表',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/ent/ent_res_schema.jsp",null);
	}
	EntResEdit.petraList = function(){
		 popup.layerShow({type:2,title:'磐石资源列表',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/ent/ent_res_petra.jsp",null);
	}
	EntResEdit.marsList = function(){
		 popup.layerShow({type:2,title:'Mars资源列表',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/ent/ent_res_mars.jsp",null);
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>