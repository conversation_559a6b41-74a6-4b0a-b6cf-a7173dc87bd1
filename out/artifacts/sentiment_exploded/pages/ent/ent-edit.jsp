<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业信息</title>
	<script type="text/javascript" src="/yc-center/static/js/lazyload-min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="ent.getEnt" data-pk="${param.entId}" method="post"  autocomplete="off" data-mars-prefix="ent." >
				  <table class="table  table-vzebra mt-10" >
	                    <tbody>
		                     <tr>
			                        <td class="required">企业ID</td>
			                        <td><input type="text" name="ent.ENT_ID" readonly="readonly" data-rules="required" id="entCode" class="form-control input-sm"></td>
			                        <td class="required">企业代码</td>
			                        <td><input type="text" name="ent.ENT_CODE" readonly="readonly" data-rules="required" id="entCode" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                     		
			                        <td class="required">企业名称</td>
			                        <td><input type="text" name="ent.ENT_NAME" data-rules="required" class="form-control input-sm"></td>
			                        <td class="required">开户时间</td>
			                        <td><input type="text" onClick="WdatePicker()" data-rules="required" name="ent.OPEN_TIME" class="form-control input-sm Wdate"></td>
		                     </tr>
		                     <tr>
			                        <td class="required">企业状态</td>
			                        <td colspan="3">
			                        	<label class="radio-inline">
										  <input type="radio" name="ent.ENT_STATE" checked="checked" value="0"> 正常
										</label>
										<label class="radio-inline">
										  <input type="radio" name="ent.ENT_STATE" value="1"> 暂停
										</label>
										<label class="radio-inline">
										  <input type="radio" name="ent.ENT_STATE" value="9"> 销户
										</label>
			                        </td>
		                     </tr>
		                      <tr>
			                       
			                        <td class="required">管理员账号</td>
			                        <td>
                                       <input type="text" onblur="EntEdit.autoEntCode()" value="" data-rules="required" id="userName" data-mars="ent.getuserAcct" name="userName" class="form-control input-sm">
			                        </td>
		                            <td>管理员密码</td>
			                        <td><input type="text" name="userPwd" class="form-control input-sm"></td>
		                   	  </tr>
		                     <tr>
		                            <td>企业联系人</td>
			                        <td><input type="text" name="ent.LINKMAN" class="form-control input-sm"></td>
		                            <td class="required">联系电话</td>
			                        <td><input type="text" name="ent.LINKPHONE" data-rules="required|mobile" class="form-control input-sm"></td>
		                     </tr>
		                      <tr>
			                       
			                        <td>法人姓名</td>
			                        <td><input type="text" name="ent.CORPORATE" class="form-control input-sm"></td>
		                            <td>法人身份证</td>
			                        <td>
			                            <div class="input-group input-group-sm">
				                        	<input type="text" name="ent.IDCARD_URL" id="certifyFile" readonly="readonly" class="form-control input-sm">
				                        	<span class="input-group-addon" onclick="EntEdit.uploadFile('certifyCallback')" style="cursor: pointer;"><i class="glyphicon glyphicon-upload"></i></span>
				                         </div>
				                    </td>
	                         </tr>
		                      <tr>
			                        <td class="required">邮箱</td>
			                        <td><input type="text" name="ent.EMAIL" data-rules="required|email" class="form-control input-sm"></td>
			                     	<td>营业执照</td>
			                        <td>
				                        <div class="input-group input-group-sm">
				                        	<input type="text" name="ent.BUSI_LICENSE_URL" readonly="readonly" id="licenseFile" class="form-control input-sm">
				                        	<span class="input-group-addon" onclick="EntEdit.uploadFile('licenseCallback')" style="cursor: pointer;"><i class="glyphicon glyphicon-upload"></i></span>
				                        </div>
			                         </td>
		                     </tr>
		                      <tr>
			                        <td>所属行业</td>
			                        <td>
			                             <select class="form-control input-sm" data-mars="common.industryDict" name="ent.INDUSTRY">
			                                    <option value="">请选择</option>
			                      		 </select>
			                        </td>
			                        <td>企业规模</td>
			                        <td>
			                        	<select name="ent.ENT_SCALE" data-mars="common.entScaleDict" class="form-control input-sm">
					                    		<option value="">请选择</option>
              						    </select>
			                        </td>
		                     </tr>
		                      <tr>
			                        <td>大数据企业ID</td>
			                        <td>
			                            <input type="text" name="ent.BD_ENT_ID" class="form-control input-sm">
			                        </td>
			                        <td>大数据企业密匙</td>
			                        <td>
			                            <input type="text" name="ent.BD_ENT_KEY" class="form-control input-sm">
			                        </td>
		                     </tr>
		                      <tr>
			                        <td>计费编码</td>
			                        <td><input type="text" name="ent.FEE_CODE" class="form-control input-sm"></td>
			                        <td>所属地市</td>
			                        <td>
			                            <input type="text" id="areaCode" name="ent.AREA_CODE" class="form-control input-sm">
			                        </td>
		                     </tr>
		                      <tr>
			                        <td>CCBAR访问密匙</td>
			                        <td colspan="3">
				                       <div class="input-group input-group-sm" style="width: 100%">
				                           <input type="text" name="ent.CCBAR_KEY" readonly="readonly" id="ccBarKey" class="form-control input-sm">
				                           <span class="input-group-addon" onclick="EntEdit.newCCBarKey()" style="cursor: pointer;">更新密匙</span>
				                       </div>
			                        </td>
		                     </tr>
		                      <tr>
			                        <td>企业地址</td>
			                        <td colspan="3"><input type="text" name="ent.ADDR" class="form-control input-sm"></td>
		                     </tr>
		                     <tr>
		                         
			                         <td>备注</td>
				                     <td colspan="3">
				                        <textarea cols="3" class="form-control input-sm" name="ent.MEMO"></textarea>
				                     </td>
		                     </tr>
	                    </tbody>
	                  </table>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="EntEdit.ajaxSubmitForm()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				    </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
	jQuery.namespace("EntEdit");
	
	EntEdit.entId='${param.entId}';
	
	LazyLoad.css(["/yc-center/static/css/cityStyle.css"], function () {
        LazyLoad.js(["/yc-center/static/js/cityScript.js"], function () {
            var test = new citySelector.cityInit("areaCode");
        });
    });
	  
	$(function(){
		$("#editForm").render();  
	});
	
	EntEdit.autoEntCode = function(){
		var userName = $("#userName").val();
		userName = $.trim(userName);
		if(userName.length <=0){
			$("#userName").val(userName);
			return;
		}
		var index = userName.indexOf("@");
		if(index > -1){
			userName = $.trim(userName.substring(0,index));
			if(userName.length <=0){
				$("#userName").val("");
				return;
			}
		}
		userName = userName +"@"+ $("#entCode").val();
		$("#userName").val(userName);
	}
	
	EntEdit.uploadFile = function(callback){
		var setting = {storage:"local",fileType:".jpg,.png",fileMaxSize:(1024*3)};
		ajax.uploadFile(setting,callback);
	}
	var licenseCallback = function(data){
		$("#licenseFile").val(data.url);
	}
	var certifyCallback = function(data){
		$("#certifyFile").val(data.url);
	}
	EntEdit.ajaxSubmitForm = function(){
		if(form.validate("#editForm")){
			EntEdit.updateData(); 
		};
	}
	EntEdit.updateData = function(){
		var data = form.getJSONObject("#editForm");
		ajax.remoteCall("${ctxPath}/servlet/ent?action=update",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1},function(){
					Ent.searchEntInfo();
					layer.closeAll();
				});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	//更新CCBAR密匙
	EntEdit.newCCBarKey = function(){
		ajax.remoteCall("${ctxPath}/servlet/ent?action=newCCBarKey",null,function(result) { 
			if(result.state == 1){
				$("#ccBarKey").val(result.data);
			}
		});
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>