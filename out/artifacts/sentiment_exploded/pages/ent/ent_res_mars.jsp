<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>MARS资源列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5><span class="glyphicon glyphicon-user"></span> MARS资源列表</h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">MARS节点名称</span>	
									  <input type="text" name="marsName" class="form-control input-sm" style="width:140px">
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="MarsRes.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-auto-fill="10" id="tableHead" data-mars="marsRes.list">
                             <thead>
	                         	 <tr>
								      <th class="text-c">节点名称</th>
								      <th class="text-c">节点说明</th>
								      <th class="text-c">网络地址</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:MARS_NAME}}</td>
											<td>{{:MARS_DESC}}</td>
											<td>{{:MARS_URL}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     		<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("MarsRes");
		
		MarsRes.searchData = function(){
			$("#searchForm").searchData();
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>