<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业外呼设置</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="ent.getEntRes" data-pk="${param.entId}" method="post"  autocomplete="off" data-mars-prefix="entRes.">
			      <input type="hidden" name="entRes.ENT_ID" value="${param.entId}">
				  <table class="table table-edit table-vzebra mt-10">
	                    <tbody>
		                    <tr>
		                        <td class="required">单号码最大外呼次数/天</td>
		                        <td><input type="number" name="entRes.CALL_COUNT" data-rules="required|digits" class="form-control input-sm"></td>
			                </tr>	
		                    <tr>
		                        <td class="required">单号码最少外呼间隔/秒</td>
		                        <td><input type="number" name="entRes.CALL_INTERVAL" data-rules="required|digits" class="form-control input-sm"></td>
			                </tr> 
			                <tr>
		                        <td class="required">企业最大外呼次数/天</td>
		                        <td><input type="number" name="entRes.TOTAL_CALL_COUNT" data-rules="required|digits" class="form-control input-sm"></td>
			                </tr>
			                <tr>
		                        <td class="required">企业数据保存时间/月</td>
		                        <td><input type="number" id="dataValidTime" name="entRes.DATA_VALID_TIME" data-rules="required|digits" class="form-control input-sm"></td>
			                </tr> 
			                <tr>
		                        <td>BPO人工外呼号码前缀</td>
		                        <td><input type="text" name="entRes.BPO_CALL_PREFIX" class="form-control input-sm" maxlength="12"></td>
			                </tr> 
			                <tr>
		                        <td>BPO自动外呼号码前缀</td>
		                        <td><input type="text" name="entRes.BPO_AGENT_PHONE" class="form-control input-sm" maxlength="17"></td>
			                </tr>                    
	                    </tbody>
	                  </table>
	                  <div class="alert alert-warning mt-10 mb-5">
						    <a href="#" class="close" data-dismiss="alert">
						        &times;
						    </a>
						            BPO人工外呼号码前缀格式：合作方代码（3位）+接入点代码（3位）+企业客户代码（6位）<br/>
						            BPO自动外呼号码前缀格式：合作方代码（3位）+接入点代码（3位）+企业客户代码（6位）+座席代码（5位）
					  </div>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="EntResEdit.updateData()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	
	jQuery.namespace("EntResEdit");
	
	EntResEdit.entId='${param.entId}';
	$(function(){
		$("#editForm").render();    
	});

	EntResEdit.updateData = function(){
		if(form.validate("#editForm")){
			if($("#dataValidTime").val() < 3){
				layer.alert("企业数据保存时间最少设置3个月",{icon: 5});
				return;
			}
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/ent?action=updateCallRes",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						Ent.searchEntInfo();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>