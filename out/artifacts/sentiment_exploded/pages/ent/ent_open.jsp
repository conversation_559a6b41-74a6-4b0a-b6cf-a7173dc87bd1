<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>新建企业</title>
	<style>
	    .blank-page{padding: 20px 40px;overflow-y: hidden;border: solid 1px #cccccc; background-color: #ffffff;margin: 20px auto;box-shadow: -2px -2px 7px #cccccc;width:890px;margin-bottom: 100px}
        .blank-page .header-title{text-align:center;color:#333;font-size:19px}
        .blank-page .p-title{border-bottom:1px solid #cccccc;font-size:16px;color:#555;margin-top:30px;margin-bottom:15px}
        .blank-page .p-title>span{border-bottom:2px solid #00a0f0;padding:2px 6px}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     <div class="blank-page">
     	<form id="editForm" class="form-horizontal" method="post"  autocomplete="off" data-mars-prefix="ent.">
		<h4 class="header-title">企业开通</h4>
		<p class="p-title"><span>企业基本信息</span></p>
		<table class="table table-vzebra">
	        <tbody>
	            <tr>
                    <td width="90px" class="required">企业ID</td>
                    <td><input type="text" onchange="entIdChange(this)" name="ent.ENT_ID" maxlength="6" data-rules="required|digits" class="form-control input-sm"></td>
                    <td width="90px" class="required">企业代码</td>
                    <td><input type="text" name="ent.ENT_CODE" maxlength="20" onblur="EntEdit.fomatEntCode()" data-rules="required" id="entCode" class="form-control input-sm"></td>
                    <td width="90px" class="required">企业名称</td>
                    <td><input type="text" name="ent.ENT_NAME" data-rules="required" class="form-control input-sm"></td>
	            </tr>
	            <tr>
                    <td width="90px" class="required">开户时间</td>
                    <td><input type="text" onClick="WdatePicker()" data-rules="required" name="ent.OPEN_TIME"  class="form-control input-sm Wdate"></td>
                    <td class="required">管理员账号</td>
                    <td><input type="text" onblur="EntEdit.autoEntCode()" value="" data-rules="required" id="userName" name="userName" class="form-control input-sm"></td>
                    <td class="required">管理员密码</td>
                    <td><input type="text" data-mars="ent.randomPwd" data-rules="required" name="userPwd" class="form-control input-sm"></td>
                </tr>
	            <tr>
                    <td>法人姓名</td>
                    <td><input type="text" name="ent.CORPORATE" class="form-control input-sm"></td>
                    <td class="required">企业联系人</td>
                    <td><input type="text" data-rules="required" name="ent.LINKMAN" class="form-control input-sm"></td>
                    <td class="required">联系电话</td>
                    <td><input type="text" name="ent.LINKPHONE" data-rules="required" class="form-control input-sm"></td>
                </tr>
	            <tr>
                    <td class="required">邮箱</td>
                    <td><input type="text" name="ent.EMAIL" data-rules="required|email" class="form-control input-sm"></td>
                    <td>法人身份证</td>
                    <td>
                        <div class="input-group input-group-sm" style="width: 166px;">
                        	<input type="text" name="ent.IDCARD_URL" id="certifyFile" readonly="readonly" class="form-control input-sm">
                        	<span class="input-group-addon" onclick="EntEdit.uploadFile('certifyCallback')" style="cursor: pointer;"><i class="glyphicon glyphicon-upload"></i></span>
				        </div>
                    </td>
                    <td>营业执照</td>
               	    <td>
               	        <div class="input-group input-group-sm" style="width: 166px;">
                        	<input type="text" name="ent.BUSI_LICENSE_URL" readonly="readonly" id="licenseFile" class="form-control input-sm">
                        	<span class="input-group-addon" onclick="EntEdit.uploadFile('licenseCallback')" style="cursor: pointer;"><i class="glyphicon glyphicon-upload"></i></span>
                        </div>
               	    </td>
                </tr>
	
                <tr>
               	    <td>计费编码</td>
               	    <td><input type="text" name="ent.FEE_CODE" class="form-control input-sm"></td>
                    <td class="required">所属行业</td>
                    <td>
                        <select class="form-control input-sm" data-rules="required" data-mars="common.industryDict" name="ent.INDUSTRY">
                                <option value="">请选择</option>
                     	</select>
                    </td>
                    <td class="required">企业规模</td>
                    <td>
                        <select name="ent.ENT_SCALE" data-rules="required" data-mars="common.entScaleDict" class="form-control input-sm">
                    		<option value="">请选择</option>
         				</select>
                    </td>
                </tr>
                <tr>
                    <td>大数据企业ID</td>
                    <td><input type="text" name="ent.BD_ENT_ID" class="form-control input-sm"></td>
               	    <td>大数据密匙</td>
               	    <td><input type="text" name="ent.BD_ENT_KEY" class="form-control input-sm"></td>
                    <td>所属地市</td>
                    <td><input type="text" id="areaCode" name="ent.AREA_CODE" class="form-control input-sm"></td>
	            </tr>
	            <tr>
	            	<td>企业地址</td>
               	    <td colspan="5"><input type="text" name="ent.ADDR" class="form-control input-sm"></td>
	            </tr>
	            <tr>
	                <td>备注</td>
	               	<td colspan="5">
	               	    <textarea rows="3" class="form-control input-sm"  name="ent.MEMO"></textarea>
	               	</td>
	            </tr>
	        </tbody>
	    </table>
	    <p class="p-title"><span>资源信息配置</span></p>
	    <table class="table table-vzebra" data-mars-prefix="entRes.">
	        <tbody>
	            <tr>
                   <td class="required">Petra资源</td>
                   <td>
                       <div class="input-group input-group-sm">
                       	   <select class="form-control input-sm" name="entRes.PETRA_ID" data-rules="required" data-mars="ent.petraResDict">
                               <option value="">请选择</option>
                           </select>			                            
                       	   <span class="input-group-addon" onclick="EntEdit.petraList()" style="cursor: pointer;">详情</span>
                       </div>
                   </td>
                   <td class="required">MARS资源</td>
                   <td>
                       <div class="input-group input-group-sm">
                           <select class="form-control input-sm" name="entRes.MARS_ID" data-rules="required" data-mars="ent.marsResDict">
                               <option value="" selected="selected">请选择</option>
                           </select>
                           <span class="input-group-addon" onclick="EntEdit.marsList()" style="cursor: pointer;">详情</span>
                       </div>
                   </td>
	            </tr>
	            <tr>
                   <td class="required">数据库资源</td>
                   <td>
                       <div class="input-group input-group-sm">
                           <select class="form-control input-sm" name="entRes.SCHEMA_ID" data-rules="required" data-mars="ent.schemaResDict">
                               <option value="">请选择</option>
                           </select>
                           <span class="input-group-addon" onclick="EntEdit.schemaList()" style="cursor: pointer;">详情</span>
                       </div>
                   </td>
                   <td class="required">坐席工号数</td>
                   <td><input type="number" name="entRes.USER_LICENSE" id="userLicense" data-rules="required|digits" class="form-control input-sm" ></td>
                </tr>
                <tr>
                   <td class="required">中继资源数</td>
                   <td><input type="number" name="entRes.CALL_LICENSE" id="callLicense" data-rules="required|digits" class="form-control input-sm" ></td>
                   <td class="required">智能外呼中继资源数</td>
                   <td><input type="number" name="entRes.AUTO_CALL_LICENSE" id="autoCallLicense" data-rules="required|digits" class="form-control input-sm" ></td>
                </tr>
                <tr>
                   <td class="required">话机数</td>
                   <td><input type="number" name="entRes.AGENT_LICENSE" id="agentLicense" data-rules="required|digits" class="form-control input-sm" ></td>
                   <td class="required">话机前缀</td>
                   <td>
                  		 <input name="textfield" class="form-control input-sm" value="115" style="display: inline-block;width: 70px" type="text" id="macAddr1" size="3" maxlength="3" /> 
						-
						<input name="textfield2" class="form-control input-sm" placeholder="区号" data-rules="required|digits" style="display: inline-block;width: 70px" type="text" id="macAddr2" size="4" maxlength="4" />
						-
						<input name="textfield3"  class="form-control input-sm" placeholder="企业ID" style="display: inline-block;width: 70px" type="text" id="macAddr3" size="4" maxlength="4" />
                  	    <input type="text" name="entRes.PHONE_PREFIX" id="phonePrefix" class="form-control input-sm hidden" >
                  </td>
                </tr>
	        </tbody>
	    </table>
	    <p class="p-title"><span>外呼设置</span></p>
	    <table class="table table-edit table-vzebra">
	        <tbody>
	            <tr>
                   <td class="required">单客户号码外呼最大次数/天  </td>
                   <td>
                        <input name="entRes.CALL_COUNT" style="width: 150px;display: inline-block;" data-rules="required|digits" value="0" class="form-control input-sm" type="number"/>
                   		<small class="ml-10">默认值0，表示不受限</small>
				   </td>
				   </tr>
				   <tr>
                   <td class="required">单客户号码外呼最少时间间隔/秒</td>
                   <td>
                        <input name="entRes.CALL_INTERVAL" style="width: 150px;display: inline-block;" data-rules="required|digits" value="0" class="form-control input-sm" type="number"/>
                   		<small class="ml-10">默认值0，表示不受限</small>
				   </td>
				    </tr>
				   <tr>
                   <td class="required">企业最大外呼次数/天</td>
                   <td>
                       <input name="entRes.TOTAL_CALL_COUNT" style="width: 150px;display: inline-block;" data-rules="required|digits" value="0" class="form-control input-sm" type="number"/>
				  	   <small class="ml-10">默认值0，表示不受限</small>
				   </td>
	            </tr>
				<tr>
                   <td class="required">企业数据保存时间/月</td>
                   <td>
                       <input name="entRes.DATA_VALID_TIME" id="dataValidTime" style="width: 150px;display: inline-block;" data-rules="required|digits" value="3" class="form-control input-sm" type="number"/>
				  	   <small class="ml-10">最小值3</small>
				   </td>
	           </tr>
	        </tbody>
	    </table>	
	    <p class="text-c" style="margin-top:50px">
		      <button type="button" class="btn btn-primary btn-sm" style="width: 80px" onclick="EntEdit.ajaxSubmitForm()"> 保 存 </button>
		</p>   
		
	    </form>
    </div> 
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript" src="/yc-center/static/js/lazyload-min.js"></script>
	<script type="text/javascript">
	LazyLoad.css(["/yc-center/static/css/cityStyle.css"], function () {
        LazyLoad.js(["/yc-center/static/js/cityScript.js"], function () {
            var test = new citySelector.cityInit("areaCode");
        });
    });
	  
	$(function(){
		$('[data-toggle="tooltip"]').tooltip()
		$("#editForm").render();  
	});
	
    jQuery.namespace("EntEdit");
	
	EntEdit.entId='${param.entId}';
	
	EntEdit.autoEntCode = function(){
		var userName = $("#userName").val();
		userName = $.trim(userName);
		if(userName.length <=0){
			$("#userName").val(userName);
			return;
		}
		var index = userName.indexOf("@");
		if(index > -1){
			userName = $.trim(userName.substring(0,index));
			if(userName.length <=0){
				$("#userName").val("");
				return;
			}
		}
		userName = userName +"@"+ $("#entCode").val();
		$("#userName").val(userName);
	}
	EntEdit.fomatEntCode = function(){
		var entCode = $("#entCode").val();
		$("#entCode").val($.trim(entCode));
	}
	EntEdit.uploadFile = function(callback){
		var setting = {storage:"local",fileType:".jpg,.png",fileMaxSize:(1024*3)};
		ajax.uploadFile(setting,callback);
	}
	var licenseCallback = function(data){
		$("#licenseFile").val(data.url);
	}
	var certifyCallback = function(data){
		$("#certifyFile").val(data.url);
	}
	EntEdit.ajaxSubmitForm = function(){
		if(form.validate("#editForm")){
			 EntEdit.insertData(); 
		};
	}
	EntEdit.insertData = function() {
		if($("#callLicense").val()*1 < $("#autoCallLicense").val()*1){
			layer.alert("智能外呼占用中继资不能大于中继资源数",{icon: 5});
			return;
		}
		/* if($("#userLicense").val()*1 < $("#agentLicense").val()*1){
			layer.alert("话机数不能大于工号数",{icon: 5});
			return;
		} */
		if($("#dataValidTime").val() < 3){
			layer.alert("企业数据保存时间最少设置3个月",{icon: 5});
			return;
		}
		$("#phonePrefix").val($("#macAddr1").val()+$("#macAddr2").val()+$("#macAddr3").val());
		var data = form.getJSONObject("#editForm");
		ajax.remoteCall("${ctxPath}/servlet/ent?action=add",data,function(result) { 
			if(result.state == 1){
				layer.closeAll();
				var entId = result.data.entId;
				var entCode = result.data.entCode;
				var entName = result.data.entName;
				popup.openTab('${ctxPath}/pages/ent/ent-info.jsp',entName,{entId:entId,entCode:entCode});
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	EntEdit.schemaList = function(){
		 popup.layerShow({type:2,title:'数据库资源列表',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/ent/ent_res_schema.jsp",null);
	}
	EntEdit.petraList = function(){
		 popup.layerShow({type:2,title:'磐石资源列表',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/ent/ent_res_petra.jsp",null);
	}
	EntEdit.marsList = function(){
		 popup.layerShow({type:2,title:'Mars资源列表',offset:'20px',area:['80%','80%']},"${ctxPath}/pages/ent/ent_res_mars.jsp",null);
	}
	function entIdChange(obj){
		entId=obj.value;
		if(entId&&entId.length > 6){
			obj.value="";
			layer.msg("企业ID不能大于6位的数字！");
			return;
		}
		$("#macAddr3").val(entId);
	}
	function autoFocus(objId){
	    var id = "macAddr"+(objId+1);
	    var next = document.getElementById(id);
	    if(document.getElementById("macAddr"+objId).value.length>=document.getElementById("macAddr"+objId).size && next!=undefined){
	        next.focus();
	    }
	  }

    $(document).ready(function(){
        $(":input[id^='macAddr']").bind('keyup', handleAddr);
    });

    function handleAddr(){
       var current = this;
       var currvalue = current.value;
       var reg = /[^\A-Fa-f0-9]|_/ig;
       var index = parseInt(current.id.substring(7)) ;
       autoFocus(index);
    }
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>