<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>数据库资源列表</title>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		      <h5><span class="glyphicon glyphicon-user"></span> 数据库资源列表</h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">数据库名</span>	
									  <input type="text" name="schemaName" class="form-control input-sm" style="width:120px">
							   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="SchemaRes.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed text-c" data-options="{container:'#dataList',template:'list-template',pagination:true}" data-auto-fill="10" id="tableHead" data-mars="schemaRes.list">
                             <thead>
	                         	 <tr>
								      <th class="text-c">数据库名</th>
								      <th class="text-c">最大支持用户数</th>
								      <th class="text-c">已使用企业数</th>
								      <th class="text-c">已使用用户数</th>
								      <th class="text-c">数据说明</th>
								      <th class="text-c">创建时间</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td>{{:SCHEMA_NAME}}</td>
											<td>{{:MAX_COUNT}}</td>
											<td>{{:ENT_COUNT}}</td>
											<td>{{:USER_COUNT}}</td>
                                            <td>{{:SCHEMA_DESC}}</td>
                                            <td>{{:CREATE_TIME}}</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate" id="page">
	                     	  <jsp:include page="/pages/common/pagination.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
        
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("SchemaRes");
		
		SchemaRes.searchData=function(){
			$("#searchForm").searchData("#tableHead");
		}
	
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>