<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>企业计费配置</title>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" data-mars="ent.getFeeConf" data-pk="${param.entId}" method="post"  autocomplete="off" data-mars-prefix="feeEdit.">
				  <input type="hidden" name="feeEdit.ENT_ID">
				  <table class="table table-edit table-vzebra">
	                    <tbody>
			                   <tr>
			                        <td class="required" style="width: 200px">企业ID</td>
			                        <td><input type="text" readonly="readonly" name="entId" value="${param.entId}" class="form-control input-sm"></td>
		                       </tr>	
		                      <tr>
			                        <td class="required">通道数</td>
			                        <td><input type="number" name="feeEdit.CALL_LICENSE" data-rules="required|digits" class="form-control input-sm"></td>
		                     </tr>	
		                      <tr>
			                        <td class="required">标准计费</td>
			                        <td>
			                        	<label class="radio-inline">
										  	<input type="radio" class="radioItem" name="feeEdit.FEE_UNIT" checked="checked" value="1"> 按6秒计费
										</label>
										<label class="radio-inline">
										  	<input type="radio" class="radioItem" name="feeEdit.FEE_UNIT" value="2"> 按分钟计费
										</label>
			                        </td>
		                     </tr>	
		                      <tr>
			                        <td class="required">每通道套餐内通话时长/单位:秒</td>
			                        <td><input type="number" name="feeEdit.BASE_TIMES" data-rules="required|digits" class="form-control input-sm"></td>
			                 </tr>	
		                      <tr>
			                        <td class="required">基础套餐费用/单位:元</td>
			                        <td><input type="number" name="feeEdit.BASE_FEE" data-rules="required|number" class="form-control input-sm"></td>
			                 </tr> 
			                  <tr>
			                        <td class="required" id="overFeeTip">超通话套餐<span name="feeEdit.PER">每6秒</span>费用/单位:元</td>
			                        <td><input type="number" name="feeEdit.OVER_FEE" data-rules="required|number" class="form-control input-sm"></td>
			                 </tr>                      
	                    </tbody>
	                  </table>
	                  <div class="alert alert-warning mt-10 mb-5">
						    <a href="#" class="close" data-dismiss="alert">
						        &times;
						    </a>
						            当月修改计费规则，下个月生效。
					  </div>
					<div class="layer-foot text-c">
					   		<button class="btn btn-sm btn-primary"  type="button" onclick="feeEdit.updateData()">保存</button>
					   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="layer.closeAll();">关闭</button>
				   </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
	
	jQuery.namespace("feeEdit");
	
	feeEdit.entId='${param.entId}';
	$(function(){
		$("#editForm").render({success:function(result){
			$(".radioItem").change(  
			        function() {  
			        var val = $("input[name='feeEdit.FEE_UNIT']:checked").val();  
			        if (val == 1) {  
			            $("#overFeeTip").text("超通话套餐每6秒费用/单位:元");  
			        }else if (val == 2){  
			            $("#overFeeTip").text("超通话套餐每分钟费用/单位:元");  
			        }  
			});
		}});
	});

	feeEdit.updateData = function(){
		if(form.validate("#editForm")){
			var data = form.getJSONObject("#editForm");
			ajax.remoteCall("${ctxPath}/servlet/ent?action=updateFeeConf",data,function(result) { 
				if(result.state == 1){
					layer.msg(result.msg,{icon: 1},function(){
						Ent.searchEntInfo();
						layer.closeAll();
					});
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			});
		}
	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>