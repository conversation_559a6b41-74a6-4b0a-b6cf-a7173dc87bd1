<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情处理</title>
	<style>
	    .ibox-title{margin-bottom:10px}
	    .list-block .row{margin:0}
	    .list-block .bg-white{background:#fff;}
	    .list-block .pd-0{padding:0px}
	    .list-block .list-item{border-bottom:1px dashed #ccc; padding: 1px;}
	    .list-block .list-item h5{margin-bottom:15px}
	    .list-block .list-item h5 label span{font-weight:bold;}
	    .list-block .list-item h5 label span b{font-weight:normal;color: #a94442;margin-right:10px}
	    .list-block .list-item p{padding-left:26px}
	    .list-block .list-item p a{color: #76838f;}
	    .list-block .list-item p a:hover{text-decoration: none;}
	    .list-block .list-item .highlight{color:#F00}
	    .list-block .list-item .datetime{font-size:12px;margin-top:10px}
	    	    .list-block .list-highlight {background-color: #e3e8e7;}
	    
	    #filter h5{margin-top:15px;margin-bottom:4px}
		.label-list,.new-label-list,.new-label-list1{padding:0}
	    .label-list li{float:left;list-style:none}
	    .label-list li a{font-size: 12px;color:#76838f;background-color: #fff;padding: 5px 12px; border-radius: 2px; border: 1px solid #e7eaec;margin-right: 5px;margin-top: 5px;display: block;}
	    .label-list li a.checked{background:#3cadfe;color:#fff}
	    .label-list li a.onfocus{background-color: #ffa084;}
		.new-label-list.showMore{max-height:none}
		.new-label-list li{list-style:none;place-content: center;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
		.new-label-list{display: grid;grid-template-columns: repeat(3, 1fr);grid-gap: 5px;max-height: 330px;overflow-y: hidden;}
		.new-label-list li a{font-size: 12px;color:#76838f;background-color: #fff;padding: 5px 12px; border-radius: 2px; border: 1px solid #e7eaec;display: block;}
		.new-label-list li a.checked{background:#3cadfe;color:#fff}
		.new-label-list li a.onfocus{background-color: #ffa084;color: #fff;border-color: #ef9e82;}
		.more{display:none;font-size:12px}
		.new-label-list1 li{list-style:none;place-content: center;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
		.new-label-list1{display: grid;grid-template-columns: repeat(3, 1fr);grid-gap: 5px;max-height: 330px;overflow-y: hidden;}
		.new-label-list1 li a{font-size: 12px;color:#76838f;background-color: #fff;padding: 5px 12px; border-radius: 2px; border: 1px solid #e7eaec;display: block;}
		.new-label-list1 li a.checked{background:#3cadfe;color:#fff}
		.new-label-list1 li a.onfocus{background-color: #ffa084;color: #fff;border-color: #ef9e82;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
             <input type="hidden" data-mars="dataInfo.sentimentChannelList">
			<input type="hidden" data-mars="dataInfo.sentimentChannelDelete">
             <input type="hidden" data-mars="dataInfo.sentimentTime">
             <input type="hidden" data-mars="dataInfo.sentimentType">
             <input type="hidden" data-mars="dataInfo.sentimentDept">
             <input type="hidden" id="CHANNEL" name="CHANNEL">
             <input type="hidden" id="SENTIMENT_TYPE" name="SENTIMENT_TYPE" value="-1">
             <input type="hidden" id="CREATE_TIME" name="CREATE_TIME">
             <input type="hidden" id="DEPT" name="DEPT">
             <input type="hidden" id="changePage" name="changePage">                                 
               <div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
							   <h5><span class="glyphicon glyphicon-list"></span> 舆情处理</h5>
						       <div class="input-group input-group-sm">
									  <input type="text" name="CREATER" class="form-control input-sm" style="width:300px">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" id="listSearchData" onclick="selectList()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						 </div>
				    </div>
	                <div class="list-block">
		           	     <div class="row">
		           	         <div class="col-md-9 pd-0">
		           	              <div class="bg-white mr-10 pd-15">
		           	                 <div class="mb-10 clearfix">
	           	                          <button type="button" class="btn btn-xs btn-success pull-right" onclick="newOrder()">新建舆情</button>
	           	                          <button type="button" class="btn btn-xs btn-success mr-5 pull-right" onclick="orderDetailAll()">合并处理</button>
<!-- 	           	                          <button type="button" class="btn btn-xs btn-success mr-5 pull-right" onclick="checkAll(this)">全选</button>
 -->		           	                 </div>
		           	            		<div data-container="#labelTableHead"
					data-template="labelList-template" id="labelTableHead"
					data-mars="order.orderList" data-mars-search-list="true"></div>	
		           	       <script id="labelList-template" type="text/x-jsrender">
								   {{for  list}}
											{{if SENTIMENT_TYPE==-1}}
										 <div class="list-item list-highlight" >
										{{else }}
										 <div class="list-item " >
										{{/if}}
		           	                    <h5>
		           	                         <label class="checkbox checkbox-info checkbox-inline">
										   		<input type="checkbox" name="" value="{{:ID}}" onchange=""> <span ><b style="color:gray">{{:SENTIMENT_FROM}}</b>{{:CREATER}}</span>
										     </label>
		           	                         <span class="label label-warning ml-10" >{{dictFUN:SENTIMENT_TYPE "SENTIMENT_TYPE"}}</span>
										 </h5>
		           	                     <p class="mb-5" ><a href="{{:URL}}"  target="_blank"><span class="highlight" style="color:black">{{:TITLE}}</span>：<span  style="color:black">{{:CONTENT}}</span></a></p>
		           	                     <p class="datetime">发帖时间：<span class="mr-20">{{:CREATE_TIME}}</span>获取时间：<span>{{:CRAWL_TIME}}</span>同步时间：<span>{{:SYNC_TIME}}</span></p>
		           	                    	{{if SENTIMENT_FROM=='天涯社区'}}
											<p class="datetime">
		           	                           	     点击量：<span class="mr-20">{{:HITS_COUNT}}</span>
		           	                                                                                              回复量：<span class="mr-20">{{:REPLY_COUNT}}</span>                                      
		           	                    	 </p>
									{{else SENTIMENT_FROM=='百度贴吧'}}
												<p class="datetime">
      									      	 评论数量：<span class="mr-20">{{:REPLY_COUNT}}</span>
		           	                    	 </p>
										{{else }}
											<p class="datetime">      									      	
      										   粉丝数：<span class="mr-20">{{:FANS_COUNT}}</span>
		           	                                                                            注册地：<span class="mr-20">{{:ACCOUNT_AREA}}</span>
		           	                                                                            评论数量：<span class="mr-20">{{:REPLY_COUNT}}</span>
		           	                                                                           是否转载 ：<span class="mr-20">{{dictFUN:IS_REPRINTED "SF_YN"}}</span>
		           	                                                                             转载量 ：<span class="mr-20">{{:FORWARD_COUNT}}</span>
		           	                                                                            阅读量 ：<span>{{:LIKE_COUNT}}</span>
		           	                    	 </p>
										{{/if}}	
		           	                     	<p class="datetime">
		           	                         <a href="{{:URL}}"  target="_blank" class="mr-20">小天鹅官微</a>
		           	                         <a href="{{:URL}}"  target="_blank" class="mr-20">美的客服官微</a>
											{{if DATA_SOURCES=='01'}}
		           	                        	 <button type="button" class="btn btn-xs mr-20"><span  style="color:red">系统爬取</span></button>
											{{/if}}
											{{if DATA_SOURCES=='02'}}
		           	                        	 <button type="button" class="btn btn-xs mr-20"><span  style="color:red">手工建单</span></button>
											{{/if}}
											{{if NUM>0}}
		           	                        	 <button type="button"   class="btn btn-xs btn-success mr-20" id="orderHistory{{:NUM}}" onclick="orderHistory('{{:CREATER}}','{{:ID}}')"><span  style="color:red">追加舆情</span></button>
											{{/if}}
		           	                         <button type="button" class="btn btn-xs btn-success pull-right mr-10" onclick="orderDetail('{{dictFUN:SENTIMENT_TYPE "SENTIMENT_TYPE"}}','{{:ID}}')">{{dictFUN:SENTIMENT_TYPE "SENTIMENT_TYPE"}}</button> 
		           	                         <button type="button" class="glyphicon glyphicon-share-alt btn btn-xs  pull-right mr-10" onclick="orderReback('{{:ID}}')">回撤</button> 
		           	                      </p>
		           	                 </div>
								    {{/for}}					         
			     			  </script>
		           	              
		           	              <div class="row paginate">
	                     			<jsp:include page="/pages/common/pagination.jsp"/>
	                    		 </div> 
		           	              </div>
		           	         </div>
		           	             <div class="col-md-3 pd-0">
		           	              <div class="bg-white pd-15" id="filter">

									<div style="margin-bottom:5px;display: flex;justify-content: space-between; align-items: center;">
										<h5>来源</h5>
										<div onclick="showMore()" class="more">更多</div>
									</div>
									<ul  class="new-label-list1">
									<li><a id="all" onclick="selectList(0)">全部</a></li>
									</ul>
										<ul id="dataList1"  class="new-label-list">
										</ul>
										
		           	                   <h5>舆情类型</h5>
		           	                  <ul id="dataList2"  class="label-list clearfix">
										</ul>
		           	                   <h5>发帖时间</h5>
		           	                     <div class="clearfix">
		           	                       <div class="input-group  mt-10">
				             		             <span class="input-group-addon">开始日期</span>	
											     <input type="text" name="beginTime" id="beginTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:160px">
										   </div>
										  <div class="input-group  mt-10">
				             		             <span class="input-group-addon">结束日期</span>	
		                                         <input type="text" name="endTime" id="endTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss', onpicked:selectData})" style="width:160px">										 
		           	                       </div>
		           	                   </div>
		           	                  <!-- <ul id="dataList3"  class="label-list clearfix">
										</ul> -->
										   <h5>获取时间</h5>
		           	                     <div class="clearfix">
		           	                       <div class="input-group  mt-10">
				             		             <span class="input-group-addon">开始日期</span>	
											     <input type="text" name="CRAWL_TIME1" id="CRAWL_TIME1" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:160px">
										   </div>
										  <div class="input-group  mt-10">
				             		             <span class="input-group-addon">结束日期</span>	
		                                         <input type="text" name="CRAWL_TIME2" id="CRAWL_TIME2" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss', onpicked:selectData})" style="width:160px">										 
		           	                       </div>
		           	                   </div>
		           	                   <h5>事业部</h5>
		           	                  <ul id="dataList4"  class="label-list clearfix">
										</ul>
		           	              </div>
		           	         </div>
		           	     </div>
	                </div> 
               </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

	<script type="text/javascript">	
	jQuery.namespace("list");
	var val=true;
	$(function(){ 
		  $(document).keydown(function(event){ 
			  if(event.keyCode == 13){ //绑定回车 
			  $('#listSearchData').click(); 
			 	 } 
			  }); 
		$("#searchForm").render({
			success : function(result) {
				if(val){
				var dataInfo= result["dataInfo.sentimentChannelList"]['data'];
				var dataInfo_1 = result["dataInfo.sentimentType"]['data'];
				var dataInfo_2= result["dataInfo.sentimentTime"]['data'];
				var dataInfo_3= result["dataInfo.sentimentDept"]['data'];
				//获取被删掉的来源
				var dataInfo5= result["dataInfo.sentimentChannelDelete"]['data'];
				uiFill(dataInfo,dataInfo5);
				uiFill_1(dataInfo_1);
				uiFill_2(dataInfo_2);
				uiFill_3(dataInfo_3);
				val=false;	
				}

				//$('select[name="SENTIMENT_TYPE"]').render();
			}
		});

	})
	     function checkAll(obj){
	    	  var list = $(".list-block")
	    	  var ifCheck = list.find("input").eq(0).prop("checked");
	    	  list.find("input").prop("checked",!ifCheck);
	    	  if(!ifCheck){
	    		  $(obj).text("取消全选");
	    	  }
	    	  else{
	    		  $(obj).text("全选");
	    	  }
	     }
	     
	function selectList(count){	
			var option= $("select[name='pageSize'] option:selected");
			var pageSize=option.val();
		if(count>0){
			if('-1'!=count){	
			var num= $("input[name='pageIndex']").val();
			$("#changePage").val(num);
			$("#searchForm").searchData({success:function(result){
				var totalRowV=result["order.orderList"]["count"];
				var total=Math.ceil(totalRowV/pageSize);
				 $(".totalRowV").val(totalRowV);
				 $(".totalRow").html(result["order.orderList"]["count"]);				 
				 $(".totalPageV").val(total);
				$("#changePage").val('');
				}});
			
			}

		}else{
			if(count === 0) {
				$('#dataList1').find('.onfocus').removeClass('onfocus');
				$('#dataList2').find('.onfocus').removeClass('onfocus');
				$('#dataList3').find('.onfocus').removeClass('onfocus');
				$('#dataList4').find('.onfocus').removeClass('onfocus');
				$('#all').addClass('onfocus');
				$("#CHANNEL").val('');
			}else {
				$('#all').removeClass('onfocus');
			}
			$("#changePage").val('');
			$("#searchForm").searchData();
		}
	};
	     function newOrder(){
	    	 popup.layerShow({type:2,title:'新建舆情',shadeClose:false,area:['800px','550px'],offset:'20px'},"${ctxPath}/pages/order/order-new.jsp",null);
	     }
	     
	     function orderDetail(ago,id){

	    	 if('正向舆情'==ago){
		    	 popup.layerShow({type:2,title:'舆情工单',shadeClose:false,area:['90%','820px'],offset:'10px'},"${ctxPath}/pages/order/order-archive.jsp",{id:id});	 
	    	 }else{
	    		 if(getCurrentStatus(id)){
	 				popup.openTab("${ctxPath}/pages/order/order-deal.jsp","舆情工单",{id:id});

	    		 }
				//popup.layerShow({type:2,title:'舆情工单',area:['90%','800px'],offset:'20px'},"${ctxPath}/pages/order/order-deal.jsp",{id:id});
	    	 }
	     }
	     
	     function orderHistory(val,val2){
				popup.openTab("${ctxPath}/pages/order/order-history.jsp","历史工单",{name:val,id:val2});	 

	     }
	     function selectList4(ago){
				$("#CHANNEL").val(ago);
				selectList();
		    }
		    function selectList1(ago){
				$("#SENTIMENT_TYPE").val(ago);
				selectList();
		    }
		    function selectList2(ago){
				$("#CREATE_TIME").val(ago);
				selectList();
		    }
			function showMore(){
				$(".new-label-list").toggleClass('showMore')
			}
		    function selectList3(ago){
				$("#DEPT").val(ago);
				selectList();
		    }
		    function uiFill(data,delData){
<%--		    	var liStr = "<li><a href=\"javascript:selectList4('0');\">全部</a></li>";--%>
				var liStr = ""
				var targetList=[]
		    	for(var i of data){
				if(delData[i.value]){
					continue;
				}
				targetList.push(i.text)
		    		var ago=data[i.text];
					 liStr+=("<li><a title="+i.text+" href=\"javascript:selectList4("+ i.value++ +");\">"+i.text+"</a></li>");
		    	}
				if(Object.keys(targetList).length>30){
					$('.more').show()
				}
				$("#dataList1").html(liStr);
				$('#dataList1').find('a').unbind('click').bind({
					click:function(e){
						$('#dataList1').find('.onfocus').removeClass('onfocus');
						$(this).toggleClass('onfocus');
						//return false;
					}
				})
		    }
		    function uiFill_1(data){
		    	var liStr = "<li><a href=\"javascript:selectList1('4');\">全部</a></li>";
		    	for(var i in data){	 
		    		var ago=data[i];
					 liStr+=("<li><a href=\"javascript:selectList1("+i+");\">"+ago+"</a></li>");				
		    	}	    	
				$("#dataList2").html(liStr);
				$('#dataList2').find('a').unbind('click').bind({
					click:function(e){
						$('#dataList2').find('.onfocus').removeClass('onfocus');
						$(this).toggleClass('onfocus');
						//return false;
					}
				})
				$($("#dataList2").find('li')[3]).find('a').click();
		    }
		    function uiFill_2(data){
		    	var liStr = "<li><a href=\"javascript:selectList2('0');\">全部</a></li>";
		    	for(var i in data){	 
		    		var ago=data[i];		    		
					 liStr+=("<li><a href=\"javascript:selectList2("+i+");\">"+ago+"</a></li>");			
		    	}	    	
				$("#dataList3").html(liStr);
				$('#dataList3').find('a').unbind('click').bind({
					click:function(e){
						$('#dataList3').find('.onfocus').removeClass('onfocus');
						$(this).toggleClass('onfocus');
						//return false;
					}
				})
		    }
		    function uiFill_3(data){
		    	var liStr = "<li><a href=\"javascript:selectList3('0');\">全部</a></li>";
		    	for(var i in data){	 
		    		var ago=data[i];
					 liStr+=("<li><a href=\"javascript:selectList3("+i+++");\">"+ago+"</a></li>");			
		    	}	    	
				$("#dataList4").html(liStr);
				$('#dataList4').find('a').unbind('click').bind({
					click:function(e){
						$('#dataList4').find('.onfocus').removeClass('onfocus');
						$(this).toggleClass('onfocus');
						//return false;
					}
				})
		    }
		    function orderDetailAll(){
		    	var data={};
		    	var arr=new Array();
		    	var ago4="";
				 var checkedRecord = $(".list-block").find("input[type='checkbox']:checked");
				 if(checkedRecord.length<2){
						layer.alert('合并处理至少需要两行',{icon : 2,time:1000});
						return false;
				 }
				 for(var i=0;i<checkedRecord.length;i++){
						arr.push(checkedRecord[i].defaultValue);
						ago4+=checkedRecord[i].defaultValue+";";
					}
					data.ids=arr;
					
					if(!getCurrentStatus(ago4)){
						return false;
					}
					ajax.remoteCall("${ctxPath}/servlet/order?query=OrderTo",data,function(result) { 
						//debugger;
							if(result.state == 1){
									arr=arr.toString();
								 if('1'==result.msg){
							    	 popup.layerShow({type:2,title:'舆情工单',area:['90%','820px'],offset:'10px'},"${ctxPath}/pages/order/order-archive.jsp",{id:arr});	 
						    	 }else{
										popup.openTab("${ctxPath}/pages/order/order-deal.jsp","舆情工单",{id:arr});	 
						    	 }
								}else{
								layer.alert(result.msg,{icon: 5,time:1000});
							}
						}
					);
		    }
		    function orderReback(val){
		    	 layer.confirm('确认回撤?', 
				            
				            {
				              btn: ['确定','取消'] //按钮
				    
				            }, 
				            
				            function(index){
					var data={};
					data.id=val;
					ajax.remoteCall("${ctxPath}/servlet/discriminant?action=retreat",data,function(result) { 

				    				//debugger;
				    					if(result.state == 1){
				    						layer.alert(result.msg,{icon: 1,closeBtn:0,time: 1000},function(){
				    						});
				    						selectList('1');
				    						}else{
				    						layer.alert(result.msg,{icon: 5});
				    					}
				    				}
				    			);
				              return true;
				            },
				            function(index){
				                layer.msg('已取消！', {icon: 1});
				                return false;
				            }
				        );

		    }
		 	function getCurrentStatus(val) {
				r = true;
		 		$.ajax({
					url:"${ctxPath}/servlet/order?query=SeStatus1",
					data:{"id":val},
					async:false,
					success:function(result) { 
						if(result.state == 1){
							var status=result.data;
							if("1"==status){
								layer.alert("舆情信息已有人正在处理....",{icon:2});
								r = false;
							}
						}
					}
				});
		 		return r;
		 	}    
		 	 function selectData(){	
					selectList();
		 
			 }

	//小眼睛功能
	function showDetail(id,name){
		showDetailCommon({
			"model":"sentiment",
			"url":"/sentiment/pages/deal/deal-list.jsp",
			"action":"acces",
			"describe":"舆情[舆情处理]数据，看客户[{{name}}]敏感信息"},id,name);
	}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>