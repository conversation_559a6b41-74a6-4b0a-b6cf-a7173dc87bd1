<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情监控</title>
	<style>
	    .row{height:360px}
	    .row div[class^="col-md"]{padding:0 5px}
	    .row div[class^="col-md"]>div{background:#233c50;padding:15px 0;height:600px}	    
	</style>
	<script type="text/javascript" src="/easitline-static/lib/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/worldcloud.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
        <form  name="searchEchartsForm" class="form-inline" id="searchEchartsForm"  style="height: 250px;">
	         <div class="ibox-title clearfix" > 
			      <div class="form-group">
			          <h5><span class="glyphicon glyphicon-list"></span> 舆情监控</h5>
			      </div>
					    <div class="input-group ">	           		   				
						<div class="input-group " style="width: 120px">
						<span class="input-group-addon">服务请求</span> <select name="SERVICE_TYPE"
							class="form-control input-sm" style="width: 130px"
							data-mars="order.serviceType" onchange="changRequest(this.value)" id="SERVICE_TYPE">
							<option value="">-请选择-</option>
						</select>
					</div>
					<div class="input-group " style="width: 120px">
						<span class="input-group-addon">投诉原因</span> <select
							name="COMPLAIN_REASON" class="form-control input-sm"
							style="width: 130px" data-mars="order.complainReason" onchange="changReason(this.value)" id="COMPLAIN_REASON">
							<option value="">-请选择-</option>

						</select>
					</div>
					<div class="input-group " style="width: 120px">
						<span class="input-group-addon">投诉类别</span> <select name="COMPLAIN_TYPE"
							class="form-control input-sm" style="width: 130px"
							data-mars="order.complainReason" onchange="changComplain(this.value)" id="COMPLAIN_TYPE">
							<option value="">-请选择-</option>
						</select>
					</div>
				
	           		   <div class="input-group ">	           		   
						<span class="input-group-addon">发布时间</span> <input type="text"
							class="form-control input-sm Wdate" name="start"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="start"
							placeholder=""> <span class="input-group-addon">至</span>
						<input type="text" class="form-control input-sm Wdate" name="end"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="end"
							placeholder="">
					</div> 
					
					<div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="selectEchart()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>   						     
	           		  </div>
			  <div class="ibox-content" style="height: 250px;">
			       <div class="row mb-10">
				       <div class="col-md-10" >
				           <div id="chart-1" style="height: 250px;">></div>
				       </div>
				     		  
			  </div>
	    </div>
			  </div>
  </form>
   <form  name="searchEchartsForm1" class="form-inline" id="searchEchartsForm1"  style="height: 300px;"> 
	         <div class="ibox-title clearfix" > 
			 
					    <div class="input-group ">	           		   				
						<div class="input-group " style="width: 120px">
						<span class="input-group-addon">服务请求</span> <select name="SERVICE_TYPE1"
							class="form-control input-sm" style="width: 130px"
							data-mars="order.serviceType" onchange="changRequest(this.value,'1')" id="SERVICE_TYPE1">
							<option value="">-请选择-</option>
						</select>
					</div>
					<div class="input-group " style="width: 120px">
						<span class="input-group-addon">投诉原因</span> <select
							name="COMPLAIN_REASON1" class="form-control input-sm"
							style="width: 130px" data-mars="order.complainReason" onchange="changReason(this.value,'1')" id="COMPLAIN_REASON1">
							<option value="">-请选择-</option>

						</select>
					</div>
					<div class="input-group " style="width: 120px">
						<span class="input-group-addon">投诉类别</span> <select name="COMPLAIN_TYPE1"
							class="form-control input-sm" style="width: 130px"
							data-mars="order.complainReason" onchange="changComplain(this.value,'1')" id="COMPLAIN_TYPE1">
							<option value="">-请选择-</option>
						</select>
					</div>
					 <div class="input-group ">	           		   
	           	 	<span  class="input-group-addon">事业部</span>
	           	 	<select  onChange="upSentitimeByCh1(this.value)"  class="form-control input-sm" id="division1" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')" >
				       			<option value="">全部</option>

				       	</select>
				    </div>
	           		  
					
					<div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="selectEchart1()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>   						     
	           		  </div>
	           	 <div class="form-group">
	           
				    <div class="input-group ">	           		   
	           	 <span  class="input-group-addon">统计类型</span>
	           	 <select  onChange="upSentitimeByCh2(this.value)"  class="form-control input-sm" id="division2" name="division2" >
				       			<option value="">全部</option>
				       			<option value="1">按日</option>
				       			<option value="2">按月</option>

				       			</select>
				       			</div>
				       			
			 <div class="input-group ">	           		   
						<input type="text"
							class="form-control input-sm Wdate" name="start3"
							onClick="WdatePicker({dateFmt:'yyyy-MM'})" id="start3" style="display:none" 
							placeholder=""><!-- 按日 -->
					<input type="text"
							class="form-control input-sm Wdate" name="start1"  style="display:none"
							onClick="WdatePicker({dateFmt:'yyyy'})" id="start1"
							placeholder=""><!-- 按月 -->
					</div> 
	           		  </div>
			  <div class="ibox-content" style="height: 300px;">
			       <div class="row mb-10">
				       <div class="col-md-10" >
				           <div id="chart-2" style="height: 300px;"></div>
				       </div>
				     		  
			  </div>
	    </div>
			  </div>
  </form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">	
	$(function() {
		//该段代码避免在 jQuery.namespace("EmailSendTemp"); 之前，否则命名空间被改变之后，调用不到
		var currDate = getCurrDate();
		$("#start").val(get3MonthBefor(3,0));
		$("#end").val(get3MonthBefor(1,1));
	})
	jQuery.namespace("echarts");
	   //统计图表1
    var chart1 = echarts.init(document.getElementById('chart-1'));
    var chart2 = echarts.init(document.getElementById('chart-2'));
    $(function(){
    	$("#searchEchartsForm").render();
    	$("#searchEchartsForm1").render();
    	getEchar_1();
    	getEchar_2();
	});

    function getEchar_1() {
  	   chart1.clear();

	 	var data = form.getJSONObject("searchEchartsForm");
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=ChartFive",data,function(result) { 
			if(result.state == 1){	 					
				var charData=result.data;
				Object.keys(charData).forEach(function(key){
					if('li_1'==key){
						option1.xAxis[0].data = charData[key];  	
					}else if('li_2'==key){
						option1.series[0].data = charData[key];  	 	
					}
				});
				chart1.setOption(option1);

				}else{
				layer.alert(result.msg,{icon: 5});
			}
		}
	);

 }
    function getEchar_2(val) {
 	   chart2.clear();
	 	var data = form.getJSONObject("searchEchartsForm1");
		var option=$("#division1 option:selected");				
		var val1=option.text();
		if('全部'!=val1){
			data.type=val1;

		}
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=ChartFiveByType",data,function(result) { 
			if(result.state == 1){	 					
				var charData=result.data;
				Object.keys(charData).forEach(function(key){
					if('li_1'==key){
						option2.xAxis.data = charData[key];  	
					}else if('li_2'==key){
						option2.legend.data = charData[key];  	 	
					}else if('li_3'==key){
						option2.series = charData[key];  	 	
					}else{
						option2.color=charData[key];  	
					}
				});
				chart2.setOption(option2);

				}else{
				layer.alert(result.msg,{icon: 5});
			}
		}
	);

 }
    function selectEchart(){
    	
        	getEchar_1();	    
    }
    function selectEchart1(val){
    	var val=$("#division2").val();
    	if('1'==val){
    		var val1=$("#start3").val();
    		if(''==val1){
    			layer.alert("请选择时间",{icon:2});
    			return false;
    		}
    		
    	}else if('2'==val){
    		var val2=$("#start1").val();
    		if(''==val2){
    			layer.alert("请选择时间",{icon:2});
    			return false;
    		}

    	}
        	getEchar_2(val);	    
    }
    
    var option1={
    		//backgroundColor: '#233c50',
            title: {
     	    	        text: '投诉分析',
     	    	        left: 15,
     	    	        textStyle: {
     	    	            //color: '#ccc',
     	    	            fontSize: 13
     	    	        }
     	    	    },
         tooltip : {
             trigger: 'axis',
             axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                 type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
             }
         },
         grid: {
             left: '3%',
             right: '4%',
             bottom: '3%',
             containLabel: true
         },
         label: {
             normal: {
                 show: true,
                 position: 'top',
                 textStyle: {
                   //color:'#ccc',
                 }
             }
          },
         xAxis : [
             {
                 type : 'category',
     			 //axisLine: {lineStyle:{color:'#ccc'}},
                 //data : ['已暂存', '已派工', '处理中', '待回访', '已归档'],
                 axisTick: {
                     alignWithLabel: true
                 }
             }
         ],
         yAxis : [
             {
                 type : 'value',
     			//axisLine: {lineStyle:{color:'#ccc'}}
             }
         ],
         color:['#62d1de'],
         series : [
             {
                 name:'数量',
                 type:'bar',
                 barWidth: '60%',
                 
                // data:[10, 52, 200, 334, 390]
             }
         ]
    	};
    function changRequest(val,val1){
 		//$("#COMPLAIN_REASON").empty();
		if('1'==val1){
			$("#COMPLAIN_REASON1").render({data:{id:val}});
			
			$("#COMPLAIN_TYPE1").empty(); 
			$("#COMPLAIN_TYPE1").html('<option value="">-请选择-</option>');
			selectEchart1()

		}else{
			$("#COMPLAIN_REASON").render({data:{id:val}});
			$("#COMPLAIN_TYPE").empty(); 
			$("#COMPLAIN_TYPE").html('<option value="">-请选择-</option>');

			selectEchart();	
		}
 	}
 	function changReason(val,val1){
 		//$("#COMPLAIN_REASON").empty();
		if('1'==val1){
			$("#COMPLAIN_TYPE1").render({data:{id:val}});
			selectEchart1()
		}else{
			$("#COMPLAIN_TYPE").render({data:{id:val}});

			selectEchart();	
		}

 	}
 	function changComplain(val,val1){
 		//$("#COMPLAIN_REASON").empty();
		if('1'==val1){
			$("#COMPLAIN_POINT1").render({data:{id:val}});	
			selectEchart1()

			}else{		
			$("#COMPLAIN_POINT").render({data:{id:val}});	
			selectEchart();	
			}
		}
   function upSentitimeByCh2(val){
	   if('1'==val){
		   $("#start3").show();  
		   $("#start1").hide();  
	   }else if('2'==val){
		   $("#start1").show();  
		   $("#start3").hide();  
	   }else{
		   $("#start1").hide();  
		   $("#start3").hide(); 
	   }
   }
   function upSentitimeByCh1(val){
		selectEchart1(val);
   }
   var option2 = {
   		//backgroundColor: '#233c50',
   	    title: {
   	    	text: '舆情投诉分析监控(趋势)',
   	        left: 13,
   	        textStyle: {
   	           // color: '#ccc',
   	            fontSize: 13
   	        }
   	    },
   	    tooltip: {
   	        trigger: 'axis'
   	    },
   	    legend: {
   	    	  top:20,
                 itemWidth:23,
                 itemHeight:9,
   	  
   	    },
   	    grid: {
   	        left: '3%',
   	        right: '4%',
   	        bottom: '3%',
   	        containLabel: true
   	    },
   	  label: {
          normal: {
              show: true,
              position: 'top',
              textStyle: {
                //color:'#ccc',
              }
          }
       },
   	 

   	    xAxis: {
   	        type: 'category',
	       // axisLine: {lineStyle:{color:'#ccc'}},

   	    },
   	    yAxis: {
   	    	//axisLine: {lineStyle:{color:'#ccc'}},
   	        type: 'value'
   	    }
 
   	};
   function get3MonthBefor(val,val1){
       var resultDate,year,month,date,hms;
       var currDate = new Date();
       year = currDate.getFullYear();
       month = currDate.getMonth()+1;
       //date = currDate.getDate();
       hms = currDate.getHours() + ':' + currDate.getMinutes() + ':' + (currDate.getSeconds() < 10 ? '0'+currDate.getSeconds() : currDate.getSeconds());
       switch(month)
       {
         case 1:
         case 2:
         case 3:
           month += 9;
           year--;
           break;
         default:
           month -= val;
           break;
       }
       month = (month < 10) ? ('0' + month) : month;
       if('0'==val1){
       	date='01';
       }else{
       	date=	getLastDay(year,month);
       }        
       resultDate = year + '-'+month+'-'+date+' ' + " 00:00:00";
     return resultDate;
   }
   function getLastDay(year,month)   
   {   
   	  var dt = new Date(year,month,1);  
   	    cdt = new Date(dt.getTime()-1000*60*60*24);  
   	   return cdt.getDate();
   } 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>