<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情监控</title>
	<style>
	    .row{height:360px}
	    .row div[class^="col-md"]{padding:0 5px}
	    .row div[class^="col-md"]>div{background:#233c50;padding:15px 0;height:360px}	    
	</style>
	<script type="text/javascript" src="/easitline-static/lib/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/worldcloud.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
       <form  name="searchEchartsForm" class="form-inline" id="searchEchartsForm"  >
	   	<div class="ibox">
	         <div class="ibox-title clearfix">
			      <div class="form-group">
			          <h5><span class="glyphicon glyphicon-list"></span> 舆情监控</h5>
			      </div>
				<div class="form-group">
	           		   <div class="input-group input-group-sm">
						<span class="input-group-addon">发布时间</span> <input type="text"
							class="form-control input-sm Wdate" name="start"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="start"
							placeholder=""> <span class="input-group-addon">至</span>
						<input type="text" class="form-control input-sm Wdate" name="end"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="end"
							placeholder="">
					</div> 
					
					<div class="input-group input-group-sm">
									  <button type="button" class="btn btn-sm btn-default" onclick="selectEchart()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>   						     
	           		  </div>
			  </div>
			  <div class="ibox-content">
			       <div class="row mb-5">
				       <div class="col-md-4">
				           <div id="chart-1"></div>
				       </div>
				       <div class="col-md-4" style="padding:0">
				            <div id="chart-2"></div>
				       </div>
				       <div class="col-md-4">
				            <div id="chart-3"></div>
				       </div>
				   </div>
				   <div class="row">
				       <div class="col-md-4">
				           <div id="chart-4"></div>
				       </div>
				       <div class="col-md-4" style="padding:0">
				            <div id="chart-5"></div>
				       </div>
				       <div class="col-md-4">
				            <div id="chart-6"></div>
				       </div>
				   </div>
			  </div>
	    </div>
  </form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">	
	jQuery.namespace("echarts");
	   //统计图表1
    var chart1 = echarts.init(document.getElementById('chart-1'));
    var chart2 = echarts.init(document.getElementById('chart-2'));
    var chart3 = echarts.init(document.getElementById('chart-3'));
    var chart4 = echarts.init(document.getElementById('chart-4'));
    var chart5 = echarts.init(document.getElementById('chart-5'));
    $(function(){
		getEchar_1();	  
		getEchar_2();
		getEchar_3();
		getEchar_4();
		getEchar_5();
	});
    function getEchar_1() {
	 	var data = form.getJSONObject("searchEchartsForm");
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=Chart_2",data,function(result) { 
			if(result.state == 1){
				var charData=result.data;
				option1.series[0].data = charData;  	
				chart1.setOption(option1);
				}else{
				layer.alert(result.msg,{icon: 5});
			}
		}
	);

 }
    function getEchar_2() {
		var data = form.getJSONObject("searchEchartsForm");
		data.type='1';
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=Chart_3",data,function(result) { 
				if(result.state == 1){
					var charData=result.data;
					Object.keys(charData).forEach(function(key){
						if('list_1'==key){
							option2.yAxis.data = charData[key];  	
						}else if('list_2'==key){
							option2.series[0].data = charData[key];  
						}
					});
					//option2.series[0].data = result.data;   
					chart2.setOption(option2);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    function getEchar_3() {
		var data = form.getJSONObject("searchEchartsForm");
		data.type='-1';
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=Chart_3",data,function(result) { 
				if(result.state == 1){
					var charData=result.data;
					Object.keys(charData).forEach(function(key){
						if('list_1'==key){
							option3.yAxis.data = charData[key];  	
						}else if('list_2'==key){
							option3.series[0].data = charData[key];  
						}
					});
					//option2.series[0].data = result.data;   
					chart3.setOption(option3);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }

    function getEchar_5() {
		var data = form.getJSONObject("searchEchartsForm");
		data.type='-1';
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=Chart_6",data,function(result) { 
				if(result.state == 1){
					option5.series[0].data = result.data;   
					chart5.setOption(option5);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    function getEchar_4() {
		var data = form.getJSONObject("searchEchartsForm");
		data.type='1';
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=Chart_6",data,function(result) { 
				if(result.state == 1){
					option4.series[0].data = result.data;   
					chart4.setOption(option4);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }

 var option1 = {
 	backgroundColor: '#233c50',
        title: {
 	    	        text: '舆情处理状态监控(全品类)',
 	    	        left: 15,
 	    	        textStyle: {
 	    	            color: '#ccc',
 	    	            fontSize: 13
 	    	        }
 	    	    },
     tooltip : {
         trigger: 'axis',
         axisPointer : {            // 坐标轴指示器，坐标轴触发有效
             type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
         }
     },
     grid: {
         left: '3%',
         right: '4%',
         bottom: '3%',
         containLabel: true
     },
     label: {
         normal: {
             show: true,
             position: 'top',
             textStyle: {
               color:'#ccc',
             }
         }
      },
     xAxis : [
         {
             type : 'category',
 			 axisLine: {lineStyle:{color:'#ccc'}},
             data : ['已暂存', '已派工', '处理中', '待回访', '已归档'],
             axisTick: {
                 alignWithLabel: true
             }
         }
     ],
     yAxis : [
         {
             type : 'value',
 			axisLine: {lineStyle:{color:'#ccc'}}
         }
     ],
     color:['#62d1de'],
     series : [
         {
             name:'数量',
             type:'bar',
             barWidth: '60%',
             
            // data:[10, 52, 200, 334, 390]
         }
     ]
 };

    var option2 = {
    		backgroundColor: '#233c50',
    	    title: {
    	        text: '正面舆情热频关键字占比',
    	          textStyle: {
    		    	            color: '#ccc',
    		    	            fontSize: 13
    		    	        }
    	    },
    	    tooltip: {
    	        trigger: 'axis',
    	        axisPointer: {
    	            type: 'shadow'
    	        }
    	    },
    	    color:['#68AFE3'], 
    	    grid: {
    	        left: '3%',
    	        right: '4%',    
    	        bottom: '3%',
    	        containLabel: true
    	    },
    	    
    	    xAxis: {
    	        type: 'value',  
                axisLabel: {  
                      show: true,  
                      interval: 'auto',  
                      formatter: '{value} %'  
                    },  
    	        axisLine: {lineStyle:{color:'#ccc'}}
    	    },
    	    yAxis: {
    	        type: 'category',
    	    axisLine: {lineStyle:{color:'#ccc'}},
    	    axisLabel :{  
	            interval:0   
	        }
    	    },
    	    series: [
    	        {
    	            name: '关键字',
    	            type: 'bar'
    	        }
    	     
    	    ]
    	};
    var option3 = {
	    	    		backgroundColor: '#233c50',
	    	    	    title: {
	    	    	        text: '负面舆情热频关键字占比',
	    	    	          textStyle: {
	    	    		    	            color: '#ccc',
	    	    		    	            fontSize: 13
	    	    		    	        }
	    	    	    },
	    	    	    tooltip: {
	    	    	        trigger: 'axis',
	    	    	        axisPointer: {
	    	    	            type: 'shadow'
	    	    	        }
	    	    	    },
	    	    	    color:['#68AFE3'], 
	    	    	    grid: {
	    	    	        left: '3%',
	    	    	        right: '4%',    
	    	    	        bottom: '3%',
	    	    	        containLabel: true
	    	    	    },
	    	    	    
	    	    	    xAxis: {
	    	    	        type: 'value',  
	    	                axisLabel: {  
	    	                      show: true,  
	    	                      interval: 'auto',  
	    	                      formatter: '{value} %'  
	    	                    },  
	    	    	        axisLine: {lineStyle:{color:'#ccc'}}
	    	    	    },
	    	    	    yAxis: {
	    	    	        type: 'category',
	    	    	    axisLine: {lineStyle:{color:'#ccc'}},
	    	    	    axisLabel :{  
				            interval:0   
				        }
	    	    	    },
	    	    	    series: [
	    	    	        {
	    	    	            name: '关键字',
	    	    	            type: 'bar'
	    	    	        }
	    	    	     
	    	    	    ]
	    	    	};
   
    var option5 = {  
		     backgroundColor: '#233c50',
		     title: {
	    	        text: '负面舆情热频词词云图',
	    	        left: 15,
	    	        textStyle: {
	    	            color: '#ccc',
	    	            fontSize: 13
	    	        }
	    	    },
              tooltip: {  
                  show: true  
              },  
              series: [{  
                  name: '负面舆情热频词词云图',  
                  type: 'wordCloud',  
                  sizeRange: [6, 66],  
                  rotationRange: [-45, 90],  
                  textPadding: 0,  
                  autoSize: {  
                      enable: true,  
                      minSize: 6  
                  },  
                  textStyle: {  
                      normal: {  
                          color: function() {  
                              return 'rgb(' + [  
                                  Math.round(Math.random() * 160),  
                                  Math.round(Math.random() * 160),  
                                  Math.round(Math.random() * 160)  
                              ].join(',') + ')';  
                          }  
                      },  
                      emphasis: {  
                          shadowBlur: 10,  
                          shadowColor: '#333'  
                      }  
                  },  
                  data: [{  
                      name: "Jayfee",  
                      value: 666  
                  }, {  
                      name: "Nancy",  
                      value: 520  
                  }]  
              }]  
          };
    var option4 = {  
		     backgroundColor: '#233c50',
		     title: {
	    	        text: '正面舆情热频词词云图',
	    	        left: 15,
	    	        textStyle: {
	    	            color: '#ccc',
	    	            fontSize: 13
	    	        }
	    	    },
               tooltip: {  
                   show: true  
               },  
               series: [{  
                   name: '正面舆情热频词词云图',  
                   type: 'wordCloud',  
                   sizeRange: [6, 66],  
                   rotationRange: [-45, 90],  
                   textPadding: 0,  
                   autoSize: {  
                       enable: true,  
                       minSize: 6  
                   },  
                   textStyle: {  
                       normal: {  
                           color: function() {  
                               return 'rgb(' + [  
                                   Math.round(Math.random() * 160),  
                                   Math.round(Math.random() * 160),  
                                   Math.round(Math.random() * 160)  
                               ].join(',') + ')';  
                           }  
                       },  
                       emphasis: {  
                           shadowBlur: 10,  
                           shadowColor: '#333'  
                       }  
                   },  
                   data: [{  
                       name: "Jayfee",  
                       value: 666  
                   }, {  
                       name: "Nancy",  
                       value: 520  
                   }]  
               }]  
           };
    function selectEchart(){
    	chart1.clear();
 		chart2.clear();
 		chart3.clear();
 		chart4.clear();
 		chart5.clear();
 		chart6.clear();
    	getEchar_1();	  
		getEchar_2();
		getEchar_3();
		getEchar_4();
		getEchar_5();
		getEchar_6();
    }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>