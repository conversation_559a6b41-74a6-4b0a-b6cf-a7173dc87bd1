<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>舆情监控</title>
	<style>
	    .row{height:360px}
	    .row div[class^="col-md"]{padding:0 5px}
	    .row div[class^="col-md"]>div{background:#233c50;padding:15px 0;height:360px}	    
	</style>
	<script type="text/javascript" src="/easitline-static/lib/echarts/echarts.min.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/echarts-wordcloud.min.js"></script>
</EasyTag:override>
<EasyTag:override name="content">
       <form  name="searchEchartsForm" class="form-inline" id="searchEchartsForm"  >
	   	<div class="ibox">
	         <div class="ibox-title clearfix">
			      <div class="form-group">
			          <h5><span class="glyphicon glyphicon-list"></span> 舆情监控</h5>
			      </div>
				<div class="form-group">
	           		   <div class="input-group ">
						<span class="input-group-addon">发布时间</span> <input type="text"
							class="form-control input-sm Wdate" name="start"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="start"
							placeholder=""> <span class="input-group-addon">至</span>
						<input type="text" class="form-control input-sm Wdate" name="end"
							onClick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})" id="end"
							placeholder="">
					</div> 
					
					<div class="input-group ">
									  <button type="button" class="btn btn-sm btn-default" onclick="selectEchart()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>   						     
	           		  </div>
			  </div>
			  <div class="ibox-content">
			       <div class="row mb-5">
				       <div class="col-md-4">
				           <div id="chart-1"></div>
				       </div>
				       <div class="col-md-4" style="padding:0">
				       <div style="position:absolute;right:10px; top:1px; z-index:5;;height: 5px;">
				       			<select  style=" top:1px;" onChange="upSentitimeByCh(this.value)" id="divisions" data-cust-context-path="/yq_common" data-cust-mars="dict.getDictList('CC_ORG_CODE')">
				       			<option value="">全部</option>

				       			</select>
				       			</div>
				            <div id="chart-2"></div>
				       </div>
				       <div class="col-md-4">
				       			<div style="position:absolute;right:10px; top:10px; z-index:5;;height: inherit;">
				       			<select onChange="upKeyWord(this.value)">
				       			<option value="">全部</option>
				       			<option value="1">正面</option>
				       			<option value="-1">负面</option>

				       			</select>
				       			</div>				       
				            <div id="chart-3"></div>
				       </div>
				   </div>
				   <div class="row">
				       <div class="col-md-4">
				           <div id="chart-4"></div>
				       </div>
				       <div class="col-md-4" style="padding:0">
				            <div id="chart-5"></div>
				       </div>
				       <div class="col-md-4" style="position:relative;">
						<div style="position:absolute;right:10px; top:10px; z-index:5;;height: inherit;">
				       			<select onChange="upKeyWordCloud(this.value)">
				       			<option value="">全部</option>
				       			<option value="正面">正面</option>
				       			<option value="负面">负面</option>

				       			</select>
				       			</div>	
				       		 <div id="chart-6">
				       		 </div>				       							       			
				       </div>
				   </div>
			  </div>
	    </div>
  </form>
</EasyTag:override>

<EasyTag:override name="script">
<script type="text/javascript"
		src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">	
	$(function() {
		//该段代码避免在 jQuery.namespace("EmailSendTemp"); 之前，否则命名空间被改变之后，调用不到
		var currDate = getCurrDate();
		$("#start").val(get3MonthBefor(3,0));
		$("#end").val(get3MonthBefor(1,1));
	})
	jQuery.namespace("echarts");
	   //统计图表1
    var chart1 = echarts.init(document.getElementById('chart-1'));
    var chart2 = echarts.init(document.getElementById('chart-2'));
    var chart3 = echarts.init(document.getElementById('chart-3'));
    var chart4 = echarts.init(document.getElementById('chart-4'));
    var chart5 = echarts.init(document.getElementById('chart-5'));
    var chart6 = echarts.init(document.getElementById('chart-6'));
    $(function(){
    	$("#division").render();
		getEchar_1();	  
		getEchar_2();
		getEchar_3();
		getEchar_4();
		getEchar_5();
		getEchar_6();
	});
    function getEchar_1() {
		var data = form.getJSONObject("searchEchartsForm");
		ajax.remoteCall("${ctxPath}/servlet/echarts?action=Chart_1",data,function(result) { 
				if(result.state == 1){
					option1.series[0].data = result.data;   
					chart1.setOption(option1);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    function getEchar_2(val) {
		var data = form.getJSONObject("searchEchartsForm");
		var option=$("#divisions option:selected");				
		var val1=option.text();
		if(''!=val&&undefined!=val){
			data.type=val1;	
		}
		ajax.remoteCall("${ctxPath}/servlet/echarts?action=Chart_2",data,function(result) { 
				if(result.state == 1){
					var charData=result.data;
					Object.keys(charData).forEach(function(key){
						if('list'==key){
							option2.xAxis.data = charData[key];  	
						/* }else if('list_1'==key){
							option2.series[0].data = charData[key];  
						}else if('list_2'==key){
							option2.series[1].data = charData[key];   */
						}else {
							option2.series = charData[key];  
						}
					});
					//option2.series[0].data = result.data;   
					chart2.setOption(option2);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    function getEchar_3(val) {
		var data = form.getJSONObject("searchEchartsForm");
		data.type=val;
		ajax.remoteCall("${ctxPath}/servlet/echarts?action=Chart_3",data,function(result) { 
				if(result.state == 1){
					var charData=result.data;
					Object.keys(charData).forEach(function(key){
						if('list_1'==key){
							option3.yAxis.data = charData[key];  	
						}else if('list_2'==key){
							option3.series[0].data = charData[key];  
						}
					});
					if('1'==val){
						option3.title.text='舆情热频词汇排名(正面)';
					}else if('-1'==val){
						option3.title.text='舆情热频词汇排名(负面面)';
					}else{
						option3.title.text='舆情热频词汇排名';
					}
					//option2.series[0].data = result.data;   
					chart3.setOption(option3);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    function getEchar_4() {
    	var data = form.getJSONObject("searchEchartsForm");
		ajax.remoteCall("${ctxPath}/servlet/echarts2?action=Chart_2",data,function(result) { 
			if(result.state == 1){
				var charData=result.data;
				option4.series[0].data = charData;  	
				chart4.setOption(option4);
				}else{
				layer.alert(result.msg,{icon: 5});
			}
		}
	);
 }
    function getEchar_5() {
		var data = form.getJSONObject("searchEchartsForm");
		ajax.remoteCall("${ctxPath}/servlet/echarts?action=Chart_5",data,function(result) { 
				if(result.state == 1){
					var charData=result.data;
					Object.keys(charData).forEach(function(key){
						 if('list'==key){
							option5.xAxis.data = charData[key];  	
						}else if('list_1'==key){// 已暂存
							option5.series[0].data = charData[key];  
						}else if('list_2'==key){//已派工
							option5.series[1].data = charData[key];  
						}else if('list_3'==key){//处理中	
							option5.series[2].data = charData[key];  
						}else if('list_4'==key){//待回访
							option5.series[3].data = charData[key];  
						}else if('list_5'==key){//已归档
							option5.series[4].data = charData[key];  
						} 
					});
					//option4.series[0].data = result.data;   
					chart5.setOption(option5);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    function getEchar_6(val) {
		var data = form.getJSONObject("searchEchartsForm");
		data.type=val;
		ajax.remoteCall("${ctxPath}/servlet/echarts?action=Chart_6",data,function(result) { 
				if(result.state == 1){
					option6.series[0].data = result.data;   
					chart6.setOption(option6);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
 }
    var option1 = {
    	    backgroundColor: '#233c50',
    	    title: {
    	        text: '舆情类别监控(全品类)',
    	        left: 15,
    	        textStyle: {
    	            color: '#ccc',
    	            fontSize: 13
    	        }
    	    },

    	    tooltip : {
    	        trigger: 'item',
    	        formatter: "{a} <br/>{b} : {c} ({d}%)"
    	    },
	        legend: {
	           x : 'center',
	           y : 'bottom',
	           data:[
	                   {name:'正向', textStyle: {color: '#68AFE3'}},
	                   {name:'中性', textStyle: {color: '#9966ff'}},
	                   {name:'负向', textStyle: {color: '#ff66ff'}}
	                   
	                ]
	                
	        },
	       // color:['#68AFE3','#ff66ff','#66ff66'], 
    	    series : [
    	        {
    	            name:'处理状态比例',
    	            type:'pie',
    	            radius : '55%',
    	            center: ['50%', '50%'],
    	            itemStyle: {
    	            	normal:{color:function(value){
    						if(value.data.name=="正向")
    							return "#68AFE3";
    						else if(value.data.name=="中性")
    							return "#9966ff";
    						else
    							return "#ff66ff";
    						}
    	                }
    	            }
    	            
    	            //roseType: 'radius',
		    	/*     label: {
		                  normal: {
		                      show: true
		                  },
		                  emphasis: {
		                      show: true
		                  }
		            },
		            lableLine: {
		                  normal: {
		                      show: true
		                  },
		                  emphasis: {
		                      show: true
		                  }
		            } */
    	        }
    	    ]
     };
    
    var option2 = {
	    	    backgroundColor: '#233c50',
	    	    title: {
	    	        text: '舆情类别监控(事业部)',
	    	        left: 15,
	    	        textStyle: {
	    	            color: '#ccc',
	    	            fontSize: 13
	    	        }
	    	    },
	    	    tooltip : {
	    	        trigger: 'axis',
	    	        axisPointer : {            // 坐标轴指示器，坐标轴触发有效
	    	            type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
	    	        }
	    	    },
	    	    legend: {
	    	    	 top:20,
	                  itemWidth:23,
	                  itemHeight:9,
	    	       data:[
	                   {name:'正向', textStyle: {color: '#68AFE3'}},
	                   {name:'中性', textStyle: {color: '#9966ff'}},
	                   {name:'负向', textStyle: {color: '#ff66ff'}}
	                ]
	    	    },
	    	   color:['#68AFE3','#9966ff','#ff66ff'], 
	    	    grid: {
	    	        left: '3%',
	    	        right: '4%',
	    	        bottom: '3%',
	    	        containLabel: true
	    	    },
	    	    xAxis:  {				
	    	    	type: 'category',
	    	       // data: ['新浪微博','腾讯微博','美粉论坛','天涯社区','百度贴吧','其他'],
	    	        axisLine: {lineStyle:{color:'#ccc'}},
	    	    axisLabel :{  
	    	    	 interval: 0,
                     formatter:function(value)
                     {
                         var ret = "";//拼接加\n返回的类目项
                         var maxLength = 2;//每项显示文字个数
                         var valLength = value.length;//X轴类目项的文字个数
                         var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                         if (rowN > 1)//如果类目项的文字大于3,
                         {
                             for (var i = 0; i < rowN; i++) {
                                 var temp = "";//每次截取的字符串
                                 var start = i * maxLength;//开始截取的位置
                                 var end = start + maxLength;//结束截取的位置
                                 //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                                 temp = value.substring(start, end) + "\n";
                                 ret += temp; //凭借最终的字符串
                             }
                             return ret;
                         }
                         else {
                             return value;
                         }
                     }

		        }
	    	    },
	    	    yAxis: {
	    	    	type: 'value',
	    	    	axisLine: {lineStyle:{color:'#ccc'}}
	    	    },
	    	   /*  series: [
	    	        {
	    	            name: '正向',
	    	            type: 'bar',
	    	            stack: '总量',
	    	            label: {
	    	                normal: {
	    	                    show: true,
	    	                    position: 'insideRight'
	    	                }
	    	            },
	    	            //data: [320, 302, 301, 334, 390, 330]
	    	        },
	    	        {
	    	            name: '中性',
	    	            type: 'bar',
	    	            stack: '总量',
	    	            label: {
	    	                normal: {
	    	                    show: true,
	    	                    position: 'insideRight'
	    	                }
	    	            },
	    	           // data: [120, 132, 101, 134, 90, 230]
	    	        },
	    	        {
	    	            name: '负向',
	    	            type: 'bar',
	    	            stack: '总量',
	    	            label: {
	    	                normal: {
	    	                    show: true,
	    	                    position: 'insideRight'
	    	                }
	    	            },
	    	           // data: [220, 182, 191, 234, 290, 330]
	    	        }
	    	    ] */
	      };
    
    var option3 = {
	    	    		backgroundColor: '#233c50',
	    	    	    title: {
	    	    	        text: '舆情热频词汇排名',
	    	    	          textStyle: {
	    	    		    	            color: '#ccc',
	    	    		    	            fontSize: 13
	    	    		    	        }
	    	    	    },
	    	    	    tooltip: {
	    	    	        trigger: 'axis',
	    	    	        axisPointer: {
	    	    	            type: 'shadow'
	    	    	        }
	    	    	    },
	    	    	    color:['#68AFE3'], 
	    	    	    grid: {
	    	    	        left: '3%',
	    	    	        right: '4%',    
	    	    	        bottom: '3%',
	    	    	        containLabel: true
	    	    	    },
	    	    	    
	    	    	    xAxis: {
	    	    	        type: 'value',  
	    	                axisLabel: {  
	    	                      show: true,  
	    	                      interval: 'auto',  
	    	                      formatter: '{value} '  
	    	                    },  
	    	    	        axisLine: {lineStyle:{color:'#ccc'}}
	    	    	    },
	    	    	    yAxis: {
	    	    	        type: 'category',
	    	    	    axisLine: {lineStyle:{color:'#ccc'}},
	    	    	    axisLabel :{  
				            interval:0   
				        }
	    	    	    },
	    	    	    series: [
	    	    	        {
	    	    	            name: '关键字',
	    	    	            type: 'bar'
	    	    	        }
	    	    	     
	    	    	    ]
	    	    	};
    var option4 = {
    	 	backgroundColor: '#233c50',
            title: {
     	    	        text: '舆情处理状态监控(全品类)',
     	    	        left: 15,
     	    	        textStyle: {
     	    	            color: '#ccc',
     	    	            fontSize: 13
     	    	        }
     	    	    },
         tooltip : {
             trigger: 'axis',
             axisPointer : {            // 坐标轴指示器，坐标轴触发有效
                 type : 'shadow'        // 默认为直线，可选为：'line' | 'shadow'
             }
         },
         grid: {
             left: '3%',
             right: '4%',
             bottom: '3%',
             containLabel: true
         },
         label: {
             normal: {
                 show: true,
                 position: 'top',
                 textStyle: {
                   color:'#ccc',
                 }
             }
          },
         xAxis : [
             {
                 type : 'category',
     			 axisLine: {lineStyle:{color:'#ccc'}},
                 data : ['已暂存', '已派工', '处理中', '待回访', '已归档'],
                 axisTick: {
                     alignWithLabel: true
                 }
             }
         ],
         yAxis : [
             {
                 type : 'value',
     			axisLine: {lineStyle:{color:'#ccc'}}
             }
         ],
         color:['#62d1de'],
         series : [
             {
                 name:'数量',
                 type:'bar',
                 barWidth: '60%',
                 
                // data:[10, 52, 200, 334, 390]
             }
         ]
     };
    var option5 = {
    		backgroundColor: '#233c50',
    	    title: {
    	    	text: '事业部舆情监控图',
    	        left: 13,
    	        textStyle: {
    	            color: '#ccc',
    	            fontSize: 13
    	        }
    	    },
    	    tooltip: {
    	        trigger: 'axis'
    	    },
    	    legend: {
    	    	  top:20,
                  itemWidth:23,
                  itemHeight:9,
    	        data:[{name:'已暂存', textStyle:{color:'#68AFE3'}},
	                   {name:'已派工', textStyle:{color:'#9966ff'}},
	                   {name:'处理中', textStyle:{color:'#9932CC'}},
	                   {name:'待回访', textStyle:{color:'#40E0D0'}},
	                   {name:'已归档', textStyle:{color:'#66ff99'}}]
    	    },
    	    grid: {
    	        left: '3%',
    	        right: '4%',
    	        bottom: '3%',
    	        containLabel: true
    	    },
    	    
    	 

    	    xAxis: {
    	        type: 'category',
    	        boundaryGap: false,
    	       // data: ['事业部1','事业部2','事业部3','事业部4','事业部5','事业部6','事业部7'],
		        axisLine: {lineStyle:{color:'#ccc'}},
		        axisLabel :{  
		        	interval: 0,
                     formatter:function(value)
                     {
                         var ret = "";//拼接加\n返回的类目项
                         var maxLength = 2;//每项显示文字个数
                         var valLength = value.length;//X轴类目项的文字个数
                         var rowN = Math.ceil(valLength / maxLength); //类目项需要换行的行数
                         if (rowN > 1)//如果类目项的文字大于3,
                         {
                             for (var i = 0; i < rowN; i++) {
                                 var temp = "";//每次截取的字符串
                                 var start = i * maxLength;//开始截取的位置
                                 var end = start + maxLength;//结束截取的位置
                                 //这里也可以加一个是否是最后一行的判断，但是不加也没有影响，那就不加吧
                                 temp = value.substring(start, end) + "\n";
                                 ret += temp; //凭借最终的字符串
                             }
                             return ret;
                         }
                         else {
                             return value;
                         }
                     }
		        }
    	    },
    	    yAxis: {
    	    	axisLine: {lineStyle:{color:'#ccc'}},
    	        type: 'value'
    	    },
    	    series: [
    	        {
    	            name:'已暂存',
    	            type:'line',
    	            stack: '总量',
    	            //data:[120, 132, 101, 134, 90, 230, 210]
    	        },
    	        {
    	            name:'已派工',
    	            type:'line',
    	            stack: '总量',
    	           // data:[220, 182, 191, 234, 290, 330, 310]
    	        },
    	        {
    	            name:'处理中',
    	            type:'line',
    	            stack: '总量',
    	           // data:[150, 232, 201, 154, 190, 330, 410]
    	        },
    	        {
    	            name:'待回访',
    	            type:'line',
    	            stack: '总量',
    	           // data:[320, 332, 301, 334, 390, 330, 320]
    	        },
    	        {
    	            name:'已归档',
    	            type:'line',
    	            stack: '总量',
    	            //data:[820, 932, 901, 934, 1290, 1330, 1320]
    	        }
    	    ]
    	};
    var option6 = {  
		     backgroundColor: '#233c50',
 
               tooltip: {  
                   show: true  
               },  
               series: [{  
                   name: '舆情关键字词云',  
                   type: 'wordCloud',  
                   sizeRange: [6, 66],  
                   rotationRange: [-45, 90],  
                   textPadding: 0,  
                   autoSize: {  
                       enable: true,  
                       minSize: 6  
                   },  
                   textStyle: {  
                       normal: {  
                           color: function() {  
                               return 'rgb(' + [  
                                   Math.round(Math.random() * 160),  
                                   Math.round(Math.random() * 160),  
                                   Math.round(Math.random() * 160)  
                               ].join(',') + ')';  
                           }  
                       },  
                       emphasis: {  
                           shadowBlur: 10,  
                           shadowColor: '#333'  
                       }  
                   },  
                   data: [{  
                       name: "Jayfee",  
                       value: 666  
                   }, {  
                       name: "Nancy",  
                       value: 520  
                   }]  
               }]  
           };
    function selectEchart(){
    	chart1.clear();
 		chart2.clear();
 		chart3.clear();
 		chart4.clear();
 		chart5.clear();
 		chart6.clear();
    	getEchar_1();	  
		getEchar_2();
		getEchar_3();
		getEchar_4();
		getEchar_5();
		getEchar_6();
    }
    function upKeyWord(val){
 		//chart3.clear();
		getEchar_3(val);

    }
    function upSentitimeByCh(val){
    	//chart2.clear();
		getEchar_2(val);
    }
    function upKeyWordCloud(val){
    	//chart6.clear();
		getEchar_6(val);
    }
    function get3MonthBefor(val,val1){
        var resultDate,year,month,date,hms;
        var currDate = new Date();
        year = currDate.getFullYear();
        month = currDate.getMonth()+1;
        //date = currDate.getDate();
        hms = currDate.getHours() + ':' + currDate.getMinutes() + ':' + (currDate.getSeconds() < 10 ? '0'+currDate.getSeconds() : currDate.getSeconds());
        switch(month)
        {
          case 1:
          case 2:
          case 3:
            month += 9;
            year--;
            break;
          default:
            month -= val;
            break;
        }
        month = (month < 10) ? ('0' + month) : month;
        if('0'==val1){
        	date='01';
        }else{
        	date=	getLastDay(year,month);
        }        
        resultDate = year + '-'+month+'-'+date+' ' + " 00:00:00";
      return resultDate;
    }
    function getLastDay(year,month)   
    {   
    	  var dt = new Date(year,month,1);  
    	    cdt = new Date(dt.getTime()-1000*60*60*24);  
    	   return cdt.getDate();
    } 
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>