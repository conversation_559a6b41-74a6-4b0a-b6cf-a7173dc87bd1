<%@ page language="java" contentType="text/html;charset=UTF-8" %>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>舆情研判</title>
    <style>
        #menuContent {
            display: none;
            position: absolute;
            border: 1px solid rgb(170, 170, 170);
            max-width: 220px;
            max-height: 350px;
            z-index: 10;
            overflow: auto;
            background-color: #f4f4f4
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form id="editForm">
            <%--表单展示--%>
        <table class="table table-edit table-vzebra mt-10">
            <tbody>
            <tr>
                <td width="40px" class="required">是否处理</td>
                <td>
                    <select class="form-control input-sm" name="RESULT" id="RESULT" data-rules="required"
                            onchange="notNeedDeal(this)">
                        <option value="" selected="selected">请选择</option>
                        <option value="0">无需处理</option>
                        <option value="1">需要处理</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td width="40px">舆情类型</td>
                <td>
                    <select class="form-control input-sm" id="TYPE" name="TYPE">
                        <option value="" selected="selected">请选择</option>
                        <option value="1">正向舆情</option>
                        <option value="0">中性舆情</option>
                        <option value="-1">负向舆情</option>
                    </select>
                </td>
            </tr>
            </tbody>
        </table>
            <%--操作--%>
        <div class="layer-foot text-c">
            <button class="btn btn-sm btn-primary" type="button" onclick="edit.doCheck('${param.ID}')">确认</button>
            <button class="btn btn-sm btn-default ml-20" type="button" onclick="popup.layerClose()">取消</button>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <link type="text/css" rel="stylesheet" href="/easitline-static/lib/ztree/css/zTreeStyle/zTreeStyle.css"/>
    <script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
    <script type="text/javascript" src="/easitline-static/lib/ztree/js/jquery.ztree.all.min.js"></script>
    <script type="text/javascript">
        jQuery.namespace("edit")
        $(function () {
            $("#editForm").custRender();//加载下拉框
            $("#editForm").render({});
        });

        //提交
        edit.doCheck = function (param) {
            var data = {};
            //获取传递参数
            var ids = param.split(',').map(item => item.includes(',') ? item.substring(0, item.indexOf(',')) : item);
            console.log("ids:", ids);
            //根据下拉框状态修改参数
            var result = $("#RESULT").val();
            var type = $("#TYPE").val();
            //判断舆情类型 (逐一判断 防止被恶意调用)
            if (type !== "") { //修改舆情类型
                updateIds(ids, type)
            }
            //判断是否处理
            if (result === "0") {
                data.ids = ids;
            } else if (result === "1") {
                data.id = ids;
                //需要处理负向舆情
                for (var i = 0; i < ids.length; i++) {
                    if(ids[i].split(";")[1]=="-1"){bury_management_confirm()}
                }
            } else {
                window.parent.layer.closeAll();
                window.parent.layer.msg("请确定是否处理", {icon: 2});
                return;
            }
            console.log("data:", data)
            //调用接口发送请求
            ajax.remoteCall("${ctxPath}/servlet/discriminant?query=upCheck", data, function (result) {
                    //debugger;
                    if (result.state == 1) {
                        setTimeout(function () {
                            window.parent.layer.closeAll();
                            window.parent.layer.msg(result.msg, {icon: 1});
                            window.parent.list.searchData(15); //保证页面停留在当前页面
                            window.parent.list.checkAll('#allButton');
                        }, 1000);
                    } else {
                        layer.alert(result.msg, {icon: 5});
                    }
                }
            );
        }

        //统一修改舆情按类型
        function updateIds(ids, type) {
            for (var i = 0; i < ids.length; i++) {
                ids[i] = ids[i].split(";")[0] + ";" + type;
            }
        }

        //弹屏提示
        function notNeedDeal(obj) {
            var check = $("#RESULT").val();
            var type = $("#TYPE").val();
            console.log("check:", check);
            console.log("type:", type);
            //为请选择、正向舆情或者负向舆情才进行弹窗提示
            //由于坐席常用的操作方式是：先选择好舆情的类型 在全选进行操作(此时不在选择舆情类型 而是直接选择是否处理) 需要外部选择的舆情类型才可以进一步判断
            var parentType = window.parent.$("#SENTIMENT_TYPE").val(); //获取父级筛选的舆情类型
            if (parentType === '4' || parentType === ''){//父级舆情类型为全部时,进一步根据当前舆情类型进行判断
                if (type === '1' || type === '' || type === '-1' ){ //当前页面舆情类型为正向舆情、负向舆情以及未选择
                    checkTip(check);
                }
            }
            else if (parentType === '1' || parentType === '-1'){//父级舆情类型为正向舆情、负向舆情
                checkTip(check);
            }
        }
        function bury_management_confirm(){
            if (top.collectEvent) {
                top.collectEvent("click_public_opinion_management_confirm", {
                    status:1,
                    inputTokens:0,
                    outputTokens:0,
                    opType:-1,
                    opTotalTime:0,
                    opTimeEfficiencyValue:"350s",
                    opEfficiencyValue:1,
                });
            }
        }
        //抽取判断方法 选择无需处理时进行弹窗提示
        function checkTip(check){
            if (check === '0') {
                layer.confirm('确定该条舆情无需处理？', {btn: ['继续', '取消']}, function (index) {
                    layer.msg('已选择！', {icon: 1});
                    return true;
                }, function (index) {
                    $("#RESULT").val("");
                    layer.msg('已取消！', {icon: 1});
                    return false;
                });
            }
        }
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>