<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>二次研判</title>
	<style>
	     select.input-xs{height:24px;line-height:24px;padding:2px 6px}

	    .ibox-title{margin-bottom:10px}
	    .list-block .row{margin:0}
	    .list-block .bg-white{background:#fff;}
	    .list-block .pd-0{padding:0px}
	    .list-block .list-item{border-bottom:1px dashed #ccc; padding: 1px;}
	    .list-block .list-item h5{margin-bottom:15px}
	    .list-block .list-item h5 span.title{font-weight:bold;}
	    .list-block .list-item h5 label{float:right}
	    .list-block .list-item h5 label span{color:#666}
	    .list-block .list-item p a{color: #76838f;}
	    .list-block .list-item p a:hover{text-decoration: none;}
	    .list-block .list-item .highlight{color:#F00}
	    .list-block .list-item .datetime{font-size:12px;margin-top:10px}
	    .list-block .list-highlight {background-color: #e3e8e7;}
	    #filter h5{margin-top:15px;margin-bottom:4px}
		.label-list,.new-label-list,.new-label-list1{padding:0}
	    .label-list li{float:left;list-style:none}
	    .label-list li a{font-size: 12px;color:#76838f;background-color: #fff;padding: 5px 12px; border-radius: 2px; border: 1px solid #e7eaec;margin-right: 5px;margin-top: 5px;display: block;}
	    .label-list li a.checked{background:#3cadfe;color:#fff}
	    .label-list li a.onfocus{background-color: #ffa084;}
		.new-label-list.showMore{max-height:none}
		.new-label-list li{list-style:none;place-content: center;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
		.new-label-list{display: grid;grid-template-columns: repeat(3, 1fr);grid-gap: 5px;max-height: 330px;overflow-y: hidden;}
		.new-label-list li a{font-size: 12px;color:#76838f;background-color: #fff;padding: 5px 12px; border-radius: 2px; border: 1px solid #e7eaec;display: block;}
		.new-label-list li a.checked{background:#3cadfe;color:#fff}
		.new-label-list li a.onfocus{background-color: #ffa084;color: #fff;border-color: #ef9e82;}
		.more{display:none;font-size:12px}
		.new-label-list1 li{list-style:none;place-content: center;overflow: hidden;text-overflow: ellipsis;white-space: nowrap;}
		.new-label-list1{display: grid;grid-template-columns: repeat(3, 1fr);grid-gap: 5px;max-height: 330px;overflow-y: hidden;}
		.new-label-list1 li a{font-size: 12px;color:#76838f;background-color: #fff;padding: 5px 12px; border-radius: 2px; border: 1px solid #e7eaec;display: block;}
		.new-label-list1 li a.checked{background:#3cadfe;color:#fff}
		.new-label-list1 li a.onfocus{background-color: #ffa084;color: #fff;border-color: #ef9e82;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" onsubmit="return false" data-toggle="">
              <input type="hidden" data-mars="dataInfo.sentimentChannelList">
			<input type="hidden" data-mars="dataInfo.sentimentChannelDelete">
             <input type="hidden" data-mars="dataInfo.sentimentStatus">
             <input type="hidden" data-mars="dataInfo.sentimentType">
             <input type="hidden" data-mars="dataInfo.sentimentDept">
              <input type="hidden" data-mars="dataInfo.sentimentTime">
             <input type="hidden" id="CHANNEL" name="CHANNEL">
             <input type="hidden" id="SENTIMENT_TYPE" name="SENTIMENT_TYPE">
             <input type="hidden" id="Status" name="Status">
               <input type="hidden" id="SYNC_TIME" name="SYNC_TIME">
             <input type="hidden" id="DEPT" name="DEPT">
              <input type="hidden" id="changePage" name="changePage">
               <div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
							   <h5><span class="glyphicon glyphicon-list"></span> 舆情研判</h5>
						       <div class="input-group input-group-sm">
									  <input type="text" name="CREATER" class="form-control input-sm" style="width:300px">
							   </div>
							   <div class="input-group input-group-sm">
									  <button type="button" id="listSearchData" class="btn btn-sm btn-default" onclick="list.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
							   <div class="input-group input-group-sm ml-10" style="margin-left:200px">
									  <button type="button" class="btn btn-sm btn-success" onclick="doCheck()">确定</button>
							   </div>
						 </div>
				    </div>
	                <div class="list-block">
		           	     <div class="row">
		           	           <div class="col-md-9 pd-0">
		           	              <div class="bg-white mr-10 pd-15" >
		           	                	<div data-container="#labelTableHead"
					data-template="labelList-template" id="labelTableHead"
					data-mars="discriminant.reDiscriminant" data-mars-search-list="true"></div>
		           	       <script id="labelList-template" type="text/x-jsrender">
								   {{for  list}}
									{{if SENTIMENT_TYPE==-1}}
										 <div class="list-item list-highlight"  id={{:ID}}>
										{{else }}
										 <div class="list-item" id={{:ID}}>

										{{/if}}
										     <h5>
		           	                         <span class="text-danger mr-10" style="color:gray">{{:SENTIMENT_FROM}}</span>
		           	                         <span class="title">{{call:CREATER CREATER_data #index+1 'name' fn='getData2'}}</span>
										     <span onclick="showDetail('{{:#index+1}}','{{:CREATER_data}}')"
												class="glyphicon glyphicon-eye-open" id="show{{:#index+1}}" style="color: rgb(255, 140, 60);"></span>
		           	                          <span class="text-danger mr-10" >
												{{if SENTIMENT_TYPE==1}}
													<select  class="form-control input-sm"  id="orderType{{:ID}}" onChange="changeStyle(this.value+';'+{{:ID}})" >
			                                 	<option value="1" selected="selected">正向舆情</option>
			                                 	<option value="0" >中性舆情</option>
			                                 	<option value="-1" >负向舆情</option>
			                         		   </select>
												{{else SENTIMENT_TYPE==0}}
													<select  class="form-control input-sm"  id="orderType{{:ID}}"  onChange="changeStyle(this.value+';'+{{:ID}})" >
			                                 	<option value="1" >正向舆情</option>
			                                 	<option value="0" selected="selected">中性舆情</option>
			                                 	<option value="-1" >负向舆情</option>
			                         		   </select>
												{{else SENTIMENT_TYPE==-1}}
												<select  class="form-control input-sm"  id="orderType{{:ID}}"  onChange="changeStyle(this.value+';'+{{:ID}})" >
			                                 	<option value="1" >正向舆情</option>
			                                 	<option value="0" >中性舆情</option>
			                                 	<option value="-1" selected="selected">负向舆情</option>
			                         		   </select>
												{{/if}}
												</span>

										     <label class="checkbox checkbox-info checkbox-inline">
										   		<input type="checkbox" name="needDeal5" id="needDeal5{{:ID}}"  value="{{:ID}}" onchange="notNeedDeal(this,'{{:ID}}','{{:SENTIMENT_TYPE}}')"> <span>无需处理</span>
										     </label>
										     <label class="checkbox checkbox-info checkbox-inline ml-20">
										   		<input type="checkbox" name="needDeal1" id="needDeal1{{:ID}}"  value="{{:ID}}" onchange="needDeal(this,'{{:ID}}','{{:SENTIMENT_TYPE}}')"> <span>需要处理</span>
										     </label>
										 </h5>
		           	                     <p class="mb-5" ><a href="{{:URL}}"  target="_blank"><span class="highlight" style="color:black">{{:TITLE}}</span>：<span  style="color:black">{{:CONTENT}}</span></a></p>
		           	                     <p class="datetime">发帖时间：<span class="mr-20">{{:CREATE_TIME}}</span>获取时间：<span>{{:CRAWL_TIME}}</span>同步时间：<span>{{:SYNC_TIME}}</span></p>
											{{if SENTIMENT_FROM=='天涯社区'}}
											<p class="datetime">
		           	                           	     点击量：<span class="mr-20">{{:HITS_COUNT}}</span>
		           	                                                                                              回复量：<span class="mr-20">{{:REPLY_COUNT}}</span>
		           	                    	 </p>
									{{else SENTIMENT_FROM=='百度贴吧'}}
												<p class="datetime">
      									      	 评论数量：<span class="mr-20">{{:REPLY_COUNT}}</span>
		           	                    	 </p>
										{{else }}
											<p class="datetime">
      										   粉丝数：<span class="mr-20">{{:FANS_COUNT}}</span>
		           	                                                                            注册地：<span class="mr-20">{{:ACCOUNT_AREA}}</span>
		           	                                                                            评论数量：<span class="mr-20">{{:REPLY_COUNT}}</span>
		           	                                                                           是否转载 ：<span class="mr-20">{{dictFUN:IS_REPRINTED "SF_YN"}}</span>
		           	                                                                             转载量 ：<span class="mr-20">{{:FORWARD_COUNT}}</span>
		           	                                                                            阅读量 ：<span>{{:LIKE_COUNT}}</span>
		           	                    	 </p>
										{{/if}}
 										</div>
								    {{/for}}
			       </script>
		           	              </div>
		       			  <div class="row paginate">
	                     	<jsp:include page="/pages/common/pagination.jsp"/>
	                     </div>
		           	         </div>
		           	         <div class="col-md-3 pd-0">
		           	              <div class="bg-white pd-15" id="filter">

										<div style="margin-bottom:5px;display: flex;justify-content: space-between; align-items: center;">
											<h5>来源</h5>
											<div onclick="showMore()" class="more">更多</div>
										</div>
										<ul  class="new-label-list1">
											<li><a  id="all" onclick="selectList(0)">全部</a></li>
										</ul>
										<ul id="dataList1"  class="new-label-list">
										</ul>

		           	                   <h5>舆情类型</h5>
		           	                  <ul id="dataList2"  class="label-list clearfix">
										</ul>
		           	                  <!--  <h5>首次研判结果</h5>
		           	                  <ul id="dataList3"  class="label-list clearfix">
										</ul> -->
		           	                   <h5>研判日期</h5>
		           	                   <div class="clearfix">
		           	                       <div class="input-group  mt-10">
				             		             <span class="input-group-addon">开始日期</span>
											     <input type="text" name="beginTime" id="beginTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:160px">
										   </div>
										  <div class="input-group  mt-10">
				             		             <span class="input-group-addon">结束日期</span>
		                                         <input type="text" name="endTime" id="endTime" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss', onpicked:selectData})" style="width:160px">
		           	                       </div>
		           	                   </div>
		           	                   <h5>发帖日期</h5>
		           	                   <div class="clearfix">
		           	                       <div class="input-group  mt-10">
				             		             <span class="input-group-addon">开始日期</span>
											     <input type="text" name="beginTime1" id="beginTime1" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({maxDate:'#F{$dp.$D(\'endTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss'})" style="width:160px">
										   </div>
										  <div class="input-group  mt-10">
				             		             <span class="input-group-addon">结束日期</span>
		                                         <input type="text" name="endTime1" id="endTime1" data-mars-top="" data-mars="" class="form-control f-12" onclick="WdatePicker({minDate:'#F{$dp.$D(\'beginTime\')}',dateFmt:'yyyy-MM-dd HH:mm:ss', onpicked:selectData1})" style="width:160px">
		           	                       </div>
		           	                   </div>
		           	                   	<h5>获取时间</h5>
		           	                   <ul id="dataList5"  class="label-list clearfix">
										</ul>
		           	                   <h5>事业部</h5>
		           	                  <ul id="dataList4"  class="label-list clearfix">
										</ul>
		           	              </div>
		           	         </div>
		           	     </div>
	                </div>
	            </div>
        </form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>

	<script type="text/javascript">
	jQuery.namespace("list");
	var val=true;
	$(function(){
		  $(document).keydown(function(event){
			  if(event.keyCode == 13){ //绑定回车
			  $('#listSearchData').click();
			 	 }
			  });
		$("#searchForm").render({
			success : function(result) {
				if(val){
				var dataInfo= result["dataInfo.sentimentChannelList"]['data'];
				var dataInfo_1 = result["dataInfo.sentimentType"]['data'];
				var dataInfo_2= result["dataInfo.sentimentStatus"]['data'];
				var dataInfo_3= result["dataInfo.sentimentDept"]['data'];
				var dataInfo_4= result["dataInfo.sentimentTime"]['data'];
				//获取被删掉的来源
				var dataInfo5= result["dataInfo.sentimentChannelDelete"]['data'];
				uiFill(dataInfo,dataInfo5);
				uiFill_1(dataInfo_1);
				uiFill_2(dataInfo_2);
				uiFill_3(dataInfo_3);
				uiFill_4(dataInfo_4);
				val=false;
				}
			}
		});
	})
		list.searchData=function(count){
		if(count>0){
			var option= $("select[name='pageSize'] option:selected");
			var pageSize=option.val();
			var num= $("input[name='pageIndex']").val();
			$("#changePage").val(num);
			 $("#searchForm").searchData({success:function(result){
				 var totalRowV=result["discriminant.reDiscriminant"]["count"];
				var total=Math.ceil(totalRowV/pageSize);
					 $(".totalPageV").val(total);
					 $(".totalRowV").val(totalRowV);
					 $(".totalRow").html(result["discriminant.reDiscriminant"]["count"])
					$("#changePage").val('');

					}});


		}else{
			$("#changePage").val('');
			$("#searchForm").searchData();
		}
	};
	 function needDeal(obj,ago,ago1){
	    	var ifCheck = $(obj).prop("checked");
	    	var other = $(obj).parent().next().find("input");
	    	var log = $("#orderType"+ago+" option:selected").val();

	    	if(ifCheck){
				if(log=='0'){
					layer.confirm('确定该条舆情需要处理？',{btn: ['继续','取消']},function(index){
				    	other.prop("checked","");
			    		layer.msg('已选择！', {icon: 1});
			    		document.getElementById('needDeal5'+ago).checked =false;
			    		return true;
			    	},function(index){
						$(obj).removeAttr("checked");
		                layer.msg('已取消！', {icon: 1});
		                return false;
		            });
	    		}else{
			    	other.prop("checked","");
		    		document.getElementById('needDeal5'+ago).checked =false;
	    		}
    	}
 }
	    function notNeedDeal(obj,ago){
	    	var index = parent.layer.getFrameIndex(window.name);
	    	var ifCheck = $(obj).prop("checked");
	    	var other = $(obj).parent().prev().find("input");
	    	var log = $("#orderType"+ago+" option:selected").val();

	    	if(ifCheck){
				if(log=='1'||log=='-1'){
	    		layer.confirm('确定该条舆情无需处理？',{btn: ['继续','取消']},function(index){
			    	other.prop("checked","");
		    		layer.msg('已选择！', {icon: 1});
		    		document.getElementById('needDeal1'+ago).checked =false;
		    		return true;
		    	},function(index){
					$(obj).removeAttr("checked");
	                layer.msg('已取消！', {icon: 1});
	                return false;
	            });
	    	}else{
		    	other.prop("checked","");
	    		document.getElementById('needDeal1'+ago).checked =false;
	    		}
	    	}
	    }
    function selectList(ago){
		if(ago === 0) {
			$('#dataList1').find('.onfocus').removeClass('onfocus');
			$('#dataList2').find('.onfocus').removeClass('onfocus');
			$('#dataList3').find('.onfocus').removeClass('onfocus');
			$('#dataList4').find('.onfocus').removeClass('onfocus');
			$('#all').addClass('onfocus');
			$("#CHANNEL").val('');
		}else {
			$('#all').removeClass('onfocus');
		}
		$("#CHANNEL").val(ago);
		list.searchData();
    }
    function selectList1(ago){
		$("#SENTIMENT_TYPE").val(ago);
		list.searchData();
    }
    function selectList2(ago){
		$("#Status").val(ago);
		list.searchData();
    }
	function showMore(){
		$(".new-label-list").toggleClass('showMore')
	}
    function selectList3(ago){
		$("#DEPT").val(ago);
		list.searchData();
    }
    function selectList4(ago){
		$("#SYNC_TIME").val(ago);
		list.searchData();
    }
    function uiFill(data,delData){
    	var liStr = "";
		var targetList=[]
    	for(var i of data){
		if(delData[i.value]){
			continue;
		}
		targetList.push(i.text)
    		var ago=data[i.text];
			 liStr+=("<li><a href=\"javascript:selectList("+i.value+++");\">"+i.text+"</a></li>");
    	}
		if(Object.keys(targetList).length>30){
			$('.more').show()
		}
		$("#dataList1").html(liStr);
		$('#dataList1').find('a').unbind('click').bind({
			click:function(e){
				$('#dataList1').find('.onfocus').removeClass('onfocus');
				$(this).toggleClass('onfocus');
				//return false;
			}
		})
    }
    function uiFill_1(data){
    	var liStr = "<li><a href=\"javascript:selectList1('4');\">全部</a></li>";
    	for(var i in data){
    		var ago=data[i];
			 liStr+=("<li><a href=\"javascript:selectList1("+i+");\">"+ago+"</a></li>");
    	}
		$("#dataList2").html(liStr);
		$('#dataList2').find('a').unbind('click').bind({
			click:function(e){
				$('#dataList2').find('.onfocus').removeClass('onfocus');
				$(this).toggleClass('onfocus');
				//return false;
			}
		})
    }
    function uiFill_2(data){
    	var liStr = "<li><a href=\"javascript:selectList2('0');\">全部</a></li>";
    	for(var i in data){
    		var ago=data[i];

			 liStr+=("<li><a href=\"javascript:selectList2("+i+++");\">"+ago+"</a></li>");
    	}
		$("#dataList3").html(liStr);
		$('#dataList3').find('a').unbind('click').bind({
			click:function(e){
				$('#dataList3').find('.onfocus').removeClass('onfocus');
				$(this).toggleClass('onfocus');
				//return false;
			}
		})
    }
    function uiFill_3(data){
    	var liStr = "<li><a href=\"javascript:selectList3('0');\">全部</a></li>";
    	for(var i in data){
    		var ago=data[i];
			 liStr+=("<li><a href=\"javascript:selectList3("+i+++");\">"+ago+"</a></li>");
    	}
		$("#dataList4").html(liStr);
		$('#dataList4').find('a').unbind('click').bind({
			click:function(e){
				$('#dataList4').find('.onfocus').removeClass('onfocus');
				$(this).toggleClass('onfocus');
				//return false;
			}
		})
    }
    function uiFill_4(data){
    	var liStr = "<li><a href=\"javascript:selectList4('0');\">全部</a></li>";
    	for(var i in data){
    		var ago=data[i];

			 liStr+=("<li><a href=\"javascript:selectList4("+i+");\">"+ago+"</a></li>");
    	}
		$("#dataList5").html(liStr);
		$('#dataList5').find('a').unbind('click').bind({
			click:function(e){
				$('#dataList5').find('.onfocus').removeClass('onfocus');
				$(this).toggleClass('onfocus');
				//return false;
			}
		})
    }
     function doCheck(){
    	var count=0;
    	var data={};
    	var arr=new Array();//需要处理
    	var arrL=new Array();//无需处理
    	 $.each($('input:checkbox[name="needDeal1"]:checked'),function(){
    	 	var val=$("#orderType"+$(this).val()).val();
    		 arr.push($(this).val()+";"+val);
	    	 	count++;

    		 });
    	 $.each($('input:checkbox[name="needDeal5"]:checked'),function(){
    			var val=$("#orderType"+$(this).val()).val();
    			arrL.push($(this).val()+";"+val);
	    	 	count++;

            });
    	 if(arr.length<1&&arrL.length<1){
				layer.alert('请选择需要处理行！',{icon : 2});
				return false;
		 }
    /* 		var reData=splitData(arr);
			if(''!=reData&&null!=reData&&reData.length>0){
		       orderInfo(reData);
			}
		data.CONTENT=countAjax; */
 		data.id=arr;
		data.ids=arrL;
		ajax.remoteCall("${ctxPath}/servlet/discriminant?query=upCheck2",data,function(result) {
			//debugger;
				if(result.state == 1){
					layer.alert(result.msg,{icon: 1,time: 1000},function(){
					});
					setTimeout(function(){
						list.searchData(count);
						layer.closeAll();

					},1000);
					}else{
					layer.alert(result.msg,{icon: 5});
				}
			}
		);
    }
    var countAjax='';
	  function orderInfo(val){
			$.ajax({
				url:"${ctxPath}/servlet/order?query=GetOrderPos1",
				data:{"id":val},
				async:false,
				success:function(result) {
					if(result.state == 1){
						var ret = result.data;
						for(var i=0;i<ret.length;i++){
							var ob=ret[i];
							var content=ob.CONTENT.replace(/(^\s*)/g,"");
							countAjax+="网络名: "+ob.CREATER+"\n";
							countAjax+="来源: "+ob.SENTIMENT_FROM+"\n";
							countAjax+="发布时间: "+ob.CREATE_TIME+"\n";
							countAjax+="原文链接: "+ob.URL+"\n";
							countAjax+="原文内容:"+content+"\n\r"+"--";
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
		});
	 	}
	 function splitData(val){
	    var arr=new Array();//正向需要处理ID
		 for(var i=0;i<val.length;i++){
			 var data=val[i].split(";");
			 if('1'==data[1]){
				 arr.push(data[0]);
			 }
		 }
	    return arr.toString();
	 }
	 function changeStyle(val){
		 var ago=val.split(";")[0];
		 var ago1=val.split(";")[1];
		if('-1'==ago){
			$("#"+ago1).addClass("list-highlight"); //添加样式
		}else{

			$("#"+ago1).removeClass("list-highlight"); //添加样式

		}
	 }
	 function selectData(){
			list.searchData();

	 }
	 function selectData1(){
			list.searchData();

	 }
	//小眼睛功能
	function showDetail(id,name){
		showDetailCommon({
			"model":"sentiment",
			"url":"/sentiment/pages/check/recheck-list.jsp",
			"action":"acces",
			"describe":"舆情[二次研判]数据，看客户[{{name}}]敏感信息"},id,name);
	}
	</script>
	<script type="text/javascript" src="/iccportal5/static/js/privacyUtil.js"></script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>