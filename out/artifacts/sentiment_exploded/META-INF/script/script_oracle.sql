/*==============================================================*/
/* Table: CC_ENT                                                */
/*==============================================================*/
create table CC_ENT
(
   ENT_ID               VARCHAR2(32) not null,
   ENT_CODE             VARCHAR2(20) not null,
   ENT_NAME             VARCHAR2(100) not null,
   OPEN_TIME            VARCHAR2(10) not null,
   ENT_STATE            INTEGER,
   LINKMAN              VARCHAR2(50),
   LINKPHONE            VARCHAR2(50),
   EMAIL                VARCHAR2(100),
   INDUSTRY             VARCHAR2(20),
   ENT_SCALE            INTEGER,
   AREA_CODE            VARCHAR2(4),
   ADDR                 VARCHAR2(255),
   CORPORATE            VARCHAR2(50),
   IDCARD_URL           VARCHAR2(500),
   BUSI_LICENSE_URL     VARCHAR2(500),
   MEMO                 VARCHAR2(500),
   CREATOR              VARCHAR2(50),
   CREATE_TIME          VARCHAR2(19),
   primary key (ENT_ID)
);
/*==============================================================*/
/* 追加舆情判断                                         */
/*==============================================================*/

CREATE OR REPLACE 
function SENTIMENT_ORDERADD(
ago in varchar2
) return VARCHAR2
is
ago1 VARCHAR2(100);
begin
select count(NICKNAME) into ago1 from C_ST_SENTIMENT_ORDER where NICKNAME=ago;
return(ago1);
end;
/*==============================================================*/
/* 部门与舆情信息关联视图                         */
/*==============================================================*/

create or replace view VIEW_ORDER_DIVISION AS
select a.STATUS,b.name from C_ST_SENTIMENT_INFO a LEFT JOIN C_ST_SENTIMENT_DIVISION b ON
 a.id=b.SENTIMENT_ID 
