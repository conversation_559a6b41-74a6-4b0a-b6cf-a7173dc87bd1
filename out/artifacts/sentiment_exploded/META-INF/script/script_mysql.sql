create table CC_ENT
(
   ENT_ID               varchar(32) not null comment '企业ID',
   ENT_CODE             varchar(20) not null comment '企业代码',
   ENT_NAME             varchar(100) not null comment '企业名称',
   OPEN_TIME            varchar(10) not null comment '开户时间',
   ENT_STATE            int comment '企业状态，０：正常　１：暂停　　９：销户',
   LINKMAN              varchar(50) comment '企业联系人',
   LINKPHONE            varchar(50) comment '联系电话',
   EMAIL                varchar(100) comment '邮箱',
   INDUSTRY             varchar(20) comment '所属行业',
   ENT_SCALE            int comment '企业规模，源于数字字典',
   AREA_CODE            varchar(4) comment '所属地市',
   ADDR                 varchar(255) comment '企业地址',
   CORPORATE            varchar(50) comment '法人姓名',
   IDCARD_URL           varchar(500) comment '法人身份证',
   BUSI_LICENSE_URL     varchar(500) comment '营业执照',
   MEMO                 varchar(500) comment '备注',
   CREATOR              varchar(50) comment '创建人',
   CREATE_TIME          varchar(19) comment '创建时间',
   primary key (ENT_ID)
);

alter table CC_ENT comment '企业信息表';