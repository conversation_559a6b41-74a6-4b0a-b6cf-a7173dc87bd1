<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
   <resource id="sentiment" name="舆情管理" url="" type="2" portal="" state="0" order="4" index = "75">
		<resource id="public_opinion_check" name="舆情研判"   type="2" portal="" icon="" state="0" index="1">
				<resource id="public_opinion_check_1" name="舆情研判"   url="/sentiment/pages/check/check-list.jsp" type="2" portal="" icon="" state="0" index="1"/>		
		</resource>
		<resource id="public_opinion_recheck" name="二次研判"    type="2" portal="" icon="" state="0" index="2">
				<resource id="public_opinion_recheck_1" name="二次研判"   url="/sentiment/pages/check/recheck-list.jsp" type="2" portal="" icon="" state="0" index="2"/>		
		</resource>
		<resource id="public_opinion_deal" name="舆情处理"   type="2" portal="" icon="" state="0" index="3">
				<resource id="public_opinion_deal_1" name="舆情处理"   url="/sentiment/pages/deal/deal-list.jsp" type="2" portal="" icon="" state="0" index="3"/>		
		</resource>
		<resource id="ordertype" name="工单类型"  type="2" portal="" icon="" state="0" index="4">	
		    	<resource id="ordertype_list1" name="正向舆情"   url="/sentiment/pages/order/ordertype-list1.jsp" type="2" portal="" icon="" state="0" index="1"/>
		    	<resource id="ordertype_list2" name="负向舆情"   url="/sentiment/pages/order/ordertype-list2.jsp" type="2" portal="" icon="" state="0" index="2"/>
		    	<resource id="ordertype_list3" name="中性舆情"   url="/sentiment/pages/order/ordertype-list3.jsp" type="2" portal="" icon="" state="0" index="3"/>
	    </resource>
	    <resource id="orderdeal" name="工单处理"  type="2" portal="" icon="" state="0" index="5">	
		    	<resource id="orderdeal_list1" name="待办工单"   url="/sentiment/pages/order/orderdeal-list1.jsp" type="2" portal="" icon="" state="0" index="1"/>
		    	<resource id="orderdeal_list2" name="已归档"   url="/sentiment/pages/order/orderdeal-list2.jsp" type="2" portal="" icon="" state="0" index="2"/>
		    	<resource id="orderdeal_list3" name="全部"   url="/sentiment/pages/order/orderdeal-list3.jsp" type="2" portal="" icon="" state="0" index="3"/>
		    	<resource id="orderdeal_list4" name="运中工单"   url="/sentiment/pages/order/orderdeal-list4.jsp" type="2" portal="" icon="" state="0" index="4"/>
	    </resource>·
	     <resource id="sendlog" name="发送日志"  type="2" portal="" icon="" state="0" index="6">	
		    	<resource id="email_log" name="邮件日志"   url="/sentiment/pages/log/email-log-list.jsp" type="2" portal="" icon="" state="0" index="1"/>
		    	<resource id="message_log" name="短信日志"   url="/sentiment/pages/log/message-log-list.jsp" type="2" portal="" icon="" state="0" index="2"/>
	    </resource>
	     <resource id="baseinfo_config" name="基础信息管理"  type="2" portal="" icon="" state="0" index="7">
	     		<resource id="binding" name="服务请求对应关系绑定"   url="/sentiment/pages/setting/binding.jsp" type="2" portal="" icon="" state="0" index="1"/>	     	
		    	<resource id="userinfo" name="事业部通讯录维护"   url="/sentiment/pages/setting/userinfo-list.jsp" type="2" portal="" icon="" state="0" index="2"/>		    	
		    	<resource id="screen" name="舆情弹屏信息配置"   url="/sentiment/pages/setting/screen-list.jsp" type="2" portal="" icon="" state="0" index="3"/>
			 	<resource id="sentiment_dialog" name="舆情话术配置"   url="/sentiment/pages/order/order-sentiment-list.jsp" type="2" portal="" icon="" state="0" index="4"/>
			 	<resource id="template_contacts" name="通讯录配置"   url="/sentiment/pages/template/contacts-list.jsp" type="2" portal="" icon="" state="0" index="5">
				 	<resource id="sentiment_compl_admin" name="通讯录配置导入导出"   url="" type="3" portal="" icon="" state="0" index="5"/>
			 	</resource>

			 <!-- 		    	<resource id="keyword" name="关键字管理"   url="/sentiment/pages/setting/keyword-list.jsp" type="2" portal="" icon="" state="0" index="2"/> -->
        </resource>
	    <resource id="public_opinion_monitor" name="舆情监控"   type="2" portal="" icon="" state="0" index="8">
	   			 <resource id="monitor1" name="舆情监控-1"   url="/sentiment/pages/monitor/monitor.jsp" type="2" portal="" icon="" state="0" index="1"/>	     	
		    	<resource id="monitor3" name="舆情监控-2"   url="/sentiment/pages/monitor/monitor3.jsp" type="2" portal="" icon="" state="0" index="3"/>		    	
 		    	<resource id="monitor2" name="舆情监控-3"   url="/sentiment/pages/monitor/monitor2.``jsp" type="2" portal="" icon="" state="0" index="2"/>
 	    </resource>
	    <resource id="interactive" name="舆情交互"   type="2" portal="" icon="" state="0" index="9">
	          <resource id="mentions" name="@我的微博"   url="/sentiment/pages/interactive/mentions.jsp" type="2" portal="" icon="" state="0" index="1"/>
	          <resource id="comment_mentions" name="@我的评论"  url="/sentiment/pages/interactive/com-mentions.jsp" type="2" portal="" icon="" state="0" index="2"/>
	          <resource id="comment_tome" name="评论"   url="/sentiment/pages/interactive/com-tome.jsp" type="2" portal="" icon="" state="0" index="3"/>
	          <resource id="message" name="私信"   url="/sentiment/pages/interactive/message.jsp" type="2" portal="" icon="" state="0" index="4"/>
	    </resource> 
	</resource>
</resources>
