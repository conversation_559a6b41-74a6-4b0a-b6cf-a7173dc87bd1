-- @author: liaopj 2024-10-25 舆情页面优化
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD COMPLAINT_UPGRADE_NO VARCHAR2(64);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD PROCESSING_STATUS VARCHAR2(5);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD PROCEESING_OPINION VARCHAR2(1000);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD DOWN_LINE VARCHAR2(5);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD ON_LINE VARCHAR2(5);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD OFF_RESULT VARCHAR2(5) DEFAULT '0';
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD OFF_REASON_CLOSED VARCHAR2(255);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD OFF_REASON_UNCLOSED VARCHAR2(255);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD OFF_TIME VARCHAR2(64);
ALTER TABLE YWDB.C_ST_SENTIMENT_ORDER ADD IS_RED VARCHAR2(5) DEFAULT '0';

create table C_ST_SENTIMENT_ADDITION
(
    ID                   VARCHAR2(32)         not null,
    ORDER_ID             VARCHAR2(32),
    DISTRIBUTED_TIME     VARCHAR2(300),
    URL                  VARCHAR2(3600),
    UPGRADETYPE1         VARCHAR2(32),
    UPGRADE_METHOD       VARCHAR2(10),
    CREATE_ACC           VARCHAR2(32),
    CREATE_TIME          VARCHAR2(20),
    constraint PK_C_ST_SENTIMENT_ADDITION primary key (ID)
);

comment on table C_ST_SENTIMENT_ADDITION is
'舆情追加记录表';

comment on column C_ST_SENTIMENT_ADDITION.ID is
'主键';

comment on column C_ST_SENTIMENT_ADDITION.ORDER_ID is
'舆情信息单号';

comment on column C_ST_SENTIMENT_ADDITION.DISTRIBUTED_TIME is
'发贴时间';

comment on column C_ST_SENTIMENT_ADDITION.URL is
'原文链接';

comment on column C_ST_SENTIMENT_ADDITION.UPGRADETYPE1 is
'升级类型分类';

comment on column C_ST_SENTIMENT_ADDITION.UPGRADE_METHOD is
'升级处理方式';

comment on column C_ST_SENTIMENT_ADDITION.CREATE_ACC is
'创建人账号';

comment on column C_ST_SENTIMENT_ADDITION.CREATE_TIME is
'创建时间';

CREATE INDEX C_ST_SENTIMENT_ADDITION_ORDER_ID_IDX ON YWDB.C_ST_SENTIMENT_ADDITION (ORDER_ID);
CREATE INDEX C_ST_SENTIMENT_ADDITION_CREATE_ACC_IDX ON YWDB.C_ST_SENTIMENT_ADDITION (CREATE_ACC);
CREATE INDEX C_ST_SENTIMENT_ADDITION_CREATE_TIME_IDX ON YWDB.C_ST_SENTIMENT_ADDITION (CREATE_TIME);