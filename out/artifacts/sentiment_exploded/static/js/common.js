/**
 * 通用js
 */

/**
 * 千家姓校验
 */
function testSurname(surname){
	var pattern = /^[a-z]+[1-4]{0,1}$/;
	if(surname){
		return pattern.test(surname);
	}else{
		return true;
	}
}
/**
 * 去掉所有的html标记
 * @param str
 * @returns
 */
function replacehtml(str){
	str=str.replace(/<[^>]+>/g,"");
	return  str;
}

/**
 * 反选
 * @param obj
 */
function checkAll(id,obj) {
	$("#"+id).find("input:checkbox").prop('checked',obj.is(':checked'));
}

/**
 * 获取当前时间
 */
function getNowTime(diffDate){
	var date = new Date();
	if(diffDate){
		date =new Date( new Date().getTime() + 86400 * 1000 * diffDate );
	}
	var day = date.getDate() < 10 ? "0" + date.getDate():date.getDate();
	var month = (date.getMonth() + 1) > 9 ? (date.getMonth() + 1) : "0"	+ (date.getMonth() + 1);
	var hour = date.getHours() <10? "0"+ date.getHours():date.getHours();
	var minute = date.getMinutes() <10 ? "0"+date.getMinutes():date.getMinutes();
	var second = date.getSeconds() <10 ? "0"+ date.getSeconds():date.getSeconds();
	return date.getFullYear() + "-" + month + "-"  + day + " " + hour + ":" + minute+ ":" + second;
}
/**
 * 获取今天日期
 */
function getTodayDate(diffDate){
	return getNowTime(diffDate).substring(0,10);
}

/**
 * 校验文本框是否为空
 */
function checkNull(value){
	if(value =="" || value ==null || value =="undefined"){
		return true;
	}
	return false;
}

/**
 * 校验文本框是否为空
 */
function formatNull(value,defaultValue){
	if(value){
		return value;
	}else{
		return defaultValue==null?"":defaultValue;
	}
}

function diffDateTime(startTime,endTime){
	var start = new Date(startTime);
	var end = new Date(endTime);
	return end.getTime() - start.getTime(); //时间差的毫秒数
}
/**
 * 批量校验电话号码
 * @param phone
 * @param separator 分隔符
 */
function batchCheckPhone(phones,separator){
	var arr = phones.split(separator);
	if(arr && arr.length > 0){
		for (var i in arr ) {
			var p = arr[i];
			if(checkNull(p)){
				continue;
			}else if(!checkPhone(p,true)){
				layer.alert("电话号码["+p+"]有误，请重新手机号码！");
				return false;
			}
		}
		return true;
	}else{
		layer.alert("电话号码栏输入有误，请重新输入！");
		return false;
	}
}

function checkPhone(phone,isAlert){
	var isMob=/^((\+?86)|(\(\+86\)))?(13[0-9]{9}|15[0-9]{9}|18[0-9]{9}|147[0-9]{8}|17[0-9]{9}|1349[0-9]{7})$/;
	var mobilePhone = $.trim(phone);
	if(mobilePhone==""){
		if(!isAlert){
			alert("请填写手机号码！");
		}
		return false;
	}else if(!(isMob.test(mobilePhone))){
		if(!isAlert){
			alert("手机号码格式不正确，请重新填写！");
		}
		return false;
	}
	return true;
}

function downloadExl(data,url){
	if(url) {
		var form = $("<form></form>").attr("action", url).attr("method","post");
		for ( var key in data) {
			form.append($("<input></input>").attr("type", "hidden").attr("name",key).attr("value", data[key]));
		}
		form.appendTo('body').submit().remove();
	}
}

function timestamp2Str(timestamp,type) {
	var time = timestamp;
	if(timestamp&&timestamp!=''&&String(timestamp).length>10){
		var date = new Date(timestamp);
		Y = date.getFullYear() + '-';
		M = (date.getMonth()+1 < 10 ? '0'+(date.getMonth()+1) : date.getMonth()+1) + '-';
		D = (date.getDate() < 10 ? '0'+date.getDate():date.getDate());
		h = ' ' + (date.getHours() < 10 ? '0'+date.getHours():date.getHours())+ ':';
		m = (date.getMinutes() < 10 ? '0'+date.getMinutes():date.getMinutes()) + ':';
		s = date.getSeconds() < 10 ? '0'+date.getSeconds():date.getSeconds();
		if(type == 'date'){
			time = Y+M+D;
		}else{
			time = Y+M+D+h+m+s;
		}
	}
	return time;
}

/**
 * 时间戳和今天零点比较，返回正数说明晚于今天零点
 * @param timestamp 时间戳
 * @returns
 */
function diffTimestamp(timestamp,defaultTimestamp) {
	if(!defaultTimestamp){
		defaultTimestamp = new Date(new Date().setHours(0, 0, 0, 0));
	}
	if(timestamp){
		return timestamp - defaultTimestamp;
	}else{
		return timestamp
	}
}
/**
 * 手机号码校验
 */
function checkMobilePhone(chkMobilePhone) {
	//手机号码（1开头，11位）
	if(chkMobilePhone!=''&&chkMobilePhone.length==11){
		var isMob=/^1[3-9]\d{9}$/;
		return isMob.test(chkMobilePhone);
	}else if(chkMobilePhone!=''&&chkMobilePhone.length==12){
		var isMob=/^01[3-9]\d{9}$/;
		return isMob.test(chkMobilePhone);
	}
	return false;
}

$.views.converters("timestamp",function(val){
	if(!checkNull(val)){
		return timestamp2Str(val);
	}else{
		return val;
	}
});