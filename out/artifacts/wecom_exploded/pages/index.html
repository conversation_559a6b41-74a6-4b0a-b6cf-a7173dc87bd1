<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="renderer" content="webkit">
    <meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport">
    <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta name="robots" content="index,follow">
    <meta http-equiv="pragma" content="no-cache">
    <meta http-equiv="cache-control" content="no-cache">
    <meta http-equiv="expires" content="0">
    <link href="/wecom/static/css/element-ui.css" rel="stylesheet">
    <link href="/wecom/static/css/scroll-bar.css" rel="stylesheet">
    <link href="/wecom/static/css/chat.css" rel="stylesheet">
    <style>
        body{
            margin: 0;
        }
        .ifmClass{
            border: 0;
            width: 100%;
            height: 100%;
        }
        .el-tabs__header{
            margin: 0px;
        }
        .el-tabs__item{
            height: 35px;
            line-height: 35px;
            padding: 0 15px;
            font-size: 12px;
        }
        .el-tabs__nav-next, .el-tabs__nav-prev{
            line-height: 35px;
        }
        [v-cloak] {
            display: none;
        }
    </style>
</head>
<body>
<div id="app">
    <el-tabs v-model="tabsModel" type="card" @tab-click="tabChange">
        <el-tab-pane label="群工单录入" name="5"></el-tab-pane>
        <el-tab-pane label="工单录入" name="0"></el-tab-pane>
        <el-tab-pane label="服务工单" name="1"></el-tab-pane>
        <el-tab-pane label="客户详情" name="2"></el-tab-pane>
        <el-tab-pane label="订单查询" name="3"></el-tab-pane>
        <el-tab-pane label="接触历史" name="4"></el-tab-pane>
        <!-- <el-tab-pane label="企微用户添加" name="5"></el-tab-pane> -->
    </el-tabs>
    <div id="userMenu" style="position: fixed;right: 2vh;top: 8vh;" v-cloak>
        <el-dropdown :show-timeout="50" trigger="hover" @command="menuClick">
            <i class="el-icon-s-custom" style="font-size: 25px;color: #4eb0fa;cursor: pointer;"></i>
            <el-dropdown-menu slot="dropdown">
                <el-dropdown-item>当前账户{{userAcc}}</el-dropdown-item>
                <el-dropdown-item command="satisfy">满意度邀评</el-dropdown-item>
                <el-dropdown-item command="logout">退出登录</el-dropdown-item>
            </el-dropdown-menu>
        </el-dropdown>
    </div>
    <div id="orderIfm">
        <iframe class="ifmClass" id="ifm" src="order/order-input.html"></iframe>
    </div>
    <div id="otherIfm" style="display: none">
        <iframe class="ifmClass" id="ifm2" src=""></iframe>
    </div>
    <!--		<div id="groupOrderIfm">-->
    <!--			<iframe class="ifmClass" id="ifm3" src="order/group-order-input.html"></iframe>-->
    <!--		</div>-->
</div>

<script type="text/javascript" src="/easitline-static/js/jquery.min.js"></script>
<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
<script type="text/javascript" src="/easitline-static/js/easitline.core-2.0.0.js?v=20180129"></script>
<script type="text/javascript" src="/wecom/static/js/vue/vue.min.js"></script>
<script type="text/javascript" src="/wecom/static/js/element/element-ui.js"></script>
<script type="text/javascript" src="/wecom/static/js/common/common.js?v=20220217"></script>
<script src="//res.wx.qq.com/open/js/jweixin-1.2.0.js"></script>
<script src="https://open.work.weixin.qq.com/wwopen/js/jwxwork-1.0.0.js"></script>
<script>
    var indexVue = new Vue({
        el:'#app',
        data(){
            let _this = this;
            return{
                tabsModel:"0",
                iframeUrl:[
                    "order/order-input.html",
                    "order/order-search.html",
                    "user/customer-info.jsp",
                    "order/purchase-history-list.html",
                    "history/contact-history.html",
                    // "/wecom/servlet/config?action=AsPage",
                    "order/group-order-input.html"
                ],
                //工单数据对象
                orderObj:{
                    phone:""
                },
                //坐席账号
                userAcc:GetQueryString("u")||"",
                //坐席code参数
                agentCode:GetQueryString("code"),
                //当前单聊客户userId
                custUserId:"",
                //当前单聊客户信息
                custInfo:{},
                //群聊ID
                // chatId:"",
                //SDK初始化重试次数
                sdkInitRetryCount: 0,
                maxRetryCount: 3,
            }
        },
        methods:{
            //高度定位
            iframeInit(){
                var ifr = document.getElementById('ifm');
                var ifr2 = document.getElementById('ifm2');
                ifr.style.height='0px';
                var height = document.documentElement.clientHeight;
                ifr.style.height = height - 40 + 'px';
                ifr2.style.height = height - 40 + 'px';
            },
            //tab切换
            tabChange(val){
                if(val.name!=0){
                    $("#orderIfm").hide();
                    $("#userMenu").hide();
                    $("#otherIfm").show();
                    let url = this.iframeUrl[val.name];
                    //客户详情 传递参数
                    if(val.name == "2"){
                        url += "?phone=" + this.orderObj.phone;
                    }
                    $("#ifm2").attr("src",url);
                }else{
                    $("#otherIfm").hide();
                    $("#orderIfm").show();
                    $("#userMenu").show();
                }
            },
            //美云销数据回传
            onlineOrderHandle(data){
                //传至工单iframe处理
                document.getElementById("ifm").contentWindow["orderfunc"].setEcmOrderInfo(data);
                this.tabsModel = "0";
                $("#otherIfm").hide();
                $("#orderIfm").show();
            },
            //美云销数据回传
            offlineOrderHandle(data){
                //传至工单iframe处理
                document.getElementById("ifm").contentWindow["orderfunc"].setOldEcmOrderInfo(data);
                this.tabsModel = "0";
                $("#otherIfm").hide();
                $("#orderIfm").show();
            },
            //用户菜单点击
            menuClick(command){
                if(command == "satisfy"){
                    this.sendSatisfyMsg();
                }else if(command == "logout"){
                    document.cookie = 'midea_sso_token=0;path=/;domain=midea.com;expires=' + new Date(0).toUTCString();
                    document.cookie = 'mideatest_sso_token=0;path=/;domain=midea.com;expires=' + new Date(0).toUTCString();
                    location.href = '/wecom/logout.jsp';
                }
            },
            //企微jssdk初始化
            wecomJsSdkInit(){
                // 检查是否在企业微信环境中
                if (typeof wx === 'undefined') {
                    console.error('企业微信 JS SDK 未加载，请确认在企业微信环境中打开');
                    // 检查重试次数
                    if (this.sdkInitRetryCount < this.maxRetryCount) {
                        this.sdkInitRetryCount++;
                        console.log(`第 ${this.sdkInitRetryCount} 次重试初始化 SDK...`);
                        setTimeout(() => {
                            this.wecomJsSdkInit();
                        }, 1000);
                    } else {
                        console.error('SDK 初始化重试次数已达上限，请手动刷新页面');
                    }
                    return;
                }

                // 检查 agentConfig 方法是否存在
                if (typeof wx.agentConfig !== 'function') {
                    console.error('wx.agentConfig 方法不存在，可能是 SDK 版本问题或加载不完整');
                    // 检查重试次数
                    if (this.sdkInitRetryCount < this.maxRetryCount) {
                        this.sdkInitRetryCount++;
                        console.log(`第 ${this.sdkInitRetryCount} 次重试初始化 SDK...`);
                        setTimeout(() => {
                            this.wecomJsSdkInit();
                        }, 1000);
                    } else {
                        console.error('SDK 初始化重试次数已达上限，请手动刷新页面');
                    }
                    return;
                }

                console.log("location.href:",location.href);
                ajaxCall("/wecom/servlet/config?action=sdkInit",{url:location.href},result=>{
                    if(result.state==1){
                        try {
                            wx.agentConfig({
                                corpid: result.data.corpid,
                                agentid: result.data.agentId,
                                timestamp: result.data.timestamp,
                                nonceStr: result.data.nonceStr,
                                signature: result.data.signature,
                                jsApiList: ['sendChatMessage',"getCurExternalContact","getContext","getCurExternalChat"], //传入需要使用的接口名称
                                // jsApiList: ["getContext","getCurExternalChat"], //群聊id获取接口测试
                                success: function(res) {// 回调
                                    console.log("企业微信 SDK 初始化成功:", res);
                                    //获取群聊id
                                    // indexVue.getCurExternalChat();
                                    //获取外部联系人id
                                    indexVue.getCurExtUserId();
                                },
                                fail: function(res) {
                                    console.error("企业微信 SDK 初始化失败:", JSON.stringify(res));
                                    if(res.errMsg && res.errMsg.indexOf('function not exist') > -1){
                                        alert('企业微信版本过低，请升级到最新版本');
                                    } else {
                                        alert('企业微信 SDK 初始化失败，请重新打开页面');
                                    }
                                }
                            });
                        } catch (error) {
                            console.error('调用 wx.agentConfig 时发生错误:', error);
                        }
                    } else {
                        console.error('获取 SDK 配置失败:', result);
                    }
                },error=>{
                    console.error('SDK 配置请求失败:', error);
                });
            },
            //获取群聊id
            // getCurExternalChat(){
            // 	wx.invoke('getCurExternalChat',{},function(res){
            // 		console.log("res:",res)
            // 		if(res.err_msg == "getCurExternalChat:ok"){
            // 			this.chatId  = res.chatId ; //返回当前外部群的群聊ID
            // 			console.log("formDataChatId:",this.chatId)
            // 			var child = document.getElementById("ifm3").contentWindow;
            // 			try {
            // 				console.log("child",child)
            // 				child.chatId = this.chatId;
            // 				console.log("child.chatId:",child.chatId)
            // 			} catch (e) {
            // 				console.log("出现了异常：",e)
            // 			}
            // 		}else {
            // 			//错误处理
            // 		}
            // 	});
            // },
            //获取外部联系人userid
            getCurExtUserId(){
                wx.invoke('getCurExternalContact', {}, function(res){
                    if(res.err_msg == "getCurExternalContact:ok"){
                        indexVue.$set(indexVue.$data,"custUserId",res.userId);
                        //获取到userId之后获取客户信息
                        indexVue.getCurExtUserInfo();
                    }
                });
            },
            //获取外部联系人详情
            getCurExtUserInfo(){
                return new Promise((resolve, reject) => {
                    console.log("开始获取外部联系人详情，userId：", this.custUserId);

                    ajaxCall("/wecom/servlet/config?action=getExtCustInfo",{userId:this.custUserId},result=>{
                        console.log("获取外部联系人详情响应：", result);

                        if(result.state==1){
                            this.custInfo = result.data;
                            console.log("客户信息设置成功：", this.custInfo);

                            var child = document.getElementById("ifm").contentWindow;
                            try {
                                child.custUserId = this.custUserId;
                                child.orderVue.$set(child.orderVue.$data.formData,"custNickname",this.custInfo.name)
                            } catch (e) {
                                console.warn("设置子页面客户信息失败：", e);
                                //do nothing
                            }

                            //获取到外部联系人信息后保存坐席账号、坐席userid、外部联系人userid关系
                            indexVue.saveAgentAndCustomerInfo();

                            resolve(this.custInfo);
                        } else {
                            console.error("获取客户信息失败：", result);
                            reject(new Error("获取客户信息失败：" + (result.msg || "未知错误")));
                        }
                    },error=>{
                        console.error("获取客户信息请求失败：", error);
                        reject(error);
                    });
                });
            },
            saveAgentAndCustomerInfo(){
                var userName = "";
                if(typeof(this.custInfo.name)!="undefined"){
                    userName = this.custInfo.name;
                }
                ajaxCall("/wecom/servlet/config?action=saveAgentAndCustomerInfo",{
                    customerUserId:this.custUserId,
                    customerUserName:userName,
                    agentCode:this.agentCode
                },result=>{

                },error=>{
                    console.error(error);
                });
            },
            //将满意度消息放至消息框
            sendSatisfyMsg(){
                // 增加客户信息完整性校验
                console.log("开始发送满意度消息，当前客户信息：", this.custInfo);

                // 检查客户信息是否完整
                if (!this.custInfo || !this.custInfo.name || this.custInfo.name.trim() === '') {
                    console.warn("客户信息不完整，尝试重新获取客户信息");
                    // this.$message.warning("正在获取客户信息，请稍候...");

                    // 重新获取客户信息
                    this.getCurExtUserInfo().then(() => {
                        // 重新检查客户信息
                        if (!this.custInfo || !this.custInfo.name || this.custInfo.name.trim() === '') {
                            // 即使重新获取客户信息后仍然不完整，也要发送满意度，后台有保底机制
                            console.error("重新获取客户信息后仍然不完整：", this.custInfo);
                            // this.$message.error("无法获取客户信息，请刷新页面后重试");
                            // return;
                        }
                        // 客户信息完整，继续发送满意度
                        this.doSendSatisfyMsg();
                    }).catch(error => {
                        console.error("重新获取客户信息失败：", error);
                        // this.$message.error("获取客户信息失败，请刷新页面后重试");
                    });
                    return;
                }

                // 客户信息完整，直接发送满意度
                this.doSendSatisfyMsg();
            },

            // 实际发送满意度消息的方法
            doSendSatisfyMsg(){
                console.log("发送满意度消息，客户姓名：", this.custInfo.name, "客户ID：", this.custUserId);

                let req = {
                    controls:["configDao.getSatisfyExample"],
                    params:{custName:this.custInfo.name,custId:this.custUserId}
                }
                //请求查询满意度评价字典
                ajaxCall("/wecom/webcall",req,result=>{
                    var r = result["configDao.getSatisfyExample"];
                    if(typeof(r)=="undefined"||r.state!="1"){
                        console.error("获取满意度模板失败：", r);
                        this.$message.error("系统错误");
                        return;
                    }
                    wx.invoke('sendChatMessage', {
                        msgtype:"text", //消息类型，必填
                        enterChat: true,
                        text: {
                            content:r.data, //文本内容
                        },
                    }, function(res) {
                        if (res.err_msg == 'sendChatMessage:ok') {
                            console.log("满意度消息发送成功");
                            //发送成功
                        } else {
                            console.error("满意度消息发送失败：", res);
                        }
                    });
                },error=>{
                    console.error("获取满意度模板请求失败：", error);
                    // this.$message.error("网络错误，请重试");
                });
            },
        },
        created(){
            //页面大小调整
            this.iframeInit();
            //延迟加载JSSDK，确保SDK文件完全加载
            this.$nextTick(() => {
                setTimeout(() => {
                    this.wecomJsSdkInit();
                }, 500);
            });
        },
        mounted(){
            //窗口变化重新定位高度
            window.onresize = () => {
                return (() => {
                    this.iframeInit();
                })();
            };
        }
    });
</script>
</body>
</html>