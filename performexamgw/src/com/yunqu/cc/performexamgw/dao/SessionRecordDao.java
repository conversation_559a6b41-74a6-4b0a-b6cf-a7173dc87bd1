package com.yunqu.cc.performexamgw.dao;

import java.util.List;

import com.alibaba.fastjson.JSONObject;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.model.AgentModel;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.performexamgw.base.CommonMediaLogger;
import com.yunqu.cc.performexamgw.base.Constants;
import com.yunqu.cc.performexamgw.model.QcSessionReocrdModel;

/**
 * 对全媒体话单进行操作的dao
 */
public class SessionRecordDao extends BaseDao{
	
	private Logger logger = CommonMediaLogger.logger;
	
	private EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	
	private EasyQuery mideaQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.MEDIA_DS_NAME);
	
	/**
	 * 根据会话记录id，查询会话记录
	 * @param recordId
	 * @return
	 */
	public EasyRow getSessionRecordById(String recordId) {
		return getRowById(queryHelper, "根据会话记录id，查询会话记录", "V_PF_SESSION_RECORD", "ID", recordId);
	}
	
	/**
	 * 根据会话记录id，查询会话明细记录
	 * @param recordId
	 * @return
	 */
	public List<EasyRow> getSessionRecordDetailByRecordId(String recordId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT * FROM V_PF_SESSION_DETAIL T WHERE T.SESSION_RECORD_ID='"+recordId+"' ORDER BY T.CREATE_TIME ASC ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据会话记录id，查询会话明细记录失败:"+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 从全媒体话单中找出没有坐席资料的话单
	 * @return
	 */
	public List<EasyRow> findNoAgentInfoRecord() {
		try {
			EasySQL sql = new EasySQL();
			String date = DateUtil.addDay(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -Constants.MEDIA_VALID_DATE);
			sql.append(" SELECT * FROM V_PF_SESSION_RECORD T  ");
			sql.append(date,"  WHERE   answer_time >?  ");
			sql.append(" AND T.AGENT_NO IS NULL   ");
			sql.append(" AND T.AGENT_ID IS NOT NULL  ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 全媒体话单查询没有坐席资料的话单失败:"+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 设置某条全媒体话单的坐席信息
	 * @param agent
	 */
	public boolean updateAgentInfo(String id,AgentModel agent) {
		try {
			if(StringUtils.isBlank(id)){
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新全媒体话单坐席信息失败:id为空!");
				return false;
			}
			EasySQL sql = new EasySQL();
			sql.append(" UPDATE ").append(Constants.SESSION_RECORD_TABLE_NAME).append(" SET SERIAL_ID = SERIAL_ID ");
			sql.append(agent.getAgentAcc()," ,AGENT_ACC =? ");
			sql.append(agent.getAgentNo()," ,AGENT_NO =? ");
			sql.append(agent.getAgentName()," ,AGENT_NAME =? ");
			sql.append(agent.getDeptCode()," ,AGENT_DEPT =? ");
			sql.append(agent.getAreaCode()," ,OP_AREA_CODE =? ");
			sql.append(agent.getEpCode()," ,EP_CODE =? ");
			sql.append(id," WHERE SERIAL_ID = ? ");
			mideaQuery.execute(sql.getSQL(),sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 设置某条全媒体话单的坐席信息失败.",e);
		}
		return false;
	}

	/**
	 * 设置某条全媒体话单的坐席工号
	 * @param workNo
	 */
	public boolean updateAgentWorkNo(String id,String workNo) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(workNo," UPDATE ").append(Constants.SESSION_RECORD_TABLE_NAME).append(" SET AGENT_NO = ? ");
			sql.append(id," WHERE SERIAL_ID = ? ");
			mideaQuery.execute(sql.toString(),sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 设置某条全媒体话单的坐席工号失败.",e);
		}
		return false;
		
	}

	/**
	 * 将已结束会话的全媒体的状态设置为待打标签的状态
	 */
	public boolean setTagStatus() {
		try {
			String today = DateUtil.getCurrentDateStr("yyyy-MM-dd 00:00:00");
			String beginTime = DateUtil.addDay("yyyy-MM-dd", today, -7);//7天的数据
			EasySQL sql = new EasySQL();
			sql.append(" UPDATE ").append(Constants.SESSION_RECORD_TABLE_NAME).append(" SET STATUS = '"+DictConstants.CALL_RECORD_STATUS_WAIT_TAG+"' ");
			sql.append(beginTime," WHERE  BEGIN_TIME > ? ");
			sql.append(" AND END_TIME IS NOT NULL AND STATUS IS NULL    ");
			mideaQuery.execute(sql.getSQL(),sql.getParams());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 将已结束会话的全媒体的状态设置为待打标签的状态失败.",e);
		}
		return false;
	}
public static void main(String[] args) {
	String today = DateUtil.getCurrentDateStr("yyyy-MM-dd 00:00:00");
	String beginTime = DateUtil.addDay("yyyy-MM-dd 00:00:00", today, -7);//7天的数据
	System.out.println(beginTime);
}
	
	/**
	 * 根据状态查询会话记录
	 * @param status
	 * @return
	 */
	public List<EasyRow> findSessionRecordByStatus(String status) {
		String today = DateUtil.getCurrentDateStr("yyyy-MM-dd 00:00:00");
		String beginTime = DateUtil.addDay("yyyy-MM-dd", today, -7);//7天的数据
//		StringBuffer sql = new StringBuffer(" SELECT * from V_PF_SESSION_RECORD where STATUS='"+status+"' and BEGIN_TIME > '"+beginTime+"' ");
		StringBuffer sql = new StringBuffer();
		sql.append("select 'CC_MEDIA_RECORD' TABLE_NAME,'02' SESSION_TYPE, T.SERIAL_ID ID,T.SESSION_ID,T.OP_AREA_CODE ,T.CHANNEL_KEY CHANNEL_ID,T.CUST_CODE CUSTOMER_ACC,T.CUST_NAME CUSTOMER_NAME");
		sql.append(" ,(CASE T.CREATE_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '01' END) DIRECTION,T.GROUP_ID,T.GROUP_NAME,");
		sql.append(" T.AGENT_ACC,T.AGENT_NO,T.AGENT_ID,T.AGENT_NAME,T.AGENT_DEPT AGENT_DEPT,T.EP_CODE EP_CODE,T.REQ_TIME BEGIN_TIME,T.REQ_TIME QUEUE_TIME,T.BEGIN_TIME ANSWER_TIME,T.END_TIME,T.SERVER_TIME LENS,");
		sql.append("(CASE T.CLEAR_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '03'  ELSE '04' END) HANGUP_TYPE,T.SATISF_CODE,T.SATISF_NAME,T.SATISF_ITEM_CODE,T.SATISF_ITEM_NAME,T.SATISF_CAUSE,T.SATISF_TIME,T.IS_TRANSFER ,");
		sql.append("(CASE T.CREATE_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '03' END) IN_TYPE,T.OLD_SESSIONID ,T.QC_RECORD_ID ,T.SH_ORDER_ID ,T.STATUS,T.CALL_RECORD_ID ,T.SESSION_RECORD_ID,T.CHANNEL_TYPE,T.ITEM_ID,T.MOBILE");
		sql.append(" FROM  YCBUSI.CC_MEDIA_RECORD T ");
		sql.append(" where STATUS='"+status+"' and BEGIN_TIME > '"+beginTime+"'");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 根据状态查询会话记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据状态查询会话记录失败.",e);
		}
		return null;
	}
	
	
	/**
	 * 根据会话记录id，找出会话记录下面的所有会话明细记录
	 * @param id
	 * @return
	 */
	public List<EasyRow> findSessionRecordDetail(String id) {
		StringBuffer sql = new StringBuffer(" SELECT * from V_PF_SESSION_DETAIL where SESSION_RECORD_ID='"+id+"'  ");
		
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 根据会话ID查询所有的会话明细记录:"+sql.toString());
			List<EasyRow> list = queryHelper.queryForList(sql.toString(),null);
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据会话ID查询所有的会话明细记录失败.",e);
		}
		return null;
	}
	
	/**
	 * 更新会话记录的状态
	 * @param id
	 * @param status
	 */
	public boolean updateSessionReocrdStatus(String id, String status) {
		StringBuffer sql = new StringBuffer(" UPDATE "+Constants.SESSION_RECORD_TABLE_NAME+" SET STATUS ='"+status+"' WHERE SERIAL_ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的状态:"+sql.toString());
			mideaQuery.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的状态失败.",e);
		}
		return false;
	}
	

	/**
	 * 更新会话记录的质检记录id
	 * @param id
	 * @param qcRecordId
	 */
	public boolean updateSessionReocrdQcRecordId(String id, String qcRecordId,String status) {
		StringBuffer sql = new StringBuffer(" UPDATE "+Constants.SESSION_RECORD_TABLE_NAME+" SET QC_RECORD_ID ='"+qcRecordId+"',STATUS ='"+status+"' WHERE SERIAL_ID='"+id+"'  ");
		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的质检记录id:"+sql.toString());
			mideaQuery.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的质检记录id失败.",e);
		}
		return false;
	}

	/**
	 * 根据全媒体质检扩展属性表里的值
	 * @param qcRecord
	 * @return
	 */
	public boolean updateSessionQcPro(QcSessionReocrdModel qcSessionReocrdModel) throws Exception{
		StringBuffer sql = new StringBuffer();
		sql.append(" UPDATE C_PF_QC_SESSION set  ");
		sql.append(" FIRST_REPLY=").append(qcSessionReocrdModel.getFirstReply());
		sql.append(" , AVG_REPLY=").append(qcSessionReocrdModel.getAvgReply());
		sql.append(" , CUST_MSG_NUM=").append(qcSessionReocrdModel.getCustMsgNum());
		sql.append(" , AGENT_MSG_NUM=").append(qcSessionReocrdModel.getAgentMsgNum());
		sql.append(" , QA_PERCENT=").append(qcSessionReocrdModel.getQaPercent());
		sql.append(" , THIRTY_SECONDS_REPLY_NUM=").append(qcSessionReocrdModel.getThirtySecondsReplyNum());
		sql.append(" , REPLY_NUM=").append(qcSessionReocrdModel.getReplyNum());
		sql.append(" , SLOW_REPLY_NUM=").append(qcSessionReocrdModel.getSlowReplyNum());
		sql.append(" , WAIT_SECONDS=").append(qcSessionReocrdModel.getWaitSeconds());
		sql.append(" , REPEAT_REPLY_NUM=").append(qcSessionReocrdModel.getRepeatReplyNum());
		sql.append(" , REPEAT_REPLEY_CONTENT='").append(qcSessionReocrdModel.getRepeatReplyContent()).append("' ");
		sql.append(" , CONSULT_ORDER_NUM=").append(qcSessionReocrdModel.getConsultOrderNum());
		sql.append(" , TRANSFER_NUM=").append(qcSessionReocrdModel.getTransferNum());
		sql.append(" , AGENT_CLOSE_SESSION='").append(qcSessionReocrdModel.getAgentCloseSession()).append("' ");
		sql.append(" , SH_ORDER_NUM=").append(qcSessionReocrdModel.getShOrderNum());
		sql.append(" , SH_APPEAL_NUM=").append(qcSessionReocrdModel.getShAppealNum());
		sql.append(" , TOTAL_REPLY_SECONDS=").append(qcSessionReocrdModel.getTotalReplySeconds());
		sql.append(" , MAX_REPLY_SECONDS=").append(qcSessionReocrdModel.getMaxReplySeconds());
		sql.append(" where SESSION_RECORD_ID='").append(qcSessionReocrdModel.getSessionRecordId()).append("'");

		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的质检记录id:"+sql.toString());
			query.execute(sql.toString());
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 更新会话记录的质检记录id失败.",e);
			throw e;
		}
	}
	
	/**
	 * 找出需要生成质检记录的会话记录
	 * @return
	 */
	public List<EasyRow> findNeedQcSessionRecord() {
		//由于会话结束后，坐席可能没及时录完咨询工单等资料，导致自动质检时无法找到对应的记录，所以进行延迟质检
		String time = DateUtil.addMinute(DateUtil.TIME_FORMAT, DateUtil.getCurrentDateStr(), -Constants.MEDIA_AUTOQC_DELAY_MINUTES);
		String beginTime = DateUtil.addDay(DateUtil.TIME_FORMAT, time, -30);
		EasySQL sql =new EasySQL();
		sql.append("select 'CC_MEDIA_RECORD' TABLE_NAME,'02' SESSION_TYPE, T.SERIAL_ID ID,T.SESSION_ID,T.OP_AREA_CODE ,T.CHANNEL_KEY CHANNEL_ID,T.CUST_CODE CUSTOMER_ACC,T.CUST_NAME CUSTOMER_NAME");
		sql.append(" ,(CASE T.CREATE_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '01' END) DIRECTION,T.GROUP_ID,T.GROUP_NAME,");
		sql.append(" T.AGENT_ACC,T.AGENT_NO,T.AGENT_ID,T.AGENT_NAME,T.AGENT_DEPT AGENT_DEPT,T.EP_CODE EP_CODE,T.REQ_TIME BEGIN_TIME,T.REQ_TIME QUEUE_TIME,T.BEGIN_TIME ANSWER_TIME,T.END_TIME,T.SERVER_TIME LENS,");
		sql.append("(CASE T.CLEAR_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '03'  ELSE '04' END) HANGUP_TYPE,T.SATISF_CODE,T.SATISF_NAME,T.SATISF_ITEM_CODE,T.SATISF_ITEM_NAME,T.SATISF_CAUSE,T.SATISF_TIME,T.IS_TRANSFER ,");
		sql.append("(CASE T.CREATE_CAUSE WHEN 1 THEN '01' WHEN 2 THEN '02' WHEN 3 THEN '03' END) IN_TYPE,T.OLD_SESSIONID ,T.QC_RECORD_ID ,T.SH_ORDER_ID ,T.STATUS,T.CALL_RECORD_ID ,T.SESSION_RECORD_ID,T.CHANNEL_TYPE,T.ITEM_ID,T.MOBILE");
		sql.append(" FROM  YCBUSI.CC_MEDIA_RECORD T ");
		
		//sql.append("SELECT V.TABLE_NAME,V.SESSION_TYPE,V.ID,V.ANSWER_TIME,V.END_TIME,V.AGENT_ACC,V.LENS,V.DIRECTION,V.CHANNEL_ID");
//		sql.append("  FROM V_PF_SESSION_RECORD V  ");
		//20250705 修改END_TIME为BEGIN_TIME条件进行查询 走索引 增加SERVER_STATE条件过滤已经结束的会话
		sql.append(time,"  WHERE T.BEGIN_TIME <=?  ");
		sql.append(beginTime,"  AND T.BEGIN_TIME >=? ");
		sql.append("  AND T.SERVER_STATE = '3' "); //服务状态为已结束
		sql.append("  AND T.QC_RECORD_ID IS NULL ");
		sql.append(DictConstants.CALL_RECORD_STATUS_GEN_QC_RECORD,"  AND T.STATUS=? ");
		
		try {
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的全媒体会话记录:"+sql.getSQL() + "，参数：" + JSONObject.toJSONString(sql.getParams()));
			queryHelper.setMaxRow(99999);
			List<EasyRow> list = queryHelper.queryForList(sql.getSQL(),sql.getParams());
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的全媒体会话记录数量:"+list.size());
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询需要进行质检的全媒体会话记录失败.",e);
		}
		return null;
	}
	
	/**
	 * 找出需要进行全媒体自动质检的记录（2023新版）
	 * @return
	 */
	public List<EasyRow> findNeedQcSessionRecord(String date) {
		if(StringUtils.isBlank(date)){//修复数据用
			 date = DateUtil.addDay("yyyy-MM-dd", DateUtil.getCurrentDateStr("yyyy-MM-dd"), -1);
		}
		EasySQL sql =new EasySQL();
		sql.append("SELECT V.TABLE_NAME,V.SESSION_TYPE,V.ID,V.ANSWER_TIME,V.END_TIME,V.AGENT_ACC,V.LENS,V.DIRECTION,V.CHANNEL_ID");
		sql.append("  FROM V_PF_SESSION_RECORD V  ");
		sql.appendRLike(date,"WHERE  END_TIME  LIKE ? ");
		sql.append("  AND V.QC_RECORD_ID IS NULL ");

		try {
			logger.debug(CommonUtil.getClassNameAndMethod(this)+" 找出需要进行自动质检的记录:"+sql.toString());
			queryHelper.setMaxRow(10000);
			List<EasyRow> list = queryHelper.queryForList(sql.getSQL(),sql.getParams());
			return list;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 找出需要进行自动质检的记录失败.",e);
		}
		return null;
	}
	/**
	 * 查询会话记录的咨询工单数量
	 * @param id
	 * @return
	 */
	public int countCousultOrderNum(String id) {
		StringBuffer sql = new StringBuffer(" SELECT COUNT(1) FROM C_OL_CONSULT_ORDER T WHERE T.SESSION_ID=?  ");
		try {
			int orderNum = queryHelper.queryForInt(sql.toString(),new Object[]{id});
			return orderNum;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询会话记录的咨询工单数量失败.",e);
		}
		return 0;
	}
	/**
	 * 查询会话记录的售后工单数量
	 * @param id
	 * @return
	 */
	public int countShOrderNum(String id) {
		StringBuffer sql = new StringBuffer(" SELECT COUNT(1) FROM C_NO_CONTACT T WHERE T.SESSION_ID=?  ");
		try {
			int orderNum = queryHelper.queryForInt(sql.toString(),new Object[]{id});
			return orderNum;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询会话记录的售后工单数量失败.",e);
		}
		return 0;
	}
	/**
	 * 查询会话记录的售后工单诉求数量
	 * @param id
	 * @return
	 */
	public int countShAppealNum(String id) {
		StringBuffer sql = new StringBuffer(" SELECT COUNT(1) FROM C_NO_APPEALLIST T WHERE T.SESSION_ID=?  ");
		try {
			int orderNum = queryHelper.queryForInt(sql.toString(),new Object[]{id});
			return orderNum;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询会话记录的售后工单诉求数量失败.",e);
		}
		return 0;
	}
}
