<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="performexamgw" name="质检处理网关" package-by="26082" package-time="2025-07-17 21:51:04" version="2.5.3#20250904-1">
    <datasources>
        <datasource description="业务数据源" isnull="true" name="yw-ds"/>
        <datasource description="Mars数据源" isnull="true" name="mars-ds"/>
        <datasource description="获取转写文本的数据源,如AMI数据源" isnull="true" name="voice_content_ds"/>
        <datasource description="用于获取语音满意度结果数据源,如Genesys 顺德数据源" isnull="true" name="voice_satisfy_ds"/>
        <datasource description="用于获取语音满意度结果数据源,如Genesys 合肥数据源" isnull="true" name="voice_satisfy_ds2"/>
        <datasource description="用于获取全媒体话单,如ycbusi数据源" isnull="true" name="media_ds"/>
    </datasources>
     <description>
    </description>
</application>
