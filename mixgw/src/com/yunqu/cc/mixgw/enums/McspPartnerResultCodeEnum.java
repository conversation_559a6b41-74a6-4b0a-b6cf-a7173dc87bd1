package com.yunqu.cc.mixgw.enums;

/**
 * <AUTHOR>
 * @ClassName McspResultCodeEnum
 * @description 服务营销商城合伙人状态枚举
 * @purpose
 * @createTime 2025-08-26 11:19:50
 */
public enum McspPartnerResultCodeEnum {
    OK("000000", "成功"),
    FAIL("999999", "系统繁忙，请稍后再试"),
    CHANNEL_ADD_ERROR("CHANNEL_ADD_000001","创建渠道失败"),
    SELLER_NOT_EXIST("********","合伙人不存在"),
    SELLER_INVALID("********","合伙人无效"),
    START_TIME_ERROR("********","活动还未开始"),
    END_TIME_ERROR("********","活动已结束"),
    MIDEAPAY_ACCOUNT_STATE_INIT("********","付账号未实名"),
    MIDEAPAY_ACCOUNT_STATE_FREEZE("********","付账号冻结"),
    MIDEAPAY_ACCOUNT_STATE_CLOSE("********","付账号注销"),
    MIDEAPAY_ACCOUNT_STATE_LOCK("********","付账号锁定"),
    MIDEAPAY_ACCOUNT_STATE_ERROR("********","付账号异常"),
        ;

    private String resultCode;
    private String resultDesc;
    private McspPartnerResultCodeEnum(String resultCode, String resultDesc) {
        this.resultCode = resultCode;
        this.resultDesc = resultDesc;
    }
    public String getResultCode() {
        return resultCode;
    }
    public String getResultDesc() {
        return resultDesc;
    }
    public static McspPartnerResultCodeEnum getInstance(String resultCode) {
        for (McspPartnerResultCodeEnum item : McspPartnerResultCodeEnum.values()) {
            if (item.getResultCode().equals(resultCode)) {
                return item;
            }
        }
        return null;
    }
}
