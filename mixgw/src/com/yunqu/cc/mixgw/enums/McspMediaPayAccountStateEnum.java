package com.yunqu.cc.mixgw.enums;

/**
 * <AUTHOR>
 * @ClassName McspMediaPayAccountStateEnum
 * @description 美的付账号状态
 * @purpose
 * @createTime 2025-08-26 11:27:55
 */
public enum McspMediaPayAccountStateEnum {
    MIDEAPAY_ACCOUNT_STATE_INIT(0, "初始态" ,Mcsp<PERSON>artnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_INIT),//合伙人未实名
    MIDEAPAY_ACCOUNT_STATE_NORMAL(1, "正常" ,McspPartnerResultCodeEnum.OK),
    MIDEAPAY_ACCOUNT_STATE_FREEZE(2, "冻结" ,McspPartnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_FREEZE),
    MIDEAPAY_ACCOUNT_STATE_CLOSE(3, "注销"  ,M<PERSON>p<PERSON>artnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_CLOSE),
    MIDEAPAY_ACCOUNT_STATE_LOCK(4, "锁定"   ,McspPartnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_LOCK),
    MIDEAPAY_ACCOUNT_STATE_ABNORMAL(5, "账号状态异常 (查账号失败，名称等不一致等情况)",McspPartnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_ERROR),
    MIDEAPAY_ACCOUNT_STATE_NOT_EXIST(6, "账号不存在",McspPartnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_ERROR),
    MIDEAPAY_ACCOUNT_STATE_PARTNER_ERR(7, "未引入美的付电商父账号",McspPartnerResultCodeEnum.MIDEAPAY_ACCOUNT_STATE_ERROR),
            ;
    private Integer state;
    private String desc;
    private McspPartnerResultCodeEnum partnerResultCode;
    McspMediaPayAccountStateEnum(Integer state, String desc, McspPartnerResultCodeEnum partnerResultCode) {
        this.state = state;
        this.desc = desc;
        this.partnerResultCode = partnerResultCode;
    }
    public static McspMediaPayAccountStateEnum getInstance(Integer state) {
        for (McspMediaPayAccountStateEnum value : McspMediaPayAccountStateEnum.values()) {
            if (value.state.equals(state)) {
                return value;
            }
        }
        return McspMediaPayAccountStateEnum.MIDEAPAY_ACCOUNT_STATE_ABNORMAL;
    }
    public Integer getState() {
        return state;
    }
    public String getDesc() {
        return desc;
    }
    public McspPartnerResultCodeEnum getPartnerResultCode() {
        return partnerResultCode;
    }
}
