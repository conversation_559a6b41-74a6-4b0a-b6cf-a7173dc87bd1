package com.yunqu.cc.mixgw.enums;

/**
 * <AUTHOR>
 * @ClassName McspResultCodeEnum
 * @description
 * @purpose
 * @createTime 2025-08-26 11:19:50
 */
public enum McspResultCodeEnum {
    OK("000000", "成功", McspPartnerResultCodeEnum.OK),
    FAIL("999999", "系统繁忙，请稍后再试" ,McspPartnerResultCodeEnum.FAIL),
    CHANNEL_ADD_ERROR("CHANNEL_ADD_000001","创建渠道失败",McspPartnerResultCodeEnum.CHANNEL_ADD_ERROR),
    SELLER_NOT_EXIST("001001","合伙人不存在",McspPartnerResultCodeEnum.SELLER_NOT_EXIST),
    SELLER_INVALID("001002","合伙人无效",McspPartnerResultCodeEnum.SELLER_INVALID),
    START_TIME_ERROR("001003","活动还未开始",McspPartnerResultCodeEnum.END_TIME_ERROR),
    END_TIME_ERROR("001004","活动已结束",McspPartnerResultCodeEnum.END_TIME_ERROR),
        ;

    private String resultCode;
    private String resultDesc;
    private McspPartnerResultCodeEnum partnerResultCode; //对应的合伙人结果码（我们自己封装后给到CC2.0的结果码）
    private McspResultCodeEnum(String resultCode, String resultDesc ,McspPartnerResultCodeEnum partnerResultCode) {
        this.resultCode = resultCode;
        this.resultDesc = resultDesc;
        this.partnerResultCode = partnerResultCode;
    }
    public String getResultCode() {
        return resultCode;
    }
    public String getResultDesc() {
        return resultDesc;
    }
    public McspPartnerResultCodeEnum getPartnerResultCode() {
        return partnerResultCode;
    }
    public static McspResultCodeEnum getInstance(String resultCode) {
        for (McspResultCodeEnum item : McspResultCodeEnum.values()) {
            if (item.getResultCode().equals(resultCode)) {
                return item;
            }
        }
        return null;
    }
}
