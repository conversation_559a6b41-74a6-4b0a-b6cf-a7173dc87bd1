package com.yunqu.cc.mixgw.listener;

import com.yq.busi.common.base.ServiceID;
import com.yunqu.cc.mixgw.base.Constants;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;

@WebListener
public class InterfaceLinstener extends ServiceContextListener {

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {

		List<ServiceResource> list = new ArrayList<ServiceResource>();

		//处理与电商的接口
		ServiceResource resource = new ServiceResource();
		resource.appName = Constants.APP_NAME;
		resource.className = "com.yunqu.cc.mixgw.inf.ElecInterfaceService";
		resource.description = "处理与ECM对接的接口,如查询用户访问轨迹、查询客服咨询记录等";
		resource.serviceId = ServiceID.MIXGW_ELEC_INTEFACE;
		resource.serviceName = "处理与ECM对接的接口";
		list.add(resource);

		//处理与ECM的接口
		ServiceResource resource2 = new ServiceResource();
		resource2.appName = Constants.APP_NAME;
		resource2.className = "com.yunqu.cc.mixgw.inf.EcmInterfaceService";
		resource2.description = "处理与ECM对接的接口，如查询用户订单信息";
		resource2.serviceId = ServiceID.MIXGW_ECM_INTEFACE;
		resource2.serviceName = "处理与ECM对接的接口";
		list.add(resource2);

		//处理与洗悦家公众号的接口
		ServiceResource resource3 = new ServiceResource();
		resource3.appName = Constants.APP_NAME;
		resource3.className = "com.yunqu.cc.mixgw.inf.WeiXinCsInterfaceService";
		resource3.description = "处理与洗悦家公众号对接的接口，如查询商品列表";
		resource3.serviceId = ServiceID.MIXGW_WEIXINCS_INTEFACE;
		resource3.serviceName = "处理与ECM对接的接口";
		list.add(resource3);

		//LDAP同步接口
		ServiceResource resource4 = new ServiceResource();
		resource4.appName = Constants.APP_NAME;
		resource4.className = "com.yunqu.cc.mixgw.inf.LDAPService";
		resource4.description = "LDAP同步网关";
		resource4.serviceId = ServiceID.MIXGW_LDAP_INTEFACE;
		resource4.serviceName = "LDAP同步网关";
		list.add(resource4);

		//旗舰店同步接口
		ServiceResource storeList = new ServiceResource();
		storeList.appName = Constants.APP_NAME;
		storeList.className = "com.yunqu.cc.mixgw.inf.StoreListSyncService";
		storeList.description = "旗舰店数据定时同步网关";
		storeList.serviceId = ServiceID.MIXGW_STORE_LIST_SYNC;
		storeList.serviceName = "旗舰店数据同步任务";
		list.add(storeList);

		//美的会员相关接口
		ServiceResource mideaVipService = new ServiceResource();
		mideaVipService.appName = Constants.APP_NAME;
		mideaVipService.className = "com.yunqu.cc.mixgw.inf.MideaVipService";
		mideaVipService.description = "美的会员相关查询接口";
		mideaVipService.serviceId = ServiceID.MIXGW_MEDIA_VIP_INTEFACE;
		mideaVipService.serviceName = "旗舰店数据同步任务";
		list.add(mideaVipService);

		//美的APP相关接口
		ServiceResource mideaAppService = new ServiceResource();
		mideaAppService.appName = Constants.APP_NAME;
		mideaAppService.className = "com.yunqu.cc.mixgw.inf.MideaAppService";
		mideaAppService.description = "美的APP相关接口";
		mideaAppService.serviceId = "MIXGW_MEDIA_APP_INTEFACE";
		mideaAppService.serviceName = "美的APP相关接口";
		list.add(mideaAppService);

		//实施考勤
		ServiceResource empClockService = new ServiceResource();
		empClockService.appName = Constants.APP_NAME;
		empClockService.className = "com.yunqu.cc.mixgw.inf.EmpClockService";
		empClockService.description = "实施考勤相关接口";
		empClockService.serviceId = "MIXGW_MEDIA_TIMECARD_PPINTEFACE";
		empClockService.serviceName = "实施考勤相关接口";
		list.add(empClockService);
		//CCS接口
		ServiceResource CcsService = new ServiceResource();
		CcsService.appName = Constants.APP_NAME;
		CcsService.className = "com.yunqu.cc.mixgw.inf.CcsService";
		CcsService.description = "CCS相关接口";
		CcsService.serviceId = "MIXGW_CCS_INTEFACE";
		CcsService.serviceName = "CCS相关接口";
		list.add(CcsService);
		//企业微信接口
		ServiceResource QyWeiXinService = new ServiceResource();
		QyWeiXinService.appName = Constants.APP_NAME;
		QyWeiXinService.className = "com.yunqu.cc.mixgw.inf.QyWeiXinService";
		QyWeiXinService.description = "CCS相关接口";
		QyWeiXinService.serviceId = "MIXGW_QYWX_INTEFACE";
		QyWeiXinService.serviceName = "CCS相关接口";
		list.add(QyWeiXinService);
		//企业微信接口
		ServiceResource midea4AService = new ServiceResource();
		midea4AService.appName = Constants.APP_NAME;
		midea4AService.className = "com.yunqu.cc.mixgw.inf.Midea4AService";
		midea4AService.description = "4A相关接口";
		midea4AService.serviceId = "MIXGW_4A_INTEFACE";
		midea4AService.serviceName = "4A相关接口";
		list.add(midea4AService);
		//中控
		ServiceResource wcpService = new ServiceResource();
		wcpService.appName = Constants.APP_NAME;
		wcpService.className = "com.yunqu.cc.mixgw.inf.WCPService";
		wcpService.description = "中控相关接口";
		wcpService.serviceId = "MIXGW_WCP_INTEFACE";
		wcpService.serviceName = "中控相关接口";
		list.add(wcpService);

		//大数据
		ServiceResource bigDataService = new ServiceResource();
		bigDataService.appName = Constants.APP_NAME;
		bigDataService.className = "com.yunqu.cc.mixgw.inf.BigDataService";
		bigDataService.description = "大数据相关接口";
		bigDataService.serviceId = "MIXGW_BIGDATA_INTEFACE";
		bigDataService.serviceName = "大数据相关接口";
		list.add(bigDataService);

		//美云销
		ServiceResource mcspService = new ServiceResource();
		mcspService.appName = Constants.APP_NAME;
		mcspService.className = "com.yunqu.cc.mixgw.inf.McspService";
		mcspService.description = "美云销相关接口";
		mcspService.serviceId = "MIXGW_MCSP_INTEFACE";
		mcspService.serviceName = "美云销相关接口";
		list.add(mcspService);

		//美云销VIP
		ServiceResource mcspVipService = new ServiceResource();
		mcspVipService.appName = Constants.APP_NAME;
		mcspVipService.className = "com.yunqu.cc.mixgw.inf.McspVipService";
		mcspVipService.description = "美云销相关接口";
		mcspVipService.serviceId = "MIXGW_MCSP_VIP_INTEFACE";
		mcspVipService.serviceName = "美云销相关接口";
		list.add(mcspVipService);

		//其他
		ServiceResource otherService = new ServiceResource();
		otherService.appName = Constants.APP_NAME;
		otherService.className = "com.yunqu.cc.mixgw.inf.OtherService";
		otherService.description = "混合网关相关接口";
		otherService.serviceId = "MIXGW_OTHER_INTEFACE";
		otherService.serviceName = "混合网关相关接口";
		list.add(otherService);

		//地动仪大数据
		ServiceResource ddyService = new ServiceResource();
		ddyService.appName = Constants.APP_NAME;
		ddyService.className = "com.yunqu.cc.mixgw.inf.DdyInterfaceService";
		ddyService.description = "地动仪大数据相关接口";
		ddyService.serviceId = "MIXGW_DDY_INTEFACE";
		ddyService.serviceName = "地动仪相关接口";
		list.add(ddyService);

		//地动仪大数据
		ServiceResource dsmpService = new ServiceResource();
		dsmpService.appName = Constants.APP_NAME;
		dsmpService.className = "com.yunqu.cc.mixgw.inf.DsmpInterfaceService";
		dsmpService.description = "DSMP相关接口";
		dsmpService.serviceId = "MIXGW_DSMP_INTEFACE";
		dsmpService.serviceName = "DSMP相关接口";
		list.add(dsmpService);

		//voc查询任务
		ServiceResource vocTaskService = new ServiceResource();
		vocTaskService.appName = Constants.APP_NAME;
		vocTaskService.className = "com.yunqu.cc.mixgw.inf.VocSearchTaskService";
		vocTaskService.description = "voc查询任务接口";
		vocTaskService.serviceId = "MIXGW_VOC_SEARCH_TASK_INTERFACE";
		vocTaskService.serviceName = "voc查询任务接口";
		list.add(vocTaskService);

		//ES推送日志
		ServiceResource esPushLogService = new ServiceResource();
		esPushLogService.appName = Constants.APP_NAME;
		esPushLogService.className = "com.yunqu.cc.mixgw.inf.EsService";
		esPushLogService.description = "推送审计日志到ES";
		esPushLogService.serviceId = "ES_PUSH_LOG_SERVICE";
		esPushLogService.serviceName = "推送审计日志到ES";
		list.add(esPushLogService);

		//推送审计日志日志
		ServiceResource auditService = new ServiceResource();
		auditService.appName = Constants.APP_NAME;
		auditService.className = "com.yunqu.cc.mixgw.inf.AuditLogService";
		auditService.description = "推送审计日志";
		auditService.serviceId = "PUSH_AUDIT_LOG_SERVICE";
		auditService.serviceName = "推送审计日志";
		list.add(auditService);
		
		//AIM接口对接
		ServiceResource aimInterfaceService = new ServiceResource();
		aimInterfaceService.appName = Constants.APP_NAME;
		aimInterfaceService.className = "com.yunqu.cc.mixgw.inf.AimInterfaceService";
		aimInterfaceService.description = "超信平台接口";
		aimInterfaceService.serviceId = "MIXGW_AIM_INTEFACE";
		aimInterfaceService.serviceName = "超信平台接口";
		list.add(aimInterfaceService);

		//voc语义匹配接口
		ServiceResource vocMatchService = new ServiceResource();
		vocMatchService.appName = Constants.APP_NAME;
		vocMatchService.className = "com.yunqu.cc.mixgw.inf.VocKeywordMatchService";
		vocMatchService.description = "voc语义匹配接口";
		vocMatchService.serviceId = "MIXGW_VOC_KEYWORD_MATCH_INTERFACE";
		vocMatchService.serviceName = "voc语义匹配接口";
		list.add(vocMatchService);

		//voc情感分析聚类查询接口
		ServiceResource vocAnalysisService = new ServiceResource();
		vocAnalysisService.appName = Constants.APP_NAME;
		vocAnalysisService.className = "com.yunqu.cc.mixgw.inf.VocAnalysisAggregationService";
		vocAnalysisService.description = "voc情感分析聚类查询接口";
		vocAnalysisService.serviceId = "MIXGW_VOC_ANALYSIS_AGGREGATION_INTERFACE";
		vocAnalysisService.serviceName = "voc情感分析聚类查询接口";
		list.add(vocAnalysisService);

		ServiceResource GVOCService = new ServiceResource();
		GVOCService.appName = Constants.APP_NAME;
		GVOCService.className = "com.yunqu.cc.mixgw.inf.GVOCService";
		GVOCService.description = "大数据voc平台接口";
		GVOCService.serviceId = "MIXGW_GVOC_INTEFACE";
		GVOCService.serviceName = "大数据voc平台接口";
		list.add(GVOCService);
		
		ServiceResource DBANService = new ServiceResource();
		DBANService.appName = Constants.APP_NAME;
		DBANService.className = "com.yunqu.cc.mixgw.inf.DbanService";
		DBANService.description = "大数据数据银行平台接口";
		DBANService.serviceId = "MIXGW_DBAN_INTEFACE";
		DBANService.serviceName = "大数据数据银行平台接口";
		list.add(DBANService);
		
		ServiceResource BardServiceService = new ServiceResource();
		BardServiceService.appName = Constants.APP_NAME;
		BardServiceService.className = "com.yunqu.cc.mixgw.inf.BardService";
		BardServiceService.description = "智慧眼平台接口";
		BardServiceService.serviceId = "MIXGW_BARD_INTEFACE";
		BardServiceService.serviceName = "智慧眼平台接口";
		list.add(BardServiceService);

		ServiceResource IopService = new ServiceResource();
		IopService.appName = Constants.APP_NAME;
		IopService.className = "com.yunqu.cc.mixgw.inf.IopService";
		IopService.description = "CC投诉总结接口";
		IopService.serviceId = "MIXGW_IOP_INTERFACE";
		IopService.serviceName = "CC投诉总结接口";
		list.add(IopService);

		//5g视频服务接口
		ServiceResource video5gService = new ServiceResource();
		video5gService.appName = Constants.APP_NAME;
		video5gService.className = "com.yunqu.cc.mixgw.inf.videoService";
		video5gService.description = "5g视频服务接口";
		video5gService.serviceId = "FIVE_G_VIDEO_SERVICE";
		video5gService.serviceName = "5g视频服务接口";
		list.add(video5gService);

		//安德物流接口
		ServiceResource andeService = new ServiceResource();
		andeService.appName = Constants.APP_NAME;
		andeService.className = "com.yunqu.cc.mixgw.inf.AndeService";
		andeService.description = "安德物流接口";
		andeService.serviceId = "MIXGW_ANDE_INTERFACE";
		andeService.serviceName = "安德物流接口";
		list.add(andeService);

		//安德物流接口
		ServiceResource qualityService = new ServiceResource();
		qualityService.appName = Constants.APP_NAME;
		qualityService.className = "com.yunqu.cc.mixgw.inf.QualityService";
		qualityService.description = "调用内部质检接口";
		qualityService.serviceId = "MIXGW_INSIDE_QUALITY_INTERFACE";
		qualityService.serviceName = "调用内部质检接口";
		list.add(qualityService);

		ServiceResource HighValueService = new ServiceResource();
		HighValueService.appName = Constants.APP_NAME;
		HighValueService.className = "com.yunqu.cc.mixgw.inf.HighValueUserVipService";
		HighValueService.description = "高价值用户vip信息查询接口";
		HighValueService.serviceId = "MIXGW_HIGH_VALUE_USER_VIP";
		HighValueService.serviceName = "高价值用户vip信息查询接口";
		list.add(HighValueService);

		return list;
	}

}
