package com.yunqu.cc.mixgw.inf;

import java.net.URLEncoder;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;

import com.yunqu.cc.mixgw.base.CommonMcspLogger;
import com.yunqu.cc.mixgw.enums.McspMediaPayAccountStateEnum;
import com.yunqu.cc.mixgw.enums.McspPartnerResultCodeEnum;
import com.yunqu.cc.mixgw.enums.McspResultCodeEnum;
import com.yunqu.cc.mixgw.utils.McspUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.mixgw.base.CommonElecLogger;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
/**
 * 处理电商的接口
 */
public class ElecInterfaceService extends IService{

	private Logger logger = CommonElecLogger.logger;
    private static Logger longTime = CommonLogger.getCommLogger("longTime");

	/**
	 * 所有请求入口
	 */
	public JSONObject invoke(JSONObject json) throws ServiceException {
		String command = json.getString("command");
		long startTime = System.currentTimeMillis();
		try {
			//电商查询客户咨询简录，电商通过uniterface入口，访问到该接口
			if(ServiceCommand.MIXGW_ELEC_CONSULT_RECORD.equals(command)){
				return consultReocrd(json);
			}
			
			//业务系统从电商查询客户的访问轨迹，其他模块可以直接调用该IService接口
			if(ServiceCommand.MIXGW_ELEC_GET_ELEC_BROWSED_ITEM.equals(command)){
				
				return getElecBrowsedItem(json);
			}
			if("getdealdetail".equals(command)){//商城订单查询
				return getdealdetail(json);
			}
			
			if("getusercouponinfo".equals(command)){//商城优惠券
				return getusercouponinfo(json);
			}
			if("getcompactdetail".equals(command)){//商品信息 https://sitm.midea.cn/next/detail_o/compactdetail
				return getcompactdetail(json);
			}
//			if("genshortscheme".equals(command)){//商城链接获取
//				return genshortscheme(json);
//			}
			if("genshortscheme".equals(command)){//商城链接获取
				return longToShort4MiniApp(json);
			}
			
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 无法识别的操作类型:"+command);
			
			JSONObject result = JsonUtil.createInfRespJson(json);
			result.put("respDesc", "不可识别的command");
			return result;
			
		} finally {
			long endTime = System.currentTimeMillis();
			long elapsedTime = endTime - startTime;
			longTime.info(command+"接口"+elapsedTime);
		}
	}

	/**
	 * 调用方向：其他业务模块->mixgw->电商
	 * 业务系统从电商查询客户的访问轨迹，其他模块可以直接调用该IService接口
	 * @param json
	 * @return
	 */
	private JSONObject getElecBrowsedItem(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		try {
			String uid = json.getString("uid");
			//非空判断
			if(StringUtils.isBlank(uid) ){
				result.put("respDesc", "请求参数 uid 不能为空!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求参数不能为空,uid="+uid);
				return result;
			}
			String url = Constants.ELEC_BROWSED_ITME_URL;
			if(StringUtils.isBlank(url)){
				result.put("respDesc", "电商接口url未配置!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 电商接口url未配置!");
				return result;
			}
			
					
			//系统根据电商的接口规范，查询数据
			StringBuffer params = new StringBuffer();
			params.append("appid=").append(Constants.ELEC_APPID);      //美的电商分配的开发者帐号id
//			params.append("&bizargs=").append(URLEncoder.encode("{\"uin\":"+uid+"}",GWConstants.ENCODE_UTF8));
			params.append("&bizargs=").append("{\"uin\":"+uid+"}");
			params.append("&nonceid=").append(Constants.ELE_NOTICEID);  //notice 仅包含数字和小写字母的字符串  电商未提供生成方式
			params.append("&source=").append(Constants.ELEC_SOURCE);
			params.append("&version=").append(Constants.ELEC_VERSION);
			
			//拼接上key，进行加密，生成签名串
			String sign = params.toString()+"&key="+Constants.ELEC_APPKEY;
			sign = SecurityUtil.encryptMsgByMD5(sign);
			
			params.append("&sign=").append(sign); //通过Md5计算得到的签名  电商未提供生成方式
			
			//根据配置的url,拼接上参数
			if(url.indexOf("?")==-1){
				url = url += "?"+params.toString();
			}else{
				url = url += "&"+params.toString();
			}
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求电商浏览轨迹:"+url);
			//调用电商接口
			HttpResp resp = HttpUtil.post(url, "", GWConstants.ENCODE_UTF8);
			
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 返回电商浏览轨迹:"+resp.getResult());
			
			if(resp.success()){
				//加入异常处理，避免解析时出错
				try {
					JSONObject rj = JsonUtil.toJSONObject(resp.getResult());
					int errorCode = rj.getInteger("errcode"); //为0时成功
					if(errorCode==0){
						JSONArray resutlArray =  new JSONArray();
						
						JSONObject data1 = rj.getJSONObject("data");
						JSONObject data2 = data1.getJSONObject("data");
						
						//用户手机号码，可以收集到客户资料里
						String userMobile  = data2.getString("UserMobile");
						
						JSONArray array = data2.getJSONArray("ItemList");
						if(array!=null && array.size()>0){
							for(int i=0 ; i<array.size(); i++){
								JSONObject elecJson = array.getJSONObject(i);
								JSONObject newJson = new JSONObject();
								newJson.put("title", elecJson.getString("Title"));
								newJson.put("itemUrl", elecJson.getString("ItemUrl"));
								newJson.put("picUrl", elecJson.getString("PicUrl"));
								try {
									if(elecJson.get("CreateTime")!=null&&!"".equals(elecJson.getString("CreateTime"))){
										long it = new Long(elecJson.getString("CreateTime")+"000");
									    Date date = new Date(it);
									    SimpleDateFormat simpleDateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
									    String createTime = simpleDateFormat.format(date);
										newJson.put("createTime", createTime);
									}else{
										newJson.put("createTime", "");
									}
								} catch (Exception e) {
									logger.error(CommonUtil.getClassNameAndMethod(this)+" 返回电商浏览轨迹CreateTime解析出错:"+e.getMessage());
									newJson.put("createTime", "");
								}
								resutlArray.add(newJson);
							}
						}
						
						//写入浏览记录集合到响应中
						result.put("browseItems", resutlArray);
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						result.put("respDesc", "查询成功");
						
					}else{
						logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询电商的用户交流轨迹，电商接口返回错误:"+resp.getResult());
						result.put("respDesc", "查询电商的用户交流轨迹，电商接口返回错误");
						return result;
					}
				} catch (Exception e) {
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询电商的用户交流轨迹，解析接口返回数据出错:"+e.getMessage()+","+resp.getResult(),e);
					result.put("respDesc", "查询电商的用户交流轨迹，解析接口返回数据出错");
					return result;
				}
				
			}else{
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询电商的用户交流轨迹出错:"+resp.getResult());
				result.put("respDesc", "查询电商的用户交流轨迹出错");
				return result;
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询电商的用户交流轨迹出现异常:"+e.getMessage(),e);
		}
		return result;
	}

	/**
	 * 调用方向：电商->uniterface->mixgw
	 * 电商查询客户咨询简录，电商通过uniterface入口，访问到该接口
	 * @param json
	 * @return
	 */
	private JSONObject consultReocrd(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		
		String keyword = json.getString("keyword");
		String beginTime = json.getString("beginTime");
		String endTime = json.getString("endTime");
		
		//非空判断
		if(StringUtils.isBlank(keyword) ||StringUtils.isBlank(beginTime) ||StringUtils.isBlank(endTime)){
			result.put("respDesc", "请求参数不能为空!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求参数不能为空,keyword="+keyword+",beginTime="+beginTime+",endTime="+endTime);
			return result;
		}
		try {
			//要返回的结果数组
			JSONArray array = new JSONArray();
			
			//查询出符合条件的记录
			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME	, Constants.DS_NAME);
			EasySQL sql = new EasySQL();
			sql.append(" SELECT CREATE_ACC,CREATE_NAME,CREATE_TIME FROM C_OL_CONSULT_ORDER T WHERE 1=1 ");
			sql.append(" AND (T.CUST_ID=? OR T.CUST_PHONE=?) ");
			sql.append(" AND T.CREATE_TIME>=? AND T.CREATE_TIME<=? ORDER BY T.CREATE_TIME DESC ");
			List<EasyRow> list = query.queryForList(sql.getSQL(), new String[]{keyword,keyword,beginTime,endTime});
			if(CommonUtil.listIsNotNull(list)){
				for(EasyRow row : list){
					String createAcc = row.getColumnValue("CREATE_ACC");
					String createTime = row.getColumnValue("CREATE_TIME");
					String createName = row.getColumnValue("CREATE_NAME");
					JSONObject j = new JSONObject();
					j.put("agentAcc", createAcc);
					j.put("agentName", createName);
					j.put("consultTime", createTime);
					array.add(j);
				}
				result.put("consultRecord", array);
			}
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询咨询记录异常:"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理出现异常");
		}
		return result;
	}
	/**
	 * 
	 * 查询订单
	 * @param json
	 * @return
	 */
	private JSONObject getdealdetail(JSONObject json) {
		String url = Constants.ELEC_GET_DEAL_DETAIL_URL;
		JSONObject result = JsonUtil.createInfRespJson(json);
		if(StringUtils.isBlank(url)){
			result.put("respDesc", "电商查询订单接口url未配置!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 电商接口url未配置!");
			return result;
		}
		
		String bizargs = json.getString("bizargs");
		StringBuffer params = new StringBuffer();
		params.append("appid=").append(Constants.ELEC_APPID);      //美的电商分配的开发者帐号id
		params.append("&bizargs=").append(bizargs);
		params.append("&nonceid=").append(Constants.ELE_NOTICEID);  //notice 仅包含数字和小写字母的字符串  电商未提供生成方式
		params.append("&source=").append(Constants.ELEC_SOURCE);
		params.append("&version=").append(Constants.ELEC_VERSION);
		String sign = params.toString()+"&key="+Constants.ELEC_APPKEY;

		sign = SecurityUtil.encryptMsgByMD5(sign);
		
		params.append("&sign=").append(sign); //通过Md5计算得到的签名  电商未提供生成方式
		
		//根据配置的url,拼接上参数
		if(url.indexOf("?")==-1){
			url = url += "?"+params.toString();
		}else{
			url = url += "&"+params.toString();
		}
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求电商订单查询接口:"+url);
		//调用电商接口
		//HttpResp resp1 = HttpUtil.post(url, "", GWConstants.ENCODE_UTF8);
		
		HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
//		logger.info(CommonUtil.getClassNameAndMethod(this)+" post返回电商订单查询接口的:"+resp1.getResult());

		logger.info(CommonUtil.getClassNameAndMethod(this)+" 返回电商订单查询接口的:"+resp.getResult());
		try {
			if(resp.success()){
				//加入异常处理，避免解析时出错
					JSONObject rj = JsonUtil.toJSONObject(resp.getResult());
					int returncode = rj.getInteger("resultcode"); //为0时成功
					int resultcode = rj.getInteger("resultcode"); //为0时成功
					if(returncode==0&&resultcode==0){
						result.put("respCode", GWConstants.RET_CODE_SUCCESS);
						result.put("respDesc", "查询成功");
						result.put("respData", JSON.parseObject(resp.getResult()));
					}else{
						logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询记录失败异常:"+resp.getResult());
						result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
						result.put("respDesc", "处理出现异常");
					}
				}else{
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询记录失败异常:"+resp.getException());
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "处理出现异常");
				}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询咨询记录异常:"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理出现异常");
		}
		return result;
	}
	public static void main(String[] args) {
		JSONObject json=new JSONObject();
		json.put("CouponCode", "A0001907156BE31E84D2");
		json.put("Uin", "1072509");
		String url = "https://m.midea.cn/next/mfop_deal/genshortscheme";
		JSONObject result = JsonUtil.createInfRespJson(json);
		String bizargs = json.getString("bizargs");
		StringBuffer params = new StringBuffer();
		params.append("appid=").append("96e22a46056724e3d48dffab471ad493");      //美的电商分配的开发者帐号id
		params.append("&bizargs=").append(bizargs);
		params.append("&nonceid=").append("1");  //notice 仅包含数字和小写字母的字符串  电商未提供生成方式
		params.append("&source=").append("1");
		params.append("&version=").append("1");
		String sign = params.toString()+"&key="+"32dab1c4f0598c34";
		
		sign = SecurityUtil.encryptMsgByMD5(sign);
		
		params.append("&sign=").append(sign); //通过Md5计算得到的签名  电商未提供生成方式
		
		JSONObject jsonObject =new JSONObject();
		jsonObject.put("appid", "96e22a46056724e3d48dffab471ad493");
		jsonObject.put("bizargs", bizargs);
		jsonObject.put("nonceid", "1");
		jsonObject.put("source", "1");
		jsonObject.put("version", "1");
		jsonObject.put("sign", sign);

		//根据配置的url,拼接上参数
		if(url.indexOf("?")==-1){
			url = url += "?"+params.toString();
		}else{
			url = url += "&"+params.toString();
		}
		HttpResp resp = HttpUtil.post(url, "", "UTF-8");
		HttpResp resp1 = HttpUtil.post(url, jsonObject.toJSONString(), "UTF-8");
		HttpResp resp2 = HttpUtil.sendGet(url, "", "UTF-8");
		System.out.println(resp);
	}
	/**
	 * 查询用户优惠券
	 * @param json
	 * @return
	 */
	private JSONObject getusercouponinfo(JSONObject json) {
		String url = Constants.ELEC_GET_COUPON_INFO_URL;
		JSONObject result = JsonUtil.createInfRespJson(json);
		if(StringUtils.isBlank(url)){
			result.put("respDesc", "电商查询用户优惠券接口url未配置!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 电商查询用户优惠券接口url未配置!");
			return result;
		}
		String bizargs = json.getString("bizargs");
		StringBuffer params = new StringBuffer();
		params.append("appid=").append(Constants.ELEC_APPID);      //美的电商分配的开发者帐号id
		params.append("&bizargs=").append(bizargs);
		params.append("&nonceid=").append(Constants.ELE_NOTICEID);  //notice 仅包含数字和小写字母的字符串  电商未提供生成方式
		params.append("&source=").append(Constants.ELEC_SOURCE);
		params.append("&version=").append(Constants.ELEC_VERSION);
		String sign = params.toString()+"&key="+Constants.ELEC_APPKEY;
		
		sign = SecurityUtil.encryptMsgByMD5(sign);
		
		params.append("&sign=").append(sign); //通过Md5计算得到的签名  电商未提供生成方式
		
		JSONObject jsonObject =new JSONObject();
		jsonObject.put("appid", Constants.ELEC_APPID);
		jsonObject.put("bizargs", bizargs);
		jsonObject.put("nonceid", Constants.ELE_NOTICEID);
		jsonObject.put("source", Constants.ELEC_SOURCE);
		jsonObject.put("version", Constants.ELEC_VERSION);
		jsonObject.put("sign", sign);
		//根据配置的url,拼接上参数
		if(url.indexOf("?")==-1){
			url = url += "?"+params.toString();
		}else{
			url = url += "&"+params.toString();
		}
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求查询用户优惠券:"+url);
		//调用电商接口
//		HttpResp resp = HttpUtil.post(url, "", GWConstants.ENCODE_UTF8);
		//HttpResp resp1 = HttpUtil.post(url, jsonObject.toJSONString(), GWConstants.ENCODE_UTF8);
		HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);

		logger.info(CommonUtil.getClassNameAndMethod(this)+" 返回查询用户优惠券:"+resp.getResult());
		try {
			if(resp.success()){
				//加入异常处理，避免解析时出错
				JSONObject rj = JsonUtil.toJSONObject(resp.getResult());
				int returncode = rj.getInteger("returncode"); //为0时成功
				int resultcode = rj.getInteger("resultcode"); //为0时成功
				if(returncode==0&&resultcode==0){
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					result.put("respData", JSON.parseObject(resp.getResult()));
				}else{
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询查询用户优惠券失败异常:"+resp.getResult());
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "处理出现异常");
				}
			}else{
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询查询用户优惠券失败异常:"+resp.getException());
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "处理出现异常");
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询查询用户优惠券异常:"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理出现异常");
		}
		return result;
	}
	
	/**
	 * 商品信息接口 获取商品包装信息，类目信息，基本信息 活动信息 优惠券信息
	 * @param json
	 * @return
	 */
	private JSONObject getcompactdetail(JSONObject json) {

		JSONObject result=new JSONObject();
		String url = Constants.ELEC_COMPACT_DETAIL_URL;
		url+="?itemid="+json.getString("itemid");
//		url="http://sitm.midea.cn/next/detail_o/compactdetail?itemid=1010001050100494298";
//			 https://sitm.midea.cn/next/detail_o/compactdetail?itemid=1010001050100494298
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求查询商品详情:"+url);
		//调用电商接口
//		HttpResp resp = HttpUtil.post(url, "", GWConstants.ENCODE_UTF8);
		HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
		//HttpResp resp1 = HttpUtil.post(url, jsonObject.toJSONString(), GWConstants.ENCODE_UTF8);
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" get查询商品详情订单:"+resp.getResult());
		try {
			if(resp.success()){
				//加入异常处理，避免解析时出错
				JSONObject rj = JsonUtil.toJSONObject(resp.getResult());
	
				int returncode = rj.getInteger("errcode"); //为0时成功
				int resultcode = rj.getInteger("errCode"); //为0时成功
				if(returncode==0&&resultcode==0){
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					result.put("respData", rj);
				}else{
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询商品详情失败异常:"+resp.getResult());
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "处理出现异常");
				}
			}else{
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询商品详情失败异常:"+resp.getException());
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "处理出现异常");
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询商品详情异常:"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理出现异常");
		}
	/*	String jsonString="{\"data\":{\"productInfo\":{\"litemid\":103205,\"mapwxskuinfo\":{\"298\":{\"lSkuid\":100156,\"strSkuTitle\":\"\",\"strSellingPoint\":\"三档调速，液晶显示，低速萃取，低噪出汁，多重安全保护\",\"strSellingPointShort\":\"\",\"lBasePrice\":85900,\"lDistributorId\":100494,\"nDisShopId\":50795606,\"nSupplierId\":100400,\"vecPicInfoList\":[{\"lSkuId\":100156,\"lPicIndex\":1,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/471385078e3e9d13A13630/471385078e3e9d13A13630.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":2,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/8891058f945d4c43A13631/8891058f945d4c43A13631.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":3,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/fc766d4e2d30409dA13643/fc766d4e2d30409dA13643.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":4,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/3071c43df1a68c09A13644/3071c43df1a68c09A13644.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":5,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/b9fb30b334a0c068A13638/b9fb30b334a0c068A13638.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":6,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/7652080f3be3356bA13633/7652080f3be3356bA13633.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":7,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/d7b6c1f9a259637aA13640/d7b6c1f9a259637aA13640.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":8,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/93a540842bc2d528A13648/93a540842bc2d528A13648.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"},{\"lSkuId\":100156,\"lPicIndex\":9,\"lPicType\":1,\"strPicSizes\":\"120*120,280*280,310*310,430*430,530*530,\",\"strPicDesc\":\"\",\"lPicProperty\":0,\"strLocalPicRelativePath\":\"https://img.mdcdn.cn/ImageStore/100156/pic/cb2b61ec225f155fA13645/cb2b61ec225f155fA13645.jpg\",\"lAddTime\":0,\"lLastModifyTime\":0,\"lPicState\":1,\"strImageSize\":\"1200*1200\"}],\"strCommodityDetail\":{\"13\":[{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/81c291571925d293A7715/81c291571925d293A7715.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*5862\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}},{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/1e5468e396ebf087A8480/1e5468e396ebf087A8480.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*3824\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}},{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/f4dd0e36353e3e38A7760/f4dd0e36353e3e38A7760.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*4595\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}},{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/f195ecdca6cfcdceA5280/f195ecdca6cfcdceA5280.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*2511\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}},{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/c0d541cf28c5de83A3792/c0d541cf28c5de83A3792.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*4306\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}}],\"14\":[{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/1381f41ce356f8cbA2744/1381f41ce356f8cbA2744.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*4999\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}},{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/1426583483175394A24184/1426583483175394A24184.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}}],\"15\":[{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/f259c5f26f3cbe4fA3085/f259c5f26f3cbe4fA3085.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"790*4087\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}},{\"templateId\":4,\"data\":{\"picUrl\":\"https://img.mdcdn.cn/ImageStore/100156/detail/1426583573024629A24198/1426583573024629A24198.jpg?x-oss-process=image/resize,limit_0,w_790/quality,Q_50\",\"picSize\":\"\",\"videoUrl\":\"\",\"title\":\"\",\"smalltitle\":\"\",\"thumbnailUrl\":\"\",\"title1\":\"\",\"title2\":\"\",\"desc\":\"\"}}],\"16\":[],\"17\":[],\"18\":[],\"19\":[]},\"strDisSkuTitle\":\"【后台自动化Yazmin专用勿动！！】 【美的】【测试商品】榨汁机（原汁机） MJ-JS15E2\",\"lDisPrice\":22300,\"nStockNum\":0,\"nLockNum\":0,\"nCalStock\":2092,\"nStock\":0,\"strSpec\":\"{\\\"colorName\\\":\\\"玫瑰金\\\",\\\"specName\\\":\\\"45555555\\\"}\",\"lCategoryId\":51,\"oWxoPkgInfo\":{\"strCategoryName\":\"\",\"nCategoryId\":0,\"strPackageList\":\"主机*1、豆腐盒*1、清洁刷*1、说明书*1、 接汁杯*1、接渣杯*1、推料棒*1\",\"lNetLength\":200,\"lNetWidth\":150,\"lNetHeight\":430,\"lNetWeight\":4300,\"lPkgLength\":285,\"lPkgWidth\":220,\"lPkgHeight\":525,\"lPkgWeight\":4900},\"oWxoskubasic\":{\"strColourName\":\"金色\",\"strVoltage\":\"220\",\"strPowerRating\":\"200\",\"strProductFunction\":\"\",\"strModel\":\"MJ-JS15E2\",\"strBrandName\":\"Midea/美的\",\"lMarketTime\":1414771200,\"strItemOrigin\":\"\",\"lBrandId\":1},\"oWxNavExInfo\":{\"vecAttrdic\":[{\"nAttrId\":78,\"nMetaId\":51,\"strName\":\"放置方式\",\"nProperty\":1,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":78,\"nOptionId\":247,\"lProperty\":0,\"nOrder\":1,\"strName\":\"立式\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":183,\"nMetaId\":51,\"strName\":\"档位数\",\"nProperty\":1,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":183,\"nOptionId\":1053,\"lProperty\":0,\"nOrder\":6,\"strName\":\"三档\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":193,\"nMetaId\":51,\"strName\":\"材质\",\"nProperty\":513,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":193,\"nOptionId\":656,\"lProperty\":0,\"nOrder\":6,\"strName\":\"其他/other\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":195,\"nMetaId\":51,\"strName\":\"附件功能\",\"nProperty\":1,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":195,\"nOptionId\":1041,\"lProperty\":0,\"nOrder\":14,\"strName\":\"混合\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":264,\"nMetaId\":51,\"strName\":\"容量（mL）\",\"nProperty\":1536,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":264,\"nOptionId\":0,\"lProperty\":0,\"nOrder\":0,\"strName\":\"450\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":265,\"nMetaId\":51,\"strName\":\"果肉渣滓盒容量（mL）\",\"nProperty\":1024,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":265,\"nOptionId\":0,\"lProperty\":0,\"nOrder\":0,\"strName\":\"1000\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":266,\"nMetaId\":51,\"strName\":\"加料口形状\",\"nProperty\":1,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":266,\"nOptionId\":1027,\"lProperty\":0,\"nOrder\":2,\"strName\":\"圆形\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":267,\"nMetaId\":51,\"strName\":\"转速（转/分）\",\"nProperty\":1024,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":267,\"nOptionId\":0,\"lProperty\":0,\"nOrder\":0,\"strName\":\"52\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":268,\"nMetaId\":51,\"strName\":\"刀片材质\",\"nProperty\":1,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":268,\"nOptionId\":1050,\"lProperty\":0,\"nOrder\":5,\"strName\":\"无刀片\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"},{\"nAttrId\":269,\"nMetaId\":51,\"strName\":\"果渣盒类型\",\"nProperty\":1,\"nPAttrId\":0,\"nOrder\":0,\"vecOptions\":[{\"nAttrId\":269,\"nOptionId\":1057,\"lProperty\":0,\"nOrder\":1,\"strName\":\"分离式果渣盒\",\"strPinYin\":\"\"}],\"strIconURL\":\"\"}],\"vecNavRoute\":[{\"nNavId\":10034,\"nMapId\":1,\"nPNavId\":0,\"strName\":\"厨房小电器\",\"nDesc\":0,\"nOrder\":4,\"lProperty\":0,\"strSearch\":\"(9|16|42|43|44|47|48|49|51|54|71|55|41|85|113|52|131)\"},{\"nNavId\":10045,\"nMapId\":1,\"nPNavId\":10034,\"strName\":\"果汁机/料理机\",\"nDesc\":0,\"nOrder\":11,\"lProperty\":0,\"strSearch\":\"(51|85)\"}]},\"nQuotaNum\":0,\"strItemCode\":\"21064010000018\",\"strNewItemCode\":\"21064010000018\",\"strErpItemCode\":\"\",\"nFaqModuleId\":50,\"strFaqModuleName\":\"果汁机常见问题\",\"strFaqModuleDesc\":\"果汁机常见问题\",\"vecFaqInfoList\":[{\"nFaqId\":433,\"nModuleId\":50,\"nFaqOrder\":1,\"strQuestion\":\"用果汁机能柞甘蔗汁吗？\",\"strAnswer\":\"可以的,但柞的时候,甘蔗要剥皮,块要切得小些。\"},{\"nFaqId\":434,\"nModuleId\":50,\"nFaqOrder\":2,\"strQuestion\":\"内置式果汁机操作注意事项\",\"strAnswer\":\"1、“连续启动”的“一周次”为：开50秒停一分钟为一个周次！\\n 2、“瞬间启动”的“一周次”为：开10秒停3秒为一个周次！ \\n在工作时不要超过这个周期，以免产生高温保护。\"}],\"nPolicyId\":12,\"strPolicyName\":\"生活电器包修服务政策\",\"strPolicyDesc\":\"美的生活电器免费包修政策及包修凭证说明\",\"vecPolicyInfoList\":[{\"nClauseId\":13,\"nPolicyId\":12,\"nClauseOrder\":1,\"strClauseName\":\"生活电器包修服务政策\",\"strDetail\":\"1.生活电器全系列产品实行国家“三包”政策，售后服务实行全国联保，24小时服务热线400-8899-315。\\n2.全系列产品享受1年整机保修，高端智能电饭煲（IH系列电饭煲）享受3年整机保修。电磁炉八大部件（变压器、电感线圈、EMC电容、传感器、互感器、数码管、液晶显示器、蜂鸣器）享受6年保修。\\n3.高端智能电饭煲（IH系列电饭煲）、GLORY系列电磁炉、IH系列电压力锅、豆浆机服务方式为上门服务方式，其余产品为送修服务方式。承诺上门服务的产品上门服务区域仅限地级以上城市市区。\\n4.包修期界定：以消费者收到产品或安装日期做为包修期的起始日期，无法提供收到产品或安装日期的，以有效包修凭证开具日期作为包修期的起始日期，无有效包修凭证的，按出厂日期后6个月作为包修期的起始日期；若既无有效包修凭证也无出厂日期，则按该产品的最早上市日期后6个月作为包修期的起始日。\\n5.包修凭证：全系列产品售后服务有效包修凭证为商用发票、机打小票或有效收据。\\n6.包装箱以外所有附件及安装辅材由消费者自费购买，自购材料不包修，网点提供的安装材料包修一年。\\n7.网购机享受正常的产品包修政策，自收到产品之日起七日内退货，且无需说明理由，产品退货网购商负责（产品及其附件必须完好无损坏）； 新购机在15天内发生的换货服务由网购商负责，属于网购商承诺的其他服务内容由网购商负责。\\n8.饭勺、量杯、电源线、按摩垫外表皮套或布套以及海绵等属附件不在包修范围内。\\n9.对整机包修期外、主件包修期内的维修服务，如果同时出现主件和非主件坏，则不收取维修费、上门费和主件配件费，但收取已坏非主件配件费。\\n10.未经售后服务部同意，各分部、经销商、网购商、服务商自行承诺的额外服务由其自行承担。\\n11.以上售后服务政策只适用于2014年1月1日以后销售的产品，前期销售的产品按销售时的售后服务政策，具体详情请参考说明书。\\n12.特殊情况（如新品首发）政策，以具体首发时发布政策为准。\\n\\n一、保外收费标准\\n保外：指用户产品在包修范围外，网点向用户提供收费服务，由用户自行支付维修费。\\n（一）\\t维修、检测（单位：元/台）\\n维修费标准\\n1、产品类别：机械电饭煲、电饭锅、电火锅、电蒸锅、电炖锅（盅）、煎烤机、面包机、按摩电器、豆浆机、果汁机、电水壶、个人护理产品、电热水瓶\\t （检测费：15元/台 维修费20元/台）\\n2、产品类别：智能电饭煲、商用电饭煲、\"}],\"strInstructionsUrl\":\"http://121.40.35.141/ImageStore/100156/instructions/7b1121a993f770e382b4d8ec9f0bef2d.pdf\",\"strInstructionsName\":\"MJ-JS15E2榨汁机（原汁机） 说明书.pdf\",\"strInstructionsUrlPC\":\"\",\"strInstructionsUrlM\":\"\",\"nSkuReferPrice\":69900,\"lSkuProperty\":49152,\"nOriginalPrice\":22300,\"oActiveInfo\":{\"lIcSkuId\":0,\"lDisSkuId\":0,\"lDistributorId\":0,\"lActiveId\":0,\"lItemPropertyMask\":0,\"nItemStatus\":0,\"nActiveStatus\":0,\"lPreheatTime\":0,\"lStartTime\":0,\"lEndTime\":0,\"nQuota\":0,\"lDiscount\":0,\"lDiscountFee\":0,\"lActiveFee\":0,\"nActiveType\":0,\"strActiveTitle\":\"\",\"strActiveTag\":\"\",\"nCloseTime\":0,\"vecUserType\":null,\"nIsDisplayActiveFee\":0,\"lActivePropertyMask\":0,\"strCouponBatch\":\"\",\"lEarnest\":0,\"lSoldStock\":0,\"lTotalStock\":0,\"lEarnestStartTime\":0,\"lEarnestEndTime\":0,\"lFinalMoneyStartTime\":0,\"lFinalMoneyEndTime\":0,\"nIsDisplayNormalBuy\":0,\"lFinalMoney\":0,\"lPreLockStock\":0,\"lWmallStock\":0,\"lMobileDiscount\":0,\"lMobileDiscountFee\":0,\"lMobileActiveFee\":0,\"vecSteps\":null,\"nStepType\":0,\"strActiveDesc\":\"\",\"lHBFreeInstallments\":0,\"lFixedStock\":0,\"strFinalExposePrice\":\"\",\"strMobileFinalExposePrice\":\"\",\"strPreselFinalExposePrice\":\"\",\"vecChannels\":null,\"vecPreSellStages\":null,\"strActiveText\":\"\",\"strPromotionalCopy\":\"\",\"strPromotionalLink\":\"\",\"nStockType\":0},\"nDetailType\":1,\"strPromoTitle\":\"\",\"strPromoTitleShort\":\"\",\"strPromoUrl\":\"\",\"lNormalPrice\":0,\"vecPkgInfos\":[{\"strCategoryName\":\"果汁机（榨汁机）\",\"nCategoryId\":51,\"strPackageList\":\"主机*1、豆腐盒*1、清洁刷*1、说明书*1、 接汁杯*1、接渣杯*1、推料棒*1\",\"lNetLength\":200,\"lNetWidth\":150,\"lNetHeight\":430,\"lNetWeight\":4300,\"lPkgLength\":285,\"lPkgWidth\":220,\"lPkgHeight\":525,\"lPkgWeight\":4900}],\"strPromPicName\":\"\",\"strPromPicUrl\":\"\",\"vecOtherActives\":[{\"lIcSkuId\":0,\"lDisSkuId\":0,\"lDistributorId\":100494,\"lActiveId\":207,\"lItemPropertyMask\":0,\"nItemStatus\":0,\"nActiveStatus\":3,\"lPreheatTime\":0,\"lStartTime\":1610676000,\"lEndTime\":1625021400,\"nQuota\":0,\"lDiscount\":0,\"lDiscountFee\":0,\"lActiveFee\":0,\"nActiveType\":5,\"strActiveTitle\":\"满三件赠送一\",\"strActiveTag\":\"\",\"nCloseTime\":0,\"vecUserType\":[1],\"nIsDisplayActiveFee\":0,\"lActivePropertyMask\":0,\"strCouponBatch\":\"\",\"lEarnest\":0,\"lSoldStock\":0,\"lTotalStock\":0,\"lEarnestStartTime\":0,\"lEarnestEndTime\":0,\"lFinalMoneyStartTime\":0,\"lFinalMoneyEndTime\":0,\"nIsDisplayNormalBuy\":0,\"lFinalMoney\":0,\"lPreLockStock\":0,\"lWmallStock\":0,\"lMobileDiscount\":0,\"lMobileDiscountFee\":0,\"lMobileActiveFee\":0,\"vecSteps\":[{\"lStepId\":233,\"lActivityId\":207,\"nStepType\":2,\"nState\":0,\"lStepValue\":1,\"lExchangePrice\":0},{\"lStepId\":232,\"lActivityId\":207,\"nStepType\":2,\"nState\":0,\"lStepValue\":2,\"lExchangePrice\":0}],\"nStepType\":2,\"strActiveDesc\":\"满三件赠送一\",\"lHBFreeInstallments\":0,\"lFixedStock\":0,\"strFinalExposePrice\":\"\",\"strMobileFinalExposePrice\":\"\",\"strPreselFinalExposePrice\":\"\",\"vecChannels\":null,\"vecPreSellStages\":null,\"strActiveText\":\"\",\"strPromotionalCopy\":\"\",\"strPromotionalLink\":\"\",\"nStockType\":0},{\"lIcSkuId\":0,\"lDisSkuId\":0,\"lDistributorId\":100494,\"lActiveId\":196,\"lItemPropertyMask\":0,\"nItemStatus\":0,\"nActiveStatus\":3,\"lPreheatTime\":0,\"lStartTime\":1604720100,\"lEndTime\":1764801000,\"nQuota\":0,\"lDiscount\":0,\"lDiscountFee\":0,\"lActiveFee\":0,\"nActiveType\":6,\"strActiveTitle\":\"zx换购\",\"strActiveTag\":\"\",\"nCloseTime\":0,\"vecUserType\":[1],\"nIsDisplayActiveFee\":0,\"lActivePropertyMask\":0,\"strCouponBatch\":\"\",\"lEarnest\":0,\"lSoldStock\":0,\"lTotalStock\":0,\"lEarnestStartTime\":0,\"lEarnestEndTime\":0,\"lFinalMoneyStartTime\":0,\"lFinalMoneyEndTime\":0,\"nIsDisplayNormalBuy\":0,\"lFinalMoney\":0,\"lPreLockStock\":0,\"lWmallStock\":0,\"lMobileDiscount\":0,\"lMobileDiscountFee\":0,\"lMobileActiveFee\":0,\"vecSteps\":[{\"lStepId\":222,\"lActivityId\":196,\"nStepType\":2,\"nState\":0,\"lStepValue\":2,\"lExchangePrice\":100}],\"nStepType\":2,\"strActiveDesc\":\"zx活动换购快来\",\"lHBFreeInstallments\":0,\"lFixedStock\":0,\"strFinalExposePrice\":\"\",\"strMobileFinalExposePrice\":\"\",\"strPreselFinalExposePrice\":\"\",\"vecChannels\":null,\"vecPreSellStages\":null,\"strActiveText\":\"\",\"strPromotionalCopy\":\"\",\"strPromotionalLink\":\"\",\"nStockType\":0}],\"strFiid\":\"1010001050100494298\",\"nPcPrice\":22300,\"nMobilePrice\":22300,\"strFsid\":\"1010001050100494\",\"oPostFreeFee\":{\"lPostModuleId\":0,\"lDistributorId\":0,\"nPostModuleType\":0,\"nPostAmount\":0},\"oPostFee\":{\"lPostModuleId\":0,\"lDistributorId\":0,\"nPostModuleType\":0,\"nPostAmount\":0},\"TagInfo\":{\"TitleTag\":{\"TitleTag\":\"\",\"TitleProperty\":\"\",\"TitleActive\":\"\"},\"TagHtmlList\":[\"\\u003cdiv class='sku_tag sku_tag_important'\\u003e满赠\\u003c/div\\u003e\",\"\\u003cdiv class='sku_tag sku_tag_important'\\u003e换购\\u003c/div\\u003e\"],\"SingleTagHtml\":\"\\u003cdiv class='sku_tag sku_tag_important'\\u003e满赠\\u003c/div\\u003e\",\"SinglePropertyTagHtml\":\"\",\"SingleActiveTagHtml\":\"\\u003cdiv class='sku_tag sku_tag_important'\\u003e满赠\\u003c/div\\u003e\",\"TagNameList\":[\"满赠\",\"换购\"],\"TagColorList\":[\"#ff6600\",\"#ff6600\"],\"SingleTag\":{\"TagName\":\"满赠\",\"TagColor\":\"#ff6600\"},\"SingleActiveTag\":{\"TagName\":\"满赠\",\"TagColor\":\"#ff6600\"}},\"strDeliveryText\":\"\",\"strDeliveryLinkUrl\":\"\",\"oPriceProtect\":null,\"strStoreAddrShort\":\"\",\"strNewPromoPicPCArea1\":\"\",\"strNewPromoPicPcArea2\":\"\",\"strNewPromoPicH5Area1\":\"\",\"strNewPromoPicH5Area2\":\"\",\"lProMemPrice\":0,\"lCategoryProperty\":2049,\"nSecKillSituation\":0,\"oInsurance\":null,\"nSkuType\":0,\"lSupSkuStockPro\":0,\"bDeliveryFlag\":false,\"bDisplayPkgInfosFlag\":true,\"vipList\":null,\"strPresellTag\":\"\",\"nShowOriginalPrice\":0,\"lPropertyCode\":0,\"strSingleActiveTagHtml\":\"\",\"lOld2NewDiscountFee\":10000,\"nBestPriceByCoupon\":0,\"nP4BD\":18300,\"nBestCouponBatchId\":2057,\"lActiveFlag\":49152,\"oVideoInfo\":null,\"oMainPic\":{\"strMainPicUrl\":\"\",\"lMainPicStartTime\":0,\"lMainPicEndTime\":0,\"strMainPicLabelUrl\":\"\",\"lMainPicLabelStartTime\":0,\"lMainPicLabelEndTime\":0,\"strMainPicLabelLinkUrl\":\"\"},\"newPromoPicH5Area1\":null,\"newPromoPicH5Area2\":null,\"newPromoPicPCArea1\":null,\"newPromoPicPCArea2\":null,\"SalerCommissionRate\":0,\"EstimateCommission\":\"0.00\",\"bShowBuyBtnFlag\":true,\"oDetailVideoTop\":null,\"oDetailVideoBottom\":null,\"strNewPromoPicH5Area3\":\"\",\"strNewPromoPicPCArea3\":\"\",\"newPromoPicH5Area3\":null,\"newPromoPicPCArea3\":null}},\"strSpecDict\":\"{\\\"colorName\\\":[\\\"玫瑰金\\\"],\\\"specName\\\":[\\\"45555555\\\"]}\",\"nState\":2,\"lDisSkuId\":298,\"addrStateText\":\"有货\",\"buyStateText\":\"立即购买\",\"activeList\":[{\"activeItemId\":2,\"activeItemNameShort\":\"满赠\",\"activeItemName\":\"满赠\",\"activeItemTitle\":\"满赠\",\"activeItemDesc\":\"满三件赠送一\",\"activeItemUrl\":\"https://w.midea.com/search/active_list?act_id=207\\u0026fsid=1010001050100494\",\"activeId\":207,\"activeType\":5,\"fsidFront\":\"1010001050100494\",\"activeItemDescList\":null},{\"activeItemId\":2,\"activeItemNameShort\":\"换购\",\"activeItemName\":\"换购\",\"activeItemTitle\":\"换购\",\"activeItemDesc\":\"zx活动换购快来\",\"activeItemUrl\":\"https://w.midea.com/search/active_list?act_id=196\\u0026fsid=1010001050100494\",\"activeId\":196,\"activeType\":6,\"fsidFront\":\"1010001050100494\",\"activeItemDescList\":null}],\"packagelistUrl\":\"\",\"isOfficial\":true,\"HBInstallment\":{\"activeItemName\":\"花呗分期\",\"activeItemDesc\":\"该商品支持分期支付\",\"HBInstallmentRate\":{\"12\":750,\"3\":230,\"6\":450}},\"oSpecDict\":{\"colorName\":[\"玫瑰金\"],\"specName\":[\"45555555\"],\"colorPicMap\":null},\"isToshiba\":false,\"isFenXiao\":false,\"domain\":\"sitm.midea.cn\",\"lActId4SwitchSpec\":207,\"nActType4SwitchSpec\":5,\"isZgPackageExist\":0},\"trialDetail\":{\"reportContent\":\"\",\"reportImg\":\"\",\"reportTitle\":\"\",\"reportId\":0,\"time\":\"\",\"userName\":\"\",\"headImg\":\"\"},\"commentCount\":0,\"couponList\":[{\"nCouponBatchId\":2057,\"strCouponTitle\":\"每满减\",\"strCouponDetail\":\"\",\"nCouponType\":3,\"nCouponMethod\":4,\"nGetType\":3,\"nMaxDiscountFee\":10000,\"nLimitPrice\":5000,\"nMaxDiscountRate\":0,\"strShopList\":\"\",\"strCategoryList\":\"\",\"strDisSkuIdList\":\"\",\"nLimitNum\":0,\"nTotalNum\":0,\"nExpireType\":1643548860,\"nExpireInterval\":0,\"lExpireTime\":1643548860,\"lStartTime\":1610507040,\"nColorType\":1,\"lDistributorId\":100494,\"nState\":0,\"lCreateTime\":0,\"lModifyTime\":0,\"nAlreadyObtain\":0,\"nExpireState\":0,\"strDistributorShopName\":\"\",\"lObtainId\":************,\"nProperty\":14,\"lObtainExpireTime\":1643472000,\"nObtainExpireState\":3,\"lObtainStartTime\":1610467200,\"nObtainChannel\":4,\"nObtainState\":1,\"nMinPrice4Rate\":0,\"strCouponDes\":\"每满50减10\",\"nMinBuyNum\":0,\"nBuyNum\":5,\"nEveryDiscountFee\":1000,\"strCouponTxtByMember\":\"黄金会员\",\"nCouponTxtType\":3,\"strExchangeId\":\"\",\"lCostScore\":0,\"strBrandList\":\"\"},{\"nCouponBatchId\":2001,\"strCouponTitle\":\"wl折扣券--供货商承担\",\"strCouponDetail\":\"\",\"nCouponType\":3,\"nCouponMethod\":2,\"nGetType\":3,\"nMaxDiscountFee\":0,\"nLimitPrice\":20000,\"nMaxDiscountRate\":880,\"strShopList\":\"\",\"strCategoryList\":\"\",\"strDisSkuIdList\":\"\",\"nLimitNum\":0,\"nTotalNum\":0,\"nExpireType\":1631243460,\"nExpireInterval\":0,\"lExpireTime\":1631243460,\"lStartTime\":1605859740,\"nColorType\":3,\"lDistributorId\":100494,\"nState\":0,\"lCreateTime\":0,\"lModifyTime\":0,\"nAlreadyObtain\":0,\"nExpireState\":0,\"strDistributorShopName\":\"\",\"lObtainId\":************,\"nProperty\":2,\"lObtainExpireTime\":1620267000,\"nObtainExpireState\":3,\"lObtainStartTime\":1606443660,\"nObtainChannel\":10,\"nObtainState\":1,\"nMinPrice4Rate\":0,\"strCouponDes\":\"8.8折最高减200元\",\"nMinBuyNum\":0,\"nBuyNum\":0,\"nEveryDiscountFee\":0,\"strCouponTxtByMember\":\"\",\"nCouponTxtType\":0,\"strExchangeId\":\"\",\"lCostScore\":0,\"strBrandList\":\"\"},{\"nCouponBatchId\":1959,\"strCouponTitle\":\"ny-套系减满\",\"strCouponDetail\":\"\",\"nCouponType\":3,\"nCouponMethod\":1,\"nGetType\":3,\"nMaxDiscountFee\":6000,\"nLimitPrice\":10000,\"nMaxDiscountRate\":0,\"strShopList\":\"\",\"strCategoryList\":\"\",\"strDisSkuIdList\":\"\",\"nLimitNum\":0,\"nTotalNum\":0,\"nExpireType\":1797436800,\"nExpireInterval\":0,\"lExpireTime\":1797436800,\"lStartTime\":1607443200,\"nColorType\":1,\"lDistributorId\":100494,\"nState\":0,\"lCreateTime\":0,\"lModifyTime\":0,\"nAlreadyObtain\":0,\"nExpireState\":0,\"strDistributorShopName\":\"\",\"lObtainId\":195917091104,\"nProperty\":64,\"lObtainExpireTime\":1796659200,\"nObtainExpireState\":3,\"lObtainStartTime\":1607356800,\"nObtainChannel\":10,\"nObtainState\":1,\"nMinPrice4Rate\":0,\"strCouponDes\":\"满100减60\",\"nMinBuyNum\":2,\"nBuyNum\":0,\"nEveryDiscountFee\":0,\"strCouponTxtByMember\":\"\",\"nCouponTxtType\":0,\"strExchangeId\":\"\",\"lCostScore\":0,\"strBrandList\":\"\"},{\"nCouponBatchId\":2120,\"strCouponTitle\":\"满50减20\",\"strCouponDetail\":\"\",\"nCouponType\":3,\"nCouponMethod\":1,\"nGetType\":3,\"nMaxDiscountFee\":2000,\"nLimitPrice\":6000,\"nMaxDiscountRate\":0,\"strShopList\":\"\",\"strCategoryList\":\"\",\"strDisSkuIdList\":\"\",\"nLimitNum\":0,\"nTotalNum\":0,\"nExpireType\":1619781540,\"nExpireInterval\":0,\"lExpireTime\":1619781540,\"lStartTime\":1615884720,\"nColorType\":1,\"lDistributorId\":100494,\"nState\":0,\"lCreateTime\":0,\"lModifyTime\":0,\"nAlreadyObtain\":0,\"nExpireState\":0,\"strDistributorShopName\":\"\",\"lObtainId\":************,\"nProperty\":4,\"lObtainExpireTime\":0,\"nObtainExpireState\":3,\"lObtainStartTime\":0,\"nObtainChannel\":10,\"nObtainState\":1,\"nMinPrice4Rate\":0,\"strCouponDes\":\"满60减20\",\"nMinBuyNum\":0,\"nBuyNum\":0,\"nEveryDiscountFee\":0,\"strCouponTxtByMember\":\"\",\"nCouponTxtType\":0,\"strExchangeId\":\"\",\"lCostScore\":0,\"strBrandList\":\"\"},{\"nCouponBatchId\":2108,\"strCouponTitle\":\"sh品牌品类券2\",\"strCouponDetail\":\"\",\"nCouponType\":1,\"nCouponMethod\":1,\"nGetType\":3,\"nMaxDiscountFee\":2000,\"nLimitPrice\":12000,\"nMaxDiscountRate\":0,\"strShopList\":\"\",\"strCategoryList\":\"\",\"strDisSkuIdList\":\"\",\"nLimitNum\":0,\"nTotalNum\":0,\"nExpireType\":1619712000,\"nExpireInterval\":0,\"lExpireTime\":1619712000,\"lStartTime\":1612281600,\"nColorType\":1,\"lDistributorId\":100494,\"nState\":0,\"lCreateTime\":0,\"lModifyTime\":0,\"nAlreadyObtain\":0,\"nExpireState\":0,\"strDistributorShopName\":\"\",\"lObtainId\":************,\"nProperty\":0,\"lObtainExpireTime\":1619712000,\"nObtainExpireState\":3,\"lObtainStartTime\":1612108800,\"nObtainChannel\":10,\"nObtainState\":1,\"nMinPrice4Rate\":0,\"strCouponDes\":\"满120减20\",\"nMinBuyNum\":0,\"nBuyNum\":0,\"nEveryDiscountFee\":0,\"strCouponTxtByMember\":\"\",\"nCouponTxtType\":0,\"strExchangeId\":\"\",\"lCostScore\":0,\"strBrandList\":\"1,2,3,6,10\"},{\"nCouponBatchId\":1991,\"strCouponTitle\":\"砖石会员清洗券\",\"strCouponDetail\":\"\",\"nCouponType\":3,\"nCouponMethod\":1,\"nGetType\":3,\"nMaxDiscountFee\":1000,\"nLimitPrice\":10000,\"nMaxDiscountRate\":0,\"strShopList\":\"\",\"strCategoryList\":\"\",\"strDisSkuIdList\":\"\",\"nLimitNum\":0,\"nTotalNum\":0,\"nExpireType\":1640876520,\"nExpireInterval\":0,\"lExpireTime\":1640876520,\"lStartTime\":1606204920,\"nColorType\":1,\"lDistributorId\":100494,\"nState\":0,\"lCreateTime\":0,\"lModifyTime\":0,\"nAlreadyObtain\":0,\"nExpireState\":0,\"strDistributorShopName\":\"\",\"lObtainId\":199193718931,\"nProperty\":16,\"lObtainExpireTime\":0,\"nObtainExpireState\":3,\"lObtainStartTime\":0,\"nObtainChannel\":10,\"nObtainState\":4,\"nMinPrice4Rate\":0,\"strCouponDes\":\"满100减10\",\"nMinBuyNum\":0,\"nBuyNum\":0,\"nEveryDiscountFee\":0,\"strCouponTxtByMember\":\"钻石会员\",\"nCouponTxtType\":2,\"strExchangeId\":\"\",\"lCostScore\":0,\"strBrandList\":\"\"}],\"itemaboutinfo\":{\"ServiceSupportInfo\":null,\"AccessoryInfo\":null,\"EWarrantyInfo\":null,\"LiveBroadcastInfo\":null,\"SupSpProjectInfo\":null,\"ActiveSubscribeInfo\":{\"nActiveSubscribeState\":0,\"lStartTime\":0}},\"skuDiscountInfo\":{\"skuDiscountFee\":0,\"displayType\":0,\"skuProActiveDiscountFee\":0,\"skuProCouponDiscountFee\":0,\"proCouponInfo\":null,\"pro10TimesScoreValue\":0,\"historyTotalDiscountFee\":0,\"proWillExpireInterval\":0},\"proGuide\":{\"strDiscountTextHead\":\"开通PRO会员，立享全场家电270元券礼包\",\"strDiscountTextBody\":\"\",\"strDiscountTextTail\":\"\",\"strBtnText\":\"立即开卡\",\"bHasDiscount\":false},\"saveMoneyData\":null,\"isUserLogin\":false,\"uin\":0,\"oUserInfo\":null,\"scoreCouponList\":null,\"userScore\":0,\"willSendScore\":22,\"scoreTimes\":1,\"isShopSaler\":false},\"errCode\":\"0\",\"errMsg\":\"\",\"errcode\":\"0\",\"errmsg\":\"\"}";
		JSONObject rj =JSON.parseObject(jsonString);
		int returncode = rj.getInteger("errcode"); //为0时成功
		int resultcode = rj.getInteger("errCode"); //为0时成功
		if(returncode==0&&resultcode==0){
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
			result.put("respData", rj);
		}*/
		return result;
	}
	/**
	 * 商城链接获取
	 * @param json
	 * @return
	 */
	private JSONObject genshortscheme(JSONObject json) {
		String url = Constants.ELEC_GEN_SHORT_SCHEME;
		JSONObject result = JsonUtil.createInfRespJson(json);
		if(StringUtils.isBlank(url)){
			result.put("respDesc", "商城链接获取接口url未配置!");
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 商城链接获取接口url未配置!");
			return result;
		}
		String bizargs = json.getString("bizargs");
		StringBuffer signParams = new StringBuffer();
		signParams.append("appid=").append(Constants.ELEC_APPID);      //美的电商分配的开发者帐号id
		signParams.append("&bizargs=").append(bizargs);
		signParams.append("&nonceid=").append(Constants.ELE_NOTICEID);  //notice 仅包含数字和小写字母的字符串  电商未提供生成方式
		signParams.append("&source=").append(Constants.ELEC_SOURCE);
		signParams.append("&version=").append(Constants.ELEC_VERSION);
		String sign = signParams.toString()+"&key="+Constants.ELEC_APPKEY;
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 商城链接获取加密前:"+sign);
		sign = SecurityUtil.encryptMsgByMD5(sign);
		
		StringBuffer params = new StringBuffer();
		params.append("appid=").append(Constants.ELEC_APPID);      //美的电商分配的开发者帐号id
		String encodeBizargs=URLEncoder.encode(bizargs);
		params.append("&bizargs=").append(encodeBizargs);
		params.append("&nonceid=").append(Constants.ELE_NOTICEID);  //notice 仅包含数字和小写字母的字符串  电商未提供生成方式
		params.append("&source=").append(Constants.ELEC_SOURCE);
		params.append("&version=").append(Constants.ELEC_VERSION);
		params.append("&sign=").append(sign); //通过Md5计算得到的签名  电商未提供生成方式
		

		JSONObject jsonObject =new JSONObject();
		jsonObject.put("appid", Constants.ELEC_APPID);
		jsonObject.put("bizargs", bizargs);
		jsonObject.put("nonceid", Constants.ELE_NOTICEID);
		jsonObject.put("source", Constants.ELEC_SOURCE);
		jsonObject.put("version", Constants.ELEC_VERSION);
		jsonObject.put("sign", sign);
		if(url.indexOf("?")==-1){
			url = url += "?"+params.toString();
		}else{
			url = url += "&"+params.toString();
		}
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求商城链接获:"+url);
		//调用电商接口
		HttpResp resp = HttpUtil.sendGet(url, "", GWConstants.ENCODE_UTF8);
		
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 返回商城链接获:"+resp.getResult());
		try {
			if(resp.success()){
				//加入异常处理，避免解析时出错
				JSONObject rj = JsonUtil.toJSONObject(resp.getResult());
				int resultCode = rj.getInteger("resultCode"); //为0时成功
				if(resultCode==0||resultCode==10102115){
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");
					result.put("respData", JSON.parseObject(resp.getResult()));
				}else{
					logger.info(CommonUtil.getClassNameAndMethod(this)+" 商城链接获失败异常:"+resp.getResult());
					result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
					result.put("respDesc", "处理出现异常");
				}
			}else{
				logger.info(CommonUtil.getClassNameAndMethod(this)+" 商城链接获失败异常:"+resp.getException());
				result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
				result.put("respDesc", "处理出现异常");
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询查询用户优惠券异常:"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理出现异常");
		}
		return result;
	}

	/**
	 * 商城链接获取（2024年12月智慧家短链改造）
	 * @param json
	 * @return
	 */
	private JSONObject longToShort4MiniApp(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		JSONObject bizargs = json.getJSONObject("bizargs");
		String longUrl = bizargs.getString("page_path");
		String phone = bizargs.getString("seller_mobile");
		try {
			JSONObject restParams = new JSONObject();
			restParams.put("mobile", phone);
			JSONObject params = new JSONObject();
			params.put("restParams", restParams);
			String resp = null;
			String url = Constants.getMcspURL() + Constants.GET_SM_BY_PHONE;
			resp = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, Constants.getMcspUcAppkey(), Constants.getMcspUcAppSecret());
			CommonMcspLogger.logger.info("商城链接获取前先通过坐席手机号获取对应的合伙人sm,url=" + url + ",请求参数=" +  params.toJSONString() + ",结果：" + resp);
			JSONObject jsonObject = JSONObject.parseObject(resp);
			String resultCode = McspResultCodeEnum.OK.getResultCode(); //合伙人状态，根据下面两个错误码封装后给CC2.0那边使用的状态
			String resultDesc = McspResultCodeEnum.OK.getResultDesc();
			String shortResultCode = "0"; //美云销接口响应码，具体查看枚举-McspResultCodeEnum
			int mideaPayAcctState = 0; //美的合伙人付账号状态，具体查看枚举-McspMediaPayAccountStateEnum
			if (jsonObject != null) {
				shortResultCode = jsonObject.getString("code");
				resultCode = shortResultCode;
				if (jsonObject.containsKey("data") && jsonObject.getJSONObject("data") != null){
					mideaPayAcctState = jsonObject.getJSONObject("data").getIntValue("mideaPayAcctState");
					String smToken = jsonObject.getJSONObject("data").getString("smToken");
					if (StringUtils.isNotBlank(smToken)) {
						if (longUrl.contains("?")) {
							longUrl = longUrl + "&smShare=" + smToken;
						} else {
							longUrl = longUrl + "?smShare=" + smToken;
						}
					}
				}

				//获取合伙人结果
				McspPartnerResultCodeEnum mcspPartnerResultCodeEnum = null;
				McspResultCodeEnum mcspResultCodeEnum = McspResultCodeEnum.getInstance(StringUtils.stripStart(shortResultCode,"mtc-seller-backend-COM-"));
				if (mcspResultCodeEnum != null){
					mcspPartnerResultCodeEnum = mcspResultCodeEnum.getPartnerResultCode();
				}
				//获取合伙人付账号状态
				McspMediaPayAccountStateEnum mcspMediaPayAccountStateEnum = McspMediaPayAccountStateEnum.getInstance(mideaPayAcctState);
				if (mcspMediaPayAccountStateEnum != null){
					//如果合伙人存在，但是合伙人账号状态异常，则返回该异常
					if (McspResultCodeEnum.OK.getResultCode().equals(resultCode) && !McspResultCodeEnum.OK.getResultCode().equals(mcspMediaPayAccountStateEnum.getPartnerResultCode().getResultCode())){
						mcspPartnerResultCodeEnum = mcspMediaPayAccountStateEnum.getPartnerResultCode();
					}
				}
				if (mcspPartnerResultCodeEnum != null){
					resultCode = mcspPartnerResultCodeEnum.getResultCode();
					resultDesc = mcspPartnerResultCodeEnum.getResultDesc();
				}
			}
			restParams.remove("mobile");
			restParams.put("appId", Constants.MCSP_MEDIA_APPID);
			restParams.put("longLinkUrl", longUrl);
			restParams.put("effectiveTimeStart", DateUtil.getCurrentDateStr());
			restParams.put("effectiveTimeEnd", "2099-12-31 23:59:59");
			restParams.put("tenantCode", Constants.MIDEA_TENANT_CODE);
			restParams.put("createdBy", "cc");
			params.put("restParams", restParams);
			url = Constants.getMcspURL() + Constants.LONG_TO_SHORT;
			resp = McspUtil.post(url, params.toJSONString(), Constants.MCSP_AK_TYPE_JWT, Constants.getMcspUcAppkey(), Constants.getMcspUcAppSecret());
			CommonMcspLogger.logger.info("商城链接获取（2024年12月智慧家短链改造）url=" + url + ",请求参数=" +  params.toJSONString() + ",结果：" + resp);
			JSONObject parseObject = JSONObject.parseObject(resp);
			parseObject.put("resultCode",resultCode);
			parseObject.put("resultDesc",resultDesc);
			parseObject.put("shortResultCode",shortResultCode);
			parseObject.put("mideaPayAcctState",mideaPayAcctState);
			result.put("respData", parseObject);
			result.put("respCode", GWConstants.RET_CODE_SUCCESS);
			result.put("respDesc", "查询成功");
		} catch (Exception e) {
			CommonMcspLogger.logger.error(CommonUtil.getClassNameAndMethod(this)+" ,商城链接获取（2024年12月智慧家短链改造）:"+e.getMessage(),e);
			result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
			result.put("respDesc", "处理出现异常");
		}
		return result;
	}

}
