package com.yunqu.cc.mixgw.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.JsonUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yunqu.cc.mixgw.base.CommonLogger;
import com.yunqu.cc.mixgw.base.Constants;
import com.yunqu.cc.mixgw.utils.CsUtil;
import com.yunqu.cc.mixgw.utils.GVOCSignUtil;
import com.yunqu.cc.mixgw.utils.HttpClientUtil;
import com.yunqu.cc.mixgw.utils.McspUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import java.util.Date;
import java.util.SortedMap;
import java.util.TreeMap;

/**
 * 高价值获取会员信息接口
 * <AUTHOR>
 *
 */
public class HighValueUserVipService extends IService{
	
    private static Logger logger = CommonLogger.getCommLogger("gjzVip");
    private static Logger longTime = CommonLogger.getCommLogger("longTime");

	private String funcUrl = "";
	
	@Override
	public JSONObject invoke(JSONObject resqJson) throws ServiceException {
		String command = resqJson.getString("command");
		long startTime = System.currentTimeMillis();
		try {
			if("getVipInfo".equals(command)){ //提供查VIP信息 包括 colmo 美粉
				return getVipInfo(resqJson);
			}else{
				JSONObject result = new JSONObject();
				result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
				result.put("respDesc", "不存在的command,请检查！");
				return result;
			}
		} finally {
			long endTime = System.currentTimeMillis();
			long elapsedTime = endTime - startTime;
			longTime.info(command+"接口"+elapsedTime);
		}
	}
	/**
	 * 根据openid、手机号码、uid等查询用户的会员等级
	 * @param json
	 * @return
	 */
	private JSONObject getVipInfo(JSONObject json) {
		JSONObject result = JsonUtil.createInfRespJson(json);
		try {
			String openId = json.getString("openId");
			String brand = json.getString("brand");  //品牌 1-美的 2-小天鹅
			String mobile = json.getString("mobile");
			String uid = json.getString("uid");
			//非空判断
			if(StringUtils.isBlank(mobile) && StringUtils.isBlank(uid)){
				result.put("respDesc", "请求参数 mobile、uid 至少有一个不能为空!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求参数 openId、mobile、uid 至少有一个不能为空! openId="+openId
						+",mobile="+mobile+",uid="+uid);
				return result;
			}
			//非空判断
			if(StringUtils.isBlank(brand)){
				result.put("respDesc", "请求参数 brand 不能为空!");
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 请求参数 brand不能为空!");
				return result;
			}

			logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询会员等级,请求json:"+json.toJSONString());

			String url = Constants.getMcspURL() + "/api/cms_api/mcsp-uc-member/member/getMemberInfo.do";

			JSONObject paramJson = new JSONObject();
			paramJson.put("brand", brand);
			paramJson.put("sourceSys", "CC");

			String levelName = "";
			String feelevelName = "";
			String levelImgUrl = "";

			if(StringUtils.isNotBlank(mobile)){
				paramJson.put("mobile", mobile);
			}

			if(StringUtils.isBlank(mobile)&&StringUtils.isNotBlank(openId)){
				paramJson.put("openId", openId);
				paramJson.put("wxmp", "5");
			}
			if(StringUtils.isBlank(mobile)&&StringUtils.isBlank(openId)&&StringUtils.isNotBlank(uid)){
				paramJson.put("uid", uid);
			}

			JSONObject restParams=new JSONObject();
			restParams.put("restParams", paramJson);
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求获取美的会员等级信息，请求信息:"+url+",请求参数:"+restParams.toJSONString());
			//调用美云销接口
			String resp = McspUtil.post(url, restParams.toJSONString(), Constants.MCSP_AK_TYPE_JWT, Constants.getMcspUcAppkey(), Constants.getMcspUcAppSecret());
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 请求获取美的会员等级信息，信息返回:"+resp);


			//加入异常处理，避免解析时出错
			try {
				JSONObject rj = JSON.parseObject(resp);
				String code = rj.getString("code");
				if("000000".equals(code)){
					JSONObject dataJson = rj.getJSONObject("data");
					if(dataJson==null) {
						logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询会员等级,接口返回无数据");
						result.put("respDesc", "查询美的会员等级,接口返回data为空");
						return result;
					}
					if("1".equals(brand)){
						levelName = dataJson.getString("mfansLevelName");
						levelImgUrl = dataJson.getString("mfansLevelPic");
					}else{
						levelName = dataJson.getString("levelName");
						levelImgUrl = dataJson.getString("levelImgUrl");
					}
					feelevelName="";
					if(dataJson.get("paymentValidDate")!=null){
						String paymentValidDate=dataJson.getString("paymentValidDate");//时间戳
						if(brand.equals("1")&&dataJson.get("paymentValidDate")!=null&&!"".equals(dataJson.getString("paymentValidDate"))){
							try {
								long time = System.currentTimeMillis();
								long paymentValidDateLong = Long.valueOf(paymentValidDate).longValue();
								Date systime = new Date(time);
								Date validDate = new Date(paymentValidDateLong);
								if(validDate.compareTo(systime)>0){
									feelevelName="PRO会员";
								}else{
									feelevelName="";//非PRO会员
								}
							} catch (Exception e) {
								logger.info(CommonUtil.getClassNameAndMethod(this)+" 处理PRO会员信息失败:"+e.getMessage());
							}
						}
					}
					logger.info("feelevelName:"+feelevelName);

					//写入记录到响应中
					result.put("levelName", levelName);
					result.put("feelevelName", feelevelName);
					result.put("levelImgUrl", levelImgUrl);
					result.put("respCode", GWConstants.RET_CODE_SUCCESS);
					result.put("respDesc", "查询成功");

				}else{
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询美的会员等级,接口返回错误:"+resp);
					result.put("respDesc", "查询美的会员等级,接口返回错误:"+code+","+rj.getString("msg"));
					return result;
				}
			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询美的会员等级,解析接口返回数据出错:"+e.getMessage()+","+resp,e);
				result.put("respDesc", "查询美的会员等级,解析接口返回数据出错");
				return result;
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询美的会员等级出现异常:"+e.getMessage(),e);
		}
		logger.info(CommonUtil.getClassNameAndMethod(this)+" 查询会员等级,返回json:"+result.toJSONString());
		return result;
	}




}
