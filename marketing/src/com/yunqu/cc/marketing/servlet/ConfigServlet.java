package com.yunqu.cc.marketing.servlet;

import java.sql.SQLException;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import com.yunqu.openapi.utils.OpenApiUserUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.marketing.base.AppBaseServlet;
import com.yunqu.cc.marketing.base.Constants;
import com.yunqu.cc.marketing.log.CommLogger;
import com.yunqu.cc.marketing.utils.StringUtil;

/**
 * 配置数据操作servlet
 * <AUTHOR>
 *
 */
@WebServlet(urlPatterns = {"/servlet/config/*","/openServlet/config/*"})
@MultipartConfig
public class ConfigServlet extends AppBaseServlet{
	private Logger logger = CommLogger.logger;
	private static final long serialVersionUID = -6541915693708204184L;
	 protected static EasyQuery getDSQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	  }
	
	public  JSONObject  actionForUpdateNo(){
		JSONObject json = this.getJSONObject();
		try {
			JSONObject data =new JSONObject();
			data.put("PRODCODE",json.getString("prodCode"));
			data.put("BRANDCODE",json.getString("brandCode"));
			data.put("ORGCODE",json.getString("orgCode"));
			data.put("no",json.getString("no"));
			
			data.put("CREATE_ACC",OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
			EasyRecord record1 = new EasyRecord("C_NO_CUSTOM_HOT_PROD","PRODCODE","BRANDCODE","ORGCODE","CREATE_ACC").setColumns(data);
			getDSQuery().update(record1);
			return EasyResult.ok("", "操作成功！");
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.error(500, e.getMessage());
		}
		
	}
	public  JSONObject  actionForSaveSMS(){
		JSONObject data = this.getJSONObject("marketingSMS");
		
		try {
			data.put("UPDATE_TIME", EasyDate.getCurrentDateString());
			data.put("UPDATE_ACC",OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
			if(StringUtils.isBlank(data.getString("ID"))){
				String sql="select  count(1) from C_NO_MARKETING_SMS where user_acc=?";
				int queryForInt = getQuery().queryForInt(sql, new Object[]{data.get("USER_ACC")});
				if(queryForInt!=0){
					return EasyResult.error(500, "该坐席已有配置，请勿重复配置");
				}
				data.put("ID",RandomKit.randomStr());
				data.put("CREATE_TIME", EasyDate.getCurrentDateString());
				data.put("CREATE_ACC", OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
				EasyRecord record = new EasyRecord("C_NO_MARKETING_SMS").setColumns(data);
				getQuery().save(record);
			}else{
				data.remove("USER_ACC");
				EasyRecord record = new EasyRecord("C_NO_MARKETING_SMS","ID").setColumns(data);
				getQuery().update(record);
			}
			return EasyResult.ok("", "操作成功！");
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.error(500, e.getMessage());
		}
		
	}
	/**
	 * 删除
	 * @return
	 */
	public JSONObject actionForDeleteSMS(){
		JSONObject surname = new JSONObject();
		String id = this.getJsonPara("id");
		try {
			surname.put("ID",id);
			EasyRecord record = new EasyRecord("C_NO_MARKETING_SMS", "ID").setColumns(surname);
			this.getQuery().deleteById(record);
			logger.info("删除成功，id= "+id);
			return EasyResult.ok(record,"删除成功!");
		} catch (SQLException e) {
			logger.error("[SQLException] 删除失败，原因：",e);
			return EasyResult.error(501, "删除失败，原因：" + e.getMessage());
		}
	}
	
	public  JSONObject actionForGetSMSConetnt(){
		String sql="select  CONTENT from C_NO_MARKETING_SMS where user_acc=?";
		 JSONObject queryForRow;
		 JSONObject result=new JSONObject();
		 boolean userPhone=true;//坐席的手机号是否存在
		try {
			queryForRow = getQuery().queryForRow(sql, new Object[]{OpenApiUserUtil.getUser(this.getRequest()).getUserAcc()}, new JSONMapperImpl());
			if(queryForRow==null){
				return EasyResult.error(500, "无发送权限" );
			}
			String content=queryForRow.getString("CONTENT");
			logger.info("配置内容 "+content);
			if(content.indexOf("{URL}")>-1){
				sql="SELECT PHONENUM FROM  C_YG_EMPLOYEE where USER_ACC= ?";	
				queryForRow = getQuery().queryForRow(sql, new Object[]{OpenApiUserUtil.getUser(this.getRequest()).getUserAcc()}, new JSONMapperImpl());
				JSONObject parmas = new JSONObject();
				try {
					String phone =queryForRow.getString("PHONENUM");
					if(StringUtil.isNull(phone)){
						phone="11111111111";
						userPhone=false;
					}
					HttpServletRequest request = this.getRequest();
					JSONObject bizargs = new JSONObject();
					bizargs.put("page_path", ConfigUtil.getString(Constants.APP_NAME,"GENSHORTSCHEME_PAGEPATH"));
					//bizargs.put("sellerMobile", queryForRow.getString("PHONENUM"));
					bizargs.put("seller_mobile", phone);
					bizargs.put("mtag", ConfigUtil.getString(Constants.APP_NAME,"MTAG"));
					IService service = ServiceContext.getService(ServiceID.MIXGW_ELEC_INTEFACE);
					parmas.put("command","genshortscheme");
					parmas.put("bizargs",bizargs);
					logger.info(String.format("电商链接获取入参为:",JSON.toJSONString(parmas)));
					JSONObject respDate = service.invoke(parmas);
					logger.info(String.format("电商链接获取返回为: ",JSON.toJSONString(respDate)));
					JSONObject respData = respDate.getJSONObject("respData");
					String respCode = respDate.getString("respCode");
					if ("000".equals(respCode)) {// "000" 成功
						if("000000".equals(respData.getString("code"))){
							String shortScheme = respData.getJSONObject("data").getString("shortLinkUrl");
							content=content.replace("{URL}", " " + shortScheme.replace("https://", "") + " ");
							result.put("resultCode", respData.getString("resultCode"));//0-正常手机号，10102115-未找到合伙人信息
						} else{
							return EasyResult.error(500, "电商链接获取异常" );
						}
					}else{
						return EasyResult.error(500, "电商链接获取异常:"+respDate.getJSONObject("respData"));
					}
				} catch (Exception e) {
					logger.error(String.format("电商链接获取异常! 请求入参为:%s ,接口响应错误信息为:%s",JSON.toJSONString(parmas),e.getMessage()));
					logger.error(CommonUtil.getClassNameAndMethod(this)+"电商链接获取异常，原因："+ e.getMessage()+"",e);
					return EasyResult.error(500, "电商链接获取异常" );

				}
			}
			result.put("userPhone", userPhone);//坐席手机号是否存在
			 if(!"".equals(content)){
				 result.put("content", content);
				 return EasyResult.ok(result);
			 }else{
					return EasyResult.error(500, "无发送权限" );
			 }
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.error(500, "无发送权限" );

		}
	}

	/**
	 * 	获取短信模板、参数以及url
	 */
	public  JSONObject actionForGetSMSConetntById(){
		String sql="select * from C_NO_SHORTSCHEME_SMS where ID=?";
		JSONObject queryForRow;
		JSONObject result=new JSONObject();
		boolean userPhone=true;//坐席的手机号是否存在
		logger.info("keyId:" + this.getJSONObject().getString("keyId"));
		try {
			queryForRow = getQuery().queryForRow(sql, new Object[]{this.getJSONObject().getString("keyId")}, new JSONMapperImpl());
			if(queryForRow==null){
				return EasyResult.error(500, "无发送权限" );
			}
			String content=queryForRow.getString("CONTENT");
			String url=queryForRow.getString("URL");
			//获取超信参数
			String sendSuperSms = queryForRow.getString("SEND_SUPER_SMS");
			String superSmsType = queryForRow.getString("SUPER_SMS_TYPE");
			String superSmsSign = queryForRow.getString("SUPER_SMS_SIGN");
			String superSmsContent = queryForRow.getString("SUPER_SMS_CONTENT");
			logger.info("配置内容 "+content+",地址"+url);

			//调用接口获取短链
			if(content.indexOf("{URL}")>-1){
				sql="SELECT PHONENUM FROM  C_YG_EMPLOYEE where USER_ACC= ?";
				queryForRow = getQuery().queryForRow(sql, new Object[]{OpenApiUserUtil.getUser(this.getRequest()).getUserAcc()}, new JSONMapperImpl());
				JSONObject parmas = new JSONObject();
				try {
					String phone =queryForRow.getString("PHONENUM");
					if(StringUtil.isNull(phone)){
						phone="11111111111";
						userPhone=false;
					}
					HttpServletRequest request = this.getRequest();
					JSONObject bizargs = new JSONObject();
					bizargs.put("page_path", url);
					//bizargs.put("sellerMobile", queryForRow.getString("PHONENUM"));
					bizargs.put("seller_mobile", phone);
					bizargs.put("mtag", ConfigUtil.getString(Constants.APP_NAME,"MTAG"));
					IService service = ServiceContext.getService(ServiceID.MIXGW_ELEC_INTEFACE);
					parmas.put("command","genshortscheme");
					parmas.put("bizargs",bizargs);
					logger.info("电商链接获取入参为:" + parmas.toString());
					JSONObject respDate = service.invoke(parmas);
					logger.info("电商链接获取返回为:" + respDate.toString());
					JSONObject respData = respDate.getJSONObject("respData");
					String respCode = respDate.getString("respCode");
					if ("000".equals(respCode)) {// "000" 成功
						if("000000".equals(respData.getString("code"))){
							String shortScheme = respData.getJSONObject("data").getString("shortLinkUrl");
							content=content.replace("{URL}", " " + shortScheme.replace("https://", "") + " ");
							result.put("resultCode", respData.getString("resultCode"));//0-正常手机号，10102115-未找到合伙人信息
						} else{
							return EasyResult.error(500, "电商链接获取异常" );
						}
					}else{
						return EasyResult.error(500, "电商链接获取异常:"+respDate.getJSONObject("respData"));
					}
				} catch (Exception e) {
					logger.error(String.format("电商链接获取异常! 请求入参为:%s ,接口响应错误信息为:%s",JSON.toJSONString(parmas),e.getMessage()));
					logger.error(CommonUtil.getClassNameAndMethod(this)+"电商链接获取异常，原因："+ e.getMessage()+"",e);
					return EasyResult.error(500, "电商链接获取异常" );

				}
			}
			result.put("userPhone", userPhone);//坐席手机号是否存在
			//添加超信参数
			JSONObject superParam = new JSONObject();
			superParam.put("sendSuperSms",sendSuperSms);
			superParam.put("superSmsContent",superSmsContent);
			superParam.put("superSmsSign",superSmsSign);
			superParam.put("superSmsType",superSmsType);
			result.put("superParam",superParam);
			logger.info("superParam:" + superParam.toString());
			logger.info("result:" + result.toString());
			if(!"".equals(content)){
				result.put("content", content);
				return EasyResult.ok(result);
			}else{
				return EasyResult.error(500, "无发送权限" );
			}
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.error(500, "无发送权限" );
			
		}
	}

	public  JSONObject actionForSendConetnt(){
		JSONObject data = this.getJSONObject();
		String content = data.getString("content");
		String phone = data.getString("phone");
		JSONObject params = new JSONObject();
		params.put("command",ServiceCommand.SENDMESSAGE);
		params.put("sender", Constants.APP_NAME);
		params.put("serialId", IDGenerator.getDefaultNUMID());
		params.put("password", Constants.GW_PASSWORD);
		params.put("source", Constants.MGS_SOURCE);
		params.put("busiId", IDGenerator.getIDByCurrentTime(20));
		params.put("sendTime", EasyCalendar.newInstance().getDateTime("-")); 
		params.put("model", "2"); //事业部标识
		params.put("category", "9"); ///品类标识
		params.put("userAcc", OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
		//获取链接
		String shortScheme = data.getString("shortScheme");
		//获取超信参数
		JSONObject superParam = data.getJSONObject("superParam");
		String sendSuperSms = superParam.getString("sendSuperSms");
		//判断是否发送超信
		if ("1".equals(sendSuperSms)){
			//添加超信参数  支持发送超信
			JSONObject superSmsParam = new JSONObject();
			JSONObject dyncParams = new JSONObject();
			//shortScheme截断域名
			String shortUrl = shortScheme.replace(Constants.ZHJ_PAY_DOMAIN, "");
			dyncParams.put("url1",shortUrl);
			dyncParams.put("url2",shortUrl);
			superSmsParam.put("dyncParams", dyncParams);
			//超信模板id
			superSmsParam.put("tplId", superParam.getString("superSmsType"));
			//超信签名，需与模板保持一致
			superSmsParam.put("smsSigns", new String[] {superParam.getString("superSmsSign")});
			//超信附件内容，超信短链接会拼接到该文本下
			superSmsParam.put("superAppendConent", superParam.getString("superSmsContent"));

			params.put("receivers", StringUtil.spiltByRegex(phone,";", content));
			params.put("needSuperSms",superParam.getString("sendSuperSms"));//是否发送超信
			params.put("superSmsParam", superSmsParam);
			logger.info("发送超信，参数：" + params.toString());
		}else {
			params.put("receivers", StringUtil.spiltByRegex(phone,";", content));
			logger.info("发送普通短信，参数：" + params.toString());
		}

		try {
			logger.error("发送短信 IService请求参数"+JSON.toJSONString(params));
			IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
			JSONObject result = service.invoke(params);
			if(GWConstants.RET_CODE_SUCCESS.equals(result.getString("respCode"))){
				return EasyResult.ok("发送成功" );
			}else{
				return EasyResult.error(500, "发送失败" );
			}
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(params)+",原因"+e.getMessage());
			return EasyResult.error(500, "发送失败"+e.getMessage() );

		}
	}

	public  void actionForMallUrl(){
		redirect(Constants.MALL_URL);
	}
	public  JSONObject  actionForSaveProductSMS(){
		JSONObject data = this.getJSONObject("productSMS");
		try {
			data.put("UPDATE_TIME", EasyDate.getCurrentDateString());
			data.put("UPDATE_ACC",OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
			if(StringUtils.isBlank(data.getString("ID"))){
				String sql="select  count(1) from C_NO_SHORTSCHEME_SMS where KEY=?";
				int queryForInt = getQuery().queryForInt(sql, new Object[]{data.get("USER_ACC")});
				if(queryForInt!=0){
					return EasyResult.error(500, "该标识已有配置，请勿重复配置");
				}
				data.put("ID",RandomKit.randomStr());
				data.put("CREATE_TIME", EasyDate.getCurrentDateString());
				data.put("CREATE_ACC",OpenApiUserUtil.getUser(this.getRequest()).getUserAcc());
				EasyRecord record = new EasyRecord("C_NO_SHORTSCHEME_SMS").setColumns(data);
				getQuery().save(record);
			}else{
				data.remove("USER_ACC");
				EasyRecord record = new EasyRecord("C_NO_SHORTSCHEME_SMS","ID").setColumns(data);
				getQuery().update(record);
			}
			return EasyResult.ok("", "操作成功！");
		} catch (SQLException e) {
			e.printStackTrace();
			return EasyResult.error(500, e.getMessage());
		}
		
	}
	/**
	 * 删除
	 * @return
	 */
	public JSONObject actionForDeleteProductSMS(){
		JSONObject surname = new JSONObject();
		String id = this.getJsonPara("id");
		try {
			surname.put("ID",id);
			EasyRecord record = new EasyRecord("C_NO_SHORTSCHEME_SMS", "ID").setColumns(surname);
			this.getQuery().deleteById(record);
			logger.info("删除成功，id= "+id);
			return EasyResult.ok(record,"删除成功!");
		} catch (SQLException e) {
			logger.error("[SQLException] 删除失败，原因：",e);
			return EasyResult.error(501, "删除失败，原因：" + e.getMessage());
		}
	}
}
