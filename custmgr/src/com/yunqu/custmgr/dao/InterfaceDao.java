package com.yunqu.custmgr.dao;

import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.EasyQueryImpl;

import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.custmgr.base.CommonLogger;
import com.yunqu.custmgr.base.Constants;

public class InterfaceDao {
	
	private Logger logger = CommonLogger.logger;
	
	private EasyQuery query =EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);
	
	private EasyQuery getYcbusiQuery(){
		return new EasyQueryImpl(Constants.APP_NAME, Constants.YCBUSI_DS);
	}

	/**
	 * 根据客户资料id或者手机号码查询客户资料信息
	 * @param customerId
	 * @param phoneNum
	 * @return
	 */
	public EasyRow findCustByIdOrPhonenum(String accountType,String customerId, String phoneNum) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT T1.*,T2.VIP_GRADE FROM C_CT_CUSTOMER T1 LEFT JOIN C_RED_BLOCK_LIST T2 ON T1.PHONENUM = T2.USER_NO AND T2.STATUS = 1 where 1=1 ");
			
			//先判断电话是否存在
			if(StringUtils.isNotEmpty(phoneNum)){
				sql.append(phoneNum," AND T1.PHONENUM=? ");
			}else{
				if("openid".equals(accountType)){//微信号
					sql.append(customerId," AND T1.WEIXIN_NO=? ");
				}else if("uin".equals(accountType)){//电商UID
					sql.append(customerId," AND T1.CUST_UID=? ");
				}else{
					return null;
				}
			}
			List<EasyRow> queryForList = query.queryForList(sql.getSQL(), sql.getParams());
			if(queryForList!=null&&queryForList.size()>0){
				return queryForList.get(0);
			}else{
				return null;
			}
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询客户资料信息 出错:accountType="+accountType+",customerId="+customerId+",phoneNum="+phoneNum+","+e.getMessage(),e);
		}
		return null;
	}

	/**
	 * 根据客户资料id查询客户资料的标签
	 * @param customerId
	 * @return
	 */
	public List<EasyRow> findCustTagByCustomerId(String customerId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT * FROM C_CT_TAG T WHERE 1=1 ");
			sql.append(customerId," AND EXISTS (SELECT 1 FROM C_CT_CUSTOMER_TAG T1 WHERE T.ID = T1.TAG_ID AND T1.CUSTOMER_ID= ?) ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据客户资料id查询客户资料的标签 出错:"+e.getMessage());
		}
		return null;
	}

	/**
	 * 查询库里可用的客户标签类型
	 * 查询出标签分类的最后一级、且存在标签的标签类型
	 * @return
	 */
	public List<EasyRow> findCustTagDir() {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT * FROM C_CT_TAG_DIR  T WHERE 1=1 ");
			sql.append(" AND NOT EXISTS (SELECT 1 FROM C_CT_TAG_DIR T1 WHERE T1.PARENT_ID = T.ID) ");
			sql.append(" AND EXISTS (SELECT 1 FROM C_CT_TAG T2 WHERE T2.TAG_DIR_ID = T.ID) ");
//			sql.append(" AND T.ENABLE_STATUS='Y' ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询库里可用的客户标签类型 出错:"+e.getMessage());
		}
		return null;
	}

	/**
	 * 查询某个标签类型下的所有标签
	 * @param tagDirId
	 * @return
	 */
	public List<EasyRow> findCustTagByDirId(String tagDirId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("SELECT * from C_CT_TAG T  WHERE 1=1 ");
			sql.append(tagDirId," AND T.TAG_DIR_ID=? ");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询某个标签类型下的所有标签 出错:"+e.getMessage());
		}
		return null;
	}

	/**
	 * 保存客户标签
	 * @param customerId
	 * @param tagId
	 * @param tagDirId
	 */
	public boolean saveCustTag(String customerId, String tagId, String tagDirId) {
		try {
			EasyRecord record = new EasyRecord("C_CT_CUSTOMER_TAG","ID");
			record.put("ID", IDGenerator.getDefaultNUMID());
			record.put("CUSTOMER_ID", customerId);
			record.put("TAG_ID", tagId);
			record.put("TAG_DIR_ID", tagDirId);
			query.save(record);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 保存客户标签 出错:"+e.getMessage());
		}
		return false;
	}

	/**
	 * 根据客户id或者客户手机号码，查询客户的标签集合
	 * @param customerId
	 * @param phoneNum
	 * @return
	 */
	public List<EasyRow> findCustTagByCustomerIdOrPhoneNum(String customerId, String phoneNum) {
		try {
			EasySQL sql = new EasySQL();
			sql.append("SELECT * from C_CT_TAG T INNER JOIN C_CT_CUSTOMER_TAG T1 ON T.ID = T1.TAG_ID ");
			sql.append("AND EXISTS (SELECT 1 FROM C_CT_CUSTOMER T2 WHERE T2.ID = T1.CUSTOMER_ID ");
			sql.append(customerId," AND T2.ID=? ");
			sql.append(phoneNum," AND T2.PHONENUM=? ");
			sql.append(")");
			return query.queryForList(sql.getSQL(), sql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据客户id或者客户手机号码，查询客户的标签集合 出错:"+e.getMessage());
		}
		return null;
	}

	/**
	 * 根据客户资料id、标签id删除客户标签
	 * @param customerId
	 * @param tagId
	 */
	public boolean delCustTag(String customerId, String tagId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" DELETE FROM C_CT_CUSTOMER_TAG where CUSTOMER_ID=? and TAG_ID=? ");
			query.execute(sql.getSQL(), customerId,tagId);
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 根据客户id、标签id，删除客户的标签出错:"+e.getMessage());
		}
		return true;
	}
	
	/**
	 * 绑定客户ID到全媒体会话
	 * @param customerId
	 * @param tagId
	 * @param tagDirId
	 */
	public boolean bindCustID(String customerId, String chatSessionId) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" update ycbusi.cc_media_record t set t.cust_id=? where t.serial_id=? ");
			getYcbusiQuery().execute(sql.getSQL(), new Object[] { customerId,chatSessionId});
			return true;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 保存客户标签 出错:"+e.getMessage());
		}
		return false;
	}

	/**
	 * 查询客户的接入次数
	 * @param customerId
	 * @return
	 */
	public int findCustAccessNum(String customerId,String beginTime,String endTime) {
		try {
			EasySQL sql = new EasySQL();
			sql.append(" SELECT COUNT(1) CNT FROM V_PF_SESSION_RECORD T WHERE T.CUSTOMER_ACC = ? AND T.ANSWER_TIME>=? AND T.ANSWER_TIME<=? ");
			EasyRow row = query.queryForRow(sql.getSQL(), new Object[]{customerId,beginTime,endTime});
			if(row==null){
				return 0;
			}
			int accessNum = CommonUtil.parseInt(row.getColumnValue("CNT"));
			return accessNum;
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 查询客户接入次数 出错:"+e.getMessage());
		}
		return 0;
	}

}
