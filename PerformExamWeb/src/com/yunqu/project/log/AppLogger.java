package com.yunqu.project.log;

import com.yunqu.project.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;

public class AppLogger {
	private static Logger logger = LogEngine.getLogger(Constants.APP_NAME);
	private static Logger longTxtLogger = LogEngine.getLogger(Constants.APP_NAME+"-longtxt");


	public static Logger getLogger(){
		return logger;
	}
	public static Logger getLongTxtLogger(){
		return longTxtLogger;
	}

}
