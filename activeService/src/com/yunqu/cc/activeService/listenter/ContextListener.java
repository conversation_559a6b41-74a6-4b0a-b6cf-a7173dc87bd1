package com.yunqu.cc.activeService.listenter;

import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.inf.HighValueUserVipService;
import com.yunqu.cc.activeService.msg.AsServiceConsumerThread;
import com.yunqu.cc.activeService.msg.MqBrokerControl;
import com.yunqu.cc.activeService.thread.ScheduleThreadKit;
import com.yunqu.cc.activeService.thread.ThreadPoolKit;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;

/**
 * <p>
 *
 * </p>
 *
 * @ClassName: ContextListener
 * @Author: Bianhl
 * @Description: TODO 描述文件用途
 * @since: create in 2022/3/1 17:41
 * @Copyright Copyright (c) 2022
 * @Company 广州云趣信息科技有限公司
 */
@WebListener
public class ContextListener implements ServletContextListener {

	private static final String TASK_GROUP_NAME = Constants.APP_NAME;


	@Override
	public void contextInitialized(ServletContextEvent servletContextEvent) {
		/*try {
			CommLogger.logger.info("初始化定时器......");
			TaskServiceEnum.initializedTask();
		} catch (Exception e) {
			CommLogger.getCommLogger().error(e.getMessage(), e);
		}*/
		try {
			MqBrokerControl instance = MqBrokerControl.getInstance();
			instance.initBroker();
//			ThreadUtil.execAsync(new AsServiceConsumerThread());
			//启动线程池
			ThreadPoolKit.getInstance().initialized();
			ThreadPoolKit.getInstance().execute(new AsServiceConsumerThread());

			// 初始化VIP查询服务
			HighValueUserVipService.initialize();
		} catch (Exception e) {
			CommLogger.getCommLogger().error(e.getMessage(), e);
		}
	}

	@Override
	public void contextDestroyed(ServletContextEvent servletContextEvent) {
		/*try {
			CommLogger.logger.info("销毁化定时器......");
			TaskServiceEnum.destroyedTask();
		} catch (Exception e) {
			CommLogger.getCommLogger().error(e.getMessage(), e);
		}*/
		try {
			MqBrokerControl.getInstance().shutDown();
			//销毁线程池
			ThreadPoolKit.getInstance().destroyed();
			ScheduleThreadKit.getInstance().shutdown();
			HighValueUserVipService.shutdown(); // 关闭并发查询的全局线程池
		} catch (Exception e) {
			CommLogger.getCommLogger().error(e.getMessage(), e);
		}
	}
}
