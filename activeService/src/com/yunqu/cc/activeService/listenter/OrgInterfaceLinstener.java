package com.yunqu.cc.activeService.listenter;

import com.yunqu.cc.activeService.base.Constants;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.ServiceContextListener;

import javax.servlet.annotation.WebListener;
import java.util.ArrayList;
import java.util.List;


@WebListener
public class OrgInterfaceLinstener extends ServiceContextListener {

	@Override
	protected List<ServiceResource> serviceResourceCatalog() {

		List<ServiceResource> list = new ArrayList<ServiceResource>();
		ServiceResource resource1 = new ServiceResource();
		resource1.appName = Constants.APP_NAME;
		resource1.className = "com.yunqu.cc.activeService.inf.IvrService";
		resource1.description = "IVR策略数据接口";
		resource1.serviceId = "ACTIVE-SERVICE-INTEFACE";
		resource1.serviceName = "IVR策略数据接口";
		list.add(resource1);

		ServiceResource resource2 = new ServiceResource();
		resource2.appName = Constants.APP_NAME;
		resource2.className = "com.yunqu.cc.activeService.inf.WechatWorkService";
		resource2.description = "企业微信接口";
		resource2.serviceId = "WECHAT-WORK-SERVICE-INTEFACE";
		resource2.serviceName = "企业微信接口";
		list.add(resource2);

		ServiceResource resource3 = new ServiceResource();
		resource3.appName = Constants.APP_NAME;
		resource3.className = "com.yunqu.cc.activeService.service.UserDataDrainageService";
		resource3.description = "主动引流数据入库";
		resource3.serviceId = "ACTIVE-DRAINAGE-SERVICE";
		resource3.serviceName = "主动引流数据入库";
		list.add(resource3);

		ServiceResource resource4 = new ServiceResource();
		resource4.appName = Constants.APP_NAME;
		resource4.className = "com.yunqu.cc.activeService.inf.ActiveService";
		resource4.description = "主动服务人群包接口";
		resource4.serviceId = "ACTIVE-SERVICE-CROWD-INTEFACE";
		resource4.serviceName = "主动服务人群包接口";
		list.add(resource4);

		ServiceResource resource5 = new ServiceResource();
		resource5.appName = Constants.APP_NAME;
		resource5.className = "com.yunqu.cc.activeService.inf.DdyGroupService";
		resource5.description = "地动仪大数据接口";
		resource5.serviceId = "ACTIVE-SERVICE-DDY-INTEFACE";
		resource5.serviceName = "地动仪大数据接口";
		list.add(resource5);

		ServiceResource resource6 = new ServiceResource();
		resource6.appName = Constants.APP_NAME;
		resource6.className = "com.yunqu.cc.activeService.inf.WechatWorkStrategyJob";
		resource6.description = "企业微信策略定时器接口，定时扫描可执行的策略，进行操作";
		resource6.serviceId = "WECHAT-WORK-STRATEGY-JOB-INTEFACE";
		resource6.serviceName = "企业微信策略执行定时器接口";
		list.add(resource6);

		ServiceResource resource7 = new ServiceResource();
		resource7.appName = Constants.APP_NAME;
		resource7.className = "com.yunqu.cc.activeService.inf.WechatWorkResultJob";
		resource7.description = "企业微信策略定时器接口，定时扫描已处理策略，进行查询处理结果";
		resource7.serviceId = "WECHAT-WORK-RESULT-JOB-INTEFACE";
		resource7.serviceName = "企业微信策略结果定时器接口";
		list.add(resource7);

		ServiceResource resource8 = new ServiceResource();
		resource8.appName = Constants.APP_NAME;
		resource8.className = "com.yunqu.cc.activeService.inf.WechatWorkStrategyService";
		resource8.description = "企业微信策略处理接口，处理具体策略信息";
		resource8.serviceId = "WECHAT-WORK-STRATEGY-SERVICE-INTEFACE";
		resource8.serviceName = "企业微信策略处理接口";
		list.add(resource8);

		ServiceResource resource9 = new ServiceResource();
		resource9.appName = Constants.APP_NAME;
		resource9.className = "com.yunqu.cc.activeService.inf.RevisitService";
		resource9.description = "回访自动分配处理接口";
		resource9.serviceId = "AS-REVISIT-SERVICE-INTEFACE";
		resource9.serviceName = "回访自动分配处理接口";
		list.add(resource9);

		ServiceResource resource10 = new ServiceResource();
		resource10.appName = Constants.APP_NAME;
		resource10.className = "com.yunqu.cc.activeService.task.UserDataDrainageTask";
		resource10.description = "执行将引流数据分配给企业微信坐席";
		resource10.serviceId = "AS-DRAINAGE-USER-DATA";
		resource10.serviceName = "执行将引流数据分配给企业微信坐席";
		list.add(resource10);

		ServiceResource resource11 = new ServiceResource();
		resource11.appName = Constants.APP_NAME;
		resource11.className = "com.yunqu.cc.activeService.inf.HistoryDataHandle";
		resource11.description = "AI自动外呼数据老化";
		resource11.serviceId = "AS-ROBOT-HISTORY-CLEAR";
		resource11.serviceName = "AI自动外呼数据老化";
		list.add(resource11);

		ServiceResource resource12 = new ServiceResource();
		resource12.appName = Constants.APP_NAME;
		resource12.className = "com.yunqu.cc.activeService.inf.ReturnAIDataService";
		resource12.description = "AI自动外呼数据回推";
		resource12.serviceId = "AS-ROBOT-POST-RETURN";
		resource12.serviceName = "AI自动外呼数据回推";
		list.add(resource12);

		ServiceResource resource13 = new ServiceResource();
		resource13.appName = Constants.APP_NAME;
		resource13.className = "com.yunqu.cc.activeService.inf.HighValueUserVipService";
		resource13.description = "提供调用查询美的会员接口";
		resource13.serviceId = "AS-MEDIA-VIP-SEARCH";
		resource13.serviceName = "提供调用查询美的会员接口";
		list.add(resource13);

		return list;

	}

}
