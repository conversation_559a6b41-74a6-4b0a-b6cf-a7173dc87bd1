package com.yunqu.cc.activeService.servlet;

import java.io.File;
import java.sql.SQLException;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.Part;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.base.AppBaseServlet;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.dao.CrowdPackSql;
import com.yunqu.cc.activeService.model.DataSource;
import com.yunqu.cc.activeService.model.TouchSource;
import com.yunqu.cc.activeService.utils.OrderExcelUtil;
import com.yunqu.cc.activeService.utils.PhoneEncryptUtil;

@WebServlet("/servlet/crowdPack")
@MultipartConfig
public class CrowdPackServlet  extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private final Logger logger = CommLogger.getCommLogger();
	
	public EasyResult actionForUpdateDisture(){
		JSONObject params = this.getJSONObject();
		String id = params.getString("id");
		String value = params.getString("value");
		if(StringUtils.isBlank(id)||StringUtils.isBlank(value)) {
			return EasyResult.fail("参数异常");
		}
		try {
			//更新免打扰
			EasyRecord saveRecord = new EasyRecord("C_NO_AS_CROWD", "ID");
			saveRecord.setPrimaryValues(id);
			saveRecord.set("IS_DISTURB", value);
			this.getQuery().update(saveRecord);
		} catch (Exception e) {
			this.error("设置免打扰失败,",e);
			return EasyResult.error(500,"保存失败");
		}
		return EasyResult.ok("保存成功");
	}

	public EasyResult actionForMarkSentisivePack(){
		JSONObject params = this.getJSONObject();
		String id = params.getString("crowdPackId");
		if(StringUtils.isBlank(id)) {
			return EasyResult.fail("参数异常");
		}
		//ALTER TABLE YWDB.C_NO_AS_CROWD_PACK ADD IS_DISTURB VARCHAR2(2);
		//COMMENT ON COLUMN YWDB.C_NO_AS_CROWD_PACK.IS_DISTURB IS '标记是否免打扰';
		try {
			//更新免打扰
			EasyRecord saveRecord = new EasyRecord("C_NO_AS_CROWD_PACK", "ID");
			saveRecord.setPrimaryValues(id);
			saveRecord.set("IS_DISTURB", "1");//标记免打扰
			this.getQuery().update(saveRecord);
		} catch (Exception e) {
			this.error("标记人群包免打扰失败,",e);
			return EasyResult.error(500,"标记人群包免打扰失败");
		}
		return EasyResult.ok("标记成功");
	}
	
	public EasyResult actionForSaveCrowdPackPriority(){
		JSONObject params = this.getJSONObject();
		String priorityId = params.getString("priorityId");
		String priority = params.getString("priority");
		String channelType = params.getString("channelType");
		if(StringUtils.isBlank(priorityId)||StringUtils.isBlank(priority)||StringUtils.isBlank(channelType)) {
			return EasyResult.fail("参数异常");
		}
		if(Integer.parseInt(priority)<1) {
			return EasyResult.fail("不允许设置小于1的优先级");
		}
		try {
			EasyQuery query = this.getQuery();
			String name = query.queryForString("select t2.NAME FROM C_NO_AS_CROWDPACK_PRIORITY t1 LEFT JOIN C_NO_AS_CROWD_PACK t2 ON t1.CROWD_PACK_ID  = t2.ID WHERE t1.PRIORITY =? AND t1.CHANNEL_TYPE = ?", new Object[] {priority,channelType});
			if(StringUtils.isNotBlank(name)) {
				return EasyResult.fail("优先级重复，冲突人群包："+name);
			}
			//更新免打扰
			EasyRecord saveRecord = new EasyRecord("C_NO_AS_CROWDPACK_PRIORITY", "ID");
			saveRecord.setPrimaryValues(priorityId);
			saveRecord.set("PRIORITY", priority);
			this.getQuery().update(saveRecord);
		} catch (Exception e) {
			this.error("保存优先级失败,",e);
			return EasyResult.error(500,"保存失败");
		}
		return EasyResult.ok("保存成功");
	}
	public EasyResult actionForSaveCrowdPackLable(){
		JSONObject lable = this.getJSONObject("lable");
		try {
			EasyRecord saveRecord = new EasyRecord("C_NO_AS_CROWD_PACK", "ID").setColumns(lable);
			this.getQuery().update(saveRecord);
		} catch (Exception e) {
			this.error("保存失败,",e);
			return EasyResult.error(500,"保存失败");
		}
		return EasyResult.ok("保存成功");
	}

	/**
	 * 新增人群包
	 * @return
	 */
	public EasyResult actionForSaveCrowdPack(){
		JSONObject params = this.getJSONObject();
		String crowdPackName = params.getString("CROWD_PACK_NAME");
		String triggerType = params.getString("TRIGGER_TYPE");
		String triggerTypeName = params.getString("TRIGGER_TYPE_NAME");
		String dataSource = params.getString("DATA_SOURCE");
		String category = params.getString("CATEGORY");
		String isSentiment = params.getString("IS_SENTIMENT");
		if(StringUtils.isBlank(crowdPackName)||StringUtils.isBlank(triggerType)||StringUtils.isBlank(dataSource)) {
			return EasyResult.fail("参数异常");
		}
		try {
			EasyQuery query = this.getQuery();
			if (StringUtils.equals(triggerType, "OTHER")){
				triggerType = new SimpleDateFormat("yyMMddHHmmss").format(new Date());
			}
			String crowdPackId = dataSource+triggerType;
			List<String> notRepeatArr = new ArrayList<String>();
			notRepeatArr.add(Constants.CROWD_PACK_CATE_2);
			notRepeatArr.add(Constants.CROWD_PACK_CATE_3);
			if(notRepeatArr.contains(category)){
				boolean record = query.queryForExist("select count(1) from C_NO_AS_CROWD_PACK where CATEGORY = ?", new Object[] {category});
				if(record) {
					return EasyResult.fail("保存失败，该类人群包只能建一个");
				}
			}

			boolean record = query.queryForExist("select count(1) from C_NO_AS_CROWD_PACK where ID = ?", new Object[] {crowdPackId});
			if(record) {
				return EasyResult.fail("保存失败，该触发器已存在人群包");
			}
			//查询客户数量
			String custNum = query.queryForString("select count(1) from C_NO_AS_CROWD where CROWD_PACK_ID = ?", new Object[] {crowdPackId});

			String createAcc = UserUtil.getUser(this.getRequest()).getUserAcc();
			String createTime = EasyDate.getCurrentDateString();

			EasyRecord easyRecord = new EasyRecord("C_NO_AS_CROWD_PACK","ID");
			easyRecord.set("ID", crowdPackId);
			easyRecord.set("BG_CROWD_CODE", triggerType);
			easyRecord.set("NAME", crowdPackName);
			easyRecord.set("CATEGORY", category);//标签
			easyRecord.set("DATA_SOURCE", DataSource.DATA_SOURCE_TRI.getCode());//来源系统-触发器
			easyRecord.set("CONDITION", triggerTypeName);
			easyRecord.set("CUST_NUM", custNum);
			easyRecord.set("TOUCH_SOURCE", TouchSource.CC_PUSH.getCode());
			easyRecord.set("CREATE_TIME", createTime);
			easyRecord.set("CREATE_ACC", createAcc);
			easyRecord.set("DATA_TYPE", dataSource);//数据来源-大数据-1
			easyRecord.set("SYNC_COUNT", 0);
			easyRecord.set("BG_CROWD_STATUS", 1);//人群包状态-正常使用
			easyRecord.set("IS_SENTIMENT",isSentiment);
			query.save(easyRecord);
		} catch (Exception e) {
			this.error("保存失败,",e);
			return EasyResult.error(500,"保存失败");
		}
		return EasyResult.ok("保存成功");
	}
	
	/**
	 * 检查人群包是否存在
	 * @return
	 */
	public EasyResult actionForCheckCrowdPack(){
		JSONObject params = this.getJSONObject();
		String triggerType = params.getString("triggerType");
		String dataSource = params.getString("dataSource");
		if(StringUtils.isBlank(triggerType)||StringUtils.isBlank(dataSource)) {
			return EasyResult.fail("参数异常");
		}
		String crowdPackId = dataSource+triggerType;
		JSONObject result = new JSONObject();
		EasyQuery query = this.getQuery();
		try {
			String groupId = query.queryForString("select BG_CROWD_CODE from C_NO_AS_CROWD_PACK where ID = ?", new Object[] {crowdPackId});
			if(StringUtils.isNotBlank(groupId)) {
				result.put("groupId", groupId);
			}
		} catch (Exception e) {
			this.error("查询失败,",e);
			return EasyResult.error(500,"查询失败");
		}
		return EasyResult.ok(result,"查询成功");
	}
	
	public EasyResult actionForDelCrowdPack(){
		JSONObject params = this.getJSONObject();
		String crowdPackId = params.getString("crowdPackId");
		EasyQuery query = this.getQuery();
		if(StringUtils.isBlank(crowdPackId)) {
			return EasyResult.fail("参数异常");
		}
		try {
			//查询大数据人群包id
			EasyRow row = query.queryForRow("select t2.IVR_STRATEGY_ID,t1.DATA_SOURCE from C_NO_AS_CROWD_PACK t1 LEFT JOIN C_NO_AS_IVR_DETAIL_KEY t2 ON t1.BG_CROWD_CODE = t2.CROWD_PACK_ID where t1.ID = ?", new Object[] {crowdPackId});
			
			if(StringUtils.isNotBlank(row.getColumnValue("IVR_STRATEGY_ID"))) {
				return EasyResult.fail("删除失败，请先取消ivr策略和该人群包的关联");
			}
			
			query.begin();
			
			//删除人群包信息
			query.execute("delete from C_NO_AS_CROWD_PACK where ID = ? ", crowdPackId);
			
			//删除人群信息---触发器不删除明细
			if(!row.getColumnValue("DATA_SOURCE").equals(DataSource.DATA_SOURCE_TRI.getCode())) {
				query.execute("delete from C_NO_AS_CROWD where CROWD_PACK_ID = ? ", crowdPackId);
			}
			
			List<EasyRow> list = query.queryForList("select * from C_NO_AS_CROWDPACK_PRIORITY where CROWD_PACK_ID = ? and STRATEGY_LEVEL = '1' ", new Object[] {crowdPackId});
			
			for(int i = 0;i<list.size();i++) {
				EasyRow easyRow = list.get(i);
				String channelType  = easyRow.getColumnValue("CHANNEL_TYPE");
				String strategyId  = easyRow.getColumnValue("STRATEGY_ID");
				String table = Constants.strategyTable.get(channelType);
				
				if(Constants.CHANNEL_WECHAT.equals(channelType)) {//删除企微策略
					query.execute("delete from "+table+" where ID = ? ", strategyId);
					query.execute("delete from C_NO_AS_WECHAT_STRATEGY_EX where ID = ? ", strategyId);
				}else {
					//删除策略表
					query.execute("delete from "+table+" where ID = ? ", strategyId);
					//删除结果反馈表
					query.execute("delete from C_NO_AS_STRATEGY_RESULT where STRATEGY_ID = ? ", strategyId);
				}
			}
			
			//删除人群包关联扩展表
			query.execute("delete from C_NO_AS_CROWDPACK_PRIORITY where CROWD_PACK_ID = ? AND STRATEGY_LEVEL = '1' ", crowdPackId);

			//删除免打扰人群包关联的免打扰策略 20240704
			query.execute("delete from C_NO_AS_STRATEGY_CROWD where CROWD_PACK_ID = ?  ", crowdPackId);
			
			query.commit();
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 删除失败，原因：",e);
			}
			this.error("删除人群包失败,",e);
			return EasyResult.error(500,"删除失败");
		}
		return EasyResult.ok("删除成功");
	}
	
	/**
	 * 人群包导入
	 * @return
	 */
	public JSONObject actionForCrowdPackImport(){

		EasyQuery query = this.getQuery();
		try {
			query.begin();
			Part part = getFile("file");
			OrderExcelUtil util = new OrderExcelUtil();
			try {
				util.process(part.getInputStream());
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;// 是否正确
			String msg = "";
			String crowdPackId = null;
			int custCount = 0;
			String currentDateString = EasyDate.getCurrentDateString();
			String currentDateString2 = EasyDate.getCurrentDateString("yyyyMMdd");
			String userAcc = UserUtil.getUser(this.getRequest()).getUserAcc();
			for (int i = 0; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				Map<Object, Object> detailMap = new HashMap<Object, Object>();
				
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
					case 0:
						if(i==0) {
							if(!"人群包名称".equals(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "非导入模板";
								break;
							}
						}else if(i==1){
							if(StringUtils.isBlank(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "人群包名称不为空";
								break;
							}
							m.put("NAME", val);
							break;
						}else if(i>2){
							if(StringUtils.isBlank(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "姓名不为空";
								break;
							}
							detailMap.put("CUST_NAME", val);
							break;
						}
						break;
					case 1:
						if(i==0) {
							if(!"人群包口径".equals(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "非导入模板";
								break;
							}
						}else if(i==1){
							m.put("CONDITION", val);
							break;
						}else if(i>2){
							if(StringUtils.isBlank(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "电话号码不为空";
								break;
							}
							detailMap.put("CUST_PHONE", val);
							break;
						}
						break;
					case 2:
						if(i==0) {
							if(!"备注".equals(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "非导入模板";
								break;
							}
						}else if(i==1){
							m.put("REMARK", val);
							break;
						}else if(i>2){
							detailMap.put("ORG_CODE", val);
							break;
						}
						break;
					case 3:
						if(i>2){
							detailMap.put("BRAND_CODE", val);
							break;
						}
						break;
					case 4:
						if(i>2){
							detailMap.put("BRAND_NAME", val);
							break;
						}
						break;
					case 5:
						if(i>2){
							detailMap.put("PROD_CODE", val);
							break;
						}
						break;
					case 6:
						if(i>2){
							if(StringUtils.isBlank(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "产品品类不为空";
								break;
							}
							detailMap.put("PROD_NAME", val);
							break;
						}
						break;
					case 7:
						if(i>2){
							detailMap.put("PRODUCT_MODEL", val);
							break;
						}
						break;
					default:
					break;
					}
				}
				if (scuuess) {
					if(i==1) {
						crowdPackId = RandomKit.randomStr();
						String str = JSONObject.toJSON(m).toString();
						JSONObject jsonObject = JSONObject.parseObject(str);
						jsonObject.put("CREATE_ACC", userAcc);//导入人
						jsonObject.put("ID", crowdPackId);//id
						jsonObject.put("DATA_SOURCE", DataSource.DATA_SOURCE_IMPORT.getCode());//导入
						jsonObject.put("TOUCH_SOURCE", TouchSource.CC_OTHER.getCode());//调用模式
						jsonObject.put("CUST_NUM", "0");//人群数量
						jsonObject.put("BG_CROWD_STATUS", "1");//人群包默认使用中
						jsonObject.put("DATA_TYPE", "3");//人工导入
						jsonObject.put("CREATE_TIME",EasyDate.getCurrentDateString());//导入时间
						EasyRecord record = new EasyRecord("C_NO_AS_CROWD_PACK", "ID").setColumns(jsonObject);
						query.save(record);
					}else if(i>2) {
						if(StringUtils.isNotBlank(crowdPackId)) {
							String str = JSONObject.toJSON(detailMap).toString();
							JSONObject jsonObject = JSONObject.parseObject(str);
							jsonObject.put("CREATE_ACC", userAcc);//导入人
							jsonObject.put("ID", RandomKit.randomStr());//id
							jsonObject.put("SERVICE_STATUS", "1");//默认状态
							jsonObject.put("CROWD_PACK_ID", crowdPackId);//所属人群包
							jsonObject.put("IS_DISTURB", "0");
							jsonObject.put("CREATE_TIME",currentDateString);//同步时间
							jsonObject.put("CUST_READ", "0");
							jsonObject.put("DATA_SOURCE", "OTHER");//导入
							jsonObject.put("CREATE_DATE", currentDateString2);
							EasyRecord record = new EasyRecord("C_NO_AS_CROWD", "ID").setColumns(jsonObject);
							query.save(record);
							++custCount;
						
						}
					}
					
				} else {
					if (!lineScuuess) {
						msg = msg + "第" + i + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {// 出错了不记录
				query.roolback();
				return EasyResult.error(500, "" + msg);
			} else {
				if(custCount>0) {
					query.execute("update C_NO_AS_CROWD_PACK set CUST_NUM = CUST_NUM + ? WHERE ID = ? ", custCount,crowdPackId);
				}
				query.commit();
				return EasyResult.ok("", "人群包导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 导入失败，原因：",e);
			}
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
		}
	}

	/**
	 * 人群包导入
	 * @return
	 */
	public JSONObject actionForCrowdPackCustDndImport(){

		EasyQuery query = this.getQuery();
		try {
			query.begin();
			String crowdPackId = this.getRequest().getParameter("crowdPackId");
			String filterType = this.getRequest().getParameter("filterType");
			if(StringUtils.isBlank(crowdPackId)||StringUtils.isBlank(filterType)) {
				return EasyResult.error(500, "人群包信息异常");
			}
			Part part = getFile("file");
			OrderExcelUtil util = new OrderExcelUtil();
			try {
				util.process(part.getInputStream());
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;// 是否正确
			String msg = "";
			int custCount = 0;
			String userAcc = UserUtil.getUser(this.getRequest()).getUserAcc();
			for (int i = 0; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
						case 0:
							if(i==0) {
								if(!"电话号码".equals(val)){
									scuuess = false;
									lineScuuess = false;
									lineMsg = lineMsg + "非导入模板";
									break;
								}
							}else if(i>0){
								if(StringUtils.isBlank(val)){
									scuuess = false;
									lineScuuess = false;
									lineMsg = lineMsg + "电话号码不为空";
									break;
								}
								m.put("PHONE", val);
								break;
							}
							break;
						default:
							break;
					}
				}

				if (scuuess&&i>0) {
					String str = JSONObject.toJSON(m).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					jsonObject.put("CREATE_ACC", userAcc);//导入人
					jsonObject.put("ID", RandomKit.randomStr());//id
					jsonObject.put("CROWD_PACK_ID",crowdPackId);
					jsonObject.put("FILTER_TYPE", filterType);
					jsonObject.put("CREATE_DATE", EasyDate.getCurrentDateString("yyyyMMdd"));
					jsonObject.put("CREATE_TIME",EasyDate.getCurrentTimeStampString());
					EasyRecord record = new EasyRecord("C_NO_AS_SENSITIVE_CROWD", "ID").setColumns(jsonObject);
					query.save(record);
					++custCount;
				} else {
					if (!lineScuuess) {
						msg = msg + "第" + i + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {// 出错了不记录
				query.roolback();
				return EasyResult.error(500, "" + msg);
			} else {
				if(custCount>0) {
					query.execute("update C_NO_AS_CROWD_PACK set CUST_NUM = CUST_NUM + ? WHERE ID = ? ", custCount,crowdPackId);
				}
				query.commit();
				return EasyResult.ok("", "人群包导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 导入失败，原因：",e);
			}
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
		}
	}
	
	/**
	 * 人群包明细导入
	 * @return
	 */
	public JSONObject actionForCrowdPackCustImport(){
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			Part part = getFile("file");
			String crowdPackId = this.getRequest().getParameter("crowdPackId");
			if(StringUtils.isBlank(crowdPackId)) {
				return EasyResult.error(500, "人群包信息异常");
			}
			OrderExcelUtil util = new OrderExcelUtil();
			try {
				util.process(part.getInputStream());
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;// 是否正确
			String msg = "";
			String currentDateString = EasyDate.getCurrentDateString();
			String currentDateString2 = EasyDate.getCurrentDateString("yyyyMMdd");
			String userAcc = UserUtil.getUser(this.getRequest()).getUserAcc();
			int custCount = 0;
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
					case 0:
						if(StringUtils.isBlank(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "姓名不为空";
							break;
						}
						m.put("CUST_NAME", val);
						break;
					case 1:
						if(StringUtils.isBlank(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "电话号码不为空";
							break;
						}
						m.put("CUST_PHONE", val);
						break;
					case 2:
						m.put("ORG_CODE", val);
						break;
					case 3:
						m.put("BRAND_CODE", val);
						break;
					case 4:
						m.put("BRAND_NAME", val);
						break;
					case 5:
						m.put("PROD_CODE", val);
						break;
					case 6:
						if(StringUtils.isBlank(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "产品品类不为空";
							break;
						}
						m.put("PROD_NAME", val);
						break;
					case 7:
						m.put("PRODUCT_MODEL", val);
						break;
					case 8:
						m.put("FAULT_CODE", val);
						break;
					case 9:
						m.put("SERVICE_ORDER_CODE", val);
						break;
					default:
					break;
					}
				}
				if (scuuess) {
					String str = JSONObject.toJSON(m).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					jsonObject.put("CREATE_ACC", userAcc);//导入人
					jsonObject.put("ID", RandomKit.randomStr());//id
					jsonObject.put("SERVICE_STATUS", "1");//默认状态
					jsonObject.put("CROWD_PACK_ID", crowdPackId);//所属人群包
					jsonObject.put("IS_DISTURB", "0");
					jsonObject.put("CREATE_TIME",currentDateString);//同步时间
					jsonObject.put("CUST_READ", "0");
					jsonObject.put("DATA_SOURCE", "OTHER");//导入
					jsonObject.put("CREATE_DATE", currentDateString2);
					EasyRecord record = new EasyRecord("C_NO_AS_CROWD", "ID").setColumns(jsonObject);
					query.save(record);
					++custCount;
					
				} else {
					if (!lineScuuess) {
						msg = msg + "第" + i + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {// 出错了不记录
				query.roolback();
				return EasyResult.error(500, "" + msg);
			} else {
				if(custCount>0) {
					query.execute("update C_NO_AS_CROWD_PACK set CUST_NUM = CUST_NUM + ? WHERE ID = ? ", custCount,crowdPackId);
				}
				query.commit();
				return EasyResult.ok("", "人群明细导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 导入失败，原因：",e);
			}
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
		}
	}

	/**
	 * 机器人自动外呼人群明细导入
	 * @return
	 */
	public JSONObject actionForCrowdPackCustAiImport(){
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			Part part = getFile("file");
			String crowdPackId = this.getRequest().getParameter("crowdPackId");
			if(StringUtils.isBlank(crowdPackId)) {
				return EasyResult.error(500, "人群包信息异常");
			}
			OrderExcelUtil util = new OrderExcelUtil();
			try {
				util.process(part.getInputStream());
			} catch (Exception e) {
				logger.error(e.getMessage(),e);
			}
			List<List<String>> list = new ArrayList<>();
			list.addAll(util.getList());
			boolean scuuess = true;// 是否正确
			String msg = "";
			String currentDateString = EasyDate.getCurrentDateString();
			Timestamp currentTimestamp = new Timestamp(System.currentTimeMillis());
			String userAcc = UserUtil.getUser(this.getRequest()).getUserAcc();
			int custCount = 0;
			for (int i = 1; i < list.size(); i++) {
				Map<Object, Object> m = new HashMap<Object, Object>();
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					val = list.get(i).get(j);
					switch (j) {
						case 0:
							m.put("CUST_NAME", val);
							break;
						case 1:
							if(StringUtils.isBlank(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "电话号码不为空";
								break;
							}
							m.put("CUST_PHONE", val);
							break;
						case 2:
							m.put("ORG_CODE", val);
							break;
						case 3:
							m.put("BRAND_CODE", val);
							break;
						case 4:
							m.put("BRAND_NAME", val);
							break;
						case 5:
							m.put("PROD_CODE", val);
							break;
						case 6:
							if(StringUtils.isBlank(val)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "产品品类不为空";
								break;
							}
							m.put("PROD_NAME", val);
							break;
						case 7:
							m.put("PRODUCT_MODEL", val);
							break;
						case 8:
							m.put("FAULT_CODE", val);
							break;
						default:
							break;
					}
				}
				if (scuuess) {
					String str = JSONObject.toJSON(m).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					jsonObject.put("CREATE_ACC", userAcc);//导入人
					jsonObject.put("ID", RandomKit.randomStr());//id
					jsonObject.put("SERVICE_STATUS", "2");//初始状态  --自动发布
					jsonObject.put("CROWD_PACK_ID", crowdPackId);//所属人群包
					jsonObject.put("CREATE_TIME",currentDateString);//同步时间
					jsonObject.put("PUBLISH_TIME", currentDateString);//默认发布时间
					jsonObject.put("DATA_SOURCE", "OTHER");//导入
					jsonObject.put("CREATE_DATE", currentTimestamp);
					EasyRecord record = new EasyRecord("C_NO_AS_ROBOT_AUTO_CALL", "ID").setColumns(jsonObject);
					query.save(record);
					++custCount;

				} else {
					if (!lineScuuess) {
						msg = msg + "第" + i + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {// 出错了不记录
				query.roolback();
				return EasyResult.error(500, "" + msg);
			} else {
				if(custCount>0) {
					query.execute("update C_NO_AS_CROWD_PACK set CUST_NUM = CUST_NUM + ? WHERE ID = ? ", custCount,crowdPackId);
				}
				query.commit();
				return EasyResult.ok("", "人群明细导入成功！");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 导入失败，原因：",e);
			}
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
		}
	}
	
	
	/**
	 * 导出人群包列表
	 * @throws SQLException
	 */
	public void actionForExportCrowdPackList() throws SQLException {
		HttpServletRequest request = this.getRequest();
		JSONObject param = requestToJsonObject(request);
		EasySQL sql = new EasySQL("select t1.*,t2.GROUP_NAME,t2.ID GOURP_ID from C_NO_AS_CROWD_PACK t1 LEFT JOIN C_NO_AS_CROWDPACK_GROUP t2 on t1.GROUP_ID = t2.ID WHERE 1=1 ");
		sql.append(param.getString("groupId")," and GROUP_ID = ? ");
		sql.append(param.getString("name")," and t1.NAME=? ");
		sql.append(param.getString("touchSource")," and t1.TOUCH_SOURCE=? ");
		sql.append(param.getString("dataSource")," and t1.DATA_SOURCE=? ");
		sql.append(param.getString("createAcc")," and t1.CREATE_ACC=? ");
		sql.append(param.getString("createTimeStar")," and t1.CREATE_TIME>= ? ");
		sql.append(param.getString("createTimeEnd")," and t1.CREATE_TIME<=? ");
		sql.append(" ORDER BY t1.CREATE_TIME DESC");
		getQuery().setMaxRow(99999);
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> ccAasPackTag = DictCache.getMapAllDictListByGroupCode(depCode, "CC_AS_PACK_TAG");// 回访结果
		Map<String, Object> ccAsChannel = DictCache.getMapAllDictListByGroupCode(depCode, "CC_AS_CHANNEL");// 

		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 人群包名称");//人群包组	已配置包策略	已配置组策略
		headers.add(" 人群包组");		
		headers.add(" 已配置包策略");
		headers.add(" 已配置组策略");
		headers.add(" 来源系统");
		headers.add(" 人群口径");
		headers.add(" 人群数量");
		headers.add(" 创建时间");
		headers.add(" 创建人");
		headers.add(" 调用模式");
		headers.add(" 更新频率");
		headers.add(" 备注");//最近同步时间	
		headers.add(" 最近同步时间");
		headers.add(" 显示名称");	//显示名称 显示顺序	分类		
		headers.add(" 显示顺序");		
		headers.add(" 分类");		
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		try {
			List<String> list = null;
				for (Map<String, String> map : data) {
					list = new ArrayList<String>();
					list.add(String.valueOf(++i));
					list.add(map.get("NAME"));
					list.add(map.get("GROUP_NAME"));
					list.add(getPackType(map.get("CHANNEL_TYPE"), map.get("STRATEGY_LEVEL"), ccAsChannel, "1"));//{{PACK_CHANNEL_TYPE:CHANNEL_TYPE STRATEGY_LEVEL}}
					list.add(getPackType(map.get("CHANNEL_TYPE"), map.get("STRATEGY_LEVEL"), ccAsChannel, "2"));//{GROUP_CHANNEL_TYPE:CHANNEL_TYPE STRATEGY_LEVEL}}
					list.add(DataSource.getValue(map.get("DATA_SOURCE")));
					list.add(map.get("CONDITION"));
					list.add(map.get("CUST_NUM"));
					list.add(map.get("CREATE_TIME"));
					list.add(map.get("CREATE_ACC"));
					list.add(TouchSource.getValue(map.get("TOUCH_SOURCE")==null?"":map.get("TOUCH_SOURCE").trim()));
					list.add(map.get("SEND_RATE"));
					list.add(map.get("REMARK"));
					list.add(map.get("SYNC_TIME"));
					if("1".equals(map.get("BG_CROWD_STATUS"))){//{{if BG_CROWD_STATUS=='1'}}使用中{{/if}}{{if BG_CROWD_STATUS=='2'}}已删除{{/if}}</td>
						list.add("使用中");
					}else if("2".equals(map.get("BG_CROWD_STATUS"))){
						list.add("已删除");
					}else{
						list.add("");
					}
					list.add(map.get("SHOW_NAME"));
					list.add(map.get("SORT"));
					list.add(ccAasPackTag.get(map.get("CATEGORY"))==null?"":ccAasPackTag.get(map.get("CATEGORY")).toString());
					excelData.add(list);
				}
		}catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "人群包列表.xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}
	/**
	 * 
	 * @param channelType
	 * @param strategyType
	 * @param channelTypeDict
	 * @param checkType //只显示某个类型的数据
	 * @return
	 */
	public String getPackType(String channelType,String strategyType,Map<String, Object> channelTypeDict,String checkType){
		if(StringUtils.isNotBlank(channelType)&&StringUtils.isNotBlank(strategyType)){
			String[] channelTypeArr = channelType.split(",");
			String[] strategyTypeArr = strategyType.split(",");
			String result = "";
			for (int i = 0; i < channelTypeArr.length; i++) {
				Object channelTypeName = channelTypeDict.get(channelTypeArr[i]);
				if(checkType.equals(strategyTypeArr[i])){
					result+=channelTypeName+"<br/>";
				}	
			}
			return result;
		}else{
			return "";
		}
		
		
	}
	
	/**
	 * 导出人群明细列表
	 * @throws SQLException
	 */
	public void actionForExportCrowdList() throws SQLException {
		HttpServletRequest request = this.getRequest();
		JSONObject param = requestToJsonObject(request);
		EasySQL sql = new EasySQL("select * from C_NO_AS_CROWD WHERE 1=1 ");
		CrowdPackSql.getQueryCrowdListCondition(sql, param);
		sql.append(" ORDER BY CREATE_DATE DESC");
		
		EasyQuery query = getQuery();
		query.setMaxRow(10000);
		
		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> statusDict = DictCache.getMapAllDictListByGroupCode(depCode, "CC_AS_REVISIT_STATUS");// 状态
		Map<String, Object> revisitResultDict = DictCache.getMapAllDictListByGroupCode(depCode, "CC_AS_REVISIT_RESULT");// 回访结果

		List<Map<String, String>> data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 姓名");
		headers.add(" 电话号码");
		headers.add(" 产品品牌");
		headers.add(" 产品品类");
		headers.add(" 产品型号");
		headers.add(" 故障代码");
		headers.add(" 创建时间");
		headers.add(" 服务状态");
		headers.add(" 服务结果");
		headers.add(" 回访结果");
		headers.add(" 是否免打扰");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
			for (Map<String, String> map : data) {
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				list.add(map.get("CUST_NAME"));
				list.add(PhoneEncryptUtil.getPhone(map.get("CUST_PHONE")));
				list.add(map.get("BRAND_NAME"));
				list.add(map.get("PROD_NAME"));
				list.add(map.get("PRODUCT_MODEL"));
				list.add(map.get("FAULT_CODE"));
				list.add(map.get("CREATE_TIME"));
				list.add(statusDict.get(map.get("SERVICE_STATUS"))==null?"":statusDict.get(map.get("SERVICE_STATUS")).toString());
				list.add(map.get("RESULT_CONTENT"));
				list.add(revisitResultDict.get(map.get("REVISIT_RESULT"))==null?"":revisitResultDict.get(map.get("REVISIT_RESULT")).toString());
				list.add("1".equals(map.get("IS_DISTURB"))?"是":"否");
				excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "人群明细列表.xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}

	/**
	 * 导出Ai外呼人群明细列表
	 * @throws SQLException
	 */
	public void actionForExportCrowdAiList() throws SQLException {
		HttpServletRequest request = this.getRequest();
		JSONObject param = requestToJsonObject(request);
		EasySQL sql = new EasySQL("select * from C_NO_AS_ROBOT_AUTO_CALL WHERE 1=1 ");
		CrowdPackSql.getAiQueryCrowdListCondition(sql, param);
		sql.append(" ORDER BY CREATE_DATE DESC");

		EasyQuery query = getQuery();
		query.setMaxRow(10000);

		String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
		Map<String, Object> statusDict = DictCache.getMapAllDictListByGroupCode(depCode, "CC_AS_REVISIT_STATUS");// 状态
		Map<String, Object> revisitResultDict = DictCache.getMapAllDictListByGroupCode(depCode, "CC_AS_REVISIT_RESULT");// 回访结果

		List<Map<String, String>> data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 姓名");
		headers.add(" 电话号码");
		headers.add(" 产品品牌");
		headers.add(" 产品品类");
		headers.add(" 产品型号");
		headers.add(" 故障代码");
		headers.add(" 创建时间");
		headers.add(" 服务状态");
		headers.add(" 服务结果");
		headers.add(" 回访结果");
		headers.add(" 是否免打扰");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		for (Map<String, String> map : data) {
			list = new ArrayList<String>();
			list.add(String.valueOf(++i));
			list.add(map.get("CUST_NAME"));
			list.add(PhoneEncryptUtil.getPhone(map.get("CUST_PHONE")));
			list.add(map.get("BRAND_NAME"));
			list.add(map.get("PROD_NAME"));
			list.add(map.get("PRODUCT_MODEL"));
			list.add(map.get("FAULT_CODE"));
			list.add(map.get("CREATE_TIME"));
			list.add(statusDict.get(map.get("SERVICE_STATUS"))==null?"":statusDict.get(map.get("SERVICE_STATUS")).toString());
			list.add(map.get("RESULT_CONTENT"));
			list.add(revisitResultDict.get(map.get("REVISIT_RESULT"))==null?"":revisitResultDict.get(map.get("REVISIT_RESULT")).toString());
			list.add("1".equals(map.get("IS_DISTURB"))?"是":"否");
			excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "人群明细列表.xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}


	/**
	 * 导出免打扰外呼人群明细列表
	 * @throws SQLException
	 */
	public void actionForExportCrowdDndList() throws SQLException {
		HttpServletRequest request = this.getRequest();
		JSONObject param = requestToJsonObject(request);
		EasySQL sql = new EasySQL("select PHONE,CREATE_TIME,PARAM from C_NO_AS_SENSITIVE_CROWD WHERE 1=1 ");
		CrowdPackSql.getSensitiveCrowdListCondition(sql, param);
		sql.append(" ORDER BY CREATE_DATE DESC");

		EasyQuery query = getQuery();
		query.setMaxRow(10000);

		List<Map<String, String>> data = query.queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		// 组装表头
		File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");
		List<String> headers = new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(" 电话号码");
		headers.add(" 创建时间");
		headers.add(" 备注");
		List<ExcelHeaderStyle> styles = new ArrayList<ExcelHeaderStyle>();
		for (String header : headers) {
			ExcelHeaderStyle style = new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData = new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		for (Map<String, String> map : data) {
			list = new ArrayList<String>();
			list.add(String.valueOf(++i));
			list.add(PhoneEncryptUtil.getPhone(map.get("PHONE")));
			list.add(map.get("CREATE_TIME"));
			list.add(map.get("PARAM"));
			excelData.add(list);
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file, "人群明细列表.xlsx");
		} catch (Exception e) {
			logger.error(e.getMessage(),e);
		}
	}


	public void actionForCrowdPackTemplateDownload() {
		File file = new File(this.getRequest().getServletContext().getRealPath("/pages/template/crowd_pack_template.xlsx"));
		renderFile(file, "人群包导入模板.xlsx");
	}
	
	public void actionForCrowdPackCustTemplateDownload() {
		File file = new File(this.getRequest().getServletContext().getRealPath("/pages/template/crowd_pack_cust_template.xlsx"));
		renderFile(file, "人群包明细导入模板.xlsx");
	}

	public void actionForDndCustTemplateDownload() {
		File file = new File(this.getRequest().getServletContext().getRealPath("/pages/template/crowd_pack_dnd_cust_template.xlsx"));
		renderFile(file, "人群包明细导入模板.xlsx");
	}
	
	public static JSONObject requestToJsonObject(HttpServletRequest request) {
		JSONObject requestJson = new JSONObject();
        Enumeration<String> paramNames = request.getParameterNames();
        while (paramNames.hasMoreElements()) {
            String paramName = paramNames.nextElement();
            String[] pv = request.getParameterValues(paramName);
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < pv.length; i++) {
                if (pv[i].length() > 0) {
                    if (i > 0) {
                        sb.append(",");
                    }
                    sb.append(pv[i]);
                }
            }	
            requestJson.put(paramName, sb.toString());
        }
        return requestJson;
	}
}
