package com.yunqu.cc.activeService.servlet;

import java.io.IOException;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServlet;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;

import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.inf.HighValueUserVipService;
import com.yunqu.cc.activeService.inf.IvrIntefaceService;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;

import com.alibaba.fastjson.JSONObject;
@WebServlet("/test")
public class TestServlet extends HttpServlet{

	private static final long serialVersionUID = 2421484910443026334L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		doPost(req, resp);
	}
	
	@Override
	protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
		String command = req.getParameter("command");
		String phone = req.getParameter("phone");
		try {
			if("getH5CustPhoneCrowdPackList".equals(command)){
				IService service = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
				JSONObject json = new JSONObject();
				json.put("command", "getH5CustPhoneCrowdPackList");
				json.put("custPhone", "13411111111");
				JSONObject j = service.invoke(json);
				System.out.println(j);
			}
			
			if("getH5CrowdPackScript".equals(command)){
				IService service = ServiceContext.getService("ACTIVE-SERVICE-CROWD-INTEFACE");
				JSONObject param = new JSONObject();
				param.put("id", "83515379698649999764988");
				JSONObject reqJson = new JSONObject();
				reqJson.put("command", "getH5CrowdPackScript");
				param.put("custPhone", "13411111111");
				reqJson.put("param", param);
				JSONObject j = service.invoke(reqJson);
				System.out.println(j);
			}

			if("getcssVip".equals(command)){
				getCssVipInfo("13229207452");
			}

			if("vipResult".equals(command)){
				CommLogger.getCommLogger("high").info("获取CSS VIP信息vipResult");
				JSONObject queryData = new JSONObject();
				queryData.put("mobile", StringUtils.isBlank(phone) ? "" : phone);
				JSONObject queryParams = new JSONObject();
				queryParams.put("data", queryData);
				queryParams.put("command", "getVipInfo");
				IService service = ServiceContext.getService("AS-MEDIA-VIP-SEARCH");
				JSONObject j = service.invoke(queryParams);
				//CommLogger.getCommLogger("high").info("获取CSS VIP信息异常vipResult1");

			}

			if("vipResult1".equals(command)){
				try {
					JSONObject queryData = new JSONObject();
					queryData.put("mobile", StringUtils.isBlank(phone) ? "" : phone);
					JSONObject queryParams = new JSONObject();
					queryParams.put("data", queryData);
					queryParams.put("command", "getVipInfo");

					CommLogger.getCommLogger("high").info("调用HighValueUserVipService.getVipInfo，参数=" + queryParams.toJSONString());
					JSONObject vipResult = HighValueUserVipService.getVipInfo(queryParams);
					CommLogger.getCommLogger("high").info("HighValueUserVipService.getVipInfo返回结果=" + vipResult.toJSONString());

					// 可以在这里处理返回结果
					if (vipResult != null) {
						String respCode = vipResult.getString("respCode");
						if ("000".equals(respCode)) {
							CommLogger.getCommLogger("high").info("VIP查询成功");
						} else {
							CommLogger.getCommLogger("high").warn("VIP查询失败，respCode=" + respCode +
								", respDesc=" + vipResult.getString("respDesc"));
						}
					}
				} catch (Exception e) {
					CommLogger.getCommLogger("high").error("调用HighValueUserVipService.getVipInfo异常"+e.getMessage(), e);
				}
			}

			String call = req.getParameter("call");
			String type = req.getParameter("type");
			if("getivrinfo".equals(command)){
				JSONObject param = new JSONObject();
				JSONObject paramData = new JSONObject();
				paramData.put("phone", phone);
				paramData.put("call", call);
				paramData.put("type", type);
				param.put("Data", paramData);
				JSONObject jso=	IvrIntefaceService.invoke(param);
				CommLogger.getCommLogger("high").info("获取CSS VIP信息成功"+jso);
			}



		} catch (Exception e) {
			CommLogger.getCommLogger("high").error("获取CSS VIP信息异常"+e.getMessage (), e);
		}
		
	}

	/**
	 * 获取CSS VIP信息
	 * @param phone 手机号
	 * @return CSS VIP查询结果
	 */
	public void   getCssVipInfo(String phone) {
		JSONObject result = new JSONObject();
		try {
			if (StringUtils.isBlank(phone)) {
			}

			JSONObject params = new JSONObject();
			JSONObject json = new JSONObject();
			json.put("phoneNum", phone);
			params.put("params", json);
			params.put("command", "getCssVipInfo");

			IService service = ServiceContext.getService("CSSGW-GETVIP-USERINFO");
			JSONObject resp = service.invoke(params);


			if (GWConstants.RET_CODE_SUCCESS.equals(resp.getString("respCode"))) {
				JSONObject respData = resp.getJSONObject("respData");
				if (respData != null) {
					result.put("isSensitive", respData.getBoolean("isSensitive"));
					result.put("respData", respData.getJSONArray("respData"));
					result.put("success", true);
				}
			} else {
				result.put("success", false);
			}
		} catch (Exception e) {
			result.put("success", false);
		}
	}
}
