package com.yunqu.cc.activeService.servlet;

import java.io.File;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.base.AppBaseServlet;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.utils.ExcelUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;

import com.alibaba.fastjson.JSONObject;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

/**
 * 会员身份优先级配置Servlet
 * <AUTHOR>
 * @date 2025-01-07
 */
@MultipartConfig
@WebServlet("/servlet/MemberPriority")
public class MemberPriorityServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;
    protected Logger logger = CommLogger.getCommLogger("MemberPriority");

    /**
     * 会员身份优先级配置列表页面
     * @return
     */
    public String actionForMemberPriorityList() {
        String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
        JSONObject memberTags = new JSONObject();
        JSONObject hotlines = new JSONObject();
        
        try {
            // 获取会员身份标签数据字典
            memberTags = DictCache.getJsonAllDictListByGroupCode(depCode, "MEMBER_TAG");
            // 获取热线号码数据字典
            hotlines = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_HOTLINE");
        } catch (Exception e) {
            this.error("actionForMemberPriorityList", e);
        }
        
        this.setAttr("memberTags", memberTags);
        this.setAttr("hotlines", hotlines);
        return "/pages/config/member-priority-list.jsp";
    }

    /**
     * 会员身份优先级配置编辑页面
     * @return
     */
    public String actionForMemberPriorityEdit() {
        String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
        JSONObject memberTags = new JSONObject();
        JSONObject hotlines = new JSONObject();
        
        try {
            // 获取会员身份标签数据字典
            memberTags = DictCache.getJsonAllDictListByGroupCode(depCode, "MEMBER_TAG");
            // 获取热线号码数据字典
            hotlines = DictCache.getJsonAllDictListByGroupCode(depCode, "CC_HOTLINE");
        } catch (Exception e) {
            this.error("actionForMemberPriorityEdit", e);
        }
        
        this.setAttr("memberTags", memberTags);
        this.setAttr("hotlines", hotlines);
        return "/pages/config/member-priority-edit.jsp";
    }

    /**
     * 新增会员身份优先级配置
     * @return
     */
    public EasyResult actionForSaveMemberPriority() {
        JSONObject json = new JSONObject();
        JSONObject entInfo = this.getJSONObject();

        try {
            // 新增操作，生成新ID
            json.put("ID", RandomKit.randomStr());
            json.put("CREATE_TIME", EasyDate.getCurrentDateString());
            json.put("CREATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
            json.put("CREATE_NAME", UserUtil.getUser(getRequest()).getUserName());

            json.put("MEMBER_TAG", entInfo.getString("MemberPriority.MEMBER_TAG"));
            json.put("HOTLINE_CODE", entInfo.getString("MemberPriority.HOTLINE_CODE"));
            json.put("MEMBER_LEVEL", entInfo.getString("MemberPriority.MEMBER_LEVEL"));
            json.put("REMARK", entInfo.getString("MemberPriority.REMARK"));
            json.put("HOTLINE_NAME", entInfo.getString("MemberPriority.HOTLINE_NAME"));

            // 验证必填字段
            if (StringUtils.isBlank(json.getString("MEMBER_TAG"))) {
                return EasyResult.fail("会员身份标签不能为空");
            }
            if (StringUtils.isBlank(json.getString("HOTLINE_CODE"))) {
                return EasyResult.fail("热线号码不能为空");
            }
            if (StringUtils.isBlank(json.getString("MEMBER_LEVEL"))) {
                return EasyResult.fail("会员身份等级不能为空");
            }

            // 验证会员身份等级数值范围
            try {
                int levelValue = Integer.parseInt(json.getString("MEMBER_LEVEL"));
                if (levelValue < 1 || levelValue > 20) {
                    return EasyResult.fail("会员身份等级必须是1-20的数字");
                }
            } catch (NumberFormatException e) {
                return EasyResult.fail("会员身份等级必须是数字");
            }

            // 前端已经进行了冲突检查，这里不再重复检查

            EasyRecord record = new EasyRecord("C_AS_MEMBER_PRIORITY", "ID");
            record.setColumns(json);
            this.getQuery().save(record);

            return EasyResult.ok("新增成功");
        } catch (Exception e) {
            this.error("actionForSaveMemberPriority", e);
            return EasyResult.error(500, "新增失败：" + e.getMessage());
        }
    }

    /**
     * 修改会员身份优先级配置
     * @return
     */
    public EasyResult actionForUpdateMemberPriority() {
        JSONObject json = new JSONObject();
        JSONObject entInfo = this.getJSONObject();

        try {
            // 修改操作，使用现有ID
            String id = entInfo.getString("MemberPriority.ID");
            if (StringUtils.isBlank(id)) {
                return EasyResult.fail("修改操作必须提供ID");
            }

            json.put("ID", id);
            json.put("UPDATE_TIME", EasyDate.getCurrentDateString());
            json.put("UPDATE_ACC", UserUtil.getUser(getRequest()).getUserAcc());
            json.put("UPDATE_NAME", UserUtil.getUser(getRequest()).getUserName());

            json.put("MEMBER_TAG", entInfo.getString("MemberPriority.MEMBER_TAG"));
            json.put("HOTLINE_CODE", entInfo.getString("MemberPriority.HOTLINE_CODE"));
            json.put("MEMBER_LEVEL", entInfo.getString("MemberPriority.MEMBER_LEVEL"));
            json.put("REMARK", entInfo.getString("MemberPriority.REMARK"));
            json.put("HOTLINE_NAME", entInfo.getString("MemberPriority.HOTLINE_NAME"));

            // 验证必填字段
            if (StringUtils.isBlank(json.getString("MEMBER_TAG"))) {
                return EasyResult.fail("会员身份标签不能为空");
            }
            if (StringUtils.isBlank(json.getString("HOTLINE_CODE"))) {
                return EasyResult.fail("热线号码不能为空");
            }
            if (StringUtils.isBlank(json.getString("MEMBER_LEVEL"))) {
                return EasyResult.fail("会员身份等级不能为空");
            }

            // 验证会员身份等级数值范围
            try {
                int levelValue = Integer.parseInt(json.getString("MEMBER_LEVEL"));
                if (levelValue < 1 || levelValue > 20) {
                    return EasyResult.fail("会员身份等级必须是1-20的数字");
                }
            } catch (NumberFormatException e) {
                return EasyResult.fail("会员身份等级必须是数字");
            }

            // 前端已经进行了冲突检查，这里不再重复检查

            EasyRecord record = new EasyRecord("C_AS_MEMBER_PRIORITY", "ID");
            record.setColumns(json);
            this.getQuery().update(record);

            return EasyResult.ok("修改成功");
        } catch (Exception e) {
            this.error("actionForUpdateMemberPriority", e);
            return EasyResult.error(500, "修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除会员身份优先级配置
     * @return
     */
    public EasyResult actionForDeleteMemberPriority() {
        JSONObject data = getJSONObject();
        String ids = data.getString("ids");
        logger.info("删除操作 - 接收到的ids参数: [" + ids + "]");

        if (StringUtils.isBlank(ids)) {
            logger.warn("删除操作 - ids参数为空或null");
            return EasyResult.fail("请选择要删除的记录");
        }
        
        try {
            String[] idArray = ids.split(";");
            logger.info("删除操作 - 分割后的ID数组: " + java.util.Arrays.toString(idArray));

            for (String id : idArray) {
                if (StringUtils.isNotBlank(id)) {
                    logger.info("删除操作 - 正在删除ID: [" + id + "]");
                    EasyRecord record = new EasyRecord("C_AS_MEMBER_PRIORITY", "ID");
                    record.setPrimaryValues(id);
                    this.getQuery().deleteById(record);
                    logger.info("删除操作 - 成功删除ID: [" + id + "]");
                }
            }

            logger.info("删除操作 - 全部删除完成");
            return EasyResult.ok("删除成功");
        } catch (Exception e) {
            logger.error("删除操作失败", e);
            this.error("actionForDeleteMemberPriority", e);
            return EasyResult.error(500, "删除失败：" + e.getMessage());
        }
    }

    /**
     * 检查热线号码冲突
     * @return
     */
    public EasyResult actionForCheckHotlineConflict() {
        JSONObject data = getJSONObject();
        String memberTag = data.getString("memberTag");
        String memberTagName = data.getString("memberTagName");
        String hotlineCode = data.getString("hotlineCode");
        String hotlineCodeNames = data.getString("hotlineCodeNames");
        String memberLevel = data.getString("memberLevel");
        String excludeId = data.getString("excludeId");

        logger.info("冲突检查 - 接收参数: memberTag=[" + memberTag + "], memberTagName=[" + memberTagName + "], hotlineCode=[" + hotlineCode + "], hotlineCodeNames=[" + hotlineCodeNames + "], memberLevel=[" + memberLevel + "], excludeId=[" + excludeId + "]");

        if (StringUtils.isBlank(memberTag) || StringUtils.isBlank(hotlineCode) || StringUtils.isBlank(memberLevel)) {
            logger.warn("冲突检查 - 必要参数为空，跳过检查");
            JSONObject result = new JSONObject();
            result.put("conflict", false);
            return EasyResult.ok(result);
        }

        try {
            // 检查是否存在完全相同的配置
            String checkResult = checkExactDuplicateConfig(memberTag, memberTagName, hotlineCode, hotlineCodeNames, memberLevel, excludeId);

            JSONObject result = new JSONObject();
            if (checkResult != null) {
                result.put("conflict", true);
                result.put("message", checkResult);
            } else {
                result.put("conflict", false);
            }
            return EasyResult.ok(result);

        } catch (Exception e) {
            this.error("actionForCheckHotlineConflict", e);
            JSONObject result = new JSONObject();
            result.put("conflict", false);
            result.put("error", "检查失败：" + e.getMessage());
            return EasyResult.ok(result);
        }
    }



    /**
     * 导入Excel数据 - 参考OrgAgentServlet.actionForImportOrgAgent方法
     * @return
     */
    public EasyResult actionForImportMemberPriority() {
        try {
            Part part = getFile("file");
            List<List<Object>> list = ExcelUtils.getListByExcel(part.getInputStream(), part.getSubmittedFileName());
            logger.info("导入Excel数据 - 文件名: " + part.getSubmittedFileName()+JSONObject.toJSONString(list));
            if (list == null || list.isEmpty()) {
                throw new Exception("解析文件内容为空！");
            }
            return EasyResult.ok(analysisExcelData(list, UserUtil.getUser(this.getRequest()).getUserAcc(),UserUtil.getUser(this.getRequest()).getEpCode()));
        } catch (Exception e) {
            logger.error("数据导入失败", e);
            return EasyResult.fail("数据导入失败：" + e.getMessage());
        }
    }

    /**
     * 解析Excel数据并保存 - 参考OrgAgentServlet的实现模式
     * @param list Excel数据列表
     * @param userAcc 操作用户账号
     * @return 处理结果信息
     */
    private StringBuilder analysisExcelData(List<List<Object>> list, String userAcc,String epCode) {
        StringBuilder sb = new StringBuilder();
        int succCount = 0;
        int failCount = 0;

        // 从第二行开始处理数据（跳过标题行）
        for (int i = 0; i < list.size(); i++) {
            try {
                List<Object> objData = list.get(i);
                if (objData == null || objData.isEmpty()) {
                    continue;
                }

                // 获取Excel列数据
                String memberTag = getStringData(objData, 0);      // 会员身份标签
                String hotlineCode = getStringData(objData, 1);    // 热线号码
                String memberLevel = getStringData(objData, 2);    // 会员身份等级
                String remark = getStringData(objData, 3);         // 备注
                logger.info("导入数据 - 第" + (i + 1) + "行数据: memberTag=[" + memberTag + "], hotlineCode=[" + hotlineCode + "], memberLevel=[" + memberLevel + "], remark=[" + remark + "]");
                // 数据验证
                if (StringUtils.isBlank(memberTag)) {
                    throw new Exception("会员身份标签不能为空");
                }
                if (StringUtils.isBlank(hotlineCode)) {
                    throw new Exception("热线号码不能为空");
                }
                if (StringUtils.isBlank(memberLevel)) {
                    throw new Exception("会员身份等级不能为空");
                }

                // 验证会员身份等级是否为数字
                int levelValue;
                try {
                    levelValue = Integer.parseInt(memberLevel);
                    if (levelValue < 1 || levelValue > 20) {
                        throw new Exception("会员身份等级必须是1-20的数字");
                    }
                } catch (NumberFormatException e) {
                    throw new Exception("会员身份等级必须是数字");
                }

                // 进行冲突检查，与新增操作保持一致
                String memberTagName = DictCache.getDictVal(epCode, "MEMBER_TAG", memberTag);
                if(StringUtils.isBlank(memberTagName)){
                    sb.append("第" + (i + 1) + "行导入失败 - 会员身份标签不存在");
                    continue;
                }
                // 处理逗号分隔的热线号码，获取对应的中文名称
                String hotlineCodeName = "";
                Boolean isHotlineCodeExist = false;
                if (StringUtils.isNotBlank(hotlineCode)) {
                    String[] hotlineCodes = hotlineCode.split(",");
                    List<String> hotlineNames = new ArrayList<>();
                    for (String singleHotline : hotlineCodes) {
                        singleHotline = singleHotline.trim();
                        if (StringUtils.isNotBlank(singleHotline)) {
                            String hotlineName = DictCache.getDictVal(epCode, "CC_HOTLINE", singleHotline);
                            if(StringUtils.isNotBlank(hotlineName)){
                                hotlineNames.add(hotlineName);
                            }else{
                                isHotlineCodeExist = true;
                                logger.error("第" + (i + 1) + "行导入失败 - 热线号码不存在");
                                sb.append("第[" + (i + 1) + "]行导入失败，失败原因： 第" + (i + 1) + "行导入失败 - 热线号码："+singleHotline+"不存在  <br/>");
                                break;
                            }
                        }
                    }
                    hotlineCodeName = String.join(",", hotlineNames);
                }
                if (isHotlineCodeExist){
                    failCount++;
                    continue;
                }
                String conflictResult = checkExactDuplicateConfig(memberTag, memberTagName, hotlineCode, hotlineCodeName, memberLevel, null);
                if (conflictResult != null) {
                    logger.info("第" + (i + 1) + "行导入失败 - 冲突检查"+conflictResult);
                    sb.append("第[" + (i + 1) + "]行导入失败，失败原因：" + conflictResult + " <br/>");
                    failCount++;
                    continue;
                }

                // 保存数据
                EasyRecord record = new EasyRecord("C_AS_MEMBER_PRIORITY", "ID");
                record.setPrimaryValues(RandomKit.randomStr());
                record.set("MEMBER_TAG", memberTag);
                record.set("HOTLINE_CODE", hotlineCode);
                record.set("HOTLINE_NAME", hotlineCodeName);
                record.set("MEMBER_LEVEL", levelValue);
                record.set("REMARK", remark);
                record.set("CREATE_TIME", EasyDate.getCurrentDateString());
                record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
                record.set("CREATE_ACC", userAcc);
                record.set("UPDATE_ACC", userAcc);
                record.set("UPDATE_NAME", UserUtil.getUser(this.getRequest()).getUserName());
                record.set("CREATE_NAME", UserUtil.getUser(this.getRequest()).getUserName());

                this.getQuery().save(record);
                succCount++;

            } catch (Exception e) {
                logger.error("第" + (i + 1) + "行导入失败", e);
                sb.append("第[" + (i + 1) + "]行导入失败，失败原因：" + e.getMessage() + " <br/>");
                failCount++;
            }
        }

        sb.append("操作完成，成功导入" + succCount + "条数据，失败" + failCount + "条数据");
        return sb;
    }

    /**
     * 获取字符串数据 - 参考OrgAgentServlet的实现
     * @param objData 行数据
     * @param idx 列索引
     * @return 字符串值
     */
    private String getStringData(List<Object> objData, int idx) {
        if (objData.size() <= idx) {
            return "";
        }
        Object obj = objData.get(idx);
        return obj == null ? "" : StringUtils.trimToEmpty(obj.toString());
    }




    /**
     * 检查配置是否与现有配置冲突（用于前端实时检查）
     * 冲突规则：
     * 1. 同一个热线号码存在相同的会员身份等级（不管会员标签是什么）
     * 2. 同一个热线号码存在相同的会员身份标签（不管等级是什么）
     * @param memberTag 会员身份标签代码
     * @param memberTagName 会员身份标签中文名称
     * @param hotlineCode 热线号码（逗号分隔）
     * @param hotlineCodeNames 热线号码中文名称（逗号分隔）
     * @param memberLevel 会员身份等级
     * @param excludeId 要排除的ID（修改时使用）
     * @return 如果存在冲突返回错误信息，否则返回null
     */
    private String checkExactDuplicateConfig(String memberTag, String memberTagName, String hotlineCode, String hotlineCodeNames, String memberLevel, String excludeId) {
        try {
            // 1. 检查同一个热线号码是否已存在相同的会员身份标签
            String memberTagConflict = checkMemberTagConflict(memberTag, memberTagName, hotlineCode, excludeId);
            if (memberTagConflict != null) {
                return memberTagConflict;
            }

            // 2. 检查同一个热线号码是否存在相同的会员身份等级
            String hotlineLevelConflict = checkHotlineLevelConflict(hotlineCode, hotlineCodeNames, memberLevel, excludeId);
            if (hotlineLevelConflict != null) {
                return hotlineLevelConflict;
            }

            return null; // 没有冲突
        } catch (Exception e) {
            logger.error("检查配置冲突失败", e);
            return "数据验证失败，请重试";
        }
    }

    /**
     * 检查同一个热线号码是否已存在相同的会员身份标签
     * @param memberTag 会员身份标签代码
     * @param memberTagName 会员身份标签中文名称
     * @param hotlineCode 热线号码（逗号分隔）
     * @param excludeId 要排除的ID（修改时使用）
     * @return 如果存在冲突返回错误信息，否则返回null
     */
    private String checkMemberTagConflict(String memberTag, String memberTagName, String hotlineCode, String excludeId) {
        try {
            // 分割热线号码，逐个检查是否与现有配置冲突
            String[] hotlineCodes = hotlineCode.split(",");
            List<String> conflictHotlineNames = new ArrayList<>();

            for (String singleHotline : hotlineCodes) {
                singleHotline = singleHotline.trim();
                if (StringUtils.isNotBlank(singleHotline)) {
                    // 检查同一个热线号码是否存在相同的会员身份标签（不管等级是什么）
                    String checkSql = "SELECT COUNT(1) FROM C_AS_MEMBER_PRIORITY WHERE MEMBER_TAG = ? " +
                                     "AND REGEXP_INSTR(HOTLINE_CODE, ?) > 0";
                    Object[] checkParams;

                    if (StringUtils.isNotBlank(excludeId)) {
                        checkSql += " AND ID != ?";
                        checkParams = new Object[]{memberTag, singleHotline, excludeId};
                    } else {
                        checkParams = new Object[]{memberTag, singleHotline};
                    }

                    boolean found = this.getQuery().queryForExist(checkSql, checkParams);
                    if (found) {
                        // 获取热线号码的中文名称
                        String hotlineName = DictCache.getDictVal(UserUtil.getUser(this.getRequest()).getEpCode(), "CC_HOTLINE", singleHotline);
                        conflictHotlineNames.add(hotlineName + "(" + singleHotline + ")");
                    }
                }
            }

            if (!conflictHotlineNames.isEmpty()) {
                return "热线号码：" + String.join("、", conflictHotlineNames) + " 已存在会员身份标签：" + memberTagName + "(" + memberTag + ") 的配置";
            }

            return null; // 没有冲突
        } catch (Exception e) {
            logger.error("检查会员身份标签冲突失败", e);
            return "数据验证失败，请重试";
        }
    }

    /**
     * 检查同一个热线号码是否存在相同的会员身份等级
     * @param hotlineCode 热线号码（逗号分隔）
     * @param hotlineCodeNames 热线号码中文名称（逗号分隔）
     * @param memberLevel 会员身份等级
     * @param excludeId 要排除的ID（修改时使用）
     * @return 如果存在冲突返回错误信息，否则返回null
     */
    private String checkHotlineLevelConflict(String hotlineCode, String hotlineCodeNames, String memberLevel, String excludeId) {
        try {
            // 分割热线号码和对应的中文名称
            String[] hotlineCodes = hotlineCode.split(",");
            String[] hotlineNames = hotlineCodeNames.split(",");

            // 分割热线号码，逐个检查是否与现有配置冲突
            List<String> conflictHotlineNames = new ArrayList<>();

            for (int i = 0; i < hotlineCodes.length; i++) {
                String singleHotline = hotlineCodes[i].trim();
                if (StringUtils.isNotBlank(singleHotline)) {
                    // 检查同一个热线号码是否存在相同的会员身份等级（不管会员标签是什么）
                    String checkSql = "SELECT COUNT(1) FROM C_AS_MEMBER_PRIORITY WHERE MEMBER_LEVEL = ? " +
                                     "AND REGEXP_INSTR(HOTLINE_CODE, ?) > 0";
                    Object[] checkParams;

                    if (StringUtils.isNotBlank(excludeId)) {
                        checkSql += " AND ID != ?";
                        checkParams = new Object[]{memberLevel, singleHotline, excludeId};
                    } else {
                        checkParams = new Object[]{memberLevel, singleHotline};
                    }

                    boolean found = this.getQuery().queryForExist(checkSql, checkParams);
                    if (found) {
                        // 获取对应的中文名称
                        String hotlineName = i < hotlineNames.length ? hotlineNames[i].trim() : singleHotline;
                        conflictHotlineNames.add(hotlineName + "(" + singleHotline + ")");
                    }
                }
            }

            if (!conflictHotlineNames.isEmpty()) {
                return "热线号码：" + String.join("、", conflictHotlineNames) + " 已存在会员身份等级：" + memberLevel + " 的配置";
            }

            return null; // 没有冲突
        } catch (Exception e) {
            logger.error("检查热线号码等级冲突失败", e);
            return "数据验证失败，请重试";
        }
    }



}
