package com.yunqu.cc.activeService.servlet;

import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.Part;

import org.apache.catalina.core.ApplicationPart;
import org.apache.commons.fileupload.FileItem;
import org.apache.commons.fileupload.disk.DiskFileItemFactory;
import org.apache.commons.fileupload.servlet.ServletFileUpload;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.ss.usermodel.WorkbookFactory;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.UserUtil;
import com.yq.busi.common.util.http.HttpResp;
import com.yq.busi.common.util.http.HttpUtil;
import com.yq.busi.common.util.security.SecurityUtil;
import com.yunqu.cc.activeService.base.AppBaseServlet;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.inf.IvrRefreshDataService;
import com.yunqu.cc.activeService.inf.IvrService;

@MultipartConfig
@WebServlet("/servlet/ivrService")
public class IvrServiceServlet  extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	private final Logger logger = CommLogger.getCommLogger("ivr");
	private static final Logger ivrFileLogger = CommLogger.getCommLogger("ivrFile");
	
	
	public EasyResult actionForSaveIvrConfig(){
		EasyResult result = new EasyResult();
		JSONObject ivrConfig = this.getJSONObject("ivrConfig");
		JSONObject ivrConfigDetail = this.getJSONObject("ivrConfigDetail");
		String call=this.getJSONObject().getString("call");
		String city=this.getJSONObject().getString("city");
		String vip=this.getJSONObject().getString("vip");
		String callType=this.getJSONObject().getString("callType");
		String crowdPackList=this.getJSONObject().getString("crowdPackList");
		System.out.println(this.getJSONObject().toJSONString());
		EasyQuery query = this.getQuery();
		Boolean save=StringUtils.isBlank(ivrConfig.getString("ID"));
		try {
			query.begin();
			String ivrStrategyId = "";//策略ID
			String ivrStrategyDetailId = "";//策略详情ID
			String sysTime= EasyDate.getCurrentDateString();
			String agent=UserUtil.getUser(this.getRequest()).getUserAcc();
			if (save){
				ivrStrategyId=RandomKit.randomStr();
				ivrConfig.put("ID",ivrStrategyId);
				ivrConfig.put("CREATE_TIME", sysTime);
				ivrConfig.put("CREATE_ACC",agent);
				ivrConfig.put("UPDATE_TIME", sysTime);
				ivrConfig.put("UPDATE_ACC",agent);
			} else {
				ivrStrategyId=ivrConfig.getString("ID");
				ivrConfig.put("UPDATE_TIME", sysTime);
				ivrConfig.put("UPDATE_ACC",agent);
			}
			EasyRecord record = new EasyRecord("C_NO_AS_IVR_STRATEGY", "ID").setColumns(ivrConfig);
			if (save) {
				query.save(record);
			} else {
				query.update(record);
			}
			if(!save){//修改需要删除原先的所有的配置表
				JSONObject delJson=new JSONObject();
				delJson.put("IVR_STRATEGY_ID", ivrStrategyId);
				EasyRecord del = new EasyRecord("C_NO_AS_IVR_DETAIL", "IVR_STRATEGY_ID").setColumns(delJson);
				query.deleteById(del);
				 del = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "IVR_STRATEGY_ID").setColumns(delJson);
				query.deleteById(del);
				 del = new EasyRecord("C_NO_AS_IVR_DETAIL_CALL", "IVR_STRATEGY_ID").setColumns(delJson);
				query.deleteById(del);
			}
			//IVR策略配置详情表
			ivrStrategyDetailId=RandomKit.randomStr();
			ivrConfigDetail.put("ID", ivrStrategyDetailId);
			ivrConfigDetail.put("IVR_STRATEGY_ID", ivrStrategyId);
			ivrConfigDetail.put("IS_OPEN", ivrConfig.getString("IS_OPEN"));
			record = new EasyRecord("C_NO_AS_IVR_DETAIL", "ID").setColumns(ivrConfigDetail);
			query.save(record);

			//IVR策略配置详情热线关联表
			if(!call.equals("")){
				String[] calls=call.split(",");
				JSONObject callJson=new JSONObject();
				for(String str:calls){
					callJson=new JSONObject();
					callJson.put("IVR_STRATEGY_ID", ivrStrategyId);
					callJson.put("IVR_STRATEGY_DETAIL_ID", ivrStrategyDetailId);
					callJson.put("HOTLINE", str);
					callJson.put("CREATE_TIME", sysTime);
					callJson.put("CREATE_ACC",agent);
					callJson.put("ID", RandomKit.randomStr());
					record = new EasyRecord("C_NO_AS_IVR_DETAIL_CALL", "ID").setColumns(callJson);
					query.save(record);
				}
			}
			
			//IVR策略配置详情关联表
			if(city!=null){
				String[] keys=city.split(",");
				JSONObject keysJson=new JSONObject();
				for(String str:keys){
					keysJson.put("IVR_STRATEGY_ID", ivrStrategyId);
					keysJson.put("IVR_STRATEGY_DETAIL_ID", ivrStrategyDetailId);
					keysJson.put("STRATEGY_TYPE", 1);
					keysJson.put("PHONE_BELONG_AREA", str);
					keysJson.put("CREATE_TIME", sysTime);
					keysJson.put("CREATE_ACC",agent);
					keysJson.put("ID", RandomKit.randomStr());
					record = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "ID").setColumns(keysJson);
					query.save(record);
				}
			}
			if(crowdPackList!=null){
				String[] keys=crowdPackList.split(",");
				JSONObject keysJson=new JSONObject();
				for(String str:keys){
					keysJson.put("IVR_STRATEGY_ID", ivrStrategyId);
					keysJson.put("IVR_STRATEGY_DETAIL_ID", ivrStrategyDetailId);
					keysJson.put("STRATEGY_TYPE", 2);
					keysJson.put("CROWD_PACK_ID", str);
					keysJson.put("CREATE_TIME", sysTime);
					keysJson.put("CREATE_ACC",agent);
					keysJson.put("ID", RandomKit.randomStr());
					record = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "ID").setColumns(keysJson);
					query.save(record);
				}
			}
			if(vip!=null){
				String[] keys=vip.split(",");
				JSONObject keysJson=new JSONObject();
				for(String str:keys){
					keysJson.put("IVR_STRATEGY_ID", ivrStrategyId);
					keysJson.put("IVR_STRATEGY_DETAIL_ID", ivrStrategyDetailId);
					keysJson.put("STRATEGY_TYPE", 3);
					keysJson.put("VIP", str);
					keysJson.put("CREATE_TIME", sysTime);
					keysJson.put("CREATE_ACC",agent);
					keysJson.put("ID", RandomKit.randomStr());
					record = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "ID").setColumns(keysJson);
					query.save(record);
				}
			}
			if(callType!=null){
				String[] keys=callType.split(",");
				JSONObject keysJson=new JSONObject();
				for(String str:keys){
					keysJson.put("IVR_STRATEGY_ID", ivrStrategyId);
					keysJson.put("IVR_STRATEGY_DETAIL_ID", ivrStrategyDetailId);
					keysJson.put("STRATEGY_TYPE", 4);
					keysJson.put("CALL_TYPE", str);
					keysJson.put("CREATE_TIME", sysTime);
					keysJson.put("CREATE_ACC",agent);
					keysJson.put("ID", RandomKit.randomStr());
					record = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "ID").setColumns(keysJson);
					query.save(record);
				}
			}
			query.commit();
		}catch (Exception e) {
			try {
				query.roolback();
				this.logger.error("IVR");
				return EasyResult.error(500, "操作失败!"+e);
			} catch (SQLException e1) {
				e1.printStackTrace();
				return EasyResult.error(500, "操作失败!"+e);
			}
		}
		return EasyResult.ok("", "操作成功!");
		
	}
	public EasyResult actionForUpload(){
		EasyResult result = new EasyResult();
		try {
			DiskFileItemFactory factory = new DiskFileItemFactory();
			ServletFileUpload upload = new ServletFileUpload(factory);
			List<FileItem> items = upload.parseRequest(this.getRequest());
			String ordFileName = this.getRequest().getParameter("ordFileName");//旧的文件名称用于删除
			String voiceType = this.getPara("voiceType");//上传语音类型
			ivrFileLogger.info("语音文件类型voiceType2="+voiceType);
			String saveFileName=null;
			if("2".equals(voiceType)){//排队提示语亿讯命令格式要求：99xxxxxxx
				saveFileName="99"+getRandomNum();
				if(this.getQuery().queryForExist("select count(1) from C_NO_AS_IVR_DETAIL where WAIT_TIP_WORDS_URL like ? ", new Object[] {saveFileName+"%"})) {
					result.addFail("上传文件名重复，请重新上传");
					return result;
				}
				
			}else {
				saveFileName=new SimpleDateFormat("yyyyMMdd").format(new Date())+RandomKit.uniqueStr();
			}
			// 单个附件最大值(单位M)
			long maxFileLen = ConfigUtil.getInt(Constants.APP_NAME, "IVR_MAX_LEN", 10);
			// 附件格式
			String[] fileSuffix  = null;
			fileSuffix = ConfigUtil.getString(Constants.APP_NAME, "IVR_SUFFIX", "mp3;flac").split(";");
	
			
			Collection<Part> parts = this.getRequest().getParts();
			for(Part part : parts){
				String submittedFileName = part.getSubmittedFileName();
				if(StringUtils.isBlank(submittedFileName)){
					continue;
				}
				ivrFileLogger.info(UserUtil.getUser(this.getRequest()).getUserAcc()+"上传附件"+submittedFileName);
				if(part instanceof ApplicationPart){
					ApplicationPart p = (ApplicationPart)part;
					long sizeInBytes = p.getSize();
					String fileName = submittedFileName;
					int fileSize = 0;
					
					if(sizeInBytes == 0){
						fileSize = 0;
					}else if(sizeInBytes / 1024 == 0 && sizeInBytes > 1){
						fileSize = 1;
					}else if (sizeInBytes / 1024 > 1) {
						fileSize = (int) (sizeInBytes / 1024) + 1;
					}
					
					String suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
					if (!fileSuffixSet(fileSuffix, suffix)) {
						result.addFail("上传附件失败，附件格式应为:" + JSONObject.toJSONString(fileSuffix) + "中的一种");
						return result;
					}
					if (sizeInBytes < 1024 * 1024 * maxFileLen) {
						// 附件上传,文件地址存放地址,根目录
						String filePath = getFileSaveRootPath(voiceType);
						ivrFileLogger.info(UserUtil.getUser(this.getRequest()).getUserAcc()+"上传附件大小="+sizeInBytes+",上传目录="+filePath);
						
						// 创建文件夹
						File f = new File(filePath);
						if (!f.exists()) {
							f.mkdirs();
						}
						ivrFileLogger.info("开始上传文件");
						InputStream in = part.getInputStream();  
						byte[] buffer = new byte[1024];
						int len = 0;
						
						// 文件最终上传的位置
						String newFileName = saveFileName+"." + suffix;
						String fileFullName = filePath+newFileName;
						ivrFileLogger.info("上传文件地址="+fileFullName);
						OutputStream out = new FileOutputStream(fileFullName);
						
						while ((len = in.read(buffer)) != -1) {
							out.write(buffer, 0, len);
						}
						ivrFileLogger.info("结束本地上传文件");
						out.close();
						in.close();
						ivrFileLogger.info("开始上传另一台服务器文件");
						boolean synFile = synFile(fileFullName,newFileName,voiceType);
						if(!synFile){
							result.addFail("上传附件失败，本地上传成功但另一台服务器上传失败");
							return result;
						}
						ivrFileLogger.info("结束上传另一台服务器文件");

						result.setSuccess(newFileName, "成功!");
						result.setState(1);
						return result;
					}else{
						result.addFail("上传附件失败，上传的文件大小不能大于"+maxFileLen);
						return result;
					}
					
				}
			}
		} catch (Exception e) {
			e.printStackTrace();
			ivrFileLogger.error("上传附件失败，原因：" + e.getMessage(), e);
		}
		result.addFail("上传附件失败,无法获取上传文件");
	return result;
	}
	public static void main(String[] args) {
		synFile("/activeService/servlet/ivrService?action=Download&filePath=998438340.mp3","998438340.mp3","2");
	}
	/**
	 * 同步其他服务器
	 * @param fileUrl
	 * @return
	 */
	public static boolean synFile(String fileUrl,String fileName,String voiceType){
		String otherServerAddress = AppContext.getContext(Constants.APP_NAME).getProperty("OTHER_SERVER_ADDRESS","http://127.0.0.1:8080");
		String url = otherServerAddress+"/uinterface/Receive.do";
		JSONObject json = new JSONObject();
		json.put("sender", "CC-ACTIVESERVICE");
		json.put("password", "YQ_85521717");
		json.put("serialId", "123");
		json.put("command", "synFile");
		json.put("serviceId", "ACTIVE-SERVICE-INTEFACE");
		json.put("timestamp", DateUtil.getCurrentDateStr());
		String sign = SecurityUtil
		.encryptMsgByMD5(json.getString("sender")
		+json.getString("password")
		+json.getString("timestamp")
		+json.getString("serialId"));
		json.put("signature", sign);
		json.put("fileUrl", fileUrl);
		json.put("fileName", fileName);
		json.put("voiceType", voiceType);
		HttpResp resp = HttpUtil.post(url,json.toJSONString(),GWConstants.ENCODE_UTF8);
		ivrFileLogger.info("同步其他服务器附件结果："+resp.getCode() +",内容"+ resp.getResult());

		try {
			if(resp.getCode()==200&&JSON.parseObject(resp.getResult()).getBooleanValue("state")){
				return true;
			}
		} catch (Exception e) {
			// TODO: handle exception
		}
		return false;
	}
	public boolean fileSuffixSet(String[] arr, String value) {
		Set<String> set = new HashSet<String>(Arrays.asList(arr));
		return set.contains(value);
	}
	
	/**
	 * 下载附件
	 * 
	 * @return
	 */
	public EasyResult actionForDownload() {
		InputStream in = null;
		OutputStream out = null;
		try {
			String voiceType = this.getRequest().getParameter("voiceType");
			String rootPath = getFileSaveRootPath(voiceType);
			if(StringUtils.isBlank(rootPath)){
				ivrFileLogger.error(CommonUtil.getClassNameAndMethod(this)+" 下载文件出错,未配置文件根目录!");
				return null;
			}

			// 获得请求文件名
			String filePath = this.getRequest().getParameter("filePath");
			if(StringUtils.isBlank(filePath)){
				ivrFileLogger.error(CommonUtil.getClassNameAndMethod(this)+" 下载文件出错,未传入文件路径!");
				return null;
			}
			ivrFileLogger.info(UserUtil.getUser(this.getRequest()).getUserAcc()+"下载附件"+filePath);
			//判断文件是否存在
			File file = new File(rootPath+filePath);
			if(!file.exists()){
				ivrFileLogger.error(CommonUtil.getClassNameAndMethod(this)+" 文件下载失败,文件不存在:"+file.getAbsolutePath());
				return null;
			}
			String fileName = file.getName();
			
			//查询文件名称
			fileName = filePath;
			
			// 设置文件MIME类型
			this.getResponse().setContentType(getServletContext().getMimeType( filePath));
			// 设置Content-Disposition
			this.getResponse().setHeader("Content-Disposition","attachment;filename=" + fileName);
			// 读取目标文件，通过response将目标文件写到客户端
			// 读取文件
			in = new FileInputStream( rootPath + filePath );
			out = this.getResponse().getOutputStream();
			
			// 写文件
			int b;
			while ((b = in.read()) != -1) {
				out.write(b);
			}
		} catch (Exception e) {
			e.printStackTrace();
			ivrFileLogger.error("下载附件失败，原因：" + e.getMessage(), e);
		}finally {
			if(in!=null){
				try {
					in.close();
				} catch (IOException e) {
				}
			}
			if(out!=null){
				try {
					out.close();
				} catch (IOException e) {
				}
			}
		}
		return null;
	}

	public EasyResult actionForIvrPriorityLevelCheck() {
		EasySQL sql=new EasySQL();
		sql.append("select  NAME  from  C_NO_AS_IVR_STRATEGY  ");
		sql.append(this.getJSONObject().getString("type"),"where TYPE=?   ");
		sql.append(this.getJSONObject().getString("level"),"and  PRIORITY_LEVEL=? ");
		sql.append(this.getJSONObject().getString("id"),"and  id !=? ");
		try {
			 List<JSONObject> queryForList = this.getQuery().queryForList(sql.getSQL(), sql.getParams(),new JSONMapperImpl());
			return EasyResult.ok(queryForList);
		} catch (SQLException e) {
			e.printStackTrace();
		}
		return EasyResult.ok(null);
	}
	public EasyResult actionForIvrDelete() {
		JSONObject json = new JSONObject();
		JSONObject obj = this.getJSONObject();
		JSONArray ids = obj.getJSONArray("ids");
		EasyQuery query = this.getQuery();
		try {
			query.begin();
			EasyRecord record = null;
			for (int i = 0; i < ids.size(); i++) {
				json = new JSONObject();
				json.put("ID", ids.getString(i));
				record = new EasyRecord("C_NO_AS_IVR_STRATEGY", "ID").setColumns(json);
				query.deleteById(record);
				json = new JSONObject();
				json.put("IVR_STRATEGY_ID", ids.getString(i));
				record = new EasyRecord("C_NO_AS_IVR_DETAIL", "IVR_STRATEGY_ID").setColumns(json);
				query.deleteById(record);
				record = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "IVR_STRATEGY_ID").setColumns(json);
				query.deleteById(record);
				record = new EasyRecord("C_NO_AS_IVR_DETAIL_CALL", "IVR_STRATEGY_ID").setColumns(json);
				query.deleteById(record);
			}
			query.commit();
			return EasyResult.ok("删除成功!");
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				// TODO Auto-generated catch block
				e1.printStackTrace();
			}
			this.error("删除失败，原因：" + e.getMessage(), e);
			return EasyResult.error(500, "删除失败，原因：" + e.getMessage());
		}
	}
	/**
	 * IVR策略导入
	 * @return
	 */
	public JSONObject actionForIvrImport(){
		EasyQuery query = this.getQuery();
		String userAcc = UserUtil.getUser(this.getRequest()).getUserAcc();
		String createTime= EasyDate.getCurrentDateString();
		try {
			query.begin();
			Part part = getFile("file");
			Workbook workbook = WorkbookFactory.create(part.getInputStream());
			List<List<String>> list = new ArrayList<>();
			Sheet sheet = workbook.getSheetAt(0);
			int maxLine = sheet.getLastRowNum();
			int lastCellNum = 0;
			boolean scuuess = true;// 是否正确
			String msg = "";
			for (int ii = 0; ii <= maxLine; ii++) {
				List<String> rows = new ArrayList<>();
				Row row = sheet.getRow(ii);
				if (ii == 0) {
					lastCellNum = row.getLastCellNum();
				}
				if (row != null) {
					for (int j = 0; j < lastCellNum; j++) {
						Cell cell = row.getCell(j);
						if (cell != null) {
							String val = Utils.getCellValue(cell);
							if (StringUtils.isBlank(val)) {
								rows.add("");
							} else {
								rows.add(val);
							}
						}else {
							rows.add("");
						}
					}
					list.add(rows);
				}
			}
			List<EasyRow> data = query.queryForList("select T.CODE,T.NAME FROM V_CF_SKILLS T WHERE T.SESSION_TYPE='02' ", null);
			//技能组
			HashMap<String, String> skillMap = new HashMap<String,String>();
			for (int i = 0; i < data.size(); i++) {
				EasyRow row = data.get(i);
				skillMap.put(row.getColumnValue("NAME"), row.getColumnValue("CODE"));
			}			
			String depCode = UserUtil.getUser(this.getRequest()).getEpCode();
			//热线
			Map<String, Object> callDict = DictCache.getNCMapByGroupCode(depCode, "CC_HOTLINE");
			//用户身份
			Map<String, Object> vipDict = DictCache.getNCMapByGroupCode(depCode, "CC_AS_VIP");
			//合并MEMBER_TAG数据字典
			Map<String, Object> memberTagDict = DictCache.getNCMapByGroupCode(depCode, "MEMBER_TAG");
			if (memberTagDict != null) {
				vipDict.putAll(memberTagDict);
			}
			
			
			EasySQL sql =new  EasySQL("select t1.BG_CROWD_CODE as id,t1.name from C_NO_AS_CROWD_PACK t1 WHERE 1=1 ");
			sql.append(" and BG_CROWD_CODE is not null ");
			List<EasyRow> crowdData = query.queryForList(sql.getSQL(), sql.getParams());
			HashMap<String, String> crowdMap = new HashMap<String,String>();
			for (int i = 0; i < crowdData.size(); i++) {
				EasyRow row = crowdData.get(i);
				crowdMap.put(row.getColumnValue("NAME"), row.getColumnValue("ID"));
			}	
			//区域
			sql =new  EasySQL("select CITY_CODE AS ID,CITY_NAME AS NAME  from  C_COMM_CITY  ");
			List<EasyRow> cityData = query.queryForList(sql.getSQL(), sql.getParams());
			HashMap<String, String> cityMap = new HashMap<String,String>();
			for (int i = 0; i < cityData.size(); i++) {
				EasyRow row = cityData.get(i);
				cityMap.put(row.getColumnValue("NAME"), row.getColumnValue("ID"));
			}	
			//用户诉求
			Map<String, Object> callTypeDict = DictCache.getNCMapByGroupCode(depCode, "CC_AS_CALL_TYPE");
			//其他字典
			Map<String, Object> SF_YN = DictCache.getNCMapByGroupCode(depCode, "SF_YN01");
			Map<String, Object> CC_AS_IVR_TYPE = DictCache.getNCMapByGroupCode(depCode, "CC_AS_IVR_TYPE");
			Map<String, Object> CC_AS_IVR_FREQUENCY = DictCache.getNCMapByGroupCode(depCode, "CC_AS_IVR_FREQUENCY");

			for (int i = 1; i < list.size(); i++) {
				JSONObject js = new JSONObject();
				Map<Object, Object> strategy = new HashMap<Object, Object>();//C_NO_AS_IVR_STRATEGY表
				Map<Object, Object> detail = new HashMap<Object, Object>();//C_NO_AS_IVR_DETAIL
				List<JSONObject> keyList=new ArrayList<JSONObject>();//C_NO_AS_IVR_DETAIL_KEY
				List<JSONObject> callList=new ArrayList<JSONObject>();//C_NO_AS_IVR_DETAIL_CALL
				boolean lineScuuess = true;// 判断行内容
				String lineMsg = "";
				String val = "";
				for (int j = 0; j < list.get(i).size(); j++) {
					List<String> list2 = list.get(i);
					val = list.get(i).get(j);
					switch (j) {
					case 0://策略名称
						if("".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "脚本内容不为空";
							break;
						}
						strategy.put("NAME", val);
						break;
					case 1://策略类型
						if(!CC_AS_IVR_TYPE.containsKey(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "策略类型错误";
							break;
						}
						strategy.put("TYPE", CC_AS_IVR_TYPE.get(val));
						break;
					case 2://优先级
						strategy.put("PRIORITY_LEVEL", val);
						break;
					case 3://是否启用
						if(!SF_YN.containsKey(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "是否启用错误";
							break;
						}
						strategy.put("IS_OPEN", SF_YN.get(val));
						break;
					case 4://是否新会员
						if(StringUtils.isNotBlank( val)&& "是".equals(val)){
							strategy.put("IS_NEW_VIP", "1");
						}else{
							strategy.put("IS_NEW_VIP", "0");
						}
						break;
					case 5://生效开始时间
						strategy.put("START_TIME", val);
						break;
					case 6://生效结束时间
						strategy.put("END_TIME", val);
						break;
					case 7://热线名称
						String[] calls = val.split(",");
						for(String call:calls){
							if(!callDict.containsKey(call)&&"".equals(call)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "热线名称错误:"+call;
							}
							JSONObject json=new JSONObject();
							json.put("HOTLINE", callDict.get(call));
							callList.add(json);
						}
						break;
					case 8://提示语频次
						if(!CC_AS_IVR_FREQUENCY.containsKey(val)&&!"".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "提示语频次错误";
							break;
						}
						detail.put("FREQUENCY", CC_AS_IVR_FREQUENCY.get(val));
						break;
					case 9://是否进入智能IVR	
						if(!SF_YN.containsKey(val)&&!"".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "是否进入智能IVR错误";
							break;
						}
						detail.put("IS_IVR_CALL", SF_YN.get(val));
						break;
					case 10://用户区域
						String[] keys = val.split(",");
						for(String key:keys){
							if(!cityMap.containsKey(key)&&!"".equals(key)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "用户区域:"+key;
							}
							JSONObject json=new JSONObject();
							json.put("PHONE_BELONG_AREA", cityMap.get(key));
							json.put("STRATEGY_TYPE", "1");
							keyList.add(json);
						}
						break;
					case 11://人群包
						String[] keys2 = val.split(",");
						for(String key:keys2){
							if(!crowdMap.containsKey(key)&&!"".equals(key)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "人群包名称错误:"+key;
							}
							JSONObject json=new JSONObject();
							json.put("CROWD_PACK_ID", crowdMap.get(key));
							json.put("STRATEGY_TYPE", "2");
							keyList.add(json);
						}
						break;
					case 12://用户身份
						String[] keys3 = val.split(",");
						for(String key:keys3){
							if(!vipDict.containsKey(key)&&!"".equals(key)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "用户身份错误:"+key;
							}
							JSONObject json=new JSONObject();
							json.put("VIP", vipDict.get(key));
							json.put("STRATEGY_TYPE", "3");
							keyList.add(json);
						}
						break;
					case 13://用户诉求
						String[] keys4 = val.split(",");
						for(String key:keys4){
							if(!callTypeDict.containsKey(key)&&!"".equals(key)){
								scuuess = false;
								lineScuuess = false;
								lineMsg = lineMsg + "用户诉求错误:"+key;
							}
							JSONObject json=new JSONObject();
							json.put("CALL_TYPE", callTypeDict.get(key));
							json.put("STRATEGY_TYPE", "4");
							keyList.add(json);
						}
						break;
						
					case 14://是否启用排队接入等级
						if(!SF_YN.containsKey(val)&&!"".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "是否启用排队接入等级错误";
							break;
						}
						detail.put("IS_PRIORITY_ACCESS", SF_YN.get(val));
						break;
					case 15://排队接入等级
						detail.put("PRIORITY_ACCESS", val);
						break;
					case 16://是否启用人工技能组
						if(!SF_YN.containsKey(val)&&!"".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "是否启用人工技能组错误";
							break;
						}
						detail.put("IS_SKILL_GROUP", SF_YN.get(val));
						break;
					case 17://技能组
						if(!skillMap.containsKey(val)&&!"".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "技能组名称错误";
							break;
						}
						detail.put("SKILL_GROUP_CODE", skillMap.get(val));
						break;
					case 18://进入人工/技能组
						if("".equals(val)){
							detail.put("IS_INTO_ROBOT", "");//IS_INTO_ROBOT、IS_ARTIFICIAL
							detail.put("IS_ARTIFICIAL", "");//IS_INTO_ROBOT、IS_ARTIFICIAL
						}else if("人工".equals(val)){
							detail.put("IS_INTO_ROBOT", "0");//IS_INTO_ROBOT、IS_ARTIFICIAL
							detail.put("IS_ARTIFICIAL", "1");//IS_INTO_ROBOT、IS_ARTIFICIAL
						}else if("技能组".equals(val)){
							detail.put("IS_INTO_ROBOT", "1");//IS_INTO_ROBOT、IS_ARTIFICIAL
							detail.put("IS_ARTIFICIAL", "0");//IS_INTO_ROBOT、IS_ARTIFICIAL
						}else if(val==""){
							
						}else{
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "人工/技能组错误";
							break;
						}
						break;
					case 19://开否启挂机短信
						if(!SF_YN.containsKey(val)&&!"".equals(val)){
							scuuess = false;
							lineScuuess = false;
							lineMsg = lineMsg + "开否启挂机短信错误";
							break;
						}
						detail.put("IS_SEND_SMS", SF_YN.get(val));
						break;
					case 20://短信内容
						detail.put("SMS_CONTENT", val);
						break;
					default:
					break;
					}
				}
				if (scuuess) {
					String IVR_STRATEGY_ID=RandomKit.randomStr();
					String IVR_STRATEGY_DETAIL_ID=RandomKit.randomStr();
					String str = JSONObject.toJSON(strategy).toString();
					JSONObject jsonObject = JSONObject.parseObject(str);
					jsonObject.put("ID", IVR_STRATEGY_ID);//id
					jsonObject.put("IS_OPEN", "1");//启用
					jsonObject.put("CREATE_ACC", userAcc);//导入人
					jsonObject.put("CREATE_TIME",createTime);//同步时间
					EasyRecord record = new EasyRecord("C_NO_AS_IVR_STRATEGY", "ID").setColumns(jsonObject);
					query.save(record);
					String detailStr = JSONObject.toJSON(detail).toString();
					JSONObject jsonObject1 = JSONObject.parseObject(detailStr);
					jsonObject1.put("ID", IVR_STRATEGY_DETAIL_ID);//
					jsonObject1.put("IVR_STRATEGY_ID",IVR_STRATEGY_ID);
					jsonObject1.put("CREATE_ACC", userAcc);//导入人
					jsonObject1.put("CREATE_TIME",createTime);//同步时间
					EasyRecord record1 = new EasyRecord("C_NO_AS_IVR_DETAIL", "ID").setColumns(jsonObject1);
					query.save(record1);
					for(JSONObject call:callList){
						call.put("ID", RandomKit.randomStr());//
						call.put("IVR_STRATEGY_ID",IVR_STRATEGY_ID);//
						call.put("IVR_STRATEGY_DETAIL_ID",IVR_STRATEGY_DETAIL_ID);//
						call.put("CREATE_ACC",userAcc );//导入人
						call.put("CREATE_TIME",createTime);//同步时间
						EasyRecord record2 = new EasyRecord("C_NO_AS_IVR_DETAIL_CALL", "ID").setColumns(call);
						query.save(record2);
					}
					for(JSONObject key:keyList){
						key.put("ID", RandomKit.randomStr());//
						key.put("IVR_STRATEGY_ID",IVR_STRATEGY_ID);//
						key.put("IVR_STRATEGY_DETAIL_ID",IVR_STRATEGY_DETAIL_ID);//
						key.put("CREATE_ACC",userAcc );//导入人
						key.put("CREATE_TIME",createTime);//同步时间
						EasyRecord record3 = new EasyRecord("C_NO_AS_IVR_DETAIL_KEY", "ID").setColumns(key);
						query.save(record3);
					}
				
				} else {
					if (!lineScuuess) {
						msg = msg + "第" + i + "行出错：" + lineMsg + "<br>";
					}
				}
			}
			if(list.size()==0|list.size()==1){
				scuuess=false;
				msg="无导入信息";
			}
			if (!scuuess) {// 出错了不记录
				query.roolback();
				return EasyResult.error(500, "" + msg);
			} else {
				query.commit();
				return EasyResult.ok("", "脚本导入成功!");
			}
		} catch (Exception e) {
			try {
				query.roolback();
			} catch (SQLException e1) {
				logger.error("[Exception] 导入失败，原因：",e);
			}
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error(501, "导入失败，原因：" + e.getMessage());
		}
	}
	public void actionForDownloadImport() {
		File file = new File(this.getRequest().getServletContext().getRealPath("/pages/template/ivr-script.xlsx"));
		renderFile(file, "IVR策略导入模板.xlsx");
	}
	public JSONObject actionForRefreshIvrStrategyCache() {
		try {
			return IvrRefreshDataService.invoke();
		} catch (Exception e) {
			logger.error("[Exception] 导入失败，原因：",e);
			return EasyResult.error();
		}
	}
	public EasyResult actionForTest() throws ServiceException{
		IvrService ivr=new IvrService();
		JSONObject json=new JSONObject();
		json.put("command", "refreshData");
		ivr.invoke(json);
		return null;
		
	}
	
	public String getFileSaveRootPath(String voiceType) {
		String rootPath = null;
		if("1".equals(voiceType)) {
			rootPath = ConfigUtil.getString(Constants.APP_NAME, "IVR_ROOT_WELCOME_PAHT", "D:\\files/");
		}else if("2".equals(voiceType)){
			rootPath = ConfigUtil.getString(Constants.APP_NAME, "IVR_ROOT_WAIT_PAHT", "D:\\files/");
		}else if("3".equals(voiceType)){//2-5次排队提示语录音路径
			rootPath = ConfigUtil.getString(Constants.APP_NAME, "SECOND_WAIT_TIP_WORDS_PAHT", "/apps/record/secondWait/");
		}else if("4".equals(voiceType)){//5次排队提示语录音路径
			rootPath = ConfigUtil.getString(Constants.APP_NAME, "FIVE_WAIT_TIP_WORDS_PAHT", "/apps/record/fiveWait/");
		}else if("5".equals(voiceType)){//用户报完诉求后的播报语录音路径
			rootPath = ConfigUtil.getString(Constants.APP_NAME, "AFTER_APPEAL_TIP_WORDS_PAHT", "/apps/record/afterAppeal/");
		}
		return rootPath;
	}
	
	private String getRandomNum() {
		String random = RandomKit.random(0, 10000000)+"";
		String sufix = "";
		for(int i=0;i<7-random.length();i++) {
			sufix+="0";
		}
		return sufix+random;
	}
}
