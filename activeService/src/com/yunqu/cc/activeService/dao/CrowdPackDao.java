package com.yunqu.cc.activeService.dao;

import java.sql.ResultSet;
import java.sql.ResultSetMetaData;
import java.sql.Timestamp;

import com.yq.busi.common.util.DateUtil;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.EasyRowImpl;
import org.easitline.common.db.impl.EasyRowMapperImpl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.cc.activeService.base.AppDaoContext;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.model.TouchSource;
import com.yunqu.cc.activeService.utils.PhoneEncryptUtil;
import org.easitline.common.utils.string.StringUtils;

/**
 * 主动服务类
 */
@WebObject(name="crowdPackDao")
public class CrowdPackDao extends AppDaoContext{
	
	private final Logger logger = CommLogger.getCommLogger();
	
	@WebControl(name="crowdPackList",type=Types.LIST)
	public JSONObject crowdPackList() {
		EasySQL sql = this.getEasySQL("select t1.*,t2.GROUP_NAME,t2.ID GOURP_ID,t3.STRATEGY_LEVEL,t3.CHANNEL_TYPE from C_NO_AS_CROWD_PACK t1 LEFT JOIN C_NO_AS_CROWDPACK_GROUP t2 on t1.GROUP_ID = t2.ID ");
		sql.append("left join ( select CROWD_PACK_ID,listagg(STRATEGY_LEVEL, ',') within group(order by CHANNEL_TYPE) STRATEGY_LEVEL,listagg(CHANNEL_TYPE, ',')  within group(order by CHANNEL_TYPE) CHANNEL_TYPE from C_NO_AS_CROWDPACK_PRIORITY GROUP BY CROWD_PACK_ID ) t3 on t1.ID = t3.CROWD_PACK_ID WHERE 1=1 ");
		sql.append(this.param.getString("groupId")," and t1.GROUP_ID = ? ");
		sql.appendLike(this.param.getString("name")," and t1.NAME like ? ");
		sql.append(this.param.getString("category")," and t1.CATEGORY = ? ");
		sql.append(this.param.getString("dataSource")," and t1.DATA_SOURCE= ? ");
		sql.append(this.param.getString("touchSource")," and t1.TOUCH_SOURCE= '"+this.param.getString("touchSource")+"'");
		sql.append(this.param.getString("createAcc")," and t1.CREATE_ACC=? ");
		sql.append(this.param.getString("createTimeStar")," and t1.CREATE_TIME>= ? ");
		sql.append(this.param.getString("createTimeEnd")," and t1.CREATE_TIME<=? ");
		sql.append(this.param.getString("IS_SENTIMENT")," and t1.IS_SENTIMENT=? ");

		sql.append(" ORDER BY t1.CREATE_TIME DESC");
		logger.debug("获取人群包列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		JSONObject queryForPageList = this.queryForPageList(sql.getSQL(), sql.getParams(),null);
		return queryForPageList;
	}
	
	@WebControl(name="crowdPackDictList",type=Types.LIST)
	public JSONObject crowdPackDictList() {
		JSONObject jsonObject = this.param;
		this.param = jsonObject;
		EasySQL sql = this.getEasySQL("select t1.id,t1.name,t2.GROUP_NAME,t2.ID GOURP_ID from C_NO_AS_CROWD_PACK t1 LEFT JOIN C_NO_AS_CROWDPACK_GROUP t2 on t1.GROUP_ID = t2.ID WHERE 1=1 ");
		sql.appendLike(this.param.getString("name")," and t1.NAME like ? ");
		sql.append(" ORDER BY t1.CREATE_TIME DESC,ID ASC");
		logger.debug("获取人群包列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	/**
	 * 回访页面获取人群包数据
	 * @return
	 */
	@WebControl(name="getCrowdPackListByRevisit",type=Types.LIST)
	public JSONObject getCrowdPackListByRevisit() {
		JSONObject json = this.param;
		this.param = json;
		String callOutType = json.getString("callOutType");//外呼方式
		String serviceType = json.getString("serviceType");//服务类型
		serviceType = "1".equals(serviceType) ? Constants.CHANNEL_MARKET:Constants.CHANNEL_OUTBOUND;
		String tableName = Constants.strategyTable.get(serviceType);
		String extendSQL = "";
		if("1".equals(callOutType)) {//人工
			//判断是否配置了主动营销/主动外呼人群包策略或人群包组策略
			extendSQL = "and exists(select * from c_no_as_crowdpack_priority tb1 left join "+tableName+" tb2 on tb1.STRATEGY_ID = tb2.id "
			 + " where tb1.crowd_pack_id = t1.id"
			 + " and tb1.channel_type = '"+serviceType+"'"
			 + " and tb2.IS_OPEN = '1')";
			
		}else if("2".equals(callOutType)) {//机器人
			tableName = Constants.strategyTable.get(Constants.CHANNEL_ROBOT);
			extendSQL = "and exists(select tb1.* from c_no_as_crowdpack_priority tb1 left join "+tableName+" tb2 on tb1.STRATEGY_ID = tb2.id "
					 + " where tb1.crowd_pack_id = t1.id "
					 + " and tb1.channel_type = '"+Constants.CHANNEL_ROBOT+"' "
					 + " and tb2.IS_OPEN = '1') ";
		}
		EasySQL sql = this.getEasySQL("select t1.id,t1.name,t2.GROUP_NAME,t2.ID GOURP_ID from C_NO_AS_CROWD_PACK t1 LEFT JOIN C_NO_AS_CROWDPACK_GROUP t2 on t1.GROUP_ID = t2.ID WHERE 1=1 ");
		sql.appendLike(this.param.getString("keyword")," and t1.NAME like ? ");
		sql.append(extendSQL);
		sql.append(" ORDER BY t1.CREATE_TIME DESC");
		return queryForList(sql.getSQL(), sql.getParams(),null);
	}
	
	
	/**
	 * 大数据人群包获取
	 * @return
	 */
	@WebControl(name="bgCrowdPackDictList",type=Types.LIST)
	public JSONObject bgCrowdPackDictList() {
		JSONObject jsonObject = this.param;
		this.param = jsonObject;
		EasySQL sql = this.getEasySQL("select t1.BG_CROWD_CODE as id,t1.name from C_NO_AS_CROWD_PACK t1 WHERE 1=1 ");
		sql.appendLike(this.param.getString("name")," and t1.NAME like ? ");
		sql.append(" and t1.BG_CROWD_CODE is not null ");
		sql.append(" and t1.TOUCH_SOURCE = '"+TouchSource.CC_CALL.getCode()+"'");
		sql.append(" ORDER BY t1.NAME,id ");
		logger.debug("大数据人群包列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 过滤人群人群包获取
	 * @return
	 */
	@WebControl(name="ndCrowdPackDictList",type=Types.LIST)
	public JSONObject ndCrowdPackDictList() {
		EasySQL sql = this.getEasySQL("select t1.ID as id,t1.name from C_NO_AS_CROWD_PACK t1 WHERE 1=1 ");
		sql.append(" AND((t1.CATEGORY IN (2,3,4))");
		sql.append(" OR (t1.TOUCH_SOURCE = 'CC_CALL' AND t1.IS_DISTURB = 1 ))");
		sql.append(" ORDER BY t1.NAME,id ");
		logger.info("过滤人群人群包获取,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}

	/**
	 * 全量人群包获取
	 * @return
	 */
	@WebControl(name="allCrowdPackDictList",type=Types.LIST)
	public JSONObject allCrowdPackDictList() {
		JSONObject jsonObject = this.param;
		this.param = jsonObject;
		EasySQL sql = this.getEasySQL("select t1.ID as id,t1.name from C_NO_AS_CROWD_PACK t1 WHERE 1=1 ");
		sql.appendLike(this.param.getString("name")," and t1.NAME like ? ");
		sql.append(" ORDER BY t1.NAME,id ");
		return this.queryForList(sql.getSQL(), sql.getParams());
	}
	
	@WebControl(name="crowdPackListByGroupId",type=Types.LIST)
	public JSONObject crowdPackListByGroupId() {
		JSONObject jsonObject = this.param;
		EasySQL sql = this.getEasySQL("select id,name from C_NO_AS_CROWD_PACK WHERE 1=1 ");
		sql.append(jsonObject.getString("groupId")," and GROUP_ID = ? ");
		return this.queryForList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getCrowdsTotalNum",type=Types.RECORD)
	public JSONObject getCrowdsTotalNum() {
		EasySQL sql = this.getEasySQL("select count(1) TOTAL_NUM from C_NO_AS_CROWD WHERE 1=1 ");
		CrowdPackSql.getQueryCrowdListCondition(sql, param);
		return this.queryForRecord(sql.getSQL(), sql.getParams(),null);
	}

	@WebControl(name="crowdPackCustList",type=Types.LIST)
	public JSONObject crowdPackCustList() {
		EasySQL sql = this.getEasySQL("select * from C_NO_AS_CROWD WHERE 1=1 ");
		CrowdPackSql.getQueryCrowdListCondition(sql, param);
		sql.append(" ORDER BY CREATE_DATE DESC");
		logger.debug("获取人群包明细列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),new EasyRowMapper<JSONObject>() {

			@Override
			public JSONObject mapRow(ResultSet rs, int rowNum) {
				try {
					ResultSetMetaData meta = rs.getMetaData();
					int columnCount = meta.getColumnCount();
					String[] column = new String[columnCount];
					for (int i = 0; i < columnCount; i++)
						column[i] = meta.getColumnLabel(i + 1).toUpperCase();
					JSONObject row = new JSONObject(true);
					for (int j = 0; j < columnCount; j++) {
						String value = rs.getString(j + 1);
						if (value == null || "".equals("null"))
							value = "";
						if("CUST_PHONE".equals(column[j])) {
							value = PhoneEncryptUtil.getPhone(value);
						}
						row.put(column[j], value);
					}
					return row;
				} catch (Exception ex) {
					logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
					return null;
				}
			}
		});
	}


	/**
	*@title 获取免打扰用户列表
	*@date 2024/7/3
	*@version 1.0
	*/
	@WebControl(name="crowdPackSensitiveCustList",type=Types.LIST)
	public JSONObject crowdPackSensitiveCustList() {
		EasySQL sql = this.getEasySQL("select * from C_NO_AS_SENSITIVE_CROWD WHERE 1=1 ");
		CrowdPackSql.getSensitiveCrowdListCondition(sql, param);
		sql.append(" ORDER BY CREATE_DATE DESC");
		logger.debug("获取人群包明细列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),new EasyRowMapper<JSONObject>() {

			@Override
			public JSONObject mapRow(ResultSet rs, int rowNum) {
				try {
					ResultSetMetaData meta = rs.getMetaData();
					int columnCount = meta.getColumnCount();
					String[] column = new String[columnCount];
					for (int i = 0; i < columnCount; i++)
						column[i] = meta.getColumnLabel(i + 1).toUpperCase();
					JSONObject row = new JSONObject(true);
					for (int j = 0; j < columnCount; j++) {
						String value = rs.getString(j + 1);
						if (value == null || "".equals("null"))
							value = "";
						if("PHONE".equals(column[j])) {
							value = PhoneEncryptUtil.getPhone(value);
						}
						row.put(column[j], value);
					}
					return row;
				} catch (Exception ex) {
					logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
					return null;
				}
			}
		});
	}

	/**
	*@title 获取AI外呼人群明细列表
	*<AUTHOR>
	*@date 2023/5/4
	*@version 1.0
	*/
	@WebControl(name="crowdPackCustAiList",type=Types.LIST)
	public JSONObject crowdPackCustAiList() {
		EasySQL sql = this.getEasySQL("select CUST_NAME,CUST_PHONE,BRAND_NAME,PROD_NAME,PRODUCT_MODEL,FAULT_CODE,CREATE_TIME,SERVICE_STATUS,RESULT_CONTENT,REVISIT_RESULT,NOTES,CREATE_DATE,RESULT,EXPAND_DATA from C_NO_AS_ROBOT_AUTO_CALL WHERE 1=1 ");
		CrowdPackSql.getAiQueryCrowdListCondition(sql, param);
		String queryHisCheck = param.getString("queryHisCheck");
		if(StringUtils.isNotBlank(queryHisCheck)&&queryHisCheck.indexOf("1")>-1){
			sql.append(" union all ");
			sql.append(" select CUST_NAME,CUST_PHONE,BRAND_NAME,PROD_NAME,PRODUCT_MODEL,FAULT_CODE,CREATE_TIME,SERVICE_STATUS,RESULT_CONTENT,REVISIT_RESULT,NOTES,CREATE_DATE,RESULT,EXPAND_DATA from C_NO_AS_ROBOT_AUTO_CALL_HIS WHERE 1=1 ");
			CrowdPackSql.getAiQueryCrowdListCondition(sql, param);
			sql.append(" ORDER BY CREATE_DATE DESC");
		}
		logger.debug("获取人群包明细列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),new EasyRowMapper<JSONObject>() {

			@Override
			public JSONObject mapRow(ResultSet rs, int rowNum) {
				try {
					ResultSetMetaData meta = rs.getMetaData();
					int columnCount = meta.getColumnCount();
					String[] column = new String[columnCount];
					for (int i = 0; i < columnCount; i++)
						column[i] = meta.getColumnLabel(i + 1).toUpperCase();
					JSONObject row = new JSONObject(true);
					for (int j = 0; j < columnCount; j++) {
						String value = rs.getString(j + 1);
						if (value == null || "".equals("null"))
							value = "";
						if("CUST_PHONE".equals(column[j])) {
							value = PhoneEncryptUtil.getPhone(value);
						}
						row.put(column[j], value);
					}
					return row;
				} catch (Exception ex) {
					logger.error("EasyMapMapperImpl.mapRow() exception , cause:" + ex.getMessage());
					return null;
				}
			}
		});
	}
	
	@WebControl(name="crowdPackPriorityList",type=Types.LIST)
	public JSONObject crowdPackPriorityList() {
		EasySQL sql = this.getEasySQL("select t1.ID,t1.NAME,t1.CUST_NUM,t2.CROWD_PACK_GROUP_ID,t2.PRIORITY,t2.ID PRIORITY_ID,t2.CREATE_TIME,t2.CREATE_ACC,t2.STRATEGY_LEVEL,t2.CHANNEL_TYPE from C_NO_AS_CROWD_PACK t1 LEFT JOIN C_NO_AS_CROWDPACK_PRIORITY t2 on t1.ID = t2.CROWD_PACK_ID WHERE 1=1 ");
		sql.append(this.param.getString("CHANNEL_TYPE")," and t2.CHANNEL_TYPE = ? ",false);
		sql.appendLike(this.param.getString("CROWD_PACK_NAME")," and t1.NAME LIKE ? ");
		sql.append(" ORDER BY t2.PRIORITY,t2.CREATE_TIME DESC");
		logger.debug("获取人群包优先级列表,sql="+sql.getSQL()+"{"+JSON.toJSONString(sql.getParams())+"}");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	/**
	 * 商城短信配置详情
	 */
	@WebControl(name="edit",type=Types.RECORD)
	public JSONObject smsProductEdit() {
		return this.queryForRecord(new EasyRecord("C_NO_AS_CROWD_PACK","ID").setPrimaryValues(param.getString("lable.ID")));
	}
}
