package com.yunqu.cc.activeService.dao;

import java.sql.SQLException;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.activeService.base.AppDaoContext;
import com.yunqu.cc.activeService.base.CommLogger;
import org.apache.commons.lang.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;

/**
 * 会员身份优先级配置DAO
 * <AUTHOR>
 * @date 2025-01-07
 */
@WebObject(name = "memberPriority")
public class MemberPriorityDao extends AppDaoContext {

    private static final long serialVersionUID = 1L;
    private final Logger logger = CommLogger.logger;

    /**
     * 获取会员身份优先级配置列表
     * @return
     */
    @WebControl(name = "getMemberPriorityList", type = Types.LIST)
    public JSONObject getMemberPriorityList() {
        EasySQL sql = this.getEasySQL("SELECT * FROM C_AS_MEMBER_PRIORITY WHERE 1=1 ");

        // 添加筛选条件
        sql.appendLike(this.param.getString("memberTag"), " AND MEMBER_TAG LIKE ? ");
        sql.append(this.param.getString("hotlineCode"), " AND HOTLINE_CODE LIKE '%'||?||'%' ");
        sql.append(this.param.getString("memberLevel"), " AND MEMBER_LEVEL = ? ");

        sql.append(" ORDER BY CREATE_TIME DESC, MEMBER_LEVEL ASC");

        logger.debug("获取会员身份优先级配置列表,sql=" + sql.getSQL() + "{" + sql.getParams() + "}");
        return this.queryForPageList(sql.getSQL(), sql.getParams(), null);
    }

    /**
     * 获取会员身份优先级配置详情
     * @return
     */
    @WebControl(name = "getMemberPriority", type = Types.RECORD)
    public JSONObject getMemberPriority() {
        return this.queryForRecord(
                new EasyRecord("C_AS_MEMBER_PRIORITY", "ID").setPrimaryValues(param.getString("MemberPriority.ID")));
    }


}
