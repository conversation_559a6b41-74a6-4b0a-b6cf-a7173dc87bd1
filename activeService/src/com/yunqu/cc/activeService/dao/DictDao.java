/**
 * <html>
 * <body>
 *  <P> Copyright 广州云趣信息科技有限公司</p>
 *  <p> All rights reserved.</p>
 *  <p> Created on 2018年5月30日 下午3:00:09</p>
 *  <p> Created by wubin</p>
 *  </body>
 * </html>
 */
package com.yunqu.cc.activeService.dao;

import java.util.HashMap;
import java.util.Map;

import com.yunqu.cc.activeService.base.AppDaoContext;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.utils.CsUtil;
import com.yunqu.cc.activeService.utils.ValidateHelper;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.OrderCommand;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.util.UserUtil;

/**
* @Package：com.yunqu.cc.online.dao
* @ClassName：DictDao
* @Description：   <p> 字典相关</p>
* @Author： - wubin
* @CreatTime：2018年5月30日 下午3:00:09
* @Modify By：
* @ModifyTime：  2018年5月30日
* @Modify marker：
* @version    V1.0
*/
@WebObject(name="Dict")
public class DictDao extends AppDaoContext {

	private final Logger logger = CommLogger.logger;
	
	@WebControl(name = "getDict", type = Types.RECORD)
	public JSONObject getDict(){
		String depCode=UserUtil.getUser(request).getEpCode();
		String key = param.getString("param[0]");
		return DictCache.getJsonEnableDictListByGroupCode(depCode, key);
	}

	/**
	 * 基础配置数据查询
	 * @param codeType 根据编码类别查询该类别下所有的编码值及其对应的名称
	 * @param orgCode 主体编码
	 * @return JSONObject 分类结构数据
	 */
	@WebControl(name = "DICT_SYS_CODE", type = Types.RECORD)
	public JSONObject getArchivesTypeDict(){
		JSONObject result = new JSONObject();
		JSONObject obj = new JSONObject();
		Map<String,Object> param = new HashMap<String,Object>();
		String codeType = ValidateHelper.formatNull(getMethodParam(0), "");
		String orgCode = ValidateHelper.formatNull(getMethodParam(1), "");
		param.put("codeType", codeType);
		param.put("orgCode", orgCode);
		obj.put("command",OrderCommand.COMM_SYSCODE);
		obj.put("params", param);
		try {
			IService service = ServiceContext.getService("CSSGW-COMM");
			result = service.invoke(obj);
		} catch (ServiceException e) {
			logger.error("IService请求失败,请求参数"+JSON.toJSONString(obj)+",原因"+e.getMessage());
		}
		return CsUtil.getData(result);
	}
	@WebControl(name = "ccHotline", type = Types.RECORD)
	public JSONObject ccHotline(){
		String depCode=UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "CC_HOTLINE");
	}
	@WebControl(name = "ccAsVip", type = Types.RECORD)
	public JSONObject ccAsVip(){
		String depCode=UserUtil.getUser(request).getEpCode();

		// 获取CC_AS_VIP字典数据
		JSONObject ccAsVipData = DictCache.getJsonEnableDictListByGroupCode(depCode, "CC_AS_VIP");
		// 获取MEMBER_TAG字典数据
		JSONObject memberTagData = DictCache.getJsonEnableDictListByGroupCode(depCode, "MEMBER_TAG");

		// 合并两个字典的data部分
		JSONObject resultJson = new JSONObject();
		JSONObject mergedData = new JSONObject(true);

		// 添加CC_AS_VIP的数据
		if(ccAsVipData != null && ccAsVipData.containsKey("data")) {
			JSONObject ccAsVipDataObj = ccAsVipData.getJSONObject("data");
			if(ccAsVipDataObj != null) {
				mergedData.putAll(ccAsVipDataObj);
			}
		}

		// 添加MEMBER_TAG的数据
		if(memberTagData != null && memberTagData.containsKey("data")) {
			JSONObject memberTagDataObj = memberTagData.getJSONObject("data");
			if(memberTagDataObj != null) {
				mergedData.putAll(memberTagDataObj);
			}
		}

		resultJson.put("data", mergedData);
		return resultJson;
	}
	@WebControl(name = "callType", type = Types.RECORD)
	public JSONObject callType(){
		String depCode=UserUtil.getUser(request).getEpCode();
		JSONObject jsonEnableDictListByGroupCode = DictCache.getJsonEnableDictListByGroupCode(depCode, "CC_AS_CALL_TYPE");
		
		return jsonEnableDictListByGroupCode;
	}
	@WebControl(name = "sysCodeInvert", type = Types.RECORD)
	public JSONObject getsysCodeInvertDict(){
		String depCode=UserUtil.getUser(request).getEpCode();
		JSONObject jsonEnableDictListByGroupCode = DictCache.getJsonEnableDictListByGroupCode(depCode, "ONLINE_CONSULT_AREA");

		return jsonEnableDictListByGroupCode;
	}


	/**
	 * ͨ根据字典编号获取数据字典
	 * @return
	 */
	@WebControl(name="getDictList",type=Types.RECORD)
	public JSONObject getDictList(){
		String code = (String)this.getMethodParam(0);
		String epCode = UserUtil.getUser(request).getEpCode();
		epCode = StringUtils.isNotBlank(epCode)?epCode: "001";
//		this.info(CommonUtil.getClassNameAndMethod(this)+ "获取编号为:"+ code+ "的字典", null);
		JSONObject obj=DictCache.getJsonEnableDictListByGroupCode(epCode, code);
		return obj;
	}


	/**
	 * @Description: 用户level等级
	 * @return JSONObject
	 * @Autor: wubin - <EMAIL>
	 */
	@WebControl(name = "USER_INFO_LEVEL", type = Types.RECORD)
	public JSONObject getUserInfoLevel() {
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_LEVEL");
	}

	/**
	 * @Description: 用户民族
	 * @return JSONObject
	 * @Autor: wubin - <EMAIL>
	 */
	@WebControl(name = "USER_INFO_NATIONAL", type = Types.RECORD)
	public JSONObject getUserNational() {
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_NATIONAL");
	}

	/**
	 * @Description: 用户性别
	 * @return JSONObject
	 * @Autor: wubin - <EMAIL>
	 */
	@WebControl(name = "USER_INFO_SEX", type = Types.RECORD)
	public JSONObject getUserGender() {
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_SEX");
	}

	/**
	 * @Description: 用户类型
	 * @return JSONObject
	 * @Autor: wubin - <EMAIL>
	 */
	@WebControl(name = "USER_INFO_TYPE", type = Types.RECORD)
	public JSONObject getUserTags() {

		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "USER_INFO_TYPE");
	}

	/**
	 * @Description: 用户性别
	 * @return JSONObject
	 * @Autor: wubin - <EMAIL>
	 */
	@WebControl(name = "SF_YN", type = Types.RECORD)
	public JSONObject getSF_YN() {
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "SF_YN");
	}

	/**
	 * @Description: 用户性别
	 * @return JSONObject
	 * @Autor: wubin - <EMAIL>
	 */
	@WebControl(name = "ORDER_TYPE", type = Types.RECORD)
	public JSONObject getOrderType() {
		String depCode = UserUtil.getUser(request).getEpCode();
		return DictCache.getJsonEnableDictListByGroupCode(depCode, "ORDER_TYPE");
	}

	
}
