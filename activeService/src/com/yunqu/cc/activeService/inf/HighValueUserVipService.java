package com.yunqu.cc.activeService.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.RejectedExecutionHandler;
import java.util.ArrayList;
import java.util.List;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;

/**
 * 高价值用户VIP信息查询服务
 */
public class HighValueUserVipService extends IService{

	// 全局共享线程池，支持高并发场景
	// 可配置线程数，最大限制100个线程，支持约30并发来电（每通来电需要3个线程）
	private static final Logger logger =   CommLogger.getCommLogger("high");
	private static final int DEFAULT_THREAD_POOL_SIZE = getThreadPoolSize();
	private static final ExecutorService GLOBAL_EXECUTOR = createSecureThreadPool();




	@Override
	public JSONObject invoke(JSONObject json) throws ServiceException {
		if("getVipInfo".equals(json.getString("command"))){//定时器
			return getVipInfo(json);
		}
		return null;
	}

	/**
	 * 初始化VIP查询服务（在ContextListener中调用）
	 * 提前创建线程池，避免并发创建问题
	 */
	public static void initialize() {
		try {
			// 检查服务是否启用
			if (!isVipServiceEnabled()) {
				logger.info("VIP查询服务已禁用");
				return;
			}

			// 强制触发线程池创建
			if (GLOBAL_EXECUTOR != null) {
				logger.info("VIP查询服务初始化成功，线程池已创建");
			} else {
				logger.error("VIP查询服务初始化失败");
			}

		} catch (Exception e) {
			logger.error("VIP查询服务初始化异常", e);
		}
	}

	/**
	 * 检查VIP服务是否启用
	 * @return true=启用，false=禁用
	 */
	private static boolean isVipServiceEnabled() {
		try {
			String enabled = AppContext.getContext(Constants.APP_NAME).getProperty("VIP_SERVICE_ENABLED", "1");
			return "1".equals(enabled);
		} catch (Exception e) {
			logger.warn("读取VIP服务开关配置失败，默认启用", e);
			return true; // 默认启用
		}
	}



	/**
	 * 高价值用户VIP信息查询服务入口方法（优化版：使用全局线程池）
	 * @param json
	 * @return MIXGW_HIGH_VALUE_USER_VIP
	 */
	public static JSONObject getVipInfo(JSONObject json) {
		CommLogger.getCommLogger("high").info("HighValueUserVipService.getVipInfo()"+json);

		// 检查服务是否启用
		if (!isVipServiceEnabled()) {
			JSONObject disabledResult = new JSONObject();
			disabledResult.put("respCode", "503");
			disabledResult.put("respDesc", "VIP查询服务已禁用");
			logger.info("VIP查询服务已禁用，返回禁用状态");
			return disabledResult;
		}

		JSONObject data = json.getJSONObject("data");
		// 参数校验和清理
		String openId = sanitizeInput(data.getString("openId"));
		String mobile = sanitizeInput(data.getString("mobile"));
		String uid = sanitizeInput(data.getString("uid"));
		String isUserData = sanitizeInput(data.getString("isUserData"));

		// 参数有效性检查
		if (StringUtils.isBlank(mobile) && StringUtils.isBlank(uid)) {
			JSONObject errorResult = new JSONObject();
			errorResult.put("respCode", "400");
			errorResult.put("respDesc", "参数错误：openId、mobile、uid至少需要提供一个");
			return errorResult;
		}
		// 使用并发查询方法
		return getVipInfoConcurrent(openId, mobile, uid,isUserData);
	}


	/**
	 * 创建安全的线程池（优化版：支持降级策略）
	 */
	private static ExecutorService createSecureThreadPool() {
		// 队列大小根据线程池大小动态调整，但不超过200
		int queueSize = Math.min(DEFAULT_THREAD_POOL_SIZE * 2, 200);

		logger.info("创建VIP线程池 - 核心线程数: " + DEFAULT_THREAD_POOL_SIZE +
			", 队列大小: " + queueSize);

		return new ThreadPoolExecutor(
			DEFAULT_THREAD_POOL_SIZE, // 核心线程数
			DEFAULT_THREAD_POOL_SIZE, // 最大线程数
			60L, TimeUnit.SECONDS,    // 空闲线程存活时间
			new LinkedBlockingQueue<>(queueSize), // 动态队列大小
			r -> {
				Thread t = new Thread(r, "VIP-Query-" + System.currentTimeMillis());
				t.setDaemon(true); // 设置为守护线程
				return t;
			},
			new RejectedExecutionHandler() { // 拒绝策略：记录日志但不抛异常
				@Override
				public void rejectedExecution(Runnable r, ThreadPoolExecutor executor) {
					CommLogger.getCommLogger("high").error("VIP查询线程池已满，任务被拒绝执行");
					// 注意：这里不抛异常，因为我们在业务层面已经做了预检查
				}
			}
		);
	}

	/**
	 * 动态计算线程池大小（最大限制100个线程）
	 */
	private static int getThreadPoolSize() {
		int configuredSize = 50; // 默认线程数

		try {
			// 从配置文件读取线程池大小
			String configSize = AppContext.getContext(Constants.APP_NAME).getProperty("VIP_THREAD_POOL_SIZE","");
			if (StringUtils.isNotBlank(configSize)) {
				configuredSize = Integer.parseInt(configSize);
				CommLogger.getCommLogger("high").info("使用配置的VIP线程池大小: " + configuredSize);
			} else {
				// 如果没有配置，根据CPU核心数计算默认值
				int cpuCores = Runtime.getRuntime().availableProcessors();
				configuredSize = Math.max(20, cpuCores * 4); // 最少20个线程
				CommLogger.getCommLogger("high").info("使用计算的VIP线程池大小: " + configuredSize + " (CPU核心数: " + cpuCores + ")");
			}
		} catch (NumberFormatException e) {
			CommLogger.getCommLogger("high").error("VIP线程池配置格式错误，使用默认值50", e);
			configuredSize = 50;
		} catch (Exception e) {
			CommLogger.getCommLogger("high").warn("读取VIP线程池配置失败，使用默认值50", e);
			configuredSize = 50;
		}

		// 强制限制：最大不超过100个线程
		if (configuredSize > 150) {
			CommLogger.getCommLogger("high").warn("配置的线程数(" + configuredSize + ")超过最大限制，强制设置为100");
			configuredSize = 150;
		}

		// 最小不少于10个线程
		if (configuredSize < 10) {
			CommLogger.getCommLogger("high").warn("配置的线程数(" + configuredSize + ")过小，强制设置为10");
			configuredSize = 10;
		}

		CommLogger.getCommLogger("high").info("最终VIP线程池大小: " + configuredSize);
		return configuredSize;
	}






	/**
	 * 输入参数清理，防止注入攻击
	 */
	private static String sanitizeInput(String input) {
		if (StringUtils.isBlank(input)) {
			return "";
		}
		// 移除潜在的危险字符
		return input.replaceAll("[<>\"'&;]", "").trim();
	}


	/**
	 * 并发查询三个品牌的VIP信息（优化版：预检查+降级处理）
	 * @param openId 用户openId
	 * @param mobile 手机号
	 * @param uid 用户ID
	 * @return 包含levelName拼接字符串和数组的结果
	 */
	public static JSONObject getVipInfoConcurrent(String openId, String mobile, String uid,String isUserData) {
		long startTime = System.currentTimeMillis();

		// 1. 预检查线程池状态，如果繁忙则返回降级结果
		if (isThreadPoolBusy()) {
			CommLogger.getCommLogger("highBusy").warn("线程池繁忙，返回降级结果，mobile=" + mobile);
			return getFallbackResult(startTime);
		}

		// 2. 正常执行并发查询
		return executeNormalVipQuery(openId, mobile, uid, startTime,isUserData);
	}

	/**
	 * 检查线程池是否繁忙
	 * @return true-繁忙需要降级，false-可以正常执行
	 */
	private static boolean isThreadPoolBusy() {
		if (!(GLOBAL_EXECUTOR instanceof ThreadPoolExecutor)) {
			return false;
		}

		ThreadPoolExecutor tpe = (ThreadPoolExecutor) GLOBAL_EXECUTOR;
		int activeCount = tpe.getActiveCount();
		int queueSize = tpe.getQueue().size();
		int maxPoolSize = tpe.getMaximumPoolSize();

		// 动态计算降级阈值
		int queueThreshold = Math.min(maxPoolSize, DEFAULT_THREAD_POOL_SIZE); // 队列阈值不超过配置的线程数

		// 判断条件：活跃线程数达到100% 且 队列中任务超过阈值
		boolean isActive100Percent = activeCount >= maxPoolSize;
		boolean isQueueBusy = queueSize >= queueThreshold;

		if (isActive100Percent && isQueueBusy) {
			CommLogger.getCommLogger("highBusy").info("触发降级 - 活跃线程: " + activeCount + "/" + maxPoolSize +
				", 队列任务: " + queueSize + "/" + queueThreshold);
		}

		return isActive100Percent && isQueueBusy;
	}

	/**
	 * 获取降级结果
	 * @param startTime 开始时间
	 * @return 降级的VIP查询结果
	 */
	private static JSONObject getFallbackResult(long startTime) {
		JSONObject result = new JSONObject();

		// 可配置的默认会员等级
		String defaultLevel = getDefaultMemberLevel();

		result.put("respCode", GWConstants.RET_CODE_SUCCESS);
		result.put("respDesc", "系统繁忙，返回默认会员等级");
		result.put("levelNameStr", defaultLevel);
		result.put("levelNameArray", new String[]{defaultLevel});
		result.put("successCount", 1); // 标记为成功，但是降级结果
		result.put("totalCount", 3);
		result.put("isFallback", true); // 标记这是降级结果

		long endTime = System.currentTimeMillis();
		CommLogger.getCommLogger("high").info("VIP查询降级处理耗时: " + (endTime - startTime) + "ms");

		return result;
	}

	/**
	 * 获取默认会员等级（可配置）
	 * @return 默认会员等级名称
	 */
	private static String getDefaultMemberLevel() {
		try {
			String configLevel = AppContext.getContext(Constants.APP_NAME).getProperty("VIP_DEFAULT_LEVEL", "");
			if (StringUtils.isNotBlank(configLevel)) {
				return configLevel;
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("high").warn("读取默认会员等级配置失败，使用系统默认值", e);
		}
		return "普通会员"; // 系统默认值
	}

	/**
	 * 执行正常的VIP并发查询
	 * @param openId 用户openId
	 * @param mobile 手机号
	 * @param uid 用户ID
	 * @param startTime 开始时间
	 * @return VIP查询结果
	 */
	private static JSONObject executeNormalVipQuery(String openId, String mobile, String uid, long startTime,String isUserData) {
		JSONObject result = new JSONObject();

		try {
			// 使用全局共享线程池，避免频繁创建销毁线程池
			// 包括本地VIP查询在内的3个并发任务
			CompletableFuture<String> futureLocal = CompletableFuture.supplyAsync(() ->
				queryLocalVipData(mobile), GLOBAL_EXECUTOR);

			CompletableFuture<JSONObject> future1 = CompletableFuture.supplyAsync(() ->
				getVipInfoSingle(openId, "1", mobile, uid), GLOBAL_EXECUTOR);

			CompletableFuture<JSONObject> future3 = CompletableFuture.supplyAsync(() ->
				getVipInfoSingle(openId, "3", mobile, uid), GLOBAL_EXECUTOR);

			/*CompletableFuture<JSONObject> future5 = CompletableFuture.supplyAsync(() ->
				getVipInfoSingle(openId, "5", mobile, uid), GLOBAL_EXECUTOR);*/
			CompletableFuture<JSONObject> future11 = CompletableFuture.supplyAsync(() ->
					getUserInfo(mobile,"",isUserData), GLOBAL_EXECUTOR);

			// 新增CSS VIP接口调用
			CompletableFuture<JSONObject> futureCss = CompletableFuture.supplyAsync(() ->
					getCssVipInfo(mobile), GLOBAL_EXECUTOR);

			// 等待所有任务完成或超时（3秒）
			//CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureLocal, future1, future3, future5);
			CompletableFuture<Void> allFutures = CompletableFuture.allOf(futureLocal, future1, future3, future11, futureCss);

			try {
				// 3秒超时控制
				allFutures.get(3, TimeUnit.SECONDS);
			} catch (TimeoutException e) {
				CommLogger.getCommLogger("high").warn("VIP查询部分超时，将处理已完成的结果，mobile=" + mobile);
				// 超时后不抛异常，继续处理已完成的结果
			}

			// 收集结果（包括超时情况下的部分结果）
			List<String> levelNames = new ArrayList<>();
			StringBuilder levelNameStr = new StringBuilder();
			JSONObject	userData = new JSONObject();
			int successCount = 0;
			int totalCount = 3; // 本地查询vip1+2个品牌查询+css vip2
			String vip1 ="";
			String vip2 ="";
			String mfLevelName = "";
			String colmoLevelName = "";
			// 处理本地VIP查询结果
			try {
				if (futureLocal.isDone() && !futureLocal.isCancelled()) {
					String localVipLevel = futureLocal.get(100, TimeUnit.MILLISECONDS);
					if (StringUtils.isNotBlank(localVipLevel)) {
						levelNames.add(localVipLevel);
						levelNameStr.append(localVipLevel);
						vip1 = localVipLevel;
						successCount++;
					}
				} else {
					CommLogger.getCommLogger("high").warn("本地VIP查询未完成，mobile=" + mobile);
				}
			} catch (Exception e) {
				CommLogger.getCommLogger("high").error("处理本地VIP查询结果异常，mobile=" + mobile, e);
			}

			// 处理品牌1（美的）的结果
			try {
				if (future1.isDone() && !future1.isCancelled()) {
					JSONObject resp1 = future1.get(100, TimeUnit.MILLISECONDS); // 很短的超时，因为应该已经完成
					String levelName1 = extractLevelName(resp1, "1");
					if (StringUtils.isNotBlank(levelName1)) {
						levelNames.add("美粉_"+levelName1);
						if (levelNameStr.length() > 0) levelNameStr.append(",");
						levelNameStr.append("美粉_"+levelName1);
						mfLevelName = levelName1;
						successCount++;
					}

				} else {
					CommLogger.getCommLogger("high").warn("品牌1（美的）VIP查询未完成，mobile=" + mobile);
				}
			} catch (Exception e) {
				CommLogger.getCommLogger("high").error("处理品牌1结果异常，mobile=" + mobile, e);
			}

			// 处理品牌3（colmo）的结果
			try {
				if (future3.isDone() && !future3.isCancelled()) {
					JSONObject resp3 = future3.get(100, TimeUnit.MILLISECONDS);
					String levelName3 = extractLevelName(resp3, "3");
					if (StringUtils.isNotBlank(levelName3)) {
						levelNames.add("COLMO_"+levelName3);
						if (levelNameStr.length() > 0) levelNameStr.append(",");
						levelNameStr.append("COLMO_"+levelName3);
						colmoLevelName = levelName3;
						successCount++;
					}
				} else {
					CommLogger.getCommLogger("high").warn("品牌3（colmo）VIP查询未完成，mobile=" + mobile);
				}
			} catch (Exception e) {
				CommLogger.getCommLogger("high").error("处理品牌3结果异常，mobile=" + mobile, e);
			}
			// 处理
			try {
				if (future11.isDone() && !future11.isCancelled()) {
					 userData = future11.get(100, TimeUnit.MILLISECONDS);
				} else {
					CommLogger.getCommLogger("high").warn("品牌3（colmo）VIP查询未完成，mobile=" + mobile);
				}
			} catch (Exception e) {
				CommLogger.getCommLogger("high").error("处理品牌3结果异常，mobile=" + mobile, e);
			}

			// 处理CSS VIP查询结果
			JSONArray cssRespData = new JSONArray();
			try {
				if (futureCss.isDone() && !futureCss.isCancelled()) {
					JSONObject cssResp = futureCss.get(100, TimeUnit.MILLISECONDS);
					if (cssResp.getBoolean("success")&& cssResp.getBoolean("isSensitive")) {
						// 如果isSensitive为true，添加VIP2到等级列表
						levelNames.add("VIP2");
						if (levelNameStr.length() > 0) levelNameStr.append(",");
						levelNameStr.append("VIP2");
						vip2 = "VIP2";
						// 保存respData数组
						cssRespData = cssResp.getJSONArray("respData");
						CommLogger.getCommLogger("high").info("CSS VIP查询成功，isSensitive=true，添加VIP2等级，respData size=" +
								   (cssRespData != null ? cssRespData.size() : 0));
					} else {
						CommLogger.getCommLogger("high").info("CSS VIP查询完成，isSensitive=false，不添加VIP2等级");
					}
					successCount++;
				} else {
					CommLogger.getCommLogger("high").warn("CSS VIP查询未完成，mobile=" + mobile);
				}
			} catch (Exception e) {
				CommLogger.getCommLogger("high").error("处理CSS VIP查询结果异常，mobile=" + mobile, e);
			}

			// 处理品牌5（东芝）的结果
		/*	try {
				if (future5.isDone() && !future5.isCancelled()) {
					JSONObject resp5 = future5.get(100, TimeUnit.MILLISECONDS);
					String levelName5 = extractLevelName(resp5, "5");
					if (StringUtils.isNotBlank(levelName5)) {
						levelNames.add(levelName5);
						if (levelNameStr.length() > 0) levelNameStr.append(",");
						levelNameStr.append(levelName5);
					}
					successCount++;
				} else {
					CommLogger.getCommLogger().warn("品牌5（东芝）VIP查询未完成，mobile=" + mobile);
				}
			} catch (Exception e) {
				CommLogger.getCommLogger().error("处理品牌5结果异常，mobile=" + mobile, e);
			}*/

			// 封装返回结果
			if (successCount > 0 || (userData!=null&&!userData.isEmpty())) {
				result.put("respCode", GWConstants.RET_CODE_SUCCESS);
				result.put("respDesc", String.format("查询完成，成功%d个，总共%d个", successCount, totalCount));
				result.put("levelNameStr", levelNameStr.toString()); // 字符串拼接
				result.put("levelNameArray", levelNames.toArray(new String[0])); // 字符串数组
				result.put("successCount", successCount); // 成功查询的数量
				result.put("userData", userData);
				result.put("totalCount", totalCount); // 总查询数量
				result.put("vip1", vip1);
				result.put("vip2", vip2);
				result.put("mfLevelName", mfLevelName);
				result.put("colmoLevelName", colmoLevelName);
				// 添加CSS VIP的respData字段
				if (cssRespData != null) {
					result.put("sensitiveData", cssRespData);
					CommLogger.getCommLogger("high").info("添加CSS VIP respData到返回结果，size=" + cssRespData.size());
				}
			} else {
				result.put("respCode", "998");
				result.put("respDesc", "所有VIP查询都未能在3秒内完成");
				result.put("levelNameStr", "");
				result.put("levelNameArray", new String[0]);
				result.put("vip1", "");
				result.put("vip2", "");
				result.put("mfLevelName", "");
				result.put("colmoLevelName", "");
				result.put("successCount", 0);
				result.put("userData", new JSONObject());
				result.put("totalCount", totalCount);
			}

		} catch (Exception e) {
			CommLogger.getCommLogger("high").error("并发查询VIP信息异常，mobile=" + mobile, e);
			result.put("respCode", "999");
			result.put("respDesc", "系统繁忙，请稍后重试"); // 不暴露具体异常信息
		} finally {
			long endTime = System.currentTimeMillis();
			CommLogger.getCommLogger("high").info("VIP查询耗时: " + (endTime - startTime) + "ms, mobile=" + mobile);
		}

		return result;
	}



	/**
	 * 获取当前手机号的信息
	 * @param json
	 * @return
	 */
	public static JSONObject getUserInfo(String phone,String userInfo,String isUserData){
		JSONObject jsonObject = new JSONObject();
		try {
			if (StringUtils.isBlank(phone) || (StringUtils.isNotBlank(userInfo)&&StringUtils.equals("1",isUserData))) {
				CommLogger.getCommLogger("high").info("手机号为空或者已存在userData，跳过本地VIP查询");
				return jsonObject;
			}
			if(StringUtils.isNotBlank(userInfo)&&!"{}".equals(userInfo)&&!"Enter Value".equals(userInfo)){
				return JSON.parseObject(userInfo);
			}
			JSONObject params = new JSONObject();
			JSONObject json = new JSONObject();
			json.put("custPhone",phone);
			params.put("params", json);
			params.put("command", "getCustGroups");
			IService service;
			service = ServiceContext.getService("MIXGW_DDY_INTEFACE");
			JSONObject resp = service.invoke(params);
			CommLogger.getCommLogger("high").info("获取当前手机号的信息返回"+resp.toJSONString());
			if("000".equals(resp.get("respCode"))){
				if("0000".equals(resp.getJSONObject("respData").getString("code"))){
					return resp.getJSONObject("respData").getJSONObject("data");
				}
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("high").error("获取当前手机号的信息异常"+e.getMessage(), e);
		}
		return jsonObject;
	}

	/**
	 * 获取CSS VIP信息
	 * @param phone 手机号
	 * @return CSS VIP查询结果
	 */
	public static JSONObject getCssVipInfo(String phone) {
		JSONObject result = new JSONObject();
		try {
			if (StringUtils.isBlank(phone)) {
				CommLogger.getCommLogger("high").info("手机号为空，跳过CSS VIP查询");
				result.put("success", false);
				return result;
			}

			JSONObject params = new JSONObject();
			JSONObject json = new JSONObject();
			json.put("phoneNum", phone);
			params.put("params", json);
			params.put("command", "getCssVipInfo");

			IService service = ServiceContext.getService("CSSGW-GETVIP-USERINFO");
			JSONObject resp = service.invoke(params);

			CommLogger.getCommLogger("high").info("CSS VIP查询返回: " + resp.toJSONString());

			if (GWConstants.RET_CODE_SUCCESS.equals(resp.getString("respCode"))) {
				JSONObject respData = resp.getJSONObject("respData");
				if (respData != null) {
					result.put("isSensitive", respData.getBoolean("isSensitive"));
					result.put("respData", respData.getJSONArray("respData"));
					result.put("success", true);
				}
			} else {
				CommLogger.getCommLogger("high").warn("CSS VIP查询失败: " + resp.getString("respDesc"));
				result.put("success", false);
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("high").error("CSS VIP查询异常，phone=" + phone, e);
			result.put("success", false);
		}
		return result;
	}

	/**
	 * 从响应中提取levelName
	 * @param response 服务响应
	 * @param brand 品牌
	 * @return levelName
	 */
	private static String extractLevelName(JSONObject response, String brand) {
		try {
			if (StringUtils.equals(response.getString("respCode"), GWConstants.RET_CODE_SUCCESS)) {
				if (StringUtils.isNotBlank(response.getString("levelName"))) {
					return response.getString("levelName");
				}
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("high").error("提取levelName异常，brand=" + brand, e);
		}
		return "";
	}

	/**
	 * 单次VIP信息查询方法
	 * @param openId 用户openId
	 * @param brand 品牌（1-美的 3-colmo 5-东芝）
	 * @param mobile 手机号
	 * @param uid 用户ID
	 * @return 包含levelName的响应数据
	 */
	public static JSONObject getVipInfoSingle(String openId, String brand, String mobile, String uid) {
		try {
			JSONObject respData = new JSONObject();
			JSONObject params = new JSONObject();
			params.put("openId", StringUtils.isBlank(openId) ? "" : openId);
			params.put("brand", StringUtils.isBlank(brand) ? "" : brand);
			params.put("mobile", StringUtils.isBlank(mobile) ? "" : mobile);
			params.put("uid", StringUtils.isBlank(uid) ? "" : uid);
			params.put("command","getVipInfo");
			IService service = ServiceContext.getService("MIXGW_HIGH_VALUE_USER_VIP");
			respData = service.invoke(params);
			CommLogger.getCommLogger("high").info("VIP信息查询返回: " + respData.toJSONString());
			return respData;
		} catch (ServiceException e) {
			CommLogger.getCommLogger("high").error("VIP信息查询异常，brand=" + brand, e);
			JSONObject errorResp = new JSONObject();
			errorResp.put("respCode", "999");
			errorResp.put("respDesc", "服务暂时不可用，请稍后重试"); // 不暴露具体异常信息
			return errorResp;
		}
	}




	/**
	 * 优雅关闭线程池（应用关闭时调用）
	 */
	public static void shutdown() {
		if (GLOBAL_EXECUTOR != null && !GLOBAL_EXECUTOR.isShutdown()) {
			GLOBAL_EXECUTOR.shutdown();
			try {
				if (!GLOBAL_EXECUTOR.awaitTermination(30, TimeUnit.SECONDS)) {
					GLOBAL_EXECUTOR.shutdownNow();
				}
			} catch (InterruptedException e) {
				GLOBAL_EXECUTOR.shutdownNow();
				Thread.currentThread().interrupt();
			}
		}
	}

	/**
	 * 查询本地会员数据
	 * @param mobile 手机号
	 * @param uid 用户ID（不使用，USER_NO只对应手机号）
	 * @return 本地VIP等级，如果不存在返回空字符串
	 */
	private static String queryLocalVipData(String mobile) {
		try {
			// 只有手机号才查询，USER_NO字段只存手机号
			if (StringUtils.isBlank(mobile)) {
				CommLogger.getCommLogger("high").info("手机号为空，跳过本地VIP查询");
				return "";
			}

			EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);

			// 使用EasySQL构建查询条件
			EasySQL sql = new EasySQL();
			sql.append("SELECT count(1) FROM C_RED_BLOCK_LIST WHERE STATUS = ? AND LIST_TYPE = ? AND VIP_GRADE = ?  AND USER_NO = ?");

			CommLogger.getCommLogger("high").info("查询本地VIP数据，mobile: " + mobile);

			// 使用queryForExist检查是否存在
			boolean exists = query.queryForExist(sql.getSQL(),new Object[]{"1","01","1",mobile});

			if (exists) {
				CommLogger.getCommLogger("high").info("找到本地VIP数据，mobile: " + mobile);
				return "VIP1"; // 返回VIP1标识
			} else {
				CommLogger.getCommLogger("high").info("未找到本地VIP数据，mobile: " + mobile);
				return "";
			}
		} catch (Exception e) {
			CommLogger.getCommLogger("high").error("查询本地VIP数据异常，mobile: " + mobile, e);
			return ""; // 异常时返回空，不影响其他查询
		}
	}

	/**
	 * 获取线程池状态信息（用于监控）
	 */
	public static String getThreadPoolStatus() {
		if (GLOBAL_EXECUTOR instanceof java.util.concurrent.ThreadPoolExecutor) {
			java.util.concurrent.ThreadPoolExecutor tpe = (java.util.concurrent.ThreadPoolExecutor) GLOBAL_EXECUTOR;
			return String.format("线程池状态 - 核心线程数:%d, 活跃线程数:%d, 队列大小:%d, 已完成任务数:%d",
				tpe.getCorePoolSize(), tpe.getActiveCount(), tpe.getQueue().size(), tpe.getCompletedTaskCount());
		}
		return "线程池状态信息不可用";
	}

}
