package com.yunqu.cc.activeService.inf;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;

import org.apache.log4j.Logger;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;

import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.ConfigUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.servlet.IvrServiceServlet;
/**
 * IVR策略 定时分配
 */
public class IvrService extends IService{
	@Override
	public JSONObject invoke(JSONObject json1) throws ServiceException {
		if("refreshData".equals(json1.getString("command"))){//定时器
            IvrRefreshDataService.invoke();
            Voice5GService.invoke();
		}else if("ivrService".equals(json1.getString("command"))){
			return IvrIntefaceService.invoke(json1);
		}else if("synFile".equals(json1.getString("command"))){
			return synFile(json1);
		}
		return null;
	}
	/**
	 * 从另一台服务器同步录音文件
	 * @param json
	 * @return
	 */
	public static JSONObject synFile(JSONObject json) {
		CommLogger.getCommLogger("ivrFile").info(System.currentTimeMillis()+"从另一台服务器同步录音文件"+json.toJSONString());
		JSONObject req=new JSONObject();
		String fileUrl=json.getString("fileUrl");
		String fileName=json.getString("fileName");
		String voiceType=json.getString("voiceType");
        URL urlfile = null;
        HttpURLConnection httpUrl = null;
        BufferedInputStream bis = null;
        OutputStream bos=null;
        IvrServiceServlet ivrServiceServlet = new IvrServiceServlet();
        String rootPath = ivrServiceServlet.getFileSaveRootPath(voiceType);
        try{	
        	// 创建文件夹
        	File f = new File(rootPath);
			if (!f.exists()) {
	    		CommLogger.getCommLogger("ivrFile").info("无文件目录");
				f.mkdirs();
			}
        	//另一台服务器地址
    		String otherServerAddress = AppContext.getContext(Constants.APP_NAME).getProperty("OTHER_SERVER_ADDRESS","http://127.0.0.1:8080");
    		if("1".equals(voiceType)) {
    			fileUrl=otherServerAddress+"/attachment/out/attachment?action=download&filePath="+fileName+"&appName=activeService&key=IVR_ROOT_WELCOME_PAHT";
    		}else if("2".equals(voiceType)){
    			fileUrl=otherServerAddress+"/attachment/out/attachment?action=download&filePath="+fileName+"&appName=activeService&key=IVR_ROOT_WAIT_PAHT";
    		}else if("3".equals(voiceType)){
                fileUrl=otherServerAddress+"/attachment/out/attachment?action=download&filePath="+fileName+"&appName=activeService&key=SECOND_WAIT_TIP_WORDS_PAHT";
            }else if("4".equals(voiceType)){
                fileUrl=otherServerAddress+"/attachment/out/attachment?action=download&filePath="+fileName+"&appName=activeService&key=FIVE_WAIT_TIP_WORDS_PAHT";
            }else if("5".equals(voiceType)){
                fileUrl=otherServerAddress+"/attachment/out/attachment?action=download&filePath="+fileName+"&appName=activeService&key=AFTER_APPEAL_TIP_WORDS_PAHT";
            }
            urlfile = new URL(fileUrl);
            httpUrl = (HttpURLConnection)urlfile.openConnection();
            httpUrl.connect();
            bis = new BufferedInputStream(httpUrl.getInputStream());
			String fileFullName = rootPath+""+fileName;
			bos = new FileOutputStream(fileFullName);
            int len = 1024;
            byte[] b = new byte[len];
            int big=0;//限速 
            int size=0;
            long beginTime=System.currentTimeMillis();
            while ((len = bis.read(b)) != -1)
            {
                bos.write(b, 0, len);
                big++;
                size++;
                if(big == 30) {//30kb
                	long currentTime = System.currentTimeMillis();
                	long sleepTime = 1000 - (currentTime - beginTime);
                	if(sleepTime > 0) {
                		Thread.sleep(sleepTime);
                	}
                	big = 0;
                	beginTime = System.currentTimeMillis();
                }
                
            }
            bos.flush();
            bis.close();
            httpUrl.disconnect();
            req.put("state", true);
    		CommLogger.getCommLogger("ivrFile").info(System.currentTimeMillis()+"从另一台服务器同步录音文件完成size"+size+",url:"+fileUrl);
        }
        catch (Exception e)
        {
    		CommLogger.getCommLogger("ivrFile").info("从另一台服务器同步录音文件失败："+e);
            e.printStackTrace();
            req.put("state", false);
        }
        finally {
            try
            {
                bis.close();
                bos.close();
            }
            catch (IOException e)
            {
                e.printStackTrace();
            }
        }
		return req;
    }
}
