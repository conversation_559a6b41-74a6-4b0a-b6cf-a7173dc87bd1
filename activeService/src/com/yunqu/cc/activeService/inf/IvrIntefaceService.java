package com.yunqu.cc.activeService.inf;

import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import com.yq.busi.common.util.DateUtil;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.model.Response;
import com.yunqu.cc.activeService.utils.StringUtil;

public class IvrIntefaceService{
	protected static EasyQuery getQuery()
	  {
		 return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
	  }
	private static final Logger logger = CommLogger.getCommLogger("IVR");
	public static EasyCache cache = CacheManager.getMemcache();

	public static JSONObject invoke(JSONObject json) throws ServiceException {
		JSONObject req=new JSONObject();
		Response  response=new Response();
		JSONObject dataJson= json.getJSONObject("Data");
		logger.info("IVR策略查询接收参数"+dataJson);
			if( StringUtils.isBlank(dataJson.getString("phone"))||StringUtils.isBlank(dataJson.getString("call"))||StringUtils.isBlank(dataJson.getString("type"))){
				response.setRespCode(GWConstants.RET_CODE_OTHER_EXCEPTION);		
				response.setRespDesc("参数缺失");		
			}
			String phone=dataJson.getString("phone");
			String hotline=dataJson.getString("call");
			String userData = dataJson.getString("UserData");
			JSONObject userInfo = new JSONObject();

			//获取最高等级会员身份和原有获取用户身份的userData
			JSONObject userVipInfo = queryVipLevel(phone, hotline,userData);
			logger.info("获取当前手机号IVR策略用户信息"+userVipInfo.toJSONString());

			// 使用查询到的最高等级作为会员身份
			String vip = userVipInfo.getString("highestLevelName");
			Boolean isNewVip = true;
			if (StringUtils.isBlank(vip)) {
				isNewVip = false;
				vip = userVipInfo.getString("mediaMember"); // 如果没有查到最高等级，使用原有逻辑
			}
			logger.info("使用的会员身份vip=" + vip+"  isNewVip  "+isNewVip);
			String isProMember=userInfo.getString("isProMember");//是否pro会员1是0不是
			List<String> crowdPackId=new ArrayList<String>();//人群包
			if((userInfo.getJSONArray("touchTaskIds")!=null)){
				crowdPackId = JSONObject.parseArray(userInfo.getJSONArray("touchTaskIds").toJSONString(), String.class);
			}
			req.put("userData", userVipInfo);
			String phoneBelongArea= getUserArea(phone);//获取收手机号的区域
			logger.info("phoneBelongArea="+phoneBelongArea);
			try {
				if("2".equals(dataJson.getString("type"))){//有诉求内容查询技能组类型
					String callType=dataJson.getString("callType");
					if(StringUtils.isBlank(callType)) {
						req.put("ivrData", "{}");//诉求为空，则不查询任何策略
					}else {
						JSONObject ivrData = new JSONObject();
						callType = callType.replaceAll(" ", "");//ivr 传过来的诉求包含空格，需去掉空格再获取策略
						 ivrData = getSkillkeyData(hotline,callType,phoneBelongArea,crowdPackId,vip,isProMember);
						 if(isNewVip&&ivrData.isEmpty()){
							 vip = userVipInfo.getString("mediaMember");
							 ivrData = getSkillkeyData(hotline,callType,phoneBelongArea,crowdPackId,vip,isProMember);
						 }
						 logger.info("获取当前手机号技能组接入策略:"+ivrData.toJSONString());
						req.put("ivrData", ivrData);
					}
				}else{
					    JSONObject ivrData = new JSONObject();
					 	ivrData = getkeyData(hotline,phoneBelongArea,crowdPackId,vip,isProMember);
						logger.info("获取当前手机号IVR放音策略"+ivrData.toJSONString());
						if(isNewVip&&ivrData.isEmpty()){
							 vip = userVipInfo.getString("mediaMember");
							 ivrData = getkeyData(hotline,phoneBelongArea,crowdPackId,vip,isProMember);
							logger.info("获取当前手机号IVR放音策略"+ivrData.toJSONString());
						}
					req.put("ivrData", ivrData);
				}
			} catch (Exception e) {
				logger.debug("获取当前手机号IVR策略出错"+e.getMessage(),e);
				return req;
			}
			return req;
	}


	


	/**
	 * 获取当前手机号的信息
	 * @param json
	 * @return
	 */
	public static  JSONObject getUserInfo(String phone,String userInfo){
		JSONObject jsonObject = new JSONObject();
		try {
			if(StringUtils.isNotBlank(userInfo)&&!"{}".equals(userInfo)&&!"Enter Value".equals(userInfo)){
				return JSON.parseObject(userInfo);
			}
			JSONObject params = new JSONObject();
			JSONObject json = new JSONObject();
			json.put("custPhone",phone);
			params.put("params", json);
			params.put("command", "getCustGroups");
			IService service;
			service = ServiceContext.getService("MIXGW_DDY_INTEFACE");
			JSONObject resp = service.invoke(params);
			logger.info("获取当前手机号的信息返回"+resp.toJSONString());
			if("000".equals(resp.get("respCode"))){
				if("0000".equals(resp.getJSONObject("respData").getString("code"))){
					return resp.getJSONObject("respData").getJSONObject("data");
				}
				
			}
//			jsonObject.put("touchTaskIds", new Integer[] {13869,13875});
//			jsonObject.put("mediaMember", "普通会员");
//			jsonObject.put("isProMember", "0");
			
		} catch (ServiceException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
		return jsonObject;
	}
	/**
	 * 获取收手机号的区域
	 * @param phone
	 * @return
	 */			
	public static  String getUserArea(String phone){
		
		if(StringUtil.checkNull(phone) && phone.length() < 7){
			return "";
		}	
		phone = phone.substring(0,7);
		JSONObject result = new JSONObject();
		String sql = "SELECT CODE,CITY FROM TELNUM_REGION WHERE NUM = ? ";
		logger.error("查询来电归属地，sql = "+sql);
		try {
			List<EasyRow> list = getQuery().queryForList(sql, new Object[]{phone});
			if(list != null && list.size() >0 ){
				return list.get(0).getColumnValue("CODE");
			}
		} catch (SQLException e) {
			logger.error("[SQLException]查询来电归属地异常:"+e.getMessage(),e);
			return "";
		}
		return "";

		
	}
	
	/**
	 * 热线_区域_人群包_用户身份
	 * @return
	 */
	public static JSONObject getkeyData(String hotline,String phoneBelongArea,List<String> crowdPackId,String vip,String isProMember){
		List<String> getkeyList = getkeyList(hotline, phoneBelongArea, crowdPackId, vip,isProMember);
		JSONObject keyData=new JSONObject();
		logger.info("符合getkeyList策略信息"+JSONObject.toJSONString(getkeyList));
		for(String data:getkeyList){
			if(data!=null){
				JSONObject parseObject = JSON.parseObject(data);
				if(parseObject!=null){
					if(StringUtils.isNotBlank(parseObject.getString("PRIORITY_LEVEL"))){
						if(keyData.size()==0){//第一次直接赋值
							keyData=parseObject;
						}else{
							Integer mix = keyData.getInteger("PRIORITY_LEVEL");
							Integer num = parseObject.getInteger("PRIORITY_LEVEL");
							if(mix>num){//如果新的优先级大于旧的最小记录则替换
								keyData=parseObject;
							}
						}
					}
					String isNewVip = parseObject.getString("IS_NEW_VIP");
					if(StringUtils.isNotBlank(isNewVip)&&"1".equals(isNewVip)){
						if(keyData.size()==0){//第一次直接赋值
							keyData=parseObject;
						}else{
							String oldUpdateTime=keyData.getString("UPDATE_TIME");
							String newUpdateTime=parseObject.getString("UPDATE_TIME");
							if(DateUtil.compareDate(newUpdateTime, oldUpdateTime,"yyyy-MM-dd HH:mm:ss")>0){
								keyData=parseObject;
							}
						}
					}
				}
			}
		}
		logger.info("符合IVR策略信息"+keyData);
		return keyData;
	}
	/**
	 * 技能组
	 * @param hotline
	 * @param callType
	 * @param phoneBelongArea
	 * @param crowdPackId
	 * @param vip
	 * @return
	 */
	public static JSONObject getSkillkeyData(String hotline,String callType,String phoneBelongArea,List<String> crowdPackId,String vip,String isProMember){
		List<String> getkeyList = getSkillkeyList(hotline, callType, phoneBelongArea, crowdPackId, vip,isProMember);
		JSONObject keyData=new JSONObject();
		for(String data:getkeyList){
			if(data!=null){
				JSONObject parseObject = JSON.parseObject(data);
				if(parseObject!=null&&!StringUtils.isBlank(parseObject.getString("PRIORITY_LEVEL"))){
					if(keyData.size()==0){//第一次直接赋值
						keyData=parseObject;
					}else{
						Integer min = keyData.getInteger("PRIORITY_LEVEL");
						Integer num = parseObject.getInteger("PRIORITY_LEVEL");
						if(min>num){//如果新的优先级大于旧的最小记录则替换
							keyData=parseObject;
						}
					}
				}
				String isNewVip = parseObject.getString("IS_NEW_VIP");
				if(StringUtils.isNotBlank(isNewVip)&&"1".equals(isNewVip)){
					if(keyData.size()==0){//第一次直接赋值
						keyData=parseObject;
					}else{
						String oldUpdateTime=keyData.getString("UPDATE_TIME");
						String newUpdateTime=parseObject.getString("UPDATE_TIME");
						if(DateUtil.compareDate(newUpdateTime, oldUpdateTime,"yyyy-MM-dd HH:mm:ss")>0){
							keyData=parseObject;
						}
					}
				}
			}
		}
		return keyData;

	}
	public static void main(String[] args) {
//		JSONObject keyData=new JSONObject();
//		keyData.put("1", "2");
//		System.out.println(keyData.size());
		JSONObject parse = JSONObject.parseObject("{\"touchTaskIds\":null,\"mediaMember\":\"普通会员\",\"isProMember\":0}");
		System.out.println(parse);

	}
	/**
	 * 获取所有的组合
	 * @param hotline 热线
	 * @param area 区域
	 * @param crowdPackId 人群包
	 * @param vip 用户身份
	 * @return
	 */
	public static List<String> getkeyList(String hotline,String area,List<String> crowdPackIds,String vip,String isProMember){
		try {
			String edition=Constants.IVR_REFRESH_EDITION;
			//版本号
			String time=cache.get(edition);
			List<String> calls = new ArrayList<String>();
			calls.add(hotline);
			calls.add("0");
			List<String> areas = new ArrayList<String>();
			areas.add(area);
			areas.add("0");
			if(crowdPackIds==null){
				crowdPackIds=new ArrayList<String>();
			}
			crowdPackIds.add("0");
			List<String> vips = new ArrayList<String>();
			if(StringUtils.isNotBlank(vip))vips.add(vip);
			if(StringUtils.isNotBlank(isProMember)&&"1".equals(isProMember))vips.add("pro会员");
			vips.add("0");
			List<String> KeyList= new ArrayList<String>();
			for(String call:calls){//版本号_热线_区域_人群包_用户身份 : 配置信息
				String cacheId="";
				for(String area1:areas){
					for(String crowdPackId1:crowdPackIds){
						for(String vip1:vips){
							cacheId=time+"_"+hotline+"_"+area1+"_"+crowdPackId1+"_"+vip1;
							logger.info("缓存key="+cacheId);
							if(cache.get(cacheId)!=null){
								Object data=cache.get(cacheId);
								if (data instanceof JSONObject) {
									JSONObject json=cache.get(cacheId);
									KeyList.add(json.toJSONString());
								}
								if (data instanceof String) {
									KeyList.add(cache.get(cacheId));
								}
							}
						}
					}
				}
			}
			return KeyList;
		} catch (Exception e) {
			logger.debug("获取IVR所有的组合出错",e);
			return  new ArrayList<String>();
		}
		

	}
	/**
	 * 
	 * @param hotline
	 * @param userAppeal 接口如参是callType
	 * @return
	 */
	public static List<String> getSkillkeyList(String hotline,String userAppeal,String area,List<String> crowdPackIds,String vip,String isProMember){
		try {
			String edition=Constants.IVR_REFRESH_EDITION;
			//版本号
			String time=cache.get(edition);
			List<String> calls = new ArrayList<String>();
			calls.add(hotline);
			calls.add("0");
			List<String> userAppeals = new ArrayList<String>();
			userAppeals.add(userAppeal);
			userAppeals.add("0");
			
			if(crowdPackIds==null){
				crowdPackIds=new ArrayList<String>();
			}
			crowdPackIds.add("0");
			List<String> areas = new ArrayList<String>();
			areas.add(area);
			areas.add("0");
			List<String> vips = new ArrayList<String>();
			if(StringUtils.isNotBlank(vip))vips.add(vip);
			if(StringUtils.isNotBlank(isProMember)&&"1".equals(isProMember))vips.add("pro会员");
			vips.add("0");
			
			List<String> KeyList= new ArrayList<String>();
			for(String call:calls){//版本号_热线_区域_人群包_用户身份 : 配置信息
				String cacheId="";
				for(String appeal:userAppeals){
					for(String area1:areas){
						for(String crowdPackId:crowdPackIds){
							for(String vip1:vips){
								cacheId=time+"_"+hotline+"_"+appeal+"_"+area1+"_"+crowdPackId+"_"+vip1;
								logger.info("缓存key="+cacheId);
								if(cache.get(cacheId)!=null){
									Object data=cache.get(cacheId);
									if (data instanceof JSONObject) {
										JSONObject json=cache.get(cacheId);
										KeyList.add(json.toJSONString());
									}
									if (data instanceof String) {
										KeyList.add(cache.get(cacheId));
									}
								}
							}
						}
					}

					
				}
			}
			return KeyList;
		} catch (Exception e) {
			logger.debug("获取IVR所有的组合出错",e);
			return  new ArrayList<String>();
		}
		

	}

	/**
	 * 查询高价值用户VIP等级信息
	 * @param phone 手机号
	 * @param isUserData 是否存在用户信息
	 * @param hotline 热线号码
	 * @return VIP等级查询结果
	 */
	public static JSONObject queryVipLevel(String phone,String hotline,String userData) {
		try {
			JSONObject resuData = new JSONObject();
			if(StringUtils.isNotBlank(userData)&&!"{}".equals(userData)&&!"Enter Value".equals(userData)){
				resuData =  JSON.parseObject(userData);
				return resuData;
			}
			logger.info("开始查询VIP等级，phone=" + phone + ", hotline=" + hotline);

			// 构造查询参数
			JSONObject queryData = new JSONObject();
			queryData.put("mobile", StringUtils.isBlank(phone) ? "" : phone);
			JSONObject queryParams = new JSONObject();
			queryParams.put("data", queryData);

			// 调用高价值用户VIP服务
			JSONObject vipResult = HighValueUserVipService.getVipInfo(queryParams);
			logger.info("VIP等级查询结果：" + vipResult.toJSONString());
			// 处理查询结果
			if (GWConstants.RET_CODE_SUCCESS.equals(vipResult.getString("respCode"))) {
				// 查询成功，返回结果
				JSONArray levelNameArray  = vipResult.getJSONArray("levelNameArray");
				// 根据配置表查询最高等级
				String highestLevelName = getHighestMemberLevel(levelNameArray, hotline);
				//旧版userData
				resuData = vipResult.getJSONObject("userData");
				resuData.put("highestLevelName",highestLevelName);
				resuData.put("allVipLabelArray",levelNameArray);
				resuData.put("allVipLabelStr",vipResult.getString("levelNameStr"));
				logger.info("VIP等级查询成功，phone=" + phone + ", highestLevelName=" + highestLevelName);
				return resuData;
			} else {
				// 查询失败

				resuData.put("highestLevelName","");
				resuData.put("allVipLabelArray",new JSONArray());
				resuData.put("levelNameStr","");
				logger.warn("VIP等级查询失败，phone=" + phone + ", error=" + vipResult.getString("respDesc"));
				return resuData;
			}
		} catch (Exception e) {
			logger.error("查询VIP等级异常，phone=" + phone+"  :  " +e.getMessage(), e);
			JSONObject result = new JSONObject();
			result.put("highestLevelName", "");
			result.put("userData", new JSONObject());
			return result;
		}
	}

	/**
	 * 根据等级名称数组和热线号码查询配置表中MEMBER_LEVEL最大的等级
	 * @param levelNameArray 等级名称数组
	 * @param hotline 热线号码
	 * @return 最高等级名称，如果没有匹配到返回空字符串
	 */
	private static String getHighestMemberLevel(JSONArray levelNameArray, String hotline) {
		if (levelNameArray == null || levelNameArray.size() == 0) {
			logger.info("等级名称数组为空，返回空字符串");
			return "";
		}

		if (StringUtils.isBlank(hotline)) {
			logger.info("热线号码为空，返回空字符串");
			return "";
		}

		try {
			String highestLevelName = "";
			int maxMemberLevel = -1;

			logger.info("开始查询最高等级，levelNameArray=" + levelNameArray.toJSONString() + ", hotline=" + hotline);

			// 先查询配置表中HOTLINE_CODE包含当前热线号码的所有记录
			String sql = "SELECT MEMBER_TAG, MEMBER_LEVEL FROM C_AS_MEMBER_PRIORITY WHERE REGEXP_INSTR(HOTLINE_CODE, ?) > 0";

			try {
				List<EasyRow> rows = getQuery().queryForList(sql, new Object[]{hotline});
				logger.info("查询到包含热线号码" + hotline + "的配置记录数: " + (rows != null ? rows.size() : 0));

				if (rows != null && rows.size() > 0) {
					// 遍历查询结果
					for (EasyRow row : rows) {
						String memberTag = row.getColumnValue("MEMBER_TAG");
						String memberLevelStr = row.getColumnValue("MEMBER_LEVEL");

						if (StringUtils.isBlank(memberTag) || StringUtils.isBlank(memberLevelStr)) {
							continue;
						}

						// 检查这个MEMBER_TAG是否在levelNameArray中
						boolean foundInArray = false;
						for (int i = 0; i < levelNameArray.size(); i++) {
							String levelName = levelNameArray.getString(i);
							if (memberTag.equals(levelName)) {
								foundInArray = true;
								break;
							}
						}

						if (foundInArray) {
							try {
								int memberLevel = Integer.parseInt(memberLevelStr);
								logger.info("匹配到等级: " + memberTag + ", MEMBER_LEVEL: " + memberLevel);

								// 比较找出最大的MEMBER_LEVEL
								if (memberLevel > maxMemberLevel) {
									maxMemberLevel = memberLevel;
									highestLevelName = memberTag;
								}
							} catch (NumberFormatException e) {
								logger.error("MEMBER_LEVEL格式错误，memberTag=" + memberTag + ", memberLevel=" + memberLevelStr, e);
							}
						} else {
							logger.info("等级" + memberTag + "不在levelNameArray中，跳过");
						}
					}
				} else {
					logger.info("未找到包含热线号码" + hotline + "的配置记录");
				}

			} catch (SQLException e) {
				logger.error("查询等级配置异常，hotline=" + hotline, e);
			}

			logger.info("查询最高等级完成，highestLevelName=" + highestLevelName + ", maxMemberLevel=" + maxMemberLevel);
			return highestLevelName;

		} catch (Exception e) {
			logger.error("查询最高等级异常", e);
			return "";
		}
	}


}
