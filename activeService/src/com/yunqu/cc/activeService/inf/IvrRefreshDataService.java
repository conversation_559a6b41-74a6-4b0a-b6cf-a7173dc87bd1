package com.yunqu.cc.activeService.inf;

import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;

import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.DateUtil;
import com.yunqu.cc.activeService.base.CommLogger;
import com.yunqu.cc.activeService.base.Constants;
import com.yunqu.cc.activeService.utils.StringUtil;
/**
 * ivr数据更新
 */
public class IvrRefreshDataService {
	private static final IvrRefreshDataService service = new IvrRefreshDataService();
	private static  int num=0;
	private IvrRefreshDataService(){}
	public static EasyCache cache = CacheManager.getMemcache();
	private static final Logger logger = CommLogger.getCommLogger("IVR_REFRESH");

	public static int getNum() {
		return num;
	}

	public static void setNum(int num) {
		IvrRefreshDataService.num = num;
	}

	public static IvrRefreshDataService getInstance(){
		return service;
	}

 	protected static EasyQuery getQuery() {
 		return EasyQuery.getQuery(Constants.APP_NAME, Constants.YW_DS);
 	}

	public synchronized static JSONObject invoke() throws ServiceException {
		logger.info(Thread.currentThread().getName()+"开始....");
		String cacheId = StringUtil.formatNull("JOB_IVR_REFRESH_DATA");
		String value = cache.get(cacheId);
		JSONObject result = new JSONObject();
		if((value==null||"".equals(value))){//判断是否存在缓存
			cache.put(cacheId,"1",Constants.CACHE_FAIL_TIME);//缓存记录 xx分钟后清空（防止卡死不执行）
			logger.info(Thread.currentThread().getName()+"ivr数据定时任务更新开始....");
			result = ivrRefreshData();//定时同步call
			logger.info(Thread.currentThread().getName()+"ivr数据定时任务更新结束....");
			cache.delete(cacheId);//分配完毕删除缓存
		}else{//存在缓存直接跳过
			logger.info(Thread.currentThread().getName()+"ivr数据定时任务直接结束");
			result = EasyResult.fail();
		}
		return result;
	}
	/**
	 * ivr数据更新
	 * 每天凌晨定时任务、手动更新
	 * @return
	 */
	public static  JSONObject ivrRefreshData(){
		try {
			cache.get(Constants.IVR_REFRESH_EDITION);
			String dateTime = DateUtil.getCurrentDateStr("yyyyMMddhhmmss");
			List<JSONObject> list=getIvrStrategy();
			for (JSONObject json:list) {
				getIvrRefreshData(dateTime,json.getString("ID"), json);
			}
			logger.info("刷新ivr策略缓存"+dateTime);
			cache.put(Constants.IVR_REFRESH_EDITION, dateTime,60*60*24*2);
			return EasyResult.ok();
		} catch (Exception e) {
			logger.error("刷新ivr策略缓存异常"+e.getMessage(), e);
			return EasyResult.fail();
		}
	}

	/**
	 * @param time
	 * @param id
	 * @param json
	 * @return
	 * 热线_区域_人群包_用户身份 : 配置信息
	 * 热线_技能组  : 配置信息
	 * @throws Exception 
	 */
	public static  JSONObject getIvrRefreshData(String time,String id,JSONObject json) throws Exception{
		List<JSONObject> getkeyList = getkeyList(id);
		List<String> calls=getCallList(id);//
		List<String> strategy1=new ArrayList<String>();//
		List<String> strategy2=new ArrayList<String>();
		List<String> strategy3=new ArrayList<String>();
		List<String> strategy4=new ArrayList<String>();
		json.put("WELCOME_WORDS_URL", getFileName(json.getString("WELCOME_WORDS_URL")));
		json.put("WAIT_TIP_WORDS_URL", getFileName(json.getString("WAIT_TIP_WORDS_URL")));
		json.put("SECOND_WAIT_TIP_WORDS_URL", getFileName(json.getString("SECOND_WAIT_TIP_WORDS_URL")));
		json.put("FIVE_WAIT_TIP_WORDS_URL", getFileName(json.getString("FIVE_WAIT_TIP_WORDS_URL")));
		json.put("AFTER_APPEAL_TIP_WORDS_URL", getFileName(json.getString("AFTER_APPEAL_TIP_WORDS_URL")));
		for(JSONObject key:getkeyList){
			if("1".equals(key.getString("STRATEGY_TYPE"))){//区域
				if(!StringUtils.isBlank(key.getString("PHONE_BELONG_AREA"))) strategy1.add(key.getString("PHONE_BELONG_AREA"));
			}else if("2".equals(key.getString("STRATEGY_TYPE"))){//人群包
				if(!StringUtils.isBlank(key.getString("CROWD_PACK_ID")))  strategy2.add(key.getString("CROWD_PACK_ID"));
			}else if("3".equals(key.getString("STRATEGY_TYPE"))){//用户身份
				if(!StringUtils.isBlank(key.getString("VIP")))  strategy3.add(key.getString("VIP"));
			}else if("4".equals(key.getString("STRATEGY_TYPE"))){//用户诉求
				if(!StringUtils.isBlank(key.getString("CALL_TYPE")))  strategy4.add(key.getString("CALL_TYPE"));
			}
		}
		if(strategy1.size()==0) strategy1.add("0");//area
		if(strategy2.size()==0)strategy2.add("0");//crowdPackId
		if(strategy3.size()==0)strategy3.add("0");//vip
		if(strategy4.size()==0)strategy4.add("0");//callType

		for(String call:calls){//版本号_热线_区域_人群包_用户身份 : 配置信息
			if("1".equals(json.get("IVR_TYPE"))){//IVR放音配置
				String cacheId="";
				for(String area:strategy1){
					for(String crowdPackId:strategy2){
						for(String vip:strategy3){
							cacheId=time+"_"+call+"_"+area+"_"+crowdPackId+"_"+vip;
							logger.error("chcheId_1:"+cacheId);
							if(cache.get(cacheId)==null){
								cache.put(cacheId, json,60*60*24*2);
							}else{//如果存了相同的key 则保留优先级高的key
								try {
									JSONObject oldData = cache.get(cacheId);
									String isNewVip=oldData.getString("IS_NEW_VIP");
									//旧版策略采用策略优先级来进行决定高的存入缓存
									if(StringUtils.equals(isNewVip,"0")){
										int oldLevel=oldData.getInteger("PRIORITY_LEVEL");
										int newLevel=json.getInteger("PRIORITY_LEVEL");
										if(oldLevel>newLevel){
											cache.put(cacheId, json,60*60*24*2);
										}else{
											//原来的优先级数值小  优先级高
										}
									}else{//新版策略采用update_time时间最新的保存在缓存
										String oldUpdateTime=oldData.getString("UPDATE_TIME");
										String newUpdateTime=json.getString("UPDATE_TIME");
										if(DateUtil.compareDate(newUpdateTime, oldUpdateTime,"yyyy-MM-dd HH:mm:ss")>0){
											cache.put(cacheId, json,60*60*24*2);
										}else{
											//原来的时间小  优先级低
										}
									}
								} catch (Exception e) {
									logger.error("比较优先级异常"+e.getMessage(),e);
								}
								
							}
						}
					}
				}
			}
			
		}
		for(String call:calls){//版本号_热线_技能组  : 配置信息
			String cacheId="";
			if("2".equals(json.get("IVR_TYPE"))){//策略放音配置
				for(String callType:strategy4){
					for(String area:strategy1){
						for(String crowdPackId:strategy2){
							for(String vip:strategy3){
								cacheId=time+"_"+call+"_"+callType+"_"+area+"_"+crowdPackId+"_"+vip;
								logger.error("chcheId_2:"+cacheId);
								if(cache.get(cacheId)==null){
									cache.put(cacheId, json,60*60*24*2);
								}else{//如果存了相同的key 则保留优先级高的key
									try {
										JSONObject oldData = cache.get(cacheId);
										String isNewVip=oldData.getString("IS_NEW_VIP");
										//旧版策略采用策略优先级来进行决定高的存入缓存
										if(StringUtils.equals(isNewVip,"0")){
											int oldLevel=oldData.getInteger("PRIORITY_LEVEL");
											int newLevel=json.getInteger("PRIORITY_LEVEL");
											if(oldLevel>newLevel){
												cache.put(cacheId, json,60*60*24*2);
											}else{
												//原来的优先级数值小  优先级高
											}
										}else{//新版策略采用update_time时间最新的保存在缓存
											String oldUpdateTime=oldData.getString("UPDATE_TIME");
											String newUpdateTime=json.getString("UPDATE_TIME");
											if(DateUtil.compareDate(newUpdateTime, oldUpdateTime,"yyyy-MM-dd HH:mm:ss")>0){
												cache.put(cacheId, json,60*60*24*2);
											}else{
												//原来的时间小  优先级低
											}
										}

									} catch (Exception e) {
										logger.error("比较优先级异常"+e.getMessage(),e);
									}
									
								}
							
							}
						}
					}
					
				}
			}
		}
		return null;
	}
	/**
	 * 获所有配置信息
	 * @param id
	 * @return
	 * @throws SQLException
	 */
	public static List<JSONObject> getIvrStrategy() throws SQLException{
		
		EasySQL sql=new EasySQL();
		String currentDate = EasyDate.getCurrentDateString("yyyy-MM-dd");
		sql.append("SELECT   t2.id,CONTACT_POSITION,START_TIME,END_TIME,t2.PRIORITY_LEVEL,t2.TYPE as IVR_TYPE,t2.UPDATE_TIME,t2.IS_NEW_VIP");
		sql.append(",IS_SEND_SMS,SMS_CONTENT,IS_IVR_CALL,IS_INTO_ROBOT,IS_ARTIFICIAL,IS_SKILL_GROUP,SKILL_GROUP_CODE," +
				"PRIORITY_ACCESS,IS_PRIORITY_ACCESS,WELCOME_WORDS_URL,IS_WELCOME_WORDS,WAIT_TIP_WORDS_URL,IS_WAIT_TIP_WORDS,FREQUENCY");
		sql.append(",SECOND_WAIT_TIP_WORDS_URL,IS_SECOND_WAIT_TIP_WORDS,FIVE_WAIT_TIP_WORDS_URL,IS_FIVE_WAIT_TIP_WORDS,AFTER_APPEAL_TIP_WORDS_URL,IS_AFTER_APPEAL_TIP_WORDS");
		sql.append(" from C_NO_AS_IVR_DETAIL t1 ");
		sql.append("left join C_NO_AS_IVR_STRATEGY t2 on t1.IVR_STRATEGY_ID =t2.ID ");
		sql.append("1","where T1.IS_OPEN=? ");
		sql.append(currentDate," and START_TIME<= ?");
		sql.append(currentDate," and END_TIME>= ?");
		sql.append(" order by t2.UPDATE_TIME desc ");
		List<JSONObject> queryForList = getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		logger.info( "配置信息数量"+queryForList.size());
		logger.info( "配置信息"+sql.getSQL()+"    "+JSONObject.toJSONString(sql.getParams()));
		return queryForList;
	}
	/**
	 * 获取当前配置的关键配置信息
	 * @param id
	 * @throws SQLException
	 */
	public static List<JSONObject> getkeyList(String id) throws SQLException{
		EasySQL sql=new EasySQL();
		sql.append("SELECT  * from C_NO_AS_IVR_DETAIL_KEY t1 ");
		sql.append(id,"where t1.IVR_STRATEGY_ID=? ");

		List<JSONObject> queryForList = getQuery().queryForList(sql.getSQL(),sql.getParams(),new JSONMapperImpl());
		return queryForList;
	}
	/**
	 * 获取当前配置的所有的热线信息
	 * @param id
	 * @return
	 * @throws SQLException
	 */
	public static List<String> getCallList(String id) throws SQLException{
		EasySQL sql=new EasySQL();
		sql.append("SELECT  HOTLINE from C_NO_AS_IVR_DETAIL_CALL t1 ");
		sql.append(id,"where t1.IVR_STRATEGY_ID=? ");
		List<String> queryForList = getQuery().queryForList(sql.getSQL(),sql.getParams(),new EasyRowMapper<String>() {
			@Override
			@SuppressWarnings("unchecked")
			public String mapRow(ResultSet rs, int index) {
				// TODO Auto-generated method stub
				try {
					return rs.getString("HOTLINE");
				} catch (SQLException e) {
					logger.error(CommonUtil.getClassNameAndMethod(this)+ "查询数据库转换异常:"+ e.getMessage(), e);
				}
				return "";
			}
		});
		return queryForList;
	}
	
	private static String getFileName(String fileName) {
		if(StringUtils.isNotBlank(fileName)) {
			return fileName.substring(0, fileName.lastIndexOf("."));
		}else {
			return fileName;
		}
		
	}


	public static void main(String[] args) {
		JSONObject aa= new JSONObject();
		System.out.println(aa.getInteger("aa"));
	}
}
