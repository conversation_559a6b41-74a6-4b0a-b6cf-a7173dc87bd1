-- 会员身份优先级配置表增加热线号码名称字段
-- 执行时间：请在测试环境验证后再在生产环境执行

-- 1. 增加热线号码名称字段
ALTER TABLE C_AS_MEMBER_PRIORITY ADD HOTLINE_NAME VARCHAR2(1000);

-- 2. 添加字段注释
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.HOTLINE_NAME IS '热线号码名称集合，多个用逗号分隔';

-- 3. 更新现有数据的热线号码名称（可选，如果需要初始化现有数据）
-- 注意：这个更新脚本需要根据实际的CC_HOTLINE字典数据进行调整
/*
UPDATE C_AS_MEMBER_PRIORITY SET HOTLINE_NAME = (
    SELECT LISTAGG(d.NAME, ',') WITHIN GROUP (ORDER BY d.NAME)
    FROM C_DICT d
    WHERE d.DICT_GROUP_CODE = 'CC_HOTLINE'
    AND d.ENABLE_STATUS = '1'
    AND REGEXP_INSTR(C_AS_MEMBER_PRIORITY.HOTLINE_CODE, d.CODE) > 0
) WHERE HOTLINE_CODE IS NOT NULL;
*/

-- 4. 验证字段添加结果
SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, COMMENTS 
FROM USER_COL_COMMENTS 
WHERE TABLE_NAME = 'C_AS_MEMBER_PRIORITY' 
AND COLUMN_NAME = 'HOTLINE_NAME';

-- 5. 查看表结构
DESC C_AS_MEMBER_PRIORITY;
