<?xml version="1.0" encoding="UTF-8"?>
<!-- 
	id:必须全局唯一，命名方式：应用名称+菜单名称
	name：菜单或功能名称
	type：资源类型，1：应用 ，2：菜单，3：功能，9: 其它
	state：状态，0:正常，缺省 ，1：暂停
	portal:所属应用，不填写缺省为通用portal，根据菜单的归属，可以填写具体的所属的portal，例如：my_portal
	index:排序，菜单的显示按照升序进行排序。
	icon：菜单图标
 -->
<resources>
   <resource id="neworder" name="工单与回访" url="" type="2" portal="" state="0" order="4" index = "5">
		<resource id="template_mgr" name="模板管理" type="2" icon="" portal="" state="0" url="" index="17">
		    <resource id="as_prod_script_list" name="设备引导激活话术配置"  type="2" icon="" portal="" state="0" url="/activeService/pages/config/script-list.jsp" index="13"/>
		</resource>
   </resource>
   <resource id="activeService" name="主动服务" url="" type="2" portal="" state="0" order="28" index = "301">
		<resource id="activeService_strategy" name="策略配置" type="2" icon="" portal="" state="0" url="" index="1">
		    <resource id="as_crowd_pack_list" name="人群包列表"  type="2" icon="" portal="" state="0" url="/activeService/pages/crowdPack/crowd-pack-list.jsp" index="2">
			    <resource id="as_crowd_pack_list_import" name="人群包导入"   url="" type="3" portal="" icon="" state="0" index="1"/>
			    <resource id="as_crowd_pack_list_export" name="人群包导出"   url="" type="3" portal="" icon="" state="0" index="2"/>
			    <resource id="as_crowd_list_import" name="人群明细导入"   url="" type="3" portal="" icon="" state="0" index="3"/>
			    <resource id="as_crowd_list_export" name="人群明细导出"   url="" type="3" portal="" icon="" state="0" index="4"/>
			    <resource id="as_crowd_pack_del" name="人群包删除"   url="" type="3" portal="" icon="" state="0" index="5"/>
			    <resource id="voice_strategy" name="5G策略配置"   url="" type="3" portal="" icon="" state="0" index="6"/>
		    </resource>
		    <resource id="as_crowd_pack_group_list" name="人群包组列表"  type="2" icon="" portal="" state="0" url="/activeService/pages/crowdPack/crowd-pack-group-list.jsp" index="3">
			    <resource id="as_crowd_pack_group_create" name="新增"   url="" type="3" portal="" icon="" state="0" index="1"/>
			    <resource id="as_crowd_pack_group_import" name="导入"   url="" type="3" portal="" icon="" state="0" index="2"/>
			    <resource id="as_crowd_pack_group_add_crowdpack" name="关联人群包"   url="" type="3" portal="" icon="" state="0" index="3"/>
			    <resource id="as_crowd_pack_group_del_crowdpack" name="删除关联人群包"   url="" type="3" portal="" icon="" state="0" index="3"/>
		    </resource>
		    <resource id="as_ivr_strategy_list" name="IVR策略配置"  type="2" icon="" portal="" state="0" url="/activeService/pages/config/ivr/ivr-strategy-list.jsp" index="4"/>
			<resource id="as_member_priority_list" name="会员优先级配置"  type="2" icon="" portal="" state="0" url="/activeService/pages/config/member-priority-list.jsp" index="5"/>
		</resource>
		<resource id="activeService_other" name="其他配置及素材库" type="2" icon="" portal="" state="0" url="" index="2">
		    <resource id="as_crowd_pack_priority_list" name="人群包优先级配置"  type="2" icon="" portal="" state="0" url="/activeService/pages/crowdPack/crowd-priority-list.jsp" index="1"/>
		    <resource id="as_distrib_user_list" name="免打扰用户群"  type="2" icon="" portal="" state="0" url="/activeService/pages/config/distrib/not-distrb-user-list.jsp" index="2"/>
		    <resource id="as_questionnaire_list" name="问卷配置"  type="2" icon="" portal="" state="0" url="/activeService/pages/questionnaire/questionnaire-list.jsp" index="3"/>
		    <resource id="as_material_library_list" name="素材库"  type="2" icon="" portal="" state="0" url="/activeService/pages/config/materialLibrary/material-library.jsp" index="4"/>
			<resource id="as_org_user_config" name="组织人员配置"  type="2" icon="" portal="" state="0" url="/activeService/pages/org/org-agent-config.jsp" index="5"/>
			<resource id="not_disturb_strategy_list" name="免打扰用户标签设置"  type="2" icon="" portal="" state="0" url="/activeService/pages/notDisturbStrategy/not-disturb-strategy-list.html" index="6"/>
			<resource id="not_disturb_remind_list" name="免打扰用户外呼触发过滤提醒"  type="2" icon="" portal="" state="0" url="/activeService/pages/notDisturbStrategy/not-disturb-remind-list.html" index="7"/>
		</resource>
		<resource id="activeService_deal" name="主动服务处理" type="2" icon="" portal="" state="0" url="" index="5">
		    <resource id="as_revisit_list" name="主动外呼处理列表"  type="2" icon="" portal="" state="0" url="/activeService/pages/active/revisit/revisit-list.jsp" index="1">
		    	<resource id="as_revisit_list_apply" name="申请"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    	<resource id="as_revisit_list_publish" name="发布"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    	<resource id="as_revisit_list_recycle" name="回收"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    	<resource id="as_revisit_list_export" name="导出"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    	<resource id="as_revisit_list_monitor" name="管理员权限"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    </resource>
		    <resource id="as_wechatwork_send_list" name="企微策略待确认列表"  type="2" icon="" portal="" state="0" url="/activeService/pages/config/wechatWorkStrategy/strategy-send-list.jsp" index="2"/>
		</resource>
		<resource id="activeService_record" name="主动服务记录" type="2" icon="" portal="" state="0" url="" index="3">
		    <resource id="as_revisit_robot_list" name="机器人回访记录"  type="2" icon="" portal="" state="0" url="/activeService/pages/active/revisit/revisit-robot-result-list.jsp" index="1">
		    	<resource id="as_revisit_robot_export" name="导出"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    	<resource id="as_revisit_robot_monitor" name="管理员权限"   url="" type="3" portal="" icon="" state="0" index="1"/>
		    </resource>
		</resource>
   </resource>
</resources>

