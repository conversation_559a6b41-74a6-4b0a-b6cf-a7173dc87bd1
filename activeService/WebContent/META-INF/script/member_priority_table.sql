-- 会员身份优先级配置表
CREATE TABLE C_AS_MEMBER_PRIORITY (
    ID VARCHAR2(32) PRIMARY KEY,
    MEMBER_TAG VARCHAR2(50) NOT NULL,     -- 会员身份标签
    HOTLINE_CODE VARCHAR2(500) NOT NULL,  -- 热线号码(多个用逗号分隔)
    MEMBER_LEVEL NUMBER(2) NOT NULL,      -- 会员身份等级
    CREATE_TIME VARCHAR2(19),             -- 创建时间
    CREATE_ACC VARCHAR2(50),              -- 创建人账号
    CREATE_NAME VARCHAR2(50),             -- 创建人姓名
    UPDATE_TIME VARCHAR2(19),             -- 更新时间
    UPDATE_ACC VARCHAR2(50),              -- 更新人账号
    UPDATE_NAME VARCHAR2(50),             -- 更新人姓名
    REMARK VARCHAR2(500)                  -- 备注
);

-- 注释掉不合理的唯一约束，因为支持多选热线号码后，应该允许同一会员标签+等级对应不同的热线号码组合
-- CREATE UNIQUE INDEX UK_MEMBER_PRIORITY ON C_AS_MEMBER_PRIORITY (MEMBER_TAG, HOTLINE_CODE, MEMBER_LEVEL);

-- 添加表注释
COMMENT ON TABLE C_AS_MEMBER_PRIORITY IS '会员身份优先级配置表';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.ID IS '主键ID';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.MEMBER_TAG IS '会员身份标签';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.HOTLINE_CODE IS '热线号码(多个用逗号分隔)';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.MEMBER_LEVEL IS '会员身份等级';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.CREATE_ACC IS '创建人账号';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.CREATE_NAME IS '创建人姓名';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.UPDATE_ACC IS '更新人账号';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.UPDATE_NAME IS '更新人姓名';
COMMENT ON COLUMN C_AS_MEMBER_PRIORITY.REMARK IS '备注';

-- 插入会员身份标签数据字典组
INSERT INTO C_CF_DICTGROUP (ID, CODE, NAME, TYPE, BAKUP, CREATE_ACC, CREATE_TIME, CREATE_DEPT, EP_CODE, MODULE, UPDATE_ACC, UPDATE_TIME, SORT_NUM, ENABLE_STATUS)
VALUES ('MEMBER_TAG_GROUP_ID', 'MEMBER_TAG', '会员身份标签', '1', '会员身份标签字典', 'system', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), '001', '001', '99', NULL, NULL, 1, 'Y');

-- 插入会员身份标签字典项
INSERT INTO C_CF_DICT (ID, DICT_GROUP_ID, CODE, NAME, BAKUP, CREATE_ACC, CREATE_DEPT, CREATE_TIME, NEXT_DICT_GROUP_ID, SORT_NUM, ENABLE_STATUS)
VALUES ('MEMBER_TAG_VIP', 'MEMBER_TAG_GROUP_ID', 'VIP', 'VIP客户', 'VIP客户标签', 'system', '001', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), NULL, 1, 'Y');

INSERT INTO C_CF_DICT (ID, DICT_GROUP_ID, CODE, NAME, BAKUP, CREATE_ACC, CREATE_DEPT, CREATE_TIME, NEXT_DICT_GROUP_ID, SORT_NUM, ENABLE_STATUS)
VALUES ('MEMBER_TAG_GOLD', 'MEMBER_TAG_GROUP_ID', 'GOLD', '金牌客户', '金牌客户标签', 'system', '001', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), NULL, 2, 'Y');

INSERT INTO C_CF_DICT (ID, DICT_GROUP_ID, CODE, NAME, BAKUP, CREATE_ACC, CREATE_DEPT, CREATE_TIME, NEXT_DICT_GROUP_ID, SORT_NUM, ENABLE_STATUS)
VALUES ('MEMBER_TAG_SILVER', 'MEMBER_TAG_GROUP_ID', 'SILVER', '银牌客户', '银牌客户标签', 'system', '001', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), NULL, 3, 'Y');

INSERT INTO C_CF_DICT (ID, DICT_GROUP_ID, CODE, NAME, BAKUP, CREATE_ACC, CREATE_DEPT, CREATE_TIME, NEXT_DICT_GROUP_ID, SORT_NUM, ENABLE_STATUS)
VALUES ('MEMBER_TAG_NORMAL', 'MEMBER_TAG_GROUP_ID', 'NORMAL', '普通客户', '普通客户标签', 'system', '001', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), NULL, 4, 'Y');

INSERT INTO C_CF_DICT (ID, DICT_GROUP_ID, CODE, NAME, BAKUP, CREATE_ACC, CREATE_DEPT, CREATE_TIME, NEXT_DICT_GROUP_ID, SORT_NUM, ENABLE_STATUS)
VALUES ('MEMBER_TAG_ENTERPRISE', 'MEMBER_TAG_GROUP_ID', 'ENTERPRISE', '企业客户', '企业客户标签', 'system', '001', TO_CHAR(SYSDATE, 'YYYY-MM-DD HH24:MI:SS'), NULL, 5, 'Y');
