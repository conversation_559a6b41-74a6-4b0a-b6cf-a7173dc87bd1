-- 修复会员身份优先级配置表的约束问题
-- 删除不合理的唯一约束，因为支持多选热线号码后，应该允许同一会员标签+等级对应不同的热线号码组合

-- 检查约束是否存在并删除
BEGIN
    -- 尝试删除唯一索引
    EXECUTE IMMEDIATE 'DROP INDEX UK_MEMBER_PRIORITY';
    DBMS_OUTPUT.PUT_LINE('成功删除唯一索引 UK_MEMBER_PRIORITY');
EXCEPTION
    WHEN OTHERS THEN
        IF SQLCODE = -1418 THEN -- 索引不存在
            DBMS_OUTPUT.PUT_LINE('索引 UK_MEMBER_PRIORITY 不存在，无需删除');
        ELSE
            DBMS_OUTPUT.PUT_LINE('删除索引失败: ' || SQLERRM);
        END IF;
END;
/

-- 检查是否还有其他相关约束
SELECT constraint_name, constraint_type, search_condition 
FROM user_constraints 
WHERE table_name = 'C_AS_MEMBER_PRIORITY';

-- 检查索引
SELECT index_name, uniqueness, column_name 
FROM user_ind_columns 
WHERE table_name = 'C_AS_MEMBER_PRIORITY'
ORDER BY index_name, column_position;
