-- IVR策略配置详情表新增字段脚本
-- 表名：C_NO_AS_IVR_DETAIL
-- 执行时间：请根据实际情况调整

-- 新增2-5次排队提示语相关字段
ALTER TABLE "YWDB"."C_NO_AS_IVR_DETAIL" ADD "SECOND_WAIT_TIP_WORDS_URL" VARCHAR2(200);
ALTER TABLE "YWDB"."C_NO_AS_IVR_DETAIL" ADD "IS_SECOND_WAIT_TIP_WORDS" VARCHAR2(2) DEFAULT '0';

-- 新增>5次排队提示语相关字段
ALTER TABLE "YWDB"."C_NO_AS_IVR_DETAIL" ADD "FIVE_WAIT_TIP_WORDS_URL" VARCHAR2(200);
ALTER TABLE "YWDB"."C_NO_AS_IVR_DETAIL" ADD "IS_FIVE_WAIT_TIP_WORDS" VARCHAR2(2) DEFAULT '0';

-- 新增用户报完诉求后的播报语相关字段
ALTER TABLE "YWDB"."C_NO_AS_IVR_DETAIL" ADD "AFTER_APPEAL_TIP_WORDS_URL" VARCHAR2(200);
ALTER TABLE "YWDB"."C_NO_AS_IVR_DETAIL" ADD "IS_AFTER_APPEAL_TIP_WORDS" VARCHAR2(2) DEFAULT '0';

-- 添加字段注释
COMMENT ON COLUMN "YWDB"."C_NO_AS_IVR_DETAIL"."SECOND_WAIT_TIP_WORDS_URL" IS '2-5次排队提示语';
COMMENT ON COLUMN "YWDB"."C_NO_AS_IVR_DETAIL"."IS_SECOND_WAIT_TIP_WORDS" IS '2-5次排队提示语是否生效';
COMMENT ON COLUMN "YWDB"."C_NO_AS_IVR_DETAIL"."FIVE_WAIT_TIP_WORDS_URL" IS '>5次排队提示语';
COMMENT ON COLUMN "YWDB"."C_NO_AS_IVR_DETAIL"."IS_FIVE_WAIT_TIP_WORDS" IS '>5次排队提示语是否生效';
COMMENT ON COLUMN "YWDB"."C_NO_AS_IVR_DETAIL"."AFTER_APPEAL_TIP_WORDS_URL" IS '用户报完诉求后的播报语';
COMMENT ON COLUMN "YWDB"."C_NO_AS_IVR_DETAIL"."IS_AFTER_APPEAL_TIP_WORDS" IS '用户报完诉求后的播报语是否生效';

-- 注意：由于添加字段时已设置DEFAULT '0'，现有记录会自动设置为默认值，无需额外UPDATE

-- 提交事务
COMMIT;

-- 验证字段是否添加成功
SELECT COLUMN_NAME, DATA_TYPE, DATA_LENGTH, NULLABLE, COMMENTS 
FROM USER_COL_COMMENTS 
WHERE TABLE_NAME = 'C_NO_AS_IVR_DETAIL' 
  AND COLUMN_NAME IN (
    'SECOND_WAIT_TIP_WORDS_URL',
    'IS_SECOND_WAIT_TIP_WORDS',
    'FIVE_WAIT_TIP_WORDS_URL',
    'IS_FIVE_WAIT_TIP_WORDS',
    'AFTER_APPEAL_TIP_WORDS_URL',
    'IS_AFTER_APPEAL_TIP_WORDS'
  )
ORDER BY COLUMN_NAME;
