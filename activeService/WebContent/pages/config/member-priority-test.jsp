<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>会员身份优先级配置测试</title>
</EasyTag:override>
<EasyTag:override name="content">
	<div class="container-fluid">
		<h3>会员身份优先级配置功能测试</h3>
		
		<div class="panel panel-default">
			<div class="panel-heading">功能测试</div>
			<div class="panel-body">
				<div class="row">
					<div class="col-md-12">
						<button type="button" class="btn btn-primary" onclick="test.openList()">
							打开列表页面
						</button>
						<button type="button" class="btn btn-success" onclick="test.openAdd()">
							打开新增页面
						</button>
						<button type="button" class="btn btn-info" onclick="test.openImport()">
							打开导入页面
						</button>
						<button type="button" class="btn btn-warning" onclick="test.testDict()">
							测试数据字典
						</button>
						<button type="button" class="btn btn-danger" onclick="test.testConflictCheck()">
							测试冲突检查
						</button>
						<button type="button" class="btn btn-secondary" onclick="test.testImportFormat()">
							测试导入格式
						</button>
					</div>
				</div>
				
				<hr>
				
				<div class="row">
					<div class="col-md-12">
						<h4>测试结果：</h4>
						<div id="testResult" style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; min-height: 100px;">
							点击上方按钮进行功能测试...
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="panel panel-default">
			<div class="panel-heading">数据库测试</div>
			<div class="panel-body">
				<button type="button" class="btn btn-primary" onclick="test.testDatabase()">
					测试数据库连接
				</button>
				<button type="button" class="btn btn-success" onclick="test.testInsert()">
					测试数据插入
				</button>
				<button type="button" class="btn btn-info" onclick="test.testQuery()">
					测试数据查询
				</button>
				
				<div id="dbTestResult" style="background-color: #f5f5f5; padding: 10px; border-radius: 4px; min-height: 100px; margin-top: 10px;">
					数据库测试结果将显示在这里...
				</div>
			</div>
		</div>
	</div>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("test");
		
		test.openList = function() {
			try {
				popup.openTab("${ctxPath}/servlet/MemberPriority?action=memberPriorityList", "会员身份优先级配置列表");
				$("#testResult").html("✓ 列表页面打开成功");
			} catch (e) {
				$("#testResult").html("✗ 列表页面打开失败：" + e.message);
			}
		}
		
		test.openAdd = function() {
			try {
				popup.layerShow({
					type: 1,
					title: '新增会员身份优先级配置',
					offset: '20px',
					area: ['600px', '500px']
				}, "${ctxPath}/servlet/MemberPriority?action=memberPriorityEdit", {
					ID: ""
				});
				$("#testResult").html("✓ 新增页面打开成功");
			} catch (e) {
				$("#testResult").html("✗ 新增页面打开失败：" + e.message);
			}
		}
		
		test.openImport = function() {
			try {
				popup.layerShow({
					type: 1,
					title: "会员身份优先级配置导入",
					offset: '20px',
					area: ['500px', '300px']
				}, "${ctxPath}/servlet/MemberPriority?action=memberPriorityImport", null);
				$("#testResult").html("✓ 导入页面打开成功");
			} catch (e) {
				$("#testResult").html("✗ 导入页面打开失败：" + e.message);
			}
		}
		
		test.testDict = function() {
			ajax.remoteCall("${ctxPath}/dao/Dict?action=getDict", {param: ["MEMBER_TAG"]}, function(result) {
				if (result && result.data) {
					var html = "✓ 会员身份标签数据字典测试成功：<br>";
					for (var key in result.data) {
						html += "- " + key + ": " + result.data[key] + "<br>";
					}
					$("#testResult").html(html);
				} else {
					$("#testResult").html("✗ 会员身份标签数据字典测试失败");
				}
			});
		}
		
		test.testDatabase = function() {
			ajax.remoteCall("${ctxPath}/dao/memberPriority?action=getMemberPriorityList", {}, function(result) {
				if (result) {
					$("#dbTestResult").html("✓ 数据库连接测试成功，返回数据：" + JSON.stringify(result));
				} else {
					$("#dbTestResult").html("✗ 数据库连接测试失败");
				}
			});
		}
		
		test.testInsert = function() {
			var testData = {
				"MemberPriority.MEMBER_TAG": "VIP",
				"MemberPriority.HOTLINE_CODE": "400-123-4567",
				"MemberPriority.MEMBER_LEVEL": "1",
				"MemberPriority.REMARK": "测试数据"
			};
			
			ajax.remoteCall("${ctxPath}/servlet/MemberPriority?action=saveMemberPriority", testData, function(result) {
				if (result.state == 1) {
					$("#dbTestResult").html("✓ 数据插入测试成功：" + result.msg);
				} else {
					$("#dbTestResult").html("✗ 数据插入测试失败：" + result.msg);
				}
			});
		}
		
		test.testQuery = function() {
			ajax.remoteCall("${ctxPath}/dao/memberPriority?action=getMemberPriorityList", {}, function(result) {
				if (result && result.list) {
					$("#dbTestResult").html("✓ 数据查询测试成功，共查询到 " + result.list.length + " 条记录");
				} else {
					$("#dbTestResult").html("✗ 数据查询测试失败");
				}
			});
		}

		test.testConflictCheck = function() {
			$("#testResult").html("正在测试冲突检查逻辑...");

			// 测试1：会员身份标签冲突检查
			test.testMemberTagConflict();
		}

		test.testMemberTagConflict = function() {
			// 测试会员身份标签冲突检查
			var testData = {
				memberTag: "VIP",
				memberTagName: "VIP会员",
				hotlineCode: "400-123-4567",
				hotlineCodeNames: "客服热线",
				memberLevel: "1"
			};

			ajax.remoteCall("${ctxPath}/servlet/MemberPriority?action=checkHotlineConflict", testData, function(result) {
				var message = "";
				if (result && result.data) {
					if (result.data.conflict) {
						message = "✓ 会员标签冲突检查测试成功 - 发现冲突：" + result.data.message;
					} else {
						message = "✓ 会员标签冲突检查测试成功 - 无冲突";
					}
				} else {
					message = "✗ 会员标签冲突检查测试失败";
				}
				$("#testResult").html(message + "<br>");

				// 继续测试热线号码等级冲突
				test.testHotlineLevelConflict();
			});
		}

		test.testHotlineLevelConflict = function() {
			// 测试热线号码等级冲突检查
			var testData = {
				memberTag: "GOLD",
				memberTagName: "黄金会员",
				hotlineCode: "400-123-4567,400-765-4321",
				hotlineCodeNames: "客服热线,投诉热线",
				memberLevel: "1"
			};

			ajax.remoteCall("${ctxPath}/servlet/MemberPriority?action=checkHotlineConflict", testData, function(result) {
				var currentResult = $("#testResult").html();
				var message = "";
				if (result && result.data) {
					if (result.data.conflict) {
						message = "✓ 热线等级冲突检查测试成功 - 发现冲突：" + result.data.message;
					} else {
						message = "✓ 热线等级冲突检查测试成功 - 无冲突";
					}
				} else {
					message = "✗ 热线等级冲突检查测试失败";
				}
				$("#testResult").html(currentResult + message + "<br>");

				// 继续测试无冲突情况
				test.testNoConflict();
			});
		}

		test.testNoConflict = function() {
			// 测试无冲突情况
			var testData = {
				memberTag: "PLATINUM",
				memberTagName: "白金会员",
				hotlineCode: "400-999-8888",
				hotlineCodeNames: "专属热线",
				memberLevel: "5"
			};

			ajax.remoteCall("${ctxPath}/servlet/MemberPriority?action=checkHotlineConflict", testData, function(result) {
				var currentResult = $("#testResult").html();
				var message = "";
				if (result && result.data) {
					if (result.data.conflict) {
						message = "✗ 无冲突测试失败 - 意外发现冲突：" + result.data.message;
					} else {
						message = "✓ 无冲突测试成功 - 确认无冲突";
					}
				} else {
					message = "✗ 无冲突测试失败";
				}
				$("#testResult").html(currentResult + message + "<br><br><strong>冲突检查测试完成</strong>");
			});
		}

		test.testImportFormat = function() {
			// 测试导入数据格式
			$("#testResult").html("导入Excel格式说明：<br>" +
				"第1列：会员身份标签（必填）<br>" +
				"第2列：热线号码（必填，多个用逗号分隔）<br>" +
				"第3列：会员身份等级（必填，1-99的数字）<br>" +
				"第4列：备注（选填）<br><br>" +
				"示例数据：<br>" +
				"VIP | 400-123-4567,400-765-4321 | 1 | VIP客户优先处理<br>" +
				"GOLD | 400-888-9999 | 1 | 金牌客户专线服务");
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
