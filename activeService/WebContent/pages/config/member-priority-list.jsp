<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>会员身份优先级配置</title>
	<style>
		.table-responsive {
			overflow-x: auto;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form name="searchForm" class="form-inline" id="searchForm" method="post" onsubmit="return false" autocomplete="off">
		<div class="ibox">
			<div class="ibox-title clearfix">
				<div class="form-group">
					<h5><span class="glyphicon glyphicon-list"></span> 会员身份优先级配置</h5>
				</div>
				<hr style="margin:5px -15px">
				<input type="hidden" name="realPageIndex" id="hid_pageIndexV" />
				<div class="form-group">
					<div class="input-group width-36">
						<span class="input-group-addon">渠道筛选</span>
						<select class="form-control input-sm width-70" id="hotlineCode" name="hotlineCode" data-mars="Dict.getDict('CC_HOTLINE')" onchange="memberPriority.search()">
							<option value="">全部</option>
						</select>
					</div>
					<div class="input-group width-18">
						<span class="input-group-addon">会员身份标签</span>
						<select class="form-control input-sm" name="memberTag" data-mars="Dict.getDict('MEMBER_TAG')">
							<option value="">全部</option>
						</select>
					</div>
					<div class="input-group width-18">
						<span class="input-group-addon">会员身份等级</span>
						<input type="number" name="memberLevel" class="form-control input-sm" placeholder="请输入等级" />
					</div>
					<div class="input-group input-group-sm ml-10">
						<button type="button" class="btn btn-sm btn-default" onclick="memberPriority.search()">
							<span class="glyphicon glyphicon-search"></span> 查询
						</button>
					</div>
					<div class="pull-right">
						<button type="button" class="btn btn-sm btn-primary" onclick="memberPriority.add()">
							<span class="glyphicon glyphicon-plus"></span> 新增
						</button>
						<button type="button" class="btn btn-sm btn-success ml-5" onclick="memberPriority.importData()">
							<span class="glyphicon glyphicon-import"></span> 导入
						</button>
					</div>
				</div>
			</div>
			<div class="ibox-content">
				<div class="table-responsive">
					<table class="table table-auto table-bordered table-hover table-condensed" 
						   data-auto-fill="10" id="tableHead" data-mars="memberPriority.getMemberPriorityList">
						<thead>
							<tr>
								<%--<th class="text-c" style="width: 50px">
									<label class="checkbox checkbox-info">
										<input type="checkbox" name="checkAll" value="">
										<span></span>
									</label>
								</th>--%>
								<th style="width: 50px">序号</th>
								<th style="min-width: 150px">会员身份标签</th>
								<th style="min-width: 200px">热线号码</th>
								<th style="min-width: 100px">会员身份等级</th>
								<th style="min-width: 150px">创建时间</th>
								<th style="min-width: 100px">创建人</th>
								<th style="min-width: 200px">备注</th>
								<th style="width: 150px">操作</th>
							</tr>
						</thead>
						<tbody id="dataList">
						</tbody>
					</table>
					<script id="list-template" type="text/x-jsrender">
						{{for list}}
							<tr>
								<%--<td style="width:50px" class="text-c">
									<label class="checkbox checkbox-info">
										<input type="checkbox" data-id="{{:ID}}">
										<span></span>
									</label>
								</td>--%>
								<td>{{:#index+1}}</td>
								<td>{{dictFUN:MEMBER_TAG 'MEMBER_TAG'}}</td>
								<td>{{:HOTLINE_NAME}}</td>
								<td>{{:MEMBER_LEVEL}}</td>
								<td>{{:CREATE_TIME}}</td>
								<td>{{:CREATE_NAME}}</td>
								<td>{{:REMARK}}</td>
								<td>
									<a href="javascript:void(0)" onclick="memberPriority.edit('{{:ID}}')">修改</a>&nbsp;
									<a href="javascript:void(0)" onclick="memberPriority.del('{{:ID}}')">删除</a>&nbsp;
								</td>
							</tr>
						{{/for}}
					</script>
				</div>
				<div class="row paginate" id="page">
					<jsp:include page="/pages/common/pagination.jsp" />
				</div>
			</div>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript">
		jQuery.namespace("memberPriority");
		
		$(function(){
			memberPriority.init();
		});
		
		memberPriority.init = function() {
			$("#searchForm").render({
				success: function() {
					memberPriority.search();
				}
			});
		}
		
		memberPriority.search = function() {
			$("#searchForm").searchData();
		}
		
		memberPriority.add = function() {
			popup.layerShow({
				type: 2,
				title: '新增会员身份优先级配置',
				offset: '20px',
				area: ['600px', '500px']
			}, "${ctxPath}/servlet/MemberPriority?action=memberPriorityEdit", {
				ID: ""
			});
		}
		
		memberPriority.edit = function(id) {
			popup.layerShow({
				type: 2,
				title: '编辑会员身份优先级配置',
				offset: '20px',
				area: ['600px', '500px']
			}, "${ctxPath}/servlet/MemberPriority?action=memberPriorityEdit", {
				ID: id
			});
		}
		
		memberPriority.del = function(id) {
			console.log("删除操作 - 传入的ID:", id);
			console.log("删除操作 - 参数对象:", {ids: id});

			layer.confirm('确认要删除吗？', {icon: 3, title: '删除提示'}, function(index) {
				layer.close(index);
				ajax.remoteCall("${ctxPath}/servlet/MemberPriority?action=deleteMemberPriority", {ids: id}, function(result) {
					console.log("删除操作 - 服务器响应:", result);
					if(result.state == 1) {
						layer.msg(result.msg, {icon: 1, time: 1200}, function() {
							memberPriority.search();
						});
					} else {
						layer.alert(result.msg, {icon: 5});
					}
				});
			});
		}
		
		memberPriority.dels = function() {
			var ids = $("#dataList").find("input[type='checkbox']:checked");
			var id = "";
			if(ids.length < 1) {
				alert('请选择需要删除的选项！');
				return;
			} else {
				for(var i = 0; i < ids.length; i++) {
					id = id == "" ? $(ids[i]).attr("data-id") : id + ";" + $(ids[i]).attr("data-id");
				}
				memberPriority.del(id);
			}
		}
		
		memberPriority.importData = function() {
			popup.layerShow({
				type: 1,
				title: "会员身份优先级配置导入",
				offset: '20px',
				area: ['700px', '600px']
			}, "${ctxPath}/pages/config/member-priority-import.jsp", null);
		}
		
		// 全选/取消全选
		$("input[name='checkAll']").change(function() {
			var checked = $(this).prop("checked");
			$("#dataList input[type='checkbox']").prop("checked", checked);
		});
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
