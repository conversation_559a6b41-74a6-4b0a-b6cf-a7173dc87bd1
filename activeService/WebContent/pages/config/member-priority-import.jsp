<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>会员身份优先级配置导入</title>
	<style type="text/css">
		.import-tips {
			background-color: #f5f5f5;
			border: 1px solid #ddd;
			border-radius: 4px;
			padding: 10px;
			margin-bottom: 15px;
		}
		.import-tips h5 {
			color: #337ab7;
			margin-top: 0;
		}
		.import-tips ul {
			margin-bottom: 0;
			padding-left: 20px;
		}
		.import-tips li {
			margin-bottom: 5px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="editFormImport" name="editFormImport" method="post" class="form-inline" enctype="multipart/form-data">
		<div class="import-tips">
			<h5><span class="glyphicon glyphicon-info-sign"></span> 导入说明</h5>
			<ul>
				<li>请下载导入模板，按照模板格式填写数据</li>
				<li>会员身份标签：必填，请填写系统中已配置的标签名称，填写字典编号 系统数据字典：MEMBER_TAG</li>
				<li>热线号码：必填，多个号码用英文逗号分隔，填写字典编号 业务数据字典：CC_HOTLINE </li>
				<li>会员身份等级：必填，请填写数字（1-20）</li>
				<li>备注：选填，最多500个字符</li>
				<li>同一个热线号码、会员身份标签和会员身份等级的组合不能重复</li>
				<li>支持的文件格式：.xlsx</li>
			</ul>
		</div>
		
		<table class="table table-vzebra mt-10">
			<tbody>
				<tr>
					<td width="80px">Excel文件</td>
					<td>
						<input class="hidden" type="file" id="file" name="file" 
							   accept="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet">
						<button class="btn btn-xs btn-info" type="button" onclick="$('#file').click()">选择文件</button>
						<a class="btn btn-sm btn-link" href="javascript:memberPriorityImport.downloadTemplate()">下载导入模板</a>
					</td>
				</tr>
				<tr>
					<td>文件名</td>
					<td>
						<input id="fileName" class="form-control input-sm" style="width:300px" type="text" readonly="readonly" placeholder="请选择要导入的Excel文件">
					</td>
				</tr>
			</tbody>
		</table>
		
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="memberPriorityImport.upload()">开始导入</button>
			<button class="btn btn-sm btn-default ml-20" type="button" onclick="layer.closeAll();">关闭</button>
		</div>

		<!-- 导入结果显示区域 -->
		<div class="mt-20">
			<table class="table table-bordered" id="content">
				<thead>
					<tr>
						<th colspan="2" class="text-c">导入结果</th>
					</tr>
				</thead>
				<tbody>
					<!-- 导入结果将显示在这里 -->
				</tbody>
			</table>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("memberPriorityImport");
		
		$(function() {
			$('input[id=file]').change(function() {
				var fileName = $(this).val();
				if (fileName) {
					var splitStr = fileName.split('\\');
					var splitStrs = splitStr[splitStr.length - 1];
					$('#fileName').val(splitStrs);
				} else {
					$('#fileName').val('');
				}
			});
		});
		
		memberPriorityImport.downloadTemplate = function() {
			location.href = "${ctxPath}/pages/template/member-priority-template.xlsx";
		}
		
		memberPriorityImport.upload = function() {
			var fileName = $("#fileName").val();
			if (fileName == "") {
				layer.alert("请选择要导入的文件", {icon: 5});
				return;
			}

			// 验证文件格式
			var fileExtension = fileName.split('.').pop().toLowerCase();
			if (fileExtension !== 'xlsx' && fileExtension !== 'xls') {
				layer.alert("请选择Excel格式的文件(.xlsx或.xls)", {icon: 5});
				return;
			}

			// 参考OrgAgentServlet的导入方式
			if (form.validate("editFormImport")) {
				var formData = new FormData($("#editFormImport")[0]);
				$.ajax({
					url: '${ctxPath}/servlet/MemberPriority?action=importMemberPriority',
					type: 'POST',
					data: formData,
					async: false,
					cache: false,
					contentType: false,
					processData: false,
					success: function (result) {
						if (result.state == 1) {
							// 显示导入结果
							$('#content').append('<tr><td colspan="2" style="text-align:left;">' + result.data + '</td></tr>');
							$('#file,#fileName').val('');
							// 刷新父页面数据
							if (typeof memberPriority !== 'undefined' && memberPriority.search) {
								memberPriority.search();
							}
							layer.msg("导入完成", {icon: 1});
						} else {
							layer.alert("导入失败：" + result.msg, {icon: 5});
						}
					},
					error: function(xhr, status, error) {
						layer.alert("导入失败：网络错误", {icon: 5});
					}
				});
			}
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>
