<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>IVR放音配置</title>
	<style>
		.container-fluid{
			padding: 0px 15px;
		}
		.info-title p{
			width: 100px;
		}
		hr{
/* 			margin:0px 0px; */
		}
		.table-title>tbody> tr > td:nth-child(2){
			min-width: 50px !important;
			text-align: center !important;
		}
		.logic-btn{
			width: 70px;
		}
	 	a:hover {
		    color: #20a0ff;
		} 
		.fileName{
		    background-color: #eee;
		    padding: 8px;
/* 		    margin-left: 10px; */
		    border-radius: 5px;
		    display: inline-flex !important;
   			width: 260px;
   			overflow: hidden;
		 	text-overflow: ellipsis;
		 	white-space: nowrap;
		}
		.filebutton{
		    display: inline-block;
		}
		.panel-body {
		    padding: 0px 15px;
		}
		hr {
		    margin: 2px 0px;
		
		}
		.table {
		    margin-bottom: 1px;
		}
		.priorityLevelRemind{
			color: red;
			float:right;
		}
		.uploading{
			color: red !important;
			padding-left: 3px;
		}
		.uploaded{
			color: green !important;
			padding-left: 3px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
 	   <form id="ivrForm" data-mars="ivrDao.ivrDetail" method="post"  autocomplete="off" data-mars-prefix="" > 
		   <input id="" class="hidden" name="ivrConfig.ID" value="${param.id}" >
		   <input id="" class="hidden" name="ivrConfig.TYPE" value="1" >
	       <div class="panel panel-default">
			    <div class="panel-body">
			       <table class="table table-edit table-vzebra mt-10" >
	                   <tbody>
	                  <tr>
	                  	  <td colspan="4" style="text-align: left" id="ivr-base">
	   						<font color="#218868"><i class="glyphicon glyphicon-cog"></i> 基本属性</font>
	   						<hr>
     						</td>
	                  </tr>
	                   	<tr>
							<td style="width: 100px" class="required">是否新会员策略</td>
							<td >
								<label class="radio-inline">
									<input type="radio" value="1" name="ivrConfig.IS_NEW_VIP" checked="checked" onchange="ivrConfig.handleNewVipChange(this)"> 是
								</label>
								<label class="radio-inline">
									<input type="radio" value="0" name="ivrConfig.IS_NEW_VIP" onchange="ivrConfig.handleNewVipChange(this)"> 否
								</label>
							</td>

	                         <td id="priorityLevelTd" class="">优先级</td>
	                      	 <td style="width: 235px">
	                      	 	<input type="number" id="priorityLevelInput" style="width: 100px;display: inline;"  onchange="ivrConfig.priorityLevelCheck(this)" name="ivrConfig.PRIORITY_LEVEL" class="form-control input-sm" >
	                      	 	<span class="glyphicon glyphicon-exclamation-sign hidden" data-html="true" id="priorityLevelCheck" style="display: inline;color: red" aria-hidden="true" data-toggle="tooltip" data-placement="top" title=""></span>
	                      	 </td>
	                   	</tr>
	                   	<tr>
	                         <td class="required">策略名称</td>
	                      	 <td><input type="text" id="pulish_num"  maxlength="50" data-rules="required" name="ivrConfig.NAME" class="form-control input-sm" ></td>

							<td style="width: 100px" class="required">是否启用</td>
							<td >
								<label class="radio-inline">
									<input type="radio" value="1" name="ivrConfig.IS_OPEN" checked="checked" > 是
								</label>
								<input type="radio" value="0" name="ivrConfig.IS_OPEN"> 否
								</label>
							</td>
	                   	</tr>
	                   	<tr>
	                         <td class="required">生效时间</td>
	                      	 <td colspan="3">
	                      	 <div class="input-group" style="width:430px">
									  <input type="text" style="width: 200px" data-rules="required" name="ivrConfig.START_TIME" id="pubCreateDateStar" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d',maxDate:'#F{$dp.$D(\'pubCreateDateEnd\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" style="width: 200px" data-rules="required" name="ivrConfig.END_TIME" id="pubCreateDateEnd"  class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d',minDate:'#F{$dp.$D(\'pubCreateDateStar\')}'})">									  
							   </div>
							 </td>
	                   	</tr>
	                    <tr>
	                      <td>热线名称</td>
	                      <td colspan="3">
	                           <div id="hotlineSelect" class="xm-select" style="width:430px"></div>
	                      </td>
	                    </tr>
	                    <tr>
	                    	<td colspan="4">
	                    	 <table class="table table-edit table-title " id="keyInfoTable">
				                   <tbody id="result-el">
					                   	<tr>
						                    <td colspan="3" style="text-align: left" id="ivr-key">
						   						<font color="#218868"><i class="glyphicon glyphicon-tags"></i>关键信息</font>
						   						
						   						<hr>
					     					</td>
				     					</tr>
					                   	<tr>
					                  		 <td style="width: 180px">类型</td>
					                  		 <td >内容</td>
					                  		 <td   style="width: 50px">
					                  		 	<span class="logic-btn pull-right " onclick="ivrConfig.addItem(this)"  id="show-btn">
					                  		 		<a href="JavaScript:false">
					                  		 			<i class="fa fa-plus-circle" style="font-size: 18px;"></i>&nbsp;添加
					                  		 		</a>
												</span>
					                  		 </td>
					                   	</tr>
											
				                   </tbody>
				                   
				             </table>
				             <script id="result-option-tpl" type="text/html">
								<tr>
									<td>
										<select class="form-control input-sm mr-5" name="SYMBOL1" data-rules="required" onchange="changeIvrType(this)">
											<option value="0">请选择</option>
											<option value="1">用户区域</option>
											<option value="2">人群包</option>
											<option value="3">用户身份</option>
										</select>
									</td>
									<td class="">
										
									</td>
									<td class="">
										<div class="pull-left" style="padding-left: 20px;">
										<a href="JavaScript:false"><span class="glyphicon glyphicon-minus" onclick="ivrConfig.removeRef(this)"></span></a>
										</div>
									</td>
								  </tr>
								</script>
	                    	</td>
	                    </tr>
	                    <tr>
	                    	<td colspan="4" style="text-align: left" id="ivr-detail">
		   						<font color="#218868"><i class="glyphicon glyphicon-comment"></i> 执行策略</font>
		   						<hr>
     						</td>
	                    </tr>
	                   	<tr>
	                  		 <td>欢迎语</td>
	                      	 <td colspan="2">
	                      	 	<div class="input-group">
	                      	 	  <input class="hidden" type="text" id="welcomeFileId" name="ivrConfigDetail.WELCOME_WORDS_URL" >
                      	 	 	  <button class="btn btn-sm btn-default ml-20 filebutton"  type="button" id="backbut" onclick="fileUpLoad('welcomeFile')">上传文件</button>
								  <span class="fileName" id="welcomeFileName">未选择文件</span>
								  <a id="welcomeFileTips"></a>
							    </div>
	                      	 </td>
	                      	
 							 <td>
	                      	 	<div class="pull-left">
	 							 	<a href="JavaScript:false" class="Play" id="welcomeFilePlay" data-id="welcomeFile" data-voiceType="1"><i class="fa fa-play-circle" style="font-size: 18px;"></i>&nbsp;播放</a>
	 							 	<a class="Pause hidden" class="Pause" style="color: #d9534f;" href="JavaScript:false" id="welcomeFilePause" data-id="welcomeFile"><i class="fa fa-pause-circle" style="font-size: 18px;"></i>&nbsp;暂停</a>
									<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
					                       <input type="checkbox" class="remind-input"  name="ivrConfigDetail.IS_WELCOME_WORDS" value="1">
					                       <span class="text-black">启用</span>
			                       
			                         </label>
		                         </div>
	                         </td>
						</tr>

						<tr>
	                  		 <td>用户报完诉求后的播报语</td><!--AFTER_APPEAL  -->
							 <td colspan="2">
	                      	 	<div class="input-group">
	                      	 	  <input class="hidden" type="text" id="afterAppealFileId" name="ivrConfigDetail.AFTER_APPEAL_TIP_WORDS_URL" >
                      	 	 	  <button class="btn btn-sm btn-default ml-20 filebutton"  type="button" id="backbut" onclick="fileUpLoad('afterAppealFile')">上传文件</button>
								  <span class="fileName" id="afterAppealFileName">未选择文件</span>
								  <a id="afterAppealFileTips"></a>
							    </div>
	                      	 </td>
	                      	 <td>
	                      	 	<div class="pull-left">
		                      	 	<a href="JavaScript:false"  class="Play" id="afterAppealFilePlay" data-id="afterAppealFile" data-voiceType="5"><i class="fa fa-play-circle" style="font-size: 18px;"></i>&nbsp;播放</a>
		                      	  	<a class="Pause hidden"  style="color: #d9534f;" href="JavaScript:false" id="afterAppealFilePause" data-id="afterAppealFile"><i class="fa fa-pause-circle" style="font-size: 18px;"></i>&nbsp;暂停</a>
									<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
					                       <input type="checkbox" class="remind-input" name="ivrConfigDetail.IS_AFTER_APPEAL_TIP_WORDS" value="1">
					                       <span class="text-black">启用</span>
			                        </label>
		                        </div>
	                         </td>
						</tr>
						
	                  	<tr>
	                  		 <td>首次排队提示语</td><!--WAIT  -->
							 <td colspan="2">
	                      	 	<div class="input-group">
	                      	 	  <input class="hidden" type="text" id="waitFileId" name="ivrConfigDetail.WAIT_TIP_WORDS_URL" >
                      	 	 	  <button class="btn btn-sm btn-default ml-20 filebutton"  type="button" id="backbut" onclick="fileUpLoad('waitFile')">上传文件</button>
								  <span class="fileName" id="waitFileName">未选择文件</span>
								  <a id="waitFileTips"></a>
							    </div>
	                      	 </td>
	                      	 <td>
	                      	 	<div class="pull-left">
		                      	 	<a href="JavaScript:false"  class="Play" id="waitFilePlay" data-id="waitFile" data-voiceType="2"><i class="fa fa-play-circle" style="font-size: 18px;"></i>&nbsp;播放</a>
		                      	  	<a class="Pause hidden"  style="color: #d9534f;" href="JavaScript:false" id="waitFilePause" data-id="waitFile"><i class="fa fa-pause-circle" style="font-size: 18px;"></i>&nbsp;暂停</a>
									<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
					                       <input type="checkbox" class="remind-input" name="ivrConfigDetail.IS_WAIT_TIP_WORDS" value="1">
					                       <span class="text-black">启用</span>
			                        </label>
		                        </div>
	                         </td>
						</tr>
						<tr>
	                  		 <td>2-5次排队提示语</td><!--SECOND_WAIT  -->
							 <td colspan="2">
	                      	 	<div class="input-group">
	                      	 	  <input class="hidden" type="text" id="secondWaitFileId" name="ivrConfigDetail.SECOND_WAIT_TIP_WORDS_URL" >
                      	 	 	  <button class="btn btn-sm btn-default ml-20 filebutton"  type="button" id="backbut" onclick="fileUpLoad('secondWaitFile')">上传文件</button>
								  <span class="fileName" id="secondWaitFileName">未选择文件</span>
								  <a id="secondWaitFileTips"></a>
							    </div>
	                      	 </td>
	                      	 <td>
	                      	 	<div class="pull-left">
		                      	 	<a href="JavaScript:false"  class="Play" id="secondWaitFilePlay" data-id="secondWaitFile" data-voiceType="3"><i class="fa fa-play-circle" style="font-size: 18px;"></i>&nbsp;播放</a>
		                      	  	<a class="Pause hidden"  style="color: #d9534f;" href="JavaScript:false" id="secondWaitFilePause" data-id="secondWaitFile"><i class="fa fa-pause-circle" style="font-size: 18px;"></i>&nbsp;暂停</a>
									<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
					                       <input type="checkbox" class="remind-input" name="ivrConfigDetail.IS_SECOND_WAIT_TIP_WORDS" value="1">
					                       <span class="text-black">启用</span>
			                        </label>
		                        </div>
	                         </td>
						</tr>
						<tr>
	                  		 <td>>5次排队提示语</td><!--FIVE_WAIT  -->
							 <td colspan="2">
	                      	 	<div class="input-group">
	                      	 	  <input class="hidden" type="text" id="fiveWaitFileId" name="ivrConfigDetail.FIVE_WAIT_TIP_WORDS_URL" >
                      	 	 	  <button class="btn btn-sm btn-default ml-20 filebutton"  type="button" id="backbut" onclick="fileUpLoad('fiveWaitFile')">上传文件</button>
								  <span class="fileName" id="fiveWaitFileName">未选择文件</span>
								  <a id="fiveWaitFileTips"></a>
							    </div>
	                      	 </td>
	                      	 <td>
	                      	 	<div class="pull-left">
		                      	 	<a href="JavaScript:false"  class="Play" id="fiveWaitFilePlay" data-id="fiveWaitFile" data-voiceType="4"><i class="fa fa-play-circle" style="font-size: 18px;"></i>&nbsp;播放</a>
		                      	  	<a class="Pause hidden"  style="color: #d9534f;" href="JavaScript:false" id="fiveWaitFilePause" data-id="fiveWaitFile"><i class="fa fa-pause-circle" style="font-size: 18px;"></i>&nbsp;暂停</a>
									<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
					                       <input type="checkbox" class="remind-input" name="ivrConfigDetail.IS_FIVE_WAIT_TIP_WORDS" value="1">
					                       <span class="text-black">启用</span>
			                        </label>
		                        </div>
	                         </td>
						</tr>
						


						<tr>
							<td>提示语频次</td>
							<td colspan="3">
	                           <select name="ivrConfigDetail.FREQUENCY" data-mars="Dict.getDictList('CC_AS_IVR_FREQUENCY')" class="form-control input-sm query-count">
	                           </select>
	                      </td>
						</tr>
						<tr>
	                  		 <td>进入智能IVR</td>
	                         <td >
						 		 <label class="radio-inline">
					                <input type="radio" value="1" name="ivrConfigDetail.IS_IVR_CALL" checked="checked" > 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" name="ivrConfigDetail.IS_IVR_CALL"> 否
					             </label>
	                         </td>
	                         <td></td>
	                      	 <td></td>
	                   	</tr>
	                 
	                   </tbody>
	             </table>
	             <script id="citySelectHtml" type="text/html">
						<div id="citySelect" class="xm-select" style="width:500px"></div>
				 </script>
                 <script id="crowdPackListSelectHtml" type="text/html">
						<div id="crowdPackListSelect" class="xm-select " style="width:500px"></div>
				</script>
                <script id="vipSelectHtml" type="text/html">
						<div id="vipSelect" class="xm-select " style="width:500px"></div>
				</script>
			    </div>
		   </div>
		</form>		
        <div class=" text-c">
	   		<button class="btn btn-sm btn-primary"  type="button" onclick="ivrConfig.ajaxSubmitForm()">确认</button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="closeWin()">关闭</button>
   	 	</div>
   	 	<div>
   	 		<audio id="audioFile" src=""></audio>
   	 	</div>
   	 	<form id="welcomeFileForm">
   	 		 <input class="hidden" type="file" id="welcomeFile" name="welcomeFile" accept="">
   	 		 <input type="hidden"  name="voiceType" value="1"><!-- 欢迎语 -->
   	 	</form>
   	 	<form id="waitFileForm">
	         <input class="hidden" type="file" id="waitFile" name="waitFile" accept="">
	         <input type="hidden"  name="voiceType" value="2"><!-- 排队提示语 -->
   	 	</form>
   	 	<form id="secondWaitFileForm">
	         <input class="hidden" type="file" id="secondWaitFile" name="secondWaitFile" accept="">
	         <input type="hidden"  name="voiceType" value="3"><!-- 2-5次排队提示语 -->
   	 	</form>
   	 	<form id="fiveWaitFileForm">
	         <input class="hidden" type="file" id="fiveWaitFile" name="fiveWaitFile" accept="">
	         <input type="hidden"  name="voiceType" value="4"><!-- >5次排队提示语 -->
   	 	</form>
   	 	<form id="afterAppealFileForm">
	         <input class="hidden" type="file" id="afterAppealFile" name="afterAppealFile" accept="">
	         <input type="hidden"  name="voiceType" value="5"><!-- 用户报完诉求后的播报语 -->
   	 	</form>
   	 	
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/activeService/static/js/xm-select.js"></script>
	<script type="text/javascript">
	    jQuery.namespace("ivrConfig")
	    	var cityListData=[];//区域信息
			var crowdPackListData=[];//人群包
			var vipListData=[];//用户信息
			var uploading=false;//是否上传附件中

		// 定义优先级字段控制函数
	    ivrConfig.handleNewVipChange = function(element) {
	    	ivrConfig.setPriorityLevelRequired(element.value === "0");
	    }

	    ivrConfig.setPriorityLevelRequired = function(required) {
	    	var $priorityInput = $("#priorityLevelInput");
	    	var $priorityTd = $("#priorityLevelTd");

	    	if (required) {
	    		$priorityInput.attr("data-rules", "required");
	    		$priorityTd.addClass("required");
	    	} else {
	    		$priorityInput.removeAttr("data-rules");
	    		$priorityTd.removeClass("required");
	    	}
	    }

	    ivrConfig.initPriorityLevelStatus = function() {
	    	var isNewVip = $("input[name='ivrConfig.IS_NEW_VIP']:checked").val();
	    	ivrConfig.setPriorityLevelRequired(isNewVip === "0");
	    }
	    $(function(){
	    	layui.use('form', function(){
    		  var form = layui.form;
    		  form.render();
    		});

	    	// 初始化优先级字段状态
	    	ivrConfig.initPriorityLevelStatus();

	    	$("#ivrForm").render({success:function(data){
	    			if(data['ivrDao.ivrDetail']&&data['ivrDao.ivrDetail'].data){
	    				var WELCOME_WORDS_URL=data['ivrDao.ivrDetail'].data['ivrConfigDetail.WELCOME_WORDS_URL']
	    				if(WELCOME_WORDS_URL){
	    					$("#welcomeFileId").val(WELCOME_WORDS_URL);
		    				$("#welcomeFileName").html(WELCOME_WORDS_URL);
	    				}
	    				
	    				var WAIT_TIP_WORDS_URL=	data['ivrDao.ivrDetail'].data['ivrConfigDetail.WAIT_TIP_WORDS_URL']
	    				if(WAIT_TIP_WORDS_URL){
	    					$("#waitFileId").val(WAIT_TIP_WORDS_URL);
		    				$("#waitFileName").html(WAIT_TIP_WORDS_URL);
	    				}

	    				var SECOND_WAIT_TIP_WORDS_URL = data['ivrDao.ivrDetail'].data['ivrConfigDetail.SECOND_WAIT_TIP_WORDS_URL']
	    				if(SECOND_WAIT_TIP_WORDS_URL){
	    					$("#secondWaitFileId").val(SECOND_WAIT_TIP_WORDS_URL);
		    				$("#secondWaitFileName").html(SECOND_WAIT_TIP_WORDS_URL);
	    				}

	    				var FIVE_WAIT_TIP_WORDS_URL = data['ivrDao.ivrDetail'].data['ivrConfigDetail.FIVE_WAIT_TIP_WORDS_URL']
	    				if(FIVE_WAIT_TIP_WORDS_URL){
	    					$("#fiveWaitFileId").val(FIVE_WAIT_TIP_WORDS_URL);
		    				$("#fiveWaitFileName").html(FIVE_WAIT_TIP_WORDS_URL);
	    				}

	    				var AFTER_APPEAL_TIP_WORDS_URL = data['ivrDao.ivrDetail'].data['ivrConfigDetail.AFTER_APPEAL_TIP_WORDS_URL']
	    				if(AFTER_APPEAL_TIP_WORDS_URL){
	    					$("#afterAppealFileId").val(AFTER_APPEAL_TIP_WORDS_URL);
		    				$("#afterAppealFileName").html(AFTER_APPEAL_TIP_WORDS_URL);
	    				}
	    				
	    			    var callListData=[];
	    				$(data['ivrDao.ivrDetail'].data['callList'].data).each(function(e){
	    					callListData.push(this.HOTLINE);
	    				})
	    				
	    				setSelectData(callListData, "hotline",'setValue');	
	    				//关键信息  city crowdPackList vip
	    				
	    				$(data['ivrDao.ivrDetail'].data['keyList'].data).each(function(e){
	    					if(this.STRATEGY_TYPE==1){
	    						cityListData.push(this.PHONE_BELONG_AREA)
	    					}else if(this.STRATEGY_TYPE==2){
	    						crowdPackListData.push({value:this.CROWD_PACK_ID,name:this.CROW_PACK_NAME})
	    					}else if(this.STRATEGY_TYPE==3){
	    						vipListData.push(this.VIP)
	    					}
	    				})
	    				if(cityListData.length>0){
	    					//最后一个tr后面增加一个tr内容
	    					$("#keyInfoTable").find("tr").last().closest('tr').after($($('#result-option-tpl').html())); 
	    					//找到最后一个tr里面的下拉框修改值
	    					$($("#keyInfoTable").find("tr").last().find('select')[0]).val('1').change();
	    					cityData=cityListData;
	    				}
	    				if(crowdPackListData.length>0){
	    					$("#keyInfoTable").find("tr").last().closest('tr').after($($('#result-option-tpl').html())); 
	    					$($("#keyInfoTable").find("tr").last().find('select')[0]).val('2').change();
	    					crowdPackData=crowdPackListData;
	    				}
	    				if(vipListData.length>0){
	    					$("#keyInfoTable").find("tr").last().closest('tr').after($($('#result-option-tpl').html())); 
	    					$($("#keyInfoTable").find("tr").last().find('select')[0]).val('3').change();
	    					setVipValue=vipListData;
	    				}
	    			}
	    			//都没有的情况下默认点击新增按钮
	    			if(vipListData.length==0&&cityListData.length==0&&crowdPackListData.length==0){
		    			$("#show-btn").click();

					}

	    			// 数据回填后重新初始化优先级字段状态
	    			ivrConfig.initPriorityLevelStatus();
	    		}
	    	})
	    })
		//加载问题
		function setSelectData(callListData, funObjStr, fun){
    		var funObj = eval(funObjStr);
	    	if(intoHotline && funObj){
	    		funObj[fun](callListData);
			}else{
				setTimeout(function() {
					setSelectData(callListData,funObjStr, fun);
				}, 100)
			}
		}
	   	function changeIvrType(ths){
	    	var keyType=$(ths).val();
	    	var keys=[];
	    	$("[name='SYMBOL1']").each(function(e){
	    		if(keys.indexOf(this.value)<0){
		    		keys.push(this.value)
	    		}else{
	    			layer. msg("已存在该类型",{icon: 5});
	    			$(ths).val("0");

	    		}
	    	})
	    	if(keyType==1){
	    		$(ths).parent("td").next("td").html()
	    		var resultOptionTpl = $($('#citySelectHtml').html());
	    		$(ths).parent("td").next("td").html(resultOptionTpl); 
				initCity();
	    	}else if(keyType==2){
	    		$(ths).parent("td").next("td").html()
	    		var resultOptionTpl = $($('#crowdPackListSelectHtml').html());
	    		$(ths).parent("td").next("td").html(resultOptionTpl); 
	    		initCrowdPack()
	    	}else if(keyType==3){
	    		$(ths).parent("td").next("td").html()
	    		var resultOptionTpl = $($('#vipSelectHtml').html());
	    		$(ths).parent("td").next("td").html(resultOptionTpl); 
	    		initVip();
	    	}
	    }
    	ivrConfig.addItem= function(el){
    		if( $("[name='SYMBOL1']").length>=3){
    			layer. msg("已超过添加上限",{icon: 5});
    			return;
    		}
			var resultOptionTpl = $($('#result-option-tpl').html());
    		$("#result-el").append(resultOptionTpl); 
    		
		}
	    ivrConfig.removeRef = function(obj){
			if($(obj).closest('tr').find('select').length>=1){
				$(obj).closest('tr').remove();
			}
		}
	    
	   
	    var hotline;
	    var intoHotline=false;
	    hotline = xmSelect.render({
	    	el: '#hotlineSelect', 
	    	name:"hotline",
	    	//配置搜索
	    	filterable: true,
	    	paging: false,
	    	toolbar:{ show: true, },
	    	pageRemote: true,
	    	searchTips: '输入热线名称',
	    	remoteMethod: function(val, cb, show){
	    		ajax.remoteCall("/activeService/webcall?action=Dict.ccHotline",{},function(result) {
	    			if(result.data){
	    				var dataArr = [];
	    				$.each(result.data, function(i) {
	    					var item = {};
	    					item.name= result.data[i]
	    					item.value= i
	    					dataArr.push(item)
		    			});
	    				hotline.update({
	    					data:dataArr
	    				});
	   					cb(dataArr);
	   					intoHotline=true;
	   					
	   				}else{
	   					hotline.update({
	    					data:[]
	    				});
	   					cb([]);
	   				}
	   			});
	    	}
	    })
	 
	    //来电区域
	    var city;
	    var cityData=[]
	    function initCity(){
	    	ajax.remoteCall("/activeService/webcall?action=common.getCity",{},function(result) {
    			if(result.data){
    				city=xmSelect.render({
    			    	el: '#citySelect', 
    			    	name:"city",
    			    	//配置搜索
    					autoRow: true,
    					filterable: true,
    					repeat: true,
    					autoRow:false,
    					tree: {
    						show: true,
    						showFolderIcon: true,
    						showLine: true,
    						indent: 20,
    						expandedKeys: [ -3 ],
    					},
    					toolbar: {
    						show: true,
    						list: ['ALL', 'REVERSE', 'CLEAR']
    					},
    					filterable: true,
    					height: '300px',
    					data: function(){
    						return result.data
    					} ,
    			    }) 
   				}else{
   					
   				}
    			if(cityData){
    				city.setValue(cityData);
    				cityData=[] 
				}
   			});
	    	
	    }
	     //用户属性 
	     var vip;
	     var setVipValue=[];
	     function initVip(){
			    vip = xmSelect.render({
			    	el: '#vipSelect', 
			    	name:"vip",
			    	//配置搜索
			    	filterable: true,
			    	paging: false,
			    	toolbar:{ show: true, },
			    	pageRemote: true,
			    	searchTips: '输入用户属性名称',
			    	
			    
			    	//数据处理
			    	remoteMethod: function(val, cb, show, pageIndex){
			    		//val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
			    		if(pageIndex==1){
			    			pageIndex=-1;
			    		}
			    		ajax.remoteCall("/activeService/webcall?action=Dict.ccAsVip",{},function(result) {
			    			if(result.data){
			    				var dataArr = [];
			    				$.each(result.data, function(i) {
			    					var item = {};
			    					item.name= result.data[i];
			    					item.value= i;
			    					dataArr.push(item)
			    					
				    			});
			    				vip.update({
			    					data:dataArr
			    				});
			   					cb(dataArr)
			   					if(setVipValue){
				    				vip.setValue(setVipValue);
				    				setVipValue=[] 
			   					}
			   				}else{
			   					hotline.update({
			    					data:[]
			    				});
			   					cb([]);
			   				}
			   			});
			    	}
			    }) 
	     }

	   // 人群包类型
	    var totalPage=0;
	    var crowdPackList;
	    var crowdPackData=[];
	    function initCrowdPack(){
		    crowdPackList = xmSelect.render({
		    	el: '#crowdPackListSelect', 
		    	name:"crowdPackList",
		    	//配置搜索
		    	filterable: true,
		    	paging: false,
		    	toolbar:{ show: true, },
		    	pageRemote: true,
		    	searchTips: '输入人群包名称',
		    	on: function(data){
		    		//arr:  当前多选已选中的数据
		    		var arr = data.arr;
		    		//change, 此次选择变化的数据,数组
		    		var change = data.change;
		    		//isAdd, 此次操作是新增还是删除
		    		var isAdd = data.isAdd;
		    		var msg = "";
		    		for(var index in arr){
		    			var select = arr[index];
		    		}
		    	},
		    	
		    	remoteMethod:function(val, cb, show, pageIndex){
		    		//val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
		    		if(pageIndex==1){
		    			pageIndex=-1;
		    		}
		    		ajax.remoteCall("${ctxPath}/webcall?action=crowdPackDao.bgCrowdPackDictList",{name:val,pageIndex:pageIndex,pageSize:10},function(result) {
		    			if(result.data&&result.data.length>0){
		    				var dataArr = [];
		    				for(var i in result.data){
		    					var item = {};
		    					item.name= result.data[i].NAME;
		    					item.value= result.data[i].ID;
		    					dataArr.push(item)
		    				}
		    				if(result.totalPage>0){
		    					totalPage = result.totalPage;
		    				}
		    				
		   					cb(dataArr, totalPage)
		   					if(crowdPackData){
		   						crowdPackList.setValue(crowdPackData);
		   						crowdPackData=[] 
		   					}
		   				}else{
		   					cb([], 0);
		   				}
		   			});
		    	}
		    })
	    }
	    
	    function fileUpLoad(id){
	    	if(uploading){
				layer.msg("上传文件中！",{icon: 5});
				return
	    	}
	    	$('#'+id).click()
	    	
	    }
	    //播放按钮
	    $("#welcomeFilePlay,#waitFilePlay,#secondWaitFilePlay,#fiveWaitFilePlay,#afterAppealFilePlay").click(function(ths) {
	    	var id=$(this).attr("data-id");
	    	var voiceType=$(this).attr("data-voiceType");
	    	if($("#audioFile").attr("src")==""||$("#audioFile").attr("src").indexOf(id)<0){//没用录音地址或换了录音
	    		$("#audioFile").attr("src","${ctxPath}/servlet/ivrService?action=Download&filePath="+$("#"+id+"Id").val()+"&voiceType="+voiceType);
	    	}
	    	document.getElementById("audioFile").play();
    	   	$(".Pause").addClass("hidden");
    		$(".Play").removeClass("hidden");
	    	$("#"+id+"Play").addClass("hidden");
	    	$("#"+id+"Pause").removeClass("hidden");
	    });
	    $("#welcomeFilePause,#waitFilePause,#secondWaitFilePause,#fiveWaitFilePause,#afterAppealFilePause").click(function(ths) {
	    	document.getElementById("audioFile").pause();
	    	var id=$(this).attr("data-id");
	     	$(".Pause").addClass("hidden");
    		$(".Play").removeClass("hidden");
	    	$("#"+id+"Pause").addClass("hidden");
	    	$("#"+id+"Play").removeClass("hidden");

	    });
	    
	    $("#welcomeFile,#waitFile,#secondWaitFile,#fiveWaitFile,#afterAppealFile").change(function(ths) {
	    	var id=$(this).attr("id");
	    	$("#"+id+"Tips").html("上传中");
	    	$("#"+id+"Tips").removeClass("uploaded");
	    	$("#"+id+"Tips").addClass("uploading");
	    	uploading=true;
			if ($("#"+id).val()) {
				$("#"+id+"Form").attr("enctype", "multipart/form-data");
				var formData = new FormData($("#"+id+"Form")[0]);
				$.ajax({
					url: '${ctxPath}/servlet/ivrService?action=Upload&last=' + $("#"+id+"Form").serialize(),
					type: 'POST',
					data: formData,
					'async': true,
					cache: false,
					contentType: false,
					processData: false,
					success: function(result) {
						uploading=false;
						if(result.state==1&&result.data){
							$("#"+id+"Name").html(result.data);
							$("#"+id+"Id").val(result.data);
							$("#"+id+"Tips").html("上传完成");
							$("#"+id+"Tips").removeClass("uploading");
					    	$("#"+id+"Tips").addClass("uploaded");
						}else{
							layer.alert(result.msg,{icon: 5});
							$("#"+id+"Tips").html("上传失败");
							$("#"+id+"Tips").removeClass("uploaded");
					    	$("#"+id+"Tips").addClass("uploading");
						}
					}
				}); 
			} else {
				$("#"+id).html("未选择文件");
			}
		})
	   ivrConfig.getKeyInfoList = function(){
	    	var list=[];
	    	$("#keyInfoTable").find("select").each(function(e){
	    		var json={}
	    		if(this.value==1){
	    			json={"PHONE_BELONG_AREA":city.getValue("valueStr")}
	    			
	    		}else if(this.value==2){
	    			json={"CROWD_PACK_ID":crowdPackList.getValue("valueStr")};
	    			
	    		}else if(this.value==3){
	    			json={"VIP":vip.getValue("valueStr")};
	    			
	    		}
    			list.push(json);
	    	})
	    	return list
	    },


	    ivrConfig.priorityLevelCheck=function(ths){
			ajax.remoteCall("${ctxPath}/servlet/ivrService?action=IvrPriorityLevelCheck",{level:ths.value,type:1,id:'${param.id}'},function(result) {
				if(result.state == 1){
					if(result.data){
						var title="";
						for(var  key in result.data){
							title=title==""?result.data[key].NAME:title+"<br>"+result.data[key].NAME
						}
						//$('[data-toggle="tooltip"]').tooltip('destroy');
						$("#priorityLevelCheck").attr("data-original-title",title);
			    		$('[data-toggle="tooltip"]').tooltip();
						if(result.data.length!=0){
							$("#priorityLevelCheck").removeClass("hidden")
						}else{
							$("#priorityLevelCheck").addClass("hidden")

						}
					}else{

					}

				}else{
					layer.alert(result.msg,{icon: 5});
				}
			})
	    }
	    ivrConfig.ajaxSubmitForm = function(){
	    	// 在验证前确保优先级字段状态正确
	    	ivrConfig.initPriorityLevelStatus();

			if(!form.validate("#ivrForm")){
				return;
			}
			if(uploading){
				layer.msg("上传文件中！",{icon: 5});
				return
			}
			if(!hotline.getValue("valueStr")){
				layer.msg("请选择热线！",{icon: 5});
				return
			}
			var data = form.getJSONObject("ivrForm");
			if(data['ivrConfigDetail.IS_WELCOME_WORDS']!=null){
				data['ivrConfigDetail.IS_WELCOME_WORDS']="1";
			}else{
				data['ivrConfigDetail.IS_WELCOME_WORDS']="0";
			}
			if(data['ivrConfigDetail.IS_WAIT_TIP_WORDS']!=null){
				data['ivrConfigDetail.IS_WAIT_TIP_WORDS']="1";
			}else{
				data['ivrConfigDetail.IS_WAIT_TIP_WORDS']="0";
			}
			if(data['ivrConfigDetail.IS_SECOND_WAIT_TIP_WORDS']!=null){
				data['ivrConfigDetail.IS_SECOND_WAIT_TIP_WORDS']="1";
			}else{
				data['ivrConfigDetail.IS_SECOND_WAIT_TIP_WORDS']="0";
			}
			if(data['ivrConfigDetail.IS_FIVE_WAIT_TIP_WORDS']!=null){
				data['ivrConfigDetail.IS_FIVE_WAIT_TIP_WORDS']="1";
			}else{
				data['ivrConfigDetail.IS_FIVE_WAIT_TIP_WORDS']="0";
			}
			if(data['ivrConfigDetail.IS_AFTER_APPEAL_TIP_WORDS']!=null){
				data['ivrConfigDetail.IS_AFTER_APPEAL_TIP_WORDS']="1";
			}else{
				data['ivrConfigDetail.IS_AFTER_APPEAL_TIP_WORDS']="0";
			}
			data['key']=ivrConfig.getKeyInfoList;
			data['call']=hotline.getValue("valueStr");
			ajax.remoteCall("${ctxPath}/servlet/ivrService?action=saveIvrConfig",data,function(result) { 
					if(result.state == 1){
						parent.layer.msg(result.msg,{icon:1});
						parent.scriptConf.reload();	
						closeWin();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>