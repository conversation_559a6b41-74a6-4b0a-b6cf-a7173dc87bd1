<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>IVR放音配置</title>
	<style>
		.container-fluid{
			padding: 0px 15px;
		}
		.info-title p{
			width: 100px;
		}
		hr{
/* 			margin:0px 0px; */
		}
		.table-title>tbody> tr > td:nth-child(2){
			min-width: 50px !important;
			text-align: center !important;
		}
		.logic-btn{
			width: 70px;
		}
	 	a:hover {
		    color: #20a0ff;
		} 
		.fileName{
		    background-color: #eee;
		    padding: 8px;
/* 		    margin-left: 10px; */
		    border-radius: 5px;
		    display: inline-flex !important;
   			width: 260px;
   			overflow: hidden;
		 	text-overflow: ellipsis;
		 	white-space: nowrap;
		}
		.filebutton{
		    display: inline-block;
		}
		.panel-body {
		    padding: 0px 15px;
		}
		hr {
		    margin: 2px 0px;
		
		}
		.table {
		    margin-bottom: 1px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
 	   <form id="ivrSkillForm" data-mars="ivrDao.ivrDetail" method="post"  autocomplete="off" data-mars-prefix="" > 
		   <input id="id" class="hidden" name="ivrConfig.ID" value="${param.id }" >
		   <input id="" class="hidden" name="ivrConfig.TYPE" value="2" >
	       <div class="panel panel-default">
			    <div class="panel-body">
			       <table class="table table-edit table-vzebra mt-10" >
	                   <tbody>
	                  <tr>
	                  	  <td colspan="4" style="text-align: left" id="ivr-base">
	   						<font color="#218868"><i class="glyphicon glyphicon-cog"></i> 基本属性</font>
	   						<hr>
     						</td>
	                  </tr>
	                  <tr>
						  <td style="width: 100px" class="required">是否新会员策略</td>
						  <td >
							  <label class="radio-inline">
								  <input type="radio" value="1" name="ivrConfig.IS_NEW_VIP" checked="checked" onchange="ivrSkillConfig.handleNewVipChange(this)"> 是
							  </label>
							  <label class="radio-inline">
								  <input type="radio" value="0" name="ivrConfig.IS_NEW_VIP" onchange="ivrSkillConfig.handleNewVipChange(this)"> 否
							  </label>
						  </td>
	                         <td id="skillPriorityLevelTd" class="">优先级</td>
	                      	 <td style="width: 235px">
	                      	 	<input type="number" id="skillPriorityLevelInput" style="width: 100px;display: inline;" onchange="ivrSkillConfig.priorityLevelCheck(this)" name="ivrConfig.PRIORITY_LEVEL" class="form-control input-sm" >
	                      	 	<span class="glyphicon glyphicon-exclamation-sign hidden" id="skillPriorityLevelCheck" style="display: inline;color: red" aria-hidden="true" data-toggle="tooltip" data-placement="top" title=""></span>
	                      	 </td>
	                   	</tr>
	                   	<tr>
	                         <td class="required">策略名称</td>
	                      	 <td><input type="text" id="" maxlength="80" data-rules="required" name="ivrConfig.NAME" class="form-control input-sm" ></td>
							<td style="width: 100px" class="required">是否启用</td>
							<td >
								<label class="radio-inline">
									<input type="radio" value="1" name="ivrConfig.IS_OPEN" checked="checked" > 是
								</label>
								<label class="radio-inline">
									<input type="radio" value="0" name="ivrConfig.IS_OPEN"> 否
								</label>
							</td>
	                   	</tr>
	                   	<tr>
	                         <td class="required">生效时间</td>
	                      	 <td colspan="3">
	                      	 <div class="input-group" style="width:430px">
									  <input type="text" style="width: 200px" data-rules="required" name="ivrConfig.START_TIME" id="skillDateStar" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d 00:00:00',maxDate:'#F{$dp.$D(\'skillDateEnd\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" style="width: 200px" data-rules="required" name="ivrConfig.END_TIME" id="skillDateEnd"  class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd',startDate:'%y-%M-%d 23:59:59',minDate:'#F{$dp.$D(\'skillDateStar\')}'})">									  
							   </div>
							 </td>
	                   	</tr>
	                    <tr>
	                      <td>热线名称</td>
	                      <td colspan="3">
	                           <div id="hotlineSelect1" class="xm-select" style="width:430px"></div>
	                      </td>
	                    </tr>
	                    <tr>
	                      <td>用户诉求</td>
	                      <td colspan="3">
	                           <div id="callTypeSelect" class="xm-select" style="width:430px"></div>
	                      </td>
	                    </tr>
	                    <tr>
	                    	<td colspan="4">
	                    	 <table class="table table-edit table-title " id="keyInfoTable">
				                   <tbody id="result-el">
					                   	<tr>
						                    <td colspan="3" style="text-align: left" id="ivr-key">
						   						<font color="#218868"><i class="glyphicon glyphicon-tags"></i>关键信息</font>
						   						
						   						<hr>
					     					</td>
				     					</tr>
					                   	<tr>
					                  		 <td style="width: 180px">类型</td>
					                  		 <td >内容</td>
					                  		 <td   style="width: 50px">
					                  		 	<span class="logic-btn pull-right " onclick="ivrSkillConfig.addItem(this)"  id="show-btn">
					                  		 		<a href="JavaScript:false">
					                  		 			<i class="fa fa-plus-circle" style="font-size: 18px;"></i>&nbsp;添加
					                  		 		</a>
												</span>
					                  		 </td>
					                   	</tr>
											
				                   </tbody>
				                   
				             </table>
				             <script id="result-option-tpl" type="text/html">
								<tr>
									<td>
										<select class="form-control input-sm mr-5" name="SYMBOL1" data-rules="required" onchange="changeIvrType(this)">
											<option value="0">请选择</option>
											<option value="1">用户区域</option>
											<option value="2">人群包</option>
											<option value="3">用户身份</option>
										</select>
									</td>
									<td class="">
										
									</td>
									<td class="">
										<div class="pull-left" style="padding-left: 20px;">
										<a href="JavaScript:false"><span class="glyphicon glyphicon-minus" onclick="ivrSkillConfig.removeRef(this)"></span></a>
										</div>
									</td>
								  </tr>
								</script>
	                    	</td>
	                    </tr>
	                    <tr>
	                    	<td colspan="4" style="text-align: left" id="ivr-detail">
		   						<font color="#218868"><i class="glyphicon glyphicon-comment"></i> 执行策略</font>
		   						<hr>
     						</td>
	                    </tr>
	                  	<tr>
	                  		 <td>排队接入等级</td>
							 <td colspan="3">
							 	<div class="input-group input-group-sm pull-left  ">
									<input type="number" id="pulish_num" style=" width: 430px"  name="ivrConfigDetail.PRIORITY_ACCESS" class="form-control input-sm" >
								</div>
							
							<div class="input-group input-group-sm pull-left  ">
								<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
				                       <input type="checkbox" class="remind-input"  name="ivrConfigDetail.IS_PRIORITY_ACCESS" value="1">
				                       <span class="text-black">启用</span>
			                    </label>
							</div>
	                      	 	
	                         </td>
						</tr>
						<tr>
							<td>人工技能组</td>
							<td colspan="3">
							<div class="input-group input-group-sm pull-left  ">
 								<select name="ivrConfigDetail.SKILL_GROUP_CODE"  id="skillNo" style=" width: 430px" data-mars="Dict.getDict('CC_AS_IVR_SKILL_GROUP')" class="form-control input-sm query-count">
	                           	    	<option value="">请选择</option>
	                           </select>
	                       </div>
	                       <div class="input-group input-group-sm pull-left  ">
								<label class="checkbox checkbox-info checkbox-inline remind-checkbox" style="margin-left:20px">
				                       <input type="checkbox" class="remind-input"  name="ivrConfigDetail.IS_SKILL_GROUP" value="1">
				                       <span class="text-black">启用</span>
			                    </label>
							</div>
	                      </td>
						</tr>
						<tr>
							<td>是否进入</td><!-- IS_ARTIFICIAL 、IS_INTO_ROBOT -->
							<td colspan="3">
							<div class="input-group input-group-sm pull-left  ">
 								 <select name="" id="ivrTransfer" style=" width: 430px" data-mars="Dict.getDictList('CC_AS_IVR_TRANSFER')" class="form-control input-sm query-count">
	                           	<option value="">请选择</option>
	                           </select>
	                       </div>
	                      </td>
	                     
						</tr>
	                   	<tr>
	                  		 <td>开启挂机短信</td><!--  -->
	                         <td >
						 		 <label class="radio-inline">
					                <input type="radio" value="1" name="ivrConfigDetail.IS_SEND_SMS" checked="checked" > 是
					             </label>
					             <label class="radio-inline">
					                <input type="radio" value="0" name="ivrConfigDetail.IS_SEND_SMS"> 否
					             </label>
	                         </td>
	                         <td></td>
	                      	 <td></td>
	                   	</tr>
	                   	<tr>
	                   		<td></td>
	                   		<td colspan="2">
								<textarea rows="5" name="ivrConfigDetail.SMS_CONTENT" id="textVal"  maxlength="4000" class="form-control input-sm"></textarea>
	                   		</td>
	                   	</tr>
                 
	                   </tbody>
	             </table>
	             <script id="citySelectHtml" type="text/html">
						<div id="citySelect" class="xm-select" style="width:500px"></div>
				 </script>
                 <script id="crowdPackListSelectHtml" type="text/html">
						<div id="crowdPackListSelect" class="xm-select " style="width:500px"></div>
				</script>
                <script id="vipSelectHtml" type="text/html">
						<div id="vipSelect" class="xm-select " style="width:500px"></div>
				</script>
	            
			    </div>
		   </div>
		</form>		
        <div class=" text-c">
	   		<button class="btn btn-sm btn-primary"  type="button" onclick="ivrSkillConfig.ajaxSubmitForm()">确认</button>
	   		<button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="closeWin()">关闭</button>
   	 	</div>
   	 	<div>
   	 		<audio id="audioFile" src=""></audio>
   	 	</div>
   	 	<form id="welcomeFileForm">
   	 		 <input class="hidden" type="file" id="welcomeFile" name="welcomeFile" accept="">
   	 	</form>
   	 	<form id="waitFileForm">
	         <input class="hidden" type="file" id="waitFile" name="waitFile" accept="">
   	 	</form>
   	 	
</EasyTag:override>

<EasyTag:override name="script">
	<script type="text/javascript" src="/activeService/static/js/xm-select.js"></script>
	<script type="text/javascript">
	    jQuery.namespace("ivrSkillConfig")
        	var cityListData=[];//区域信息
			var crowdPackListData=[];//人群包
			var vipListData=[];//用户信息

		// 定义优先级字段控制函数
	    ivrSkillConfig.handleNewVipChange = function(element) {
	    	ivrSkillConfig.setPriorityLevelRequired(element.value === "0");
	    }

	    ivrSkillConfig.setPriorityLevelRequired = function(required) {
	    	var $priorityInput = $("#skillPriorityLevelInput");
	    	var $priorityTd = $("#skillPriorityLevelTd");

	    	if (required) {
	    		$priorityInput.attr("data-rules", "required");
	    		$priorityTd.addClass("required");
	    	} else {
	    		$priorityInput.removeAttr("data-rules");
	    		$priorityTd.removeClass("required");
	    	}
	    }

	    ivrSkillConfig.initPriorityLevelStatus = function() {
	    	var isNewVip = $("input[name='ivrConfig.IS_NEW_VIP']:checked").val();
	    	ivrSkillConfig.setPriorityLevelRequired(isNewVip === "0");
	    }
	    $(function(){
	    	layui.use('form', function(){
    		  var form = layui.form;
    		  form.render();
    		});

	    	// 初始化优先级字段状态
	    	if (typeof ivrSkillConfig !== 'undefined' && typeof ivrSkillConfig.initPriorityLevelStatus === 'function') {
	    		ivrSkillConfig.initPriorityLevelStatus();
	    	} else {
	    		console.error('ivrSkillConfig.initPriorityLevelStatus is not defined');
	    	}

	    	$("#ivrSkillForm").render({success:function(data){
	    		if(data['ivrDao.ivrDetail']&&data['ivrDao.ivrDetail'].data){
    				var IS_ARTIFICIAL=data['ivrDao.ivrDetail'].data['ivrConfigDetail.IS_ARTIFICIAL']
    				var IS_INTO_ROBOT=data['ivrDao.ivrDetail'].data['ivrConfigDetail.IS_INTO_ROBOT']
    				if(IS_ARTIFICIAL==0&&IS_INTO_ROBOT==0){
    					$("#ivrTransfer").val("")
    				}else if(IS_ARTIFICIAL==1&&IS_INTO_ROBOT==0){
    					$("#ivrTransfer").val("1")
    				}else if(IS_ARTIFICIAL==0&&IS_INTO_ROBOT==1){
    					$("#ivrTransfer").val("2")
    				}
	    		}
	    		 var callListData=[];
 				$(data['ivrDao.ivrDetail'].data['callList'].data).each(function(e){
 					callListData.push(this.HOTLINE);
 				})
 				setSelectData(callListData, "hotline","setValue","intoHotline");	
				
 				var callTypeData=[];
 				$(data['ivrDao.ivrDetail'].data['keyList'].data).each(function(e){
					if(this.STRATEGY_TYPE==4){
						callTypeData.push(this.CALL_TYPE)
					}
				})
 				setSelectData(callTypeData, "callType",'setValue',"intoCallType");	
 				$(data['ivrDao.ivrDetail'].data['keyList'].data).each(function(e){
					if(this.STRATEGY_TYPE==1){
						cityListData.push(this.PHONE_BELONG_AREA)
					}else if(this.STRATEGY_TYPE==2){
						crowdPackListData.push({value:this.CROWD_PACK_ID,name:this.CROW_PACK_NAME})
					}else if(this.STRATEGY_TYPE==3){
						vipListData.push(this.VIP)
					}
				})
				if(cityListData.length>0){
					//最后一个tr后面增加一个tr内容
					$("#keyInfoTable").find("tr").last().closest('tr').after($($('#result-option-tpl').html())); 
					//找到最后一个tr里面的下拉框修改值
					$($("#keyInfoTable").find("tr").last().find('select')[0]).val('1').change();
					cityData=cityListData;
				}
				if(crowdPackListData.length>0){
					$("#keyInfoTable").find("tr").last().closest('tr').after($($('#result-option-tpl').html())); 
					$($("#keyInfoTable").find("tr").last().find('select')[0]).val('2').change();
					crowdPackData=crowdPackListData;
				}
				if(vipListData.length>0){
					$("#keyInfoTable").find("tr").last().closest('tr').after($($('#result-option-tpl').html())); 
					$($("#keyInfoTable").find("tr").last().find('select')[0]).val('3').change();
					setVipValue=vipListData;
				}
				if(vipListData.length==0&&cityListData.length==0&&crowdPackListData.length==0){
	    			$("#show-btn").click();

				}

				// 数据回填后重新初始化优先级字段状态
				if (typeof ivrSkillConfig !== 'undefined' && typeof ivrSkillConfig.initPriorityLevelStatus === 'function') {
					ivrSkillConfig.initPriorityLevelStatus();
				}
	    	}

	    	})
	    })
	    //加载问题
		function setSelectData(callListData, funObjStr, fun,infoStr){
    		var funObj = eval(funObjStr);
    		var info = eval(infoStr);
	    	if(info && funObj){
	    		funObj[fun](callListData);
			}else{
				setTimeout(function() {
					setSelectData(callListData,funObjStr, fun,infoStr);
				}, 100)
			}
		}
	  
	    var hotline;
	    var intoHotline=false;
	    hotline = xmSelect.render({
	    	el: '#hotlineSelect1', 
	    	name:"hotline",
	    	//配置搜索
	    	filterable: true,
	    	paging: false,
	    	toolbar:{ show: true, },
	    	pageRemote: true,
	    	searchTips: '输入热线名称',
	    	remoteMethod: function(val, cb, show){
	    		ajax.remoteCall("/activeService/webcall?action=Dict.ccHotline",{},function(result) {
	    			if(result.data){
	    				var dataArr = [];
	    				$.each(result.data, function(i) {
	    					var item = {};
	    					item.name= result.data[i]
	    					item.value= i;
	    					dataArr.push(item)
		    			});
	    				hotline.update({
	    					data:dataArr
	    				});
	   					cb(dataArr);	
	   					intoHotline=true;
	   				}else{
	   					hotline.update({
	    					data:[]
	    				});
	   					cb([]);
	   				}
	   			});
	    	}
	    })
	 var callType;
	    var intoCallType=false;
	    callType = xmSelect.render({
	    	el: '#callTypeSelect', 
	    	name:"callType",
	    	//配置搜索
	    	filterable: true,
	    	paging: false,
	    	toolbar:{ show: true, },
	    	pageRemote: true,
	    	searchTips: '输入诉求名称',
	    	remoteMethod: function(val, cb, show){
	    		ajax.remoteCall("/activeService/webcall?action=Dict.callType",{},function(result) {
	    			if(result.data){
	    				var dataArr = [];
	    				$.each(result.data, function(i) {
	    					var item = {};
	    					item.name= result.data[i]
	    					item.value= i;
	    					dataArr.push(item)
		    			});
	    				callType.update({
	    					data:dataArr
	    				});
	   					cb(dataArr);	
	   					intoCallType=true;
	   				}else{
	   					callType.update({
	    					data:[]
	    				});
	   					cb([]);
	   				}
	   			});
	    	}
	    })


	    ivrSkillConfig.priorityLevelCheck=function(ths){
			ajax.remoteCall("${ctxPath}/servlet/ivrService?action=IvrPriorityLevelCheck",{level:ths.value,type:2,id:'${param.id}'},function(result) { 
				if(result.state == 1){
					if(result.data){
						var title="";
						for(var  key in result.data){
							title=title==""?result.data[key].NAME:title+"  "+result.data[key].NAME
						}
						$("#skillPriorityLevelCheck").attr("data-original-title",title);
			    		$('[data-toggle="tooltip"]').tooltip();
						if(result.data.length!=0){
							$("#skillPriorityLevelCheck").removeClass("hidden")
						}else{
							$("#skillPriorityLevelCheck").addClass("hidden")
						}
					}else{
						$("#skillPriorityLevelCheck").addClass("hidden")
					}
				
				}else{
					layer.alert(result.msg,{icon: 5});
				}
			})
	    }
	    ivrSkillConfig.ajaxSubmitForm = function(){
	    	// 在验证前确保优先级字段状态正确
	    	if (typeof ivrSkillConfig !== 'undefined' && typeof ivrSkillConfig.initPriorityLevelStatus === 'function') {
	    		ivrSkillConfig.initPriorityLevelStatus();
	    	}

			if(!form.validate("#ivrSkillForm")){
				return;
			}
			if(!hotline.getValue("valueStr")){
				layer.msg("请选择热线！",{icon: 5});
				return
			}
			var data = form.getJSONObject("ivrSkillForm");
			if(data['ivrConfigDetail.IS_PRIORITY_ACCESS']!=null){
				data['ivrConfigDetail.IS_PRIORITY_ACCESS']="1";
			}else{
				data['ivrConfigDetail.IS_PRIORITY_ACCESS']="0";
			}
			if(data['ivrConfigDetail.IS_SKILL_GROUP']!=null){
				data['ivrConfigDetail.IS_SKILL_GROUP']="1";
			}else{
				data['ivrConfigDetail.IS_SKILL_GROUP']="0";
			}
			if(data['ivrConfigDetail.IS_WELCOME_WORDS']!=null){
				data['ivrConfigDetail.IS_WELCOME_WORDS']="1";
			}else{
				data['ivrConfigDetail.IS_WELCOME_WORDS']="0";
			}
			if($("#ivrTransfer").val()==1){
				data['ivrConfigDetail.IS_ARTIFICIAL']="1";
				data['ivrConfigDetail.IS_INTO_ROBOT']="0";
			}else if($("#ivrTransfer").val()==2){
				data['ivrConfigDetail.IS_ARTIFICIAL']="0";
				data['ivrConfigDetail.IS_INTO_ROBOT']="1";
			}else{
				data['ivrConfigDetail.IS_ARTIFICIAL']="0";
				data['ivrConfigDetail.IS_INTO_ROBOT']="0";
			}
			data['call']=hotline.getValue("valueStr");
			ajax.remoteCall("${ctxPath}/servlet/ivrService?action=saveIvrConfig",data,function(result) { 
					if(result.state == 1){
						parent.layer.msg(result.msg,{icon:1});
						parent.scriptConf.reload();
						closeWin();
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
	    ivrSkillConfig.addItem= function(el){
    		if( $("[name='SYMBOL1']").length>=3){
    			layer. msg("已超过添加上限",{icon: 5});
    			return;
    		}
			var resultOptionTpl = $($('#result-option-tpl').html());
    		$("#result-el").append(resultOptionTpl); 
    		
		}
	    ivrSkillConfig.removeRef = function(obj){
			if($(obj).closest('tr').find('select').length>=1){
				$(obj).closest('tr').remove();
			}
		}
	    //来电区域
	    var city;
	    var cityData=[]
	    function initCity(){
	    	ajax.remoteCall("/activeService/webcall?action=common.getCity",{},function(result) {
    			if(result.data){
    				city=xmSelect.render({
    			    	el: '#citySelect', 
    			    	name:"city",
    			    	//配置搜索
    					autoRow: true,
    					filterable: true,
    					repeat: true,
    					autoRow:false,
    					tree: {
    						show: true,
    						showFolderIcon: true,
    						showLine: true,
    						indent: 20,
    						expandedKeys: [ -3 ],
    					},
    					toolbar: {
    						show: true,
    						list: ['ALL', 'REVERSE', 'CLEAR']
    					},
    					filterable: true,
    					height: '300px',
    					data: function(){
    						return result.data
    					} ,
    			    }) 
   				}else{
   					
   				}
    			if(cityData){
    				city.setValue(cityData);
    				cityData=[] 
				}
   			});
	    	
	    }
	     //用户属性 
	    var vip;
	     var setVipValue=[];
	     function initVip(){
			    vip = xmSelect.render({
			    	el: '#vipSelect', 
			    	name:"vip",
			    	//配置搜索
			    	filterable: true,
			    	paging: false,
			    	toolbar:{ show: true, },
			    	pageRemote: true,
			    	searchTips: '输入用户属性名称',
			    	
			    
			    	//数据处理
			    	remoteMethod: function(val, cb, show, pageIndex){
			    		//val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
			    		if(pageIndex==1){
			    			pageIndex=-1;
			    		}
			    		ajax.remoteCall("/activeService/webcall?action=Dict.ccAsVip",{},function(result) {
			    			if(result.data){
			    				var dataArr = [];
			    				$.each(result.data, function(i) {
			    					var item = {};
			    					item.name= result.data[i];
			    					item.value= i;
			    					dataArr.push(item)
			    					
				    			});
			    				vip.update({
			    					data:dataArr
			    				});
			   					cb(dataArr)
			   					if(setVipValue){
				    				vip.setValue(setVipValue);
				    				setVipValue=[] 
			   					}
			   				}else{
			   					hotline.update({
			    					data:[]
			    				});
			   					cb([]);
			   				}
			   			});
			    	}
			    }) 
	     }
	   // 人群包类型
	    var totalPage=0;
	    var crowdPackList;
	    var crowdPackData=[];
	    function initCrowdPack(){
		    crowdPackList = xmSelect.render({
		    	el: '#crowdPackListSelect', 
		    	name:"crowdPackList",
		    	//配置搜索
		    	filterable: true,
		    	paging: false,
		    	toolbar:{ show: true, },
		    	pageRemote: true,
		    	searchTips: '输入人群包名称',
		    	on: function(data){
		    		//arr:  当前多选已选中的数据
		    		var arr = data.arr;
		    		//change, 此次选择变化的数据,数组
		    		var change = data.change;
		    		//isAdd, 此次操作是新增还是删除
		    		var isAdd = data.isAdd;
		    		var msg = "";
		    		for(var index in arr){
		    			var select = arr[index];
		    		}
		    	},
		    	
		    	remoteMethod:function(val, cb, show, pageIndex){
		    		//val: 搜索框的内容, 不开启搜索默认为空, cb: 回调函数, show: 当前下拉框是否展开, pageIndex: 当前第几页
		    		if(pageIndex==1){
		    			pageIndex=-1;
		    		}
		    		ajax.remoteCall("${ctxPath}/webcall?action=crowdPackDao.bgCrowdPackDictList",{name:val,pageIndex:pageIndex,pageSize:10},function(result) {
		    			if(result.data&&result.data.length>0){
		    				var dataArr = [];
		    				for(var i in result.data){
		    					var item = {};
		    					item.name= result.data[i].NAME;
		    					item.value= result.data[i].ID;
		    					dataArr.push(item)
		    				}
		    				if(result.totalPage>0){
		    					totalPage = result.totalPage;
		    				}
		    				
		   					cb(dataArr, totalPage)
		   					if(crowdPackData){
		   						crowdPackList.setValue(crowdPackData);
		   						crowdPackData=[] 
		   					}
		   				}else{
		   					cb([], 0);
		   				}
		   			});
		    	}
		    })
	    }
		function changeIvrType(ths){
	    	var keyType=$(ths).val();
	    	var keys=[];
	    	$("[name='SYMBOL1']").each(function(e){
	    		if(keys.indexOf(this.value)<0){
		    		keys.push(this.value)
	    		}else{
	    			layer. msg("已存在该类型",{icon: 5});
	    			$(ths).val("0");

	    		}
	    	})
	    	if(keyType==1){
	    		$(ths).parent("td").next("td").html()
	    		var resultOptionTpl = $($('#citySelectHtml').html());
	    		$(ths).parent("td").next("td").html(resultOptionTpl); 
				initCity();
	    	}else if(keyType==2){
	    		$(ths).parent("td").next("td").html()
	    		var resultOptionTpl = $($('#crowdPackListSelectHtml').html());
	    		$(ths).parent("td").next("td").html(resultOptionTpl); 
	    		initCrowdPack()
	    	}else if(keyType==3){
	    		$(ths).parent("td").next("td").html()
	    		var resultOptionTpl = $($('#vipSelectHtml').html());
	    		$(ths).parent("td").next("td").html(resultOptionTpl); 
	    		initVip();
	    	}
	    }
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_div.jsp" %>