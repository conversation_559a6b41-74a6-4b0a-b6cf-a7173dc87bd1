<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<EasyTag:override name="head">
	<title>会员身份优先级配置</title>
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<style type="text/css">
		.form-control-static {
			padding-top: 7px;
			padding-bottom: 7px;
			margin-bottom: 0;
		}
		.multiselect {
			width: 100% !important;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="editForm" data-mars="memberPriority.getMemberPriority" method="post" autocomplete="off" data-mars-prefix="MemberPriority.">
		<input type="hidden" value="${param.ID}" name="MemberPriority.ID" id="MemberPriority.ID">
		<table class="table table-edit table-vzebra mt-10">
			<tbody>
				<tr>
					<td width="120px"><span class="text-danger">*</span>会员身份标签</td>
					<td width="300px">
						<select class="form-control input-sm" name="MemberPriority.MEMBER_TAG" id="memberTag" data-rules="required" data-mars="Dict.getDict('MEMBER_TAG')">
							<option value="">请选择会员身份标签</option>
						</select>
					</td>
				</tr>
				<tr>
					<td><span class="text-danger">*</span>热线号码</td>
					<td>
						<select class="form-control input-sm" name="MemberPriority.HOTLINE_CODE" id="hotlineCode" multiple="multiple" data-rules="required"  data-mars="Dict.getDict('CC_HOTLINE')">
							<!-- 热线号码选项将通过JavaScript动态加载 -->
						</select>
					</td>
				</tr>
				<tr>
					<td><span class="text-danger">*</span>会员身份等级</td>
					<td>
						<input type="number" name="MemberPriority.MEMBER_LEVEL" id="memberLevel"
							   class="form-control input-sm" data-rules="required|digits"
							   placeholder="请输入1-20级，数字越大优先级越高" min="1" max="20"
							   oninput="validateMemberLevel(this)">
						<small class="text-muted">提示：1-20级，数字越大优先级越高</small>
					</td>
				</tr>
				<tr>
					<td>备注</td>
					<td>
						<textarea rows="3" class="form-control input-sm" name="MemberPriority.REMARK" 
								  id="remark" placeholder="请输入备注信息" maxlength="500"></textarea>
					</td>
				</tr>
			</tbody>
		</table>
		<div class="layer-foot text-c">
			<button class="btn btn-sm btn-primary" type="button" onclick="edit.ajaxSubmitForm()">保存</button>
			<button class="btn btn-sm btn-default ml-20" type="button" id="backbut" onclick="popup.layerClose();">关闭</button>
		</div>
	</form>
</EasyTag:override>

<EasyTag:override name="script">
	<link type="text/css" rel="stylesheet" href="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.css" />
	<script type="text/javascript" src="/easitline-static/lib/bootstrap/bootstrap-multiselect/bootstrap-multiselect.js"></script>
	<script type="text/javascript" src="${ctxPath}/static/js/common.js"></script>
	<script type="text/javascript">
		jQuery.namespace("edit");

		// 多选组件配置 - 完全参考其他页面的标准配置
		edit.multiSetting = {
			buttonWidth: '100%',
			allSelectedText: "全部",
			nonSelectedText: "请选择热线号码",
			nSelectedText: "个被选中",
			selectAllNumber: false,
			maxHeight: 350,
			includeSelectAllOption: true,
			selectAllText: '全选',
			enableFiltering: true,
			buttonText: function(options, select) {
				if (options.length === 0) {
					return '请选择热线号码';
				} else if (options.length > 3) {
					return '已选择 ' + options.length + ' 个热线号码';
				} else {
					var labels = [];
					options.each(function() {
						if ($(this).attr('label') !== undefined) {
							labels.push($(this).attr('label'));
						} else {
							labels.push($(this).html());
						}
					});
					return labels.join(', ') + '';
				}
			}
		};

		$(function() {
			$("#editForm").render({
				success: function(result) {
					console.log("表单渲染成功，result:", result);

					// 获取编辑数据
					var formData = result["memberPriority.getMemberPriority"];
					var hotlineCodeToSelect = null;

					if (formData && formData.data && formData.data.HOTLINE_CODE) {
						hotlineCodeToSelect = formData.data.HOTLINE_CODE;
						console.log("需要回显的热线号码：", hotlineCodeToSelect);
					}

					// 等待data-mars加载完成后初始化多选组件
					setTimeout(function() {
						console.log("开始初始化多选组件，选项数量：", $("#hotlineCode option").length);

						// 先销毁可能存在的多选组件
						if ($("#hotlineCode").hasClass('multiselect')) {
							$("#hotlineCode").multiselect('destroy');
						}

						// 如果有需要回显的数据，先设置select的值
						if (hotlineCodeToSelect) {
							var selectedValues = hotlineCodeToSelect.split(',');
							selectedValues = selectedValues.filter(function(val) {
								return val && val.trim() !== '';
							});

							// 设置原生select的选中状态
							$("#hotlineCode").val(selectedValues);
							console.log("设置原生select选中值：", selectedValues);
						}

						// 重新初始化多选组件
						$('#hotlineCode').multiselect(edit.multiSetting);

						console.log("多选组件初始化完成");

						// 验证回显结果
						setTimeout(function() {
							var currentSelected = $('#hotlineCode').val();
							console.log("当前选中的值：", currentSelected);
						}, 500);

					}, 1000); // 增加延迟时间，确保data-mars完成加载
				}
			});
		});

		// 页面关闭时清理多选组件
		$(window).on('beforeunload', function() {
			if ($("#hotlineCode").hasClass('multiselect')) {
				$("#hotlineCode").multiselect('destroy');
			}
		});


		
		edit.ajaxSubmitForm = function() {
			// 验证表单
			if (!form.validate("#editForm")) {
				return;
			}

			// 获取多选的热线号码
			var selectedHotlines = $("#hotlineCode").val();
			if (!selectedHotlines || selectedHotlines.length === 0) {
				layer.alert("请选择热线号码", {icon: 5});
				return;
			}

			// 将多选值转换为逗号分隔的字符串
			var hotlineCodeValue = selectedHotlines.join(',');

			// 先检查热线号码冲突
			var memberTag = $("#memberTag").val();
			var memberLevel = $("#memberLevel").val();
			var currentId = $("#MemberPriority\\.ID").val();

			// 获取会员身份标签中文名称
			var memberTagName = $("#memberTag option:selected").text();

			// 获取选中的热线号码中文名称
			var selectedHotlineNames = [];
			$("#hotlineCode option:selected").each(function() {
				selectedHotlineNames.push($(this).text());
			});
			var hotlineCodeNames = selectedHotlineNames.join(',');

			var conflictCheckData = {
				memberTag: memberTag,
				memberTagName: memberTagName,
				hotlineCode: hotlineCodeValue,
				hotlineCodeNames: hotlineCodeNames,
				memberLevel: memberLevel
			};
			if (currentId) {
				conflictCheckData.excludeId = currentId;
			}

			ajax.remoteCall("${ctxPath}/servlet/MemberPriority?action=checkHotlineConflict", conflictCheckData, function(conflictResult) {
				if (conflictResult && conflictResult.data && conflictResult.data.conflict) {
					var message = conflictResult.data.message;
					if (conflictResult.data.conflictHotlines && conflictResult.data.conflictHotlines.length > 0) {
						message += "<br><br>冲突的热线号码：" + conflictResult.data.conflictHotlines.join("、");
					}
					layer.alert(message, {icon: 5});
					return;
				}

				// 没有冲突，继续提交数据
				var data = form.getJSONObject("editForm");
				data["MemberPriority.HOTLINE_CODE"] = hotlineCodeValue;
				data["MemberPriority.HOTLINE_NAME"] = hotlineCodeNames;

				// 根据是否有ID决定调用save还是update
				var currentId = $("#MemberPriority\\.ID").val();
				var actionUrl;
				if (currentId && currentId.trim() !== '') {
					// 修改操作，调用update
					actionUrl = "${ctxPath}/servlet/MemberPriority?action=updateMemberPriority";
					console.log("执行修改操作，ID:", currentId);
				} else {
					// 新增操作，调用save
					actionUrl = "${ctxPath}/servlet/MemberPriority?action=saveMemberPriority";
					console.log("执行新增操作");
				}

				// 提交数据
				ajax.remoteCall(actionUrl, data, function(result) {
					if (result.state == 1) {
						// 先显示成功消息，在消息回调中关闭界面和刷新列表
						layer.msg(result.msg, {icon: 1, time: 1200}, function() {
							// 清理多选组件
							if ($("#hotlineCode").hasClass('multiselect')) {
								$("#hotlineCode").multiselect('destroy');
							}
							// 刷新父窗口的列表
							parent.memberPriority.search();
							// 关闭界面
							popup.layerClose();
						});
					} else {
						layer.alert(result.msg, {icon: 5});
					}
				});
			});
		};

		// 验证会员身份等级输入
		function validateMemberLevel(input) {
			var value = parseInt(input.value);
			if (isNaN(value)) {
				return;
			}
			if (value < 1) {
				input.value = 1;
				layer.msg('会员身份等级不能小于1', {icon: 5, time: 2000});
			} else if (value > 20) {
				input.value = 20;
				layer.msg('会员身份等级不能大于20', {icon: 5, time: 2000});
			}
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>
