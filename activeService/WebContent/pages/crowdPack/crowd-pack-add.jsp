<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>新增人群包</title>
	<style type="text/css">
		.container-fluid:{height:100%}
		.layui-layer-page .layui-layer-content {
            position: relative;
            overflow: unset !important;
 		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" class="form-inline" method="post"  autocomplete="off" >
			  <table class="table table-edit table-vzebra mt-10" >
                  <tbody>
                    <tr>
		                 <td>来源系统</td>
		                     <td>
			                     <select class="form-control input-sm" data-rules="required"  onchange="crowdPackAdd.changeSourceList(this)" name="DATA_SOURCE" id="sourceList" data-mars="common.generalTree('001')" >
			                     	<option value="">请选择</option>
			                     </select>
		                     </td>
                     </tr>
                     <tr>
	                     <td>触发器</td>
	                     <td>
	                     	  <select class="form-control input-sm" data-rules="required"  onchange="crowdPackAdd.changeTriggerType()" name="TRIGGER_TYPE" id="triggerList" >
			                     	<option value="">请选择</option>
			                  </select>
							 <span id="tip" style="color:#a94442"></span>
	                     </td>
		             </tr>
	                 <tr>
                        <td>人群包名称</td>
                        <td>
							  <input style="width:400px" maxlength="50" data-rules="required" class="form-control input-sm"  name="CROWD_PACK_NAME">
                           </td>
                     </tr>
					 <tr>
						<td>类别</td>
						<td>
							<select class="form-control input-sm" name="CATEGORY" id="category" data-mars="Dict.getDict('CC_AS_PACK_TAG')" >
								<option value="">请选择</option>
							</select>
						</td>
					 </tr>
					<tr>
						<td>是否敏感用户</td>
						<td>
							<select class="form-control input-sm" name="IS_SENTIMENT" id="" >
								<option value="">请选择</option>
								<option value="1">是</option>
								<option value="0">否</option>
							</select>
						</td>
					</tr>

                  </tbody>
	             </table>
				  <div class="layer-foot text-c">
				   		<button class="btn btn-sm btn-primary"  type="button" onclick="crowdPackAdd.ajaxSubmitForm()">保存</button>
				   		<button class="btn btn-sm btn-default ml-20"  type="button"  onclick="popup.layerClose()">取消</button>
				  </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src="${ctxPath}/static/js/xm-select.js"></script>
	<script type="text/javascript">
	    jQuery.namespace("crowdPackAdd")
	    $(function() {
	    	$("#editForm").render({success : function(result){
	    		
			}}); 
		});
	    
	    crowdPackAdd.ajaxSubmitForm = function(){
			if(!form.validate("#editForm")){
				return;
			}
			var data = form.getJSONObject("editForm");
			data['TRIGGER_TYPE_NAME']=$("#editForm select[name='TRIGGER_TYPE']").find("option:selected").text();
			ajax.remoteCall("${ctxPath}/servlet/crowdPack?action=saveCrowdPack",data,function(result) { 
					if(result.state == 1){
						window.parent.layer.closeAll();
						window.parent.layer.msg(result.msg,{icon: 1});
						window.parent.crowdPackList.search()
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
	    
	    crowdPackAdd.changeTriggerType = function(){
			var triggerType = $("select[name='TRIGGER_TYPE']").val();
			var dataSource = $("select[name='DATA_SOURCE']").val();
			if(!triggerType||!dataSource){
				return ;
			}
			ajax.remoteCall("${ctxPath}/servlet/crowdPack?action=checkCrowdPack",{triggerType:triggerType,dataSource:dataSource},function(result) { 
					if(result.state == 1){
						if(result.data&&result.data.groupId){
							$('#tip').text("该触发器已存在人群包，编号是"+result.data.groupId);
						}else{
							$('#tip').text("");
						}
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
	    
	    crowdPackAdd.changeSourceList=function(el){
	    	var id = $(el).val();
	    	$("#triggerList").data({
				"mars":"common.queryForTreeNodesById('"+id+"')",
			});
	    	$("#triggerList").render({
	    		success:function(req){
	    			var itemStr = "<option value=''>请选择</option>";
	    			var data = req["common.queryForTreeNodesById('"+id+"')"].data;
	    			for(var i in data){
	    				itemStr+='<option value="'+data[i].CODE+'"> '+data[i].NAME+"</option>"
	    			}
	   				$("#triggerList").html(itemStr);
	    		}
	    	});
		}
	   
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>