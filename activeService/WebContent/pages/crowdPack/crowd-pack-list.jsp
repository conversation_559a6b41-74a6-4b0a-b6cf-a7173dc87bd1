<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<%@ page import="com.yunqu.cc.activeService.base.Constants" %>
<EasyTag:override name="head">
	<title>人群包列表</title>
	<style>
	    #crowdPackListData th{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	    #crowdPackListData td{overflow:hidden;text-overflow:ellipsis;white-space:nowrap;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
		
       <form name="crowdPackListForm" class="form-inline" id="crowdPackListForm" method="post" onsubmit="return false" autocomplete="off">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
	             		       <h5><span class="glyphicon glyphicon-list"></span> 人群包列表</h5>
	             		 </div>
	             		 <hr style="margin:5px -15px">
	             		 <input type="hidden" name = "realPageIndex" id="hid_pageIndexV" />
	             		 <div class="form-group">
             		           <div class="input-group width-36">
		          		              <span class="input-group-addon">创建时间</span>	
									  <input type="text" name="createTimeStar" id="createTimeStar" class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 00:00:00',maxDate:'#F{$dp.$D(\'createTimeEnd\')}'})">
									  <span class="input-group-addon">-</span>	
									  <input type="text" name="createTimeEnd" id="createTimeEnd"  class="form-control input-sm Wdate" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss',startDate:'%y-%M-%d 23:59:59',minDate:'#F{$dp.$D(\'createTimeStar\')}'})">									  
							   </div> 
							   <div class="input-group width-18">
								      <span class="input-group-addon" style="width:30%">创建人</span>	
									  <input type="text" name="createAcc" class="form-control input-sm" >
							   </div>
							   <div class="input-group width-18">
								      <span class="input-group-addon">人群包名称</span>	
									  <input type="text" name="name" class="form-control input-sm" ></td>
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">调用模式</span>	
								        <select class="form-control input-sm width-70" name="touchSource" id="touchSource">
                                          <option value="" selected="selected">--请选择--</option>
                                          <option value="CC_CALL">调</option>
                                          <option value="CC_PUSH">推</option>
                                          <option value="CC_OTHER">其他</option>
                                      </select>
							   </div>
							   <div class="input-group width-36">
							    	  <span class="input-group-addon">来源系统</span>	
								        <select class="form-control input-sm width-70" name="dataSource" id="dataSource">
                                          <option value="" selected="selected">--请选择--</option>
                                          <option value="1">大数据</option>
                                          <option value="2">触发器</option>
                                          <option value="3">人工导入</option>
                                      </select>
							   </div>
							   <div class="input-group input-group-sm" style="display: none">
									 <select class="form-control input-sm" id="channelTypeDict" data-mars="Dict.getDict('CC_AS_CHANNEL')">
									</select>
							   </div>
							   <div class="input-group input-group-sm">
								   <span class="input-group-addon">触发器类别</span>
								   <select class="form-control input-sm" name="category" id="category" data-mars="Dict.getDict('CC_AS_PACK_TAG')">
									 <option value="" selected="selected">--请选择--</option>
								 </select>
							   </div>
							 <div class="input-group input-group-sm">
								 <span class="input-group-addon">是否敏感用户</span>
								 <select class="form-control input-sm" name="IS_SENTIMENT" id="IS_SENTIMENT">
									 <option value="" selected="selected">--请选择--</option>
									 <option value="0" >否</option>
									 <option value="1" >是</option>
								 </select>
							 </div>
						  </div>
						  <div class="form-group">
							  <div class="input-group input-group-sm pull-left">
									  <button type="button" class="btn btn-sm btn-success mr-10" onclick="crowdPackList.addCrowdPack()"><span class="glyphicon glyphicon-plus"></span> 新增</button>
							   </div>
							   <div class="input-group input-group-sm pull-left">
									  <button type="button" class="btn btn-sm btn-success mr-10" onclick="crowdPackList.toCrowdDataPage()"><span class="glyphicon glyphicon-circle-arrow-right"></span> 大数据圈选</button>
							   </div>
							   <EasyTag:res resId="as_crowd_pack_list_import">
								   <div class="input-group input-group-sm pull-left mr-10">
										 <button type="button" class="btn btn-sm btn-success" onclick="crowdPackList.importData()"><span class="glyphicon glyphicon-import"></span> 导入</button>
								   </div>
							   </EasyTag:res>
							    <EasyTag:res resId="as_crowd_pack_list_export">
								   <div class="input-group input-group-sm pull-left mr-10">
										 <button type="button" class="btn btn-sm btn-success" onclick="crowdPackList.exportExcel()"><span class="glyphicon glyphicon-export"></span> 导出</button>
								   </div>
							   </EasyTag:res>
							   <div class="input-group input-group-sm">
									<button type="button" class="btn btn-sm btn-success" onclick="crowdPackList.reset()">重置</button>
								</div>
						 	   <div class="input-group input-group-sm pull-right ml-10">
									  <button type="button" class="btn btn-sm btn-default" onclick="crowdPackList.search()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
							   </div>
						  </div>
             	    </div> 
	              	<div class="ibox-content ">
	              		<div class="row table-responsive" ><!-- style="max-height:700px;" -->
	              		  <table style="border: 0px solid #fff;" class="table table-bordered" id="crowdPackListData" data-mars="crowdPackDao.crowdPackList">
	              		  		<thead>
	                         	 <tr>
	                         	      <th class="text-c" style="width:50px">序号</th>
								      <th class="text-c" style="width:200px">操作</th>
	                         	      <th class="text-c" style="width:50px">人群包/触达任务编号</th>
	                         	      <th class="text-c" style="width:50px"> 人群包/触达任务名称</th>
								      <th class="text-c" style="width:80px">人群包组</th>
								      <th class="text-c" style="width:80px">已配置包策略</th>
								      <th class="text-c" style="width:80px">已配置组策略</th>
								      <th class="text-c" style="width:170px">来源系统</th>
								      <th class="text-c" style="width:300px;max-width:400px;">人群口径</th>
								      <th class="text-c" style="width:110px">人群数量</th>
								      <th class="text-c" style="width:200px">创建时间</th>
								      <th class="text-c" style="width:100px">创建人</th>
								      <th class="text-c" style="width:110px">调用模式</th>
								      <th class="text-c" style="width:100px">更新频率</th>
								      <th class="text-c" style="width:200px;max-width:300px;">备注</th>
								      <th class="text-c" style="width:80px">最近同步时间</th>
								      <th class="text-c" style="width:80px">人群包状态</th>
								      <th class="text-c" style="width:80px">显示名称</th>
								      <th class="text-c" style="width:80px">显示顺序</th>
									  <th class="text-c" style="width:80px">分类</th>
									  <th class="text-c" style="width:80px">是否敏感用户</th>

								 </tr>
                             </thead>
							 <tbody id="dataList">
							 
							 </tbody>
							 <script id="list-template" type="text/x-jsrender">
												   {{for list}}
														<tr>
			                             					 <td>{{:#index+1}}</td>
															 <td class="text-c">
																<a {{if CATEGORY == '3'|| CATEGORY == '4'|| CATEGORY == '5'}} style="display: none;"{{/if}} href="javascript:crowdPackList.strategyConfig('{{:ID}}','{{:NAME}}','{{:CUST_NUM}}','{{:CATEGORY}}')">策略配置</a>
																<a {{if CATEGORY == '3'|| CATEGORY == '4'|| CATEGORY == '5'}} style="display: none;"{{/if}} href="javascript:crowdPackList.addToGroup('{{:ID}}','{{:GROUP_ID}}')">加入人群包组</a>
																<a {{if CATEGORY == '3'|| CATEGORY == '4'|| CATEGORY == '5'}} style="display: none;"{{/if}} href="javascript:crowdPackList.update('{{:ID}}','{{:GROUP_ID}}')">编辑标签</a>
																{{if TOUCH_SOURCE == 'CC_CALL'||TOUCH_SOURCE == 'CC_CALL   '}}
							 									<a href="javascript:crowdPackList.markSentisivePack('{{:ID}}')">标记免打扰</a>
							 									{{/if}}
																<EasyTag:res resId="as_crowd_pack_del">
																	<a href="javascript:crowdPackList.del('{{:ID}}')">删除</a>
																</EasyTag:res>
															 </td>
															 <td class="text-c">{{:BG_CROWD_CODE}}</td>
															 <td class="text-c"><a href="javascript:crowdPackList.searchCustList('{{:ID}}','{{:NAME}}','{{:CATEGORY}}')">{{:NAME}}</a></td>
															 <td class="text-c">{{:GROUP_NAME}}</td>
															 <td class="text-c">{{PACK_CHANNEL_TYPE:CHANNEL_TYPE STRATEGY_LEVEL}}</td>
															 <td class="text-c">{{GROUP_CHANNEL_TYPE:CHANNEL_TYPE STRATEGY_LEVEL}}</td>
															 <td class="text-c">{{getText:DATA_SOURCE 'dataSource'}}</td>
															 <td class="text-c" style="width:300px;max-width:400px;"><p title="{{:CONDITION}}">{{:CONDITION}}</p></td>
															 <td class="text-c">{{:CUST_NUM}}</td>
															 <td class="text-c">{{:CREATE_TIME}}</td>
															 <td class="text-c">{{:CREATE_ACC}}</td>
															 <td class="text-c">{{getText:TOUCH_SOURCE 'touchSource'}}</td>
															 <td class="text-c">{{:SEND_RATE}}</td>
															 <td class="text-c" style="width:200px;max-width:300px;"><p title="{{:REMARK}}">{{:REMARK}}</p></td>
															 <td class="text-c">{{:SYNC_TIME}}</td>
															 <td class="text-c">{{if BG_CROWD_STATUS=='1'}}使用中{{/if}}{{if BG_CROWD_STATUS=='2'}}已删除{{/if}}</td>
															 <td class="text-c">{{:SHOW_NAME}}</td>
															 <td class="text-c">{{:SORT}}</td>
															 <td class="text-c">{{dictFUN:CATEGORY 'CC_AS_PACK_TAG'}}</td>
															 <td class="text-c">{{getText:IS_SENTIMENT 'IS_SENTIMENT'}}</td>
														</tr>
												   {{/for}}
																		 
							</script>
	              		  </table>
	              		</div><!--  row table-responsive -->
	                    <div class="row paginate" id="page">
	                    		<jsp:include page="/pages/common/pagination.jsp">
	                    			<jsp:param value="10" name="pageSize"/>
	                    		</jsp:include>
	                    </div> 
	                 </div>
                </div>
        </form>
         <script id="list-template" type="text/x-jsrender">
				 {{for list}}
						<option id="{{:GROUP_ID}}" >{{:GROUP_NAME}}</option>								
				 {{/for}}
		</script>
</EasyTag:override>

<EasyTag:override name="script">
	<script type ="text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type = "text/javascript">
		jQuery.namespace("crowdPackList");
	    requreLib.setplugs("wdate");
	    var channelTypeDict = {};
	    $(function(){
	    	$("#crowdPackListForm").render({success : function(result){
	    		if(result["Dict.getDict('CC_AS_CHANNEL')"]){
	    			channelTypeDict =  result["Dict.getDict('CC_AS_CHANNEL')"].data;
	    		}
				crowdPackList.search();
	    	}});
	    });
	    
	    crowdPackList.search = function(){
    		$("#crowdPackListForm").searchData();
	    }
	    
	    crowdPackList.addGroup = function(obj,crowdPackId){
	    	var groupId = $(obj).val();
   			ajax.remoteCall("${ctxPath}/servlet/crowdPackGroup?action=addGroup",{groupId:groupId,crowdPackId:crowdPackId},function(result) { 
   				if(result.state==1){
					layer.msg(result.msg,{icon: 1});
   				}else{
   					layer.alert(result.msg,{icon: 5});
   				}
   			});
	    }
	    
	    
	    crowdPackList.addToGroup = function(crowdPackId,groupId){
	    	popup.layerShow({type:2,title:'加入人群包组',area:['400px','200px'],offset:'20px'},
			"${ctxPath}/pages/crowdPack/crowd-pack-add-group.jsp",{groupId:groupId,crowdPackId:crowdPackId});
	    }
	    crowdPackList.update = function(crowdPackId,groupId){
	    	popup.layerShow({type:2,title:'修改标签信息',area:['600px','400px'],offset:'20px'},
			"${ctxPath}/pages/crowdPack/crowd-pack-update.jsp",{id:groupId,crowdPackId:crowdPackId});
	    }
	    
	    crowdPackList.del = function(crowdPackId){
	    	layer.confirm("将删除人群包，人群明细，配置策略信息，请谨慎删除！确认删除吗？", {
	            btn : [ '确定', '取消' ]//按钮
	        }, function(index) {
	   			ajax.remoteCall("${ctxPath}/servlet/crowdPack?action=delCrowdPack",{crowdPackId:crowdPackId},function(result) { 
	   				if(result.state==1){
						layer.msg(result.msg,{icon: 1});
						crowdPackList.search();
	   				}else{
	   					layer.alert(result.msg,{icon: 5});
	   				}
	   			});
	        });
	    }
	    crowdPackList.markSentisivePack = function(crowdPackId){
	    	layer.confirm("确认标记为免打扰人群包吗？", {
	            btn : [ '确定', '取消' ]//按钮
	        }, function(index) {
	   			ajax.remoteCall("${ctxPath}/servlet/crowdPack?action=markSentisivePack",{crowdPackId:crowdPackId},function(result) {
	   				if(result.state==1){
						layer.msg(result.msg,{icon: 1});
						crowdPackList.search();
	   				}else{
	   					layer.alert(result.msg,{icon: 5});
	   				}
	   			});
	        });
	    }
	    
	    $.views.converters("PACK_CHANNEL_TYPE", function(channelType,strategyType) {
	    	var channelTypeArr = channelType.split(",");
	    	var strategyTypeArr = strategyType.split(",");
	    	var result = "";
			for(var i in channelTypeArr){
				var channelTypeName = channelTypeDict[channelTypeArr[i]]
				if(strategyTypeArr[i]=='1'){
					result+=channelTypeName+"<br/>";
				}	
			}
			return result;
		});

	    $.views.converters("GROUP_CHANNEL_TYPE", function(channelType,strategyType) {
	    	var channelTypeArr = channelType.split(",");
	    	var strategyTypeArr = strategyType.split(",");
	    	var result = "";
			for(var i in channelTypeArr){
				var channelTypeName = channelTypeDict[channelTypeArr[i]]
				if(strategyTypeArr[i]=='2'){
					result+=channelTypeName+"<br/>";
				}
			}
			return result;
		});
	    
	    crowdPackList.importData = function(){
			 popup.layerShow({type:1,title:"导入",offset:'20px',area:['450px','220px']},"${ctxPath}/pages/crowdPack/crowd-pack-import.jsp",null);
		}
	    
	    crowdPackList.strategyConfig = function(crowdPackId,crowdPackName,crowdNum,category){
	    	popup.layerShow({
				type:2,
				full:false,	
				maxmin:false,
				anim:0,
				scrollbar:false,
				shadeClose:true,
				isOutAnim:false,
				shade:0.3,
				closeBtn:1,
				offset:'r',
				anim:2,
				title:'策略配置('+crowdPackName+")",
				area:['1200px','100%'],
			},"/activeService/pages/crowdPack/strategy/strategy-config.jsp",{crowdPackId:crowdPackId,crowdNum:crowdNum,category:category,display:'inline'});
	    }
	    
	    crowdPackList.toCrowdDataPage = function(){
	    	window.open('<%=Constants.DDY_CROWDPACK_PAGE_URL%>','_blank');
	    }
	    
	    crowdPackList.addCrowdPack = function(){
	    	popup.layerShow({type:2,title:'新增人群包',area:['600px','400px'],offset:'20px'},
			"${ctxPath}/pages/crowdPack/crowd-pack-add.jsp");
	    }
	    
	    crowdPackList.searchCustList = function(crowdPackId,cpName,category){
	    	if(category=='1'){
				popup.openTab("${ctxPath}/pages/crowdPack/crowd-pack-cust-ai-list.jsp",cpName,{crowdPackId:crowdPackId});
			}else if(category=='2'||category=='3'||category=='4'){
				popup.openTab("${ctxPath}/pages/crowdPack/crowd-pack-cust-dnd-list.jsp",cpName,{crowdPackId:crowdPackId,filterType:category});
			}else{
				popup.openTab("${ctxPath}/pages/crowdPack/crowd-pack-cust-list.jsp",cpName,{crowdPackId:crowdPackId});
			}
	    }
	    
	    /*重置*/
		crowdPackList.reset = function() {
			document.crowdPackListForm.reset();
			$("#createTimeStar").val("");
	    	$("#createTimeEnd").val("");
	    	crowdPackList.search();
		}
	    
		$('#agentAreaCode').change(function() {
    		var value = $(this).val();
    		$('#agentDeptCode').data('mars', 'common.getDept("5", "'+ value +'")');
    		$('#agentDeptCode').render();
    	});
		
		crowdPackList.exportExcel = function(){
			location.href = "${ctxPath}/servlet/crowdPack?action=ExportCrowdPackList&"
				+ $("#crowdPackListForm").serialize();
		}
		
		function toggleMore(){
			var btn = $("#moreBtn").find(".glyphicon");
			$(".moreSearch").slideToggle('fast');
			btn.toggleClass("glyphicon glyphicon glyphicon-menu-up")
		}
		
		
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>