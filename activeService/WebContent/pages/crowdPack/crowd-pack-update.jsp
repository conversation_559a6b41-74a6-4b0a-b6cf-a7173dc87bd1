<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>人群包标签编辑</title>
	<style type="text/css">
		.container-fluid:{height:100%}
		.layui-layer-page .layui-layer-content {
            position: relative;
            overflow: unset !important;
 		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
			<form id="editForm" class="form-inline" method="post"  autocomplete="off" data-mars-prefix="lable." data-mars="crowdPackDao.edit" >
		  	<input style="width:400px"  value="${param.crowdPackId}" class="form-control input-sm hidden"  name="lable.ID">
			  <table class="table table-edit table-vzebra mt-10" >
                  <tbody>
                    <tr>
                        <td>人群包名称</td>
                        <td>
							  <input style="width:400px" disabled="disabled" maxlength="50"  class="form-control input-sm"  name="lable.NAME">
                           </td>
                     </tr>
                     <tr>
                        <td>状态</td>
                        <td>
							  <select style="width:400px" class="form-control input-sm " name="lable.SHOW_STATUS" id="touchSource">
                                          <option value="" selected="selected">--请选择--</option>
                                          <option value="Y">开启</option>
                                          <option value="N">关闭</option>
                                      </select>
                           </td>
                     </tr>
                     <tr>
                        <td>显示名称</td>
                        <td>
							  <input style="width:400px" maxlength="50"  class="form-control input-sm"  name="lable.SHOW_NAME">
                           </td>
                     </tr>
                     <tr>
                        <td>排序</td>
                        <td>
							  <input style="width:400px" maxlength="5" type="number" class="form-control input-sm"  name="lable.SORT">
                           </td>
                     </tr>
	                 <tr>
                        <td>话术引导</td>
                        <td>
							  <textarea rows="" cols="" style="width:400px" maxlength="1000"  class="form-control input-sm"  name="lable.SPEECHCRAFT"></textarea>
                           </td>
                     </tr>
                    <tr>
                        <td>是否敏感用户</td>
                        <td>
                            <select class="form-control input-sm" name="lable.IS_SENTIMENT" style="width:400px" >
                                <option value="">请选择</option>
                                <option value="1">是</option>
                                <option value="0">否</option>
                            </select>
                        </td>
                    </tr>
                  </tbody>
	             </table>
				  <div class="layer-foot text-c">
				   		<button class="btn btn-sm btn-primary"  type="button" onclick="lable.ajaxSubmitForm()">保存</button>
				   		<button class="btn btn-sm btn-default ml-20"  type="button"  onclick="popup.layerClose()">取消</button>
				  </div>
		</form>		
</EasyTag:override>

<EasyTag:override name="script">
	<script type = "text/javascript" src = "${ctxPath}/static/js/common.js" ></script>
	<script type="text/javascript" src="${ctxPath}/static/js/xm-select.js"></script>
	<script type="text/javascript">
	    jQuery.namespace("lable")
	    $(function() {
	    	$("#editForm").render({success : function(result){
	    		
			}}); 
		});
	    
	    lable.ajaxSubmitForm = function(){
			if(!form.validate("#editForm")){
				return;
			}
			var data = form.getJSONObject("editForm");
			ajax.remoteCall("${ctxPath}/servlet/crowdPack?action=saveCrowdPackLable",data,function(result) { 
					if(result.state == 1){
						window.parent.layer.closeAll();
						window.parent.layer.msg(result.msg,{icon: 1});
						window.parent.crowdPackList.search()
					}else{
						layer.alert(result.msg,{icon: 5});
					}
				}
			);
	 	}
	    
	   
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_form.jsp" %>