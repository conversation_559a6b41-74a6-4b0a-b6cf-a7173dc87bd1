package com.yunqu.cc.jobs.listener;

import java.util.ArrayList;
import java.util.List;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;

import org.apache.log4j.Logger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.DateUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yunqu.cc.jobs.base.CommonLogger;
import com.yunqu.cc.jobs.base.Constants;
import com.yunqu.cc.jobs.job.JobMgr;
import com.yunqu.cc.jobs.job.ThreadMgr;
import com.yunqu.cc.jobs.model.JobModel;


/**
 * 应用初始化 InitListener.java
 */
public class InitListener implements ServletContextListener
{

	protected Logger logger = CommonLogger.logger;


	public void contextInitialized(ServletContextEvent arg0) {

		//检查配置
		if(!checkConfig()){
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 定时任务管理模块启动失败!!!");
			return;
		}

		//初始化任务
		initJob();

		String runJob = ConfigUtil.getString(Constants.APP_NAME, "RUN_JOB");
		if(StringUtils.isBlank(runJob) || DictConstants.DICT_SY_YN_N.equals(runJob)){
			logger.info(CommonUtil.getClassNameAndMethod(this)+" 未开启定时任务配置,不启动定时任务!");
			return;
		}

		//启动线程池
		ThreadMgr.getInstance().start();

		//启动任务
		JobMgr.getInstance().startJob();

		logger.info(CommonUtil.getClassNameAndMethod(this)+"定时任务管理模块启动成功!");

	}


	/**
	 * 检查配置项
	 * @return
	 */
	private boolean checkConfig() {
		//初始化相关数据
		return true;
	}

	/**
	 * 初始化所有的定时任务
	 * @return
	 */
	private boolean initJob(){
		List<JobModel> list = new ArrayList<JobModel>();
		JobModel job1 = new JobModel();
		job1.setReqMain(ServiceID.EMPLOYEEMGR_INTEFACE);
		job1.setReqParam(ServiceCommand.EMPLOYEEMGR_UPDATE_USER_INFO+";"
				+ServiceCommand.EMPLOYEEMGR_ST_EMPLOYEE_LOG);
		job1.setName("员工画像定时任务-按天");
		job1.setJobDesc("每天凌晨系统更新用户状态、入职时长、汇总成长记录");
		job1.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job1.setExecRule("02:00:00");
		job1.setExecDelay(0);
		job1.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job1.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job1);

		JobModel job1_1 = new JobModel();
		job1_1.setReqMain(ServiceID.SLINFOGW_INTERFACE);
		job1_1.setReqParam(ServiceCommand.SLINFOGW_SYNC_TRAINING_RECORD+";"
				+ServiceCommand.SLINFOGW_SYNC_TEST_SCORE_RECORD);
		job1_1.setName("知识库数据同步任务-按天");
		job1_1.setJobDesc("每天凌晨系统从知识库同步培训记录、考试记录");
		job1_1.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job1_1.setExecRule("02:00:00");
		job1_1.setExecDelay(0);
		job1_1.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job1_1.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job1_1);

		JobModel job1_2 = new JobModel();
		job1_2.setReqMain(ServiceID.CSSGW_INTERFACE);
		job1_2.setReqParam(ServiceCommand.CSSGW_SYNC_CSS_ORG+";"
				+ServiceCommand.CSSGW_SYNC_CSS_ROLE+";"
				+ServiceCommand.CSSGW_SYNC_CSS_USER);
		job1_2.setName("售后组织架构同步定时任务-按天");
		job1_2.setJobDesc("每天凌晨系统从售后同步售后的组织架构、角色、人员");
		job1_2.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job1_2.setExecRule("02:00:00");
		job1_2.setExecDelay(0);
		job1_2.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job1_2.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job1_2);

		JobModel job2 = new JobModel();
		job2.setReqMain(ServiceID.SMSGW_INTEFACE);
		job2.setReqParam(ServiceCommand.SMS_PUSH);
		job2.setName("短信发送任务-按秒");
		job2.setJobDesc("每隔几秒扫描待发送的短信进行发送");
		job2.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job2.setExecRule("5");
		job2.setExecDelay(60);
		job2.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job2.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job2);


		JobModel job3 = new JobModel();
		job3.setReqMain(ServiceID.PERFORMEXAMGW_INTEFACE);
		job3.setReqParam(ServiceCommand.PERFORMEXAM_SYNC_ZX_CONTENT);
		job3.setName("质检任务(同步语音转写内容等)-按分钟");
		job3.setJobDesc("每隔几分钟处理一次质检任务,如同步语音转写内容等");
		job3.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job3.setExecRule("5");
		job3.setExecDelay(80);
		job3.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job3.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job3);

		JobModel job3_3 = new JobModel();
		job3_3.setReqMain(ServiceID.PERFORMEXAMGW_INTEFACE);
		job3_3.setReqParam(ServiceCommand.PERFORMEXAM_SYNC_VOICE);
		job3_3.setName("质检任务(同步获取录音等)-按秒");
		job3_3.setJobDesc("每隔30秒同步一次录音等");
		job3_3.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job3_3.setExecRule("30");
		job3_3.setExecDelay(80);
		job3_3.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job3_3.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job3_3);

		JobModel job3_2 = new JobModel();
		job3_2.setReqMain(ServiceID.PERFORMEXAMGW_INTEFACE);
		job3_2.setReqParam(ServiceCommand.PERFORMEXAM_TAG_SESSION_RECORD+";"
				+ServiceCommand.PERFORMEXAM_GEN_QC_RECORD+";"
				+ServiceCommand.PERFORMEXAM_AUTO_QC);
		job3_2.setName("质检任务(全媒体打标签,生成质检记录,自动质检,同步语音满意度等)-按分钟");
		job3_2.setJobDesc("每隔几分钟处理一次质检任务,如全媒体打标签,生成质检记录,自动质检,同步语音满意度等");
		job3_2.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job3_2.setExecRule("5");
		job3_2.setExecDelay(70);
		job3_2.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job3_2.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job3_2);



		JobModel job3_1 = new JobModel();
		job3_1.setReqMain(ServiceID.PERFORMEXAMGW_INTEFACE);
		job3_1.setReqParam(ServiceCommand.PERFORMEXAM_SEND_SATISFY_SMS);
		job3_1.setName("质检任务(发送满意度短信)-按分钟");
		job3_1.setJobDesc("每隔几分钟处理一次质检任务,发送满意度短信");
		job3_1.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job3_1.setExecRule("1");
		job3_1.setExecDelay(85);
		job3_1.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job3_1.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job3_1);

		JobModel job4 = new JobModel();
		job4.setReqMain(ServiceID.PERFORMEXAMGW_INTEFACE);
		job4.setReqParam(ServiceCommand.PERFORMEXAM_STAT_BY_DAY);
		job4.setName("质检任务(同步AMI关键字)-按天");
		job4.setJobDesc("每天凌晨处理一次质检任务,如同步AMI关键字等");
		job4.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job4.setExecRule("02:30:30");
		job4.setExecDelay(60);
		job4.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job4.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job4);


		JobModel job5 = new JobModel();
		job5.setReqMain(ServiceID.STATGW_INTEFACE);
		job5.setReqParam(ServiceCommand.STATGW_STAT_BY_DAY);
		job5.setName("按天统计坐席、质检员、班组、区域、队列、渠道等的工作量");
		job5.setJobDesc("每天对坐席、质检员、班组、区域、队列、渠道的数据进行汇总统计");
		job5.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job5.setExecRule("03:00:00");
		job5.setExecDelay(60);
		job5.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job5);

		JobModel job5_1 = new JobModel();
		job5_1.setReqMain(ServiceID.STATGW_INTEFACE);
		job5_1.setReqParam("STATGW_STAT_BY_MIN");
		job5_1.setName("每30分钟执行一次统计坐席、队列、渠道的话务量");
		job5_1.setJobDesc("每30分钟执行一次统计坐席、队列、渠道的话务量");
		job5_1.setExecType(DictConstants.EXEC_TYPE_BY_HALF_HOUR);
		job5_1.setExecRule("15");
		job5_1.setExecDelay(0);
		job5_1.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5_1.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job5_1);

		JobModel job5_2 = new JobModel();
		job5_2.setReqMain(ServiceID.STATGW_INTEFACE);
		job5_2.setReqParam("STATGW_STAT_BY_HOUR");
		job5_2.setName("每小时执行一次统计坐席、区域、队列、渠道的话务量");
		job5_2.setJobDesc("每小时执行一次坐席、区域、队列的、渠道话务量");
		job5_2.setExecType(DictConstants.EXEC_TYPE_BY_HOUR);
		job5_2.setExecRule("15:00");
		job5_2.setExecDelay(0);
		job5_2.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5_2.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job5_2);

		JobModel job5_3 = new JobModel();
		job5_3.setReqMain(ServiceID.STATGW_INTEFACE);
		job5_3.setReqParam("STATGW_STAT_AT10");
		job5_3.setName("每30分住统计坐席的专项数据");
		job5_3.setJobDesc("每30分钟统计坐席的专项数据");
		job5_3.setExecType(DictConstants.EXEC_TYPE_BY_HALF_HOUR);
		job5_3.setExecRule("5"); //延迟5分钟，与STATGW_STAT_BY_MIN任务隔开间隔
		job5_3.setExecDelay(0);
		job5_3.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5_3.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job5_3);

		JobModel job5_4 = new JobModel();
		job5_4.setReqMain(ServiceID.STATGW_INTEFACE);
		job5_4.setReqParam("STATGW_STAT_BF180");
		job5_4.setName("按天统计180天内的事业部工作量");
		job5_4.setJobDesc("每天对180天内的事业部数据进行汇总统计");
		job5_4.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job5_4.setExecRule("03:00:00");
		job5_4.setExecDelay(0);
		job5_4.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5_4.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job5_4);


		JobModel job5_5 = new JobModel();
		job5_5.setReqMain(ServiceID.STATGW_INTEFACE);
		job5_5.setReqParam("STATGW_STAT_JX30");
		job5_5.setName("按天统计30天内的坐席绩效历史数据");
		job5_5.setJobDesc("按天统计30天内的坐席绩效历史数据");
		job5_5.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job5_5.setExecRule("02:00:00");
		job5_5.setExecDelay(0);
		job5_5.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job5_5.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job5_5);

		JobModel job6 = new JobModel();
		job6.setReqMain(ServiceID.BIGDATAGW_SENTIMENT_INTEFACE);
		job6.setReqParam(ServiceCommand.BIGDATAGW_SYNC_SENTIMENT);
		job6.setName("舆情信息同步任务-按分钟");
		job6.setJobDesc("每隔10分钟从大数据系统同步一次舆情信息");
		job6.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job6.setExecRule("5");
		job6.setExecDelay(60);
		job6.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job6.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job6);


		JobModel job7 = new JobModel();
		job7.setReqMain(ServiceID.EMAIL_INTERFACE);
		job7.setReqParam(ServiceCommand.SEND_EMAIL);
		job7.setName("邮件发送任务-按分钟");
		job7.setJobDesc("每隔1分钟扫描一下待发送的邮件并进行发送");
		job7.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job7.setExecRule("1");
		job7.setExecDelay(60);
		job7.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job7.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job7);


		JobModel job8 = new JobModel();
		job8.setReqMain(ServiceID.SENTIMENT_INTEFACE);
		job8.setReqParam(ServiceCommand.SENTIMENT_SYNC_SEND_RESULT);
		job8.setName("定时同步舆情工单短信、邮件发送状态");
		job8.setJobDesc("将舆情工单的短信、邮件发送状态同步到发送日志里");
		job8.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job8.setExecRule("5");
		job8.setExecDelay(60);
		job8.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job8.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job8);


		JobModel job9 = new JobModel();
		job9.setReqMain("ZXFUN-ST-BY-DAY");
		job9.setReqParam(ServiceCommand.ZXFUN_STAT_STATUS_BY_DAY);
		job9.setName("每天早上10点清理过期专项数据");
		job9.setJobDesc("每天早上10点清理过期专项数据");
		job9.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job9.setExecRule("10:00:00");
		job9.setExecDelay(0);
		job9.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job9.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job9);


		JobModel job10 = new JobModel();
		job10.setReqMain("ZXFUN-ST-BY-DAY");
		job10.setReqParam(ServiceCommand.ZXFUN_STAT_BY_HOUR);
		job10.setName("每小时进行专项数据统计");
		job10.setJobDesc("每小时进行专项数据统计");
		job10.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job10.setExecRule("1");
		job10.setExecDelay(0);
		job10.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job10.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job10);


		JobModel job11 = new JobModel();
		job11.setReqMain(ServiceID.LEAVE_MSG_GW_INTEFACE);
		job11.setReqParam( ServiceCommand.SYNC_GENESYS_LEAVEMSG+";"+ServiceCommand.SYNC_GENESYS_CALLLOSS+";"+ServiceCommand.SYNC_OFFLIINE_MSG);
		job11.setName("每隔几分钟进行留言、未接来电、离线消息同步处理");
		job11.setJobDesc("每隔几分钟进行留言、未接来电、离线消息同步处理");
		job11.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job11.setExecRule("5");
		job11.setExecDelay(0);
		job11.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job11.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job11);

		JobModel job12 = new JobModel();
		job12.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job12.setReqParam(ServiceCommand.SCHEDULING_REST_START);
		job12.setName("每月一号凌晨1点初始化员工月度剩休信息");
		job12.setJobDesc("每月一号凌晨1点初始化员工月度剩休信息");
		job12.setExecType(DictConstants.EXEC_TYPE_BY_MONTH);
		job12.setExecRule("01 01:00:00");
		job12.setExecDelay(0);
		job12.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job12.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job12);

		JobModel job13 = new JobModel();
		job13.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job13.setReqParam(ServiceCommand.SCHEDULING_HOLIDAY_ADD);
		job13.setName("每天凌晨2点添加节假日可休信息");
		job13.setJobDesc("每天凌晨2点添加节假日可休信息");
		job13.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job13.setExecRule("02:00:00");
		job13.setExecDelay(0);
		job13.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job13.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job13);

		JobModel job14 = new JobModel();
		job14.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job14.setReqParam(ServiceCommand.SCHEDULING_BC_REST_ADD_1);
		job14.setName("每天凌晨3点查询班次修改月度剩休信息");
		job14.setJobDesc("每天凌晨3点查询班次修改月度剩休信息");
		job14.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job14.setExecRule("03:00:00");
		job14.setExecDelay(0);
		job14.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job14.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job14);

		JobModel job15 = new JobModel();
		job15.setReqMain(ServiceID.NEWORDER);
		job15.setReqParam(ServiceCommand.REVIST_DISTRIBUTION);
		job15.setName("每分钟自动分配回访单");
		job15.setJobDesc("每分钟自动分配回访单");
		job15.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job15.setExecRule("1");
		job15.setExecDelay(0);
		job15.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job15.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job15);

		JobModel job16 = new JobModel();
		job16.setReqMain(ServiceID.NEWORDER);
		job16.setReqParam(ServiceCommand.REVIST_ORDER_CLEAN);
		job16.setName("每天凌晨1点自动回收回访单");
		job16.setJobDesc("每天凌晨1点自动回收回访单");
		job16.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job16.setExecRule("01:00:00");
		job16.setExecDelay(0);
		job16.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job16.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job16);

		JobModel job17 = new JobModel();
		job17.setReqMain(ServiceID.SMS_BIRTHDAY);
		job17.setReqParam(ServiceCommand.SMS_BIRTHDAY);
		job17.setName("每天10点自动扫描生日用户");
		job17.setJobDesc("每天10点自动扫描生日用户进行生日短信发送");
		job17.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job17.setExecRule("09:00:00");
		job17.setExecDelay(0);
		job17.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job17.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job17);

		JobModel job18 = new JobModel();
		job18.setReqMain(ServiceID.STATGW_INTEFACE);
		job18.setReqParam(ServiceCommand.PORTALASSESSSERVICE);
		job18.setName("每隔10分钟统计个人和团队首页指标");
		job18.setJobDesc("每隔10分钟统计个人和团队首页指标");
		job18.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job18.setExecRule("10");
		job18.setExecDelay(0);
		job18.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job18.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job18);

		JobModel job19 = new JobModel();
		job19.setReqMain(ServiceID.PERFORMANCE_INTEFACE);
		job19.setReqParam(ServiceCommand.JXTARGETSERVICE);
		job19.setName("每隔10分钟统计绩效指标");
		job19.setJobDesc("每隔10分钟统计绩效指标");
		job19.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job19.setExecRule("10");
		job19.setExecDelay(0);
		job19.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job19.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job19);

		JobModel job20 = new JobModel();
		job20.setReqMain("WEIBOMGR_INTERACT");
		job20.setReqParam("getMentions;getCommentMentions;getCommentToMe");
		job20.setName("每隔5分钟获取新浪微博数据");
		job20.setJobDesc("每隔5分钟获取新浪微博数据");
		job20.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job20.setExecRule("5");
		job20.setExecDelay(0);
		job20.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job20.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job20);

		JobModel job21 = new JobModel();
		job21.setReqMain("PLANNING-INTEFACE");
		job21.setReqParam("PLANNING_DECRYPT_DATA");
		job21.setName("企划通每5分钟查询临时表数据解密入库");
		job21.setJobDesc("企划通每5分钟查询临时表数据解密入库");
		job21.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job21.setExecRule("5");
		job21.setExecDelay(0);
		job21.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job21.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job21);

		JobModel job22 = new JobModel();
		job22.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job22.setReqParam(ServiceCommand.SCHEDULING_INIT_USER_CLASS);
		job22.setName("每天凌晨3点修改人员班级信息");
		job22.setJobDesc("每天凌晨3点修改人员班级信息");
		job22.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job22.setExecRule("03:00:00");
		job22.setExecDelay(0);
		job22.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job22.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job22);

		JobModel job23 = new JobModel();
		job23.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job23.setReqParam(ServiceCommand.SCHEDULING_REND_REST_START);
		job23.setName("每天凌晨2点考勤查询员工月度可休已休信息");
		job23.setJobDesc("每天凌晨2点考勤查询员工月度可休已休信息");
		job23.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job23.setExecRule("02:00:00");
		job23.setExecDelay(0);
		job23.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job23.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job23);

		JobModel job24 = new JobModel();
		job24.setReqMain(ServiceID.NEWORDER_CONTACT);
		job24.setReqParam(ServiceCommand.NEWORDER_NOTICE_AGENT);
		job24.setName("每两个小时通知一次坐席未完成的暂存单");
		job24.setJobDesc("每两个小时通知一次坐席未完成的暂存单");
		job24.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job24.setExecRule("2");
		job24.setExecDelay(0);
		job24.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job24.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job24);


		JobModel job25 = new JobModel();
		job25.setReqMain("JOBS_DATA_OLD");
		job25.setReqParam("baseDataOld");
		job25.setName("每天凌晨3:30将一个月前不重要的数据进行删除");
		job25.setJobDesc("每天凌晨3:30将一个月前不重要的数据进行删除");
		job25.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job25.setExecRule("03:30:00");
		job25.setExecDelay(0);
		job25.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job25.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job25);


		JobModel job26 = new JobModel();
		job26.setReqMain("http://10.18.11.113:9060/genesys-stategw/servlet/state?action=SyncSkillInfo");
		job26.setReqParam("0");
		job26.setName("顺德地区的genesys监控每10分钟同步一次技能组信息");
		job26.setJobDesc("顺德地区的genesys监控每10分钟同步一次技能组信息");
		job26.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job26.setExecRule("10");
		job26.setExecDelay(0);
		job26.setType(DictConstants.JOB_TYPE_HTTP_INTEFACE);
		job26.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job26);


		JobModel job27 = new JobModel();
		job27.setReqMain("http://10.18.11.123:9060/genesys-stategw/servlet/state?action=SyncSkillInfo");
		job27.setReqParam("0");
		job27.setName("合肥地区的genesys监控每10分钟同步一次技能组信息");
		job27.setJobDesc("合肥地区的genesys监控每10分钟同步一次技能组信息");
		job27.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job27.setExecRule("10");
		job27.setExecDelay(0);
		job27.setType(DictConstants.JOB_TYPE_HTTP_INTEFACE);
		job27.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job27);


		JobModel job28 = new JobModel();
		job28.setReqMain("http://10.18.11.113:9060/genesys-stategw/servlet/AsyncOnlineAgentServlet?action=SyncAgentStatus");
		job28.setReqParam("0");
		job28.setName("顺德地区的在线坐席每5分钟同步一次PING_TIME");
		job28.setJobDesc("顺德地区的在线坐席每5分钟同步一次PING_TIME");
		job28.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job28.setExecRule("5");
		job28.setExecDelay(0);
		job28.setType(DictConstants.JOB_TYPE_HTTP_INTEFACE);
		job28.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job28);


		JobModel job29 = new JobModel();
		job29.setReqMain("http://10.18.11.123:9060/genesys-stategw/servlet/AsyncOnlineAgentServlet?action=SyncAgentStatus");
		job29.setReqParam("0");
		job29.setName("合肥地区的在线坐席每5分钟同步一次PING_TIME");
		job29.setJobDesc("合肥地区的在线坐席每5分钟同步一次PING_TIME");
		job29.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job29.setExecRule("5");
		job29.setExecDelay(0);
		job29.setType(DictConstants.JOB_TYPE_HTTP_INTEFACE);
		job29.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job29);

		JobModel job30 = new JobModel();
		job30.setReqMain(ServiceID.PERFORMEXAMWEB_INTERFACE);
		job30.setReqParam(ServiceCommand.TASK_AUTO_RECOVERY);
		job30.setName("质检任务每天晚上11点自动回收前一天的任务");
		job30.setJobDesc("质检任务每天晚上11点自动回收前一天的任务");
		job30.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job30.setExecRule("23:00:00");
		job30.setExecDelay(0);
		job30.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job30.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job30);

		JobModel job32 = new JobModel();
		job32.setReqMain(ServiceID.SENTIMENT_INTEFACE);
		job32.setReqParam(ServiceCommand.SENTIMENT_ORDER_STATUS);
		job32.setName("每隔30分钟更新舆情工单状态");
		job32.setJobDesc("每隔30分钟更新舆情工单状态");
		job32.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job32.setExecRule("30");
		job32.setExecDelay(0);
		job32.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job32.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job32);

		JobModel job33 = new JobModel();
		job33.setReqMain(ServiceID.SYS_MARKET);
		job33.setReqParam(ServiceCommand.MARKET_DISTRIBUTION);
		job33.setName("每隔1分钟分配自动大数据资料");
		job33.setJobDesc("每隔1分钟自动分配大数据资料");
		job33.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job33.setExecRule("1");
		job33.setExecDelay(0);
		job33.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job33.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job33);
		saveJob(list);

		JobModel job35 = new JobModel();
		job35.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job35.setReqParam(ServiceCommand.SCHEDULING_SYNC_LATE_DATA);
		job35.setName("每5分钟同步人脸识别考勤迟到数据");
		job35.setJobDesc("每5分钟同步人脸识别迟到考勤数据");
		job35.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job35.setExecRule("5");
		job35.setExecDelay(0);
		job35.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job35.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job35);

		JobModel job36 = new JobModel();
		job36.setReqMain(ServiceID.SCHEDULING_INTEFACE);
		job36.setReqParam(ServiceCommand.SCHEDULING_SYNC_LEAVE_EARLY_DATA);
		job36.setName("每20分钟同步人脸识别考勤早退数据");
		job36.setJobDesc("每20分钟同步人脸识别早退考勤数据");
		job36.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job36.setExecRule("5");
		job36.setExecDelay(0);
		job36.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job36.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job36);

		JobModel job37 = new JobModel();
		job37.setReqMain(ServiceID.NEWORDER);
		job37.setReqParam(ServiceCommand.SYN_SENTIMENT_ORDER);
		job37.setName("每天凌晨1点自动同步舆情汇总数据");
		job37.setJobDesc("每天凌晨1点自动同步舆情汇总数据");
		job37.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job37.setExecRule("01:00:00");
		job37.setExecDelay(0);
		job37.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job37.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job37);

		JobModel job38 = new JobModel();
		job38.setReqMain("CSSGW-GENESYS");
		job38.setReqParam("JOB_DEL_GENESYS_CALLBACK_01");
		job38.setName("定时任务删除待回访数据19:45");
		job38.setJobDesc("每天19:45开始执行");
		job38.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job38.setExecRule("19:45:00");
		job38.setExecDelay(0);
		job38.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job38.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job38);

		JobModel job39 = new JobModel();
		job39.setReqMain("CSSGW-GENESYS");
		job39.setReqParam("JOB_DEL_GENESYS_CALLBACK_02");
		job39.setName("定时任务删除待回访数据20:06");
		job39.setJobDesc("每天20:06开始执行");
		job39.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job39.setExecRule("20:06:00");
		job39.setExecDelay(0);
		job39.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job39.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job39);


		JobModel job40 = new JobModel();
		job40.setReqMain("SIDEBAR_VOICEENT");
		job40.setReqParam("voiceEntUpdate");
		job40.setName("每5分钟查一次结束时间为空的数据");
		job40.setJobDesc("每5分钟查一次结束时间为空的数据");
		job40.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job40.setExecRule("5");
		job40.setExecDelay(0);
		job40.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job40.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job40);

		JobModel job41 = new JobModel();
		job41.setReqMain("CRON-MD_AUTOMATIC_REGULATE_TIME");
		job41.setReqParam("AUTOMATIC_REGULATE");
		job41.setName("每1小时行接通率调整的数据");
		job41.setJobDesc("每1小时行接通率调整的数据");
		job41.setExecType(DictConstants.EXEC_TYPE_BY_HOUR);
		job41.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job41.setExecRule("1");
		job41.setExecDelay(0);
		job41.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job41.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job41);

		JobModel job42 = new JobModel();
		job42.setReqMain("empClockService");
		job42.setReqParam("synAttendanceRecord");
		job42.setName("每五分钟同步实时考勤数据");
		job42.setJobDesc("每五分钟同步实时考勤数据");
		job42.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job42.setExecRule("5");
		job42.setExecDelay(0);
		job42.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job42.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job42);


		JobModel job43 = new JobModel();
		job43.setReqMain("empClockService");
		job43.setReqParam("updateEmpClockRecord");
		job43.setName("每十分钟更新考勤数据");
		job43.setJobDesc("每十分钟更新考勤数据");
		job43.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job43.setExecRule("10");
		job43.setExecDelay(0);
		job43.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job43.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job43);

		JobModel job44 = new JobModel();
		job44.setReqMain("VOC_UNLOADING");
		job44.setReqParam("vocUnloading");
		job44.setName("每天凌晨3:30将执行一次VOC录音");
		job44.setJobDesc("每天凌晨3:30将执行一次VOC录音");
		job44.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job44.setExecRule("03:30:00");
		job44.setExecDelay(0);
		job44.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job44.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job44);

		JobModel job45 = new JobModel();
		job45.setReqMain("ECM_ORDERINFO");
		job45.setReqParam("ECM_ORDERINFO");
		job45.setName("每天凌晨2点自动同步电商渠道店铺订单同步接口");
		job45.setJobDesc("每天凌晨2点自动同步电商渠道店铺订单同步接口");
		job45.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job45.setExecRule("02:00:00");
		job45.setExecDelay(0);
		job45.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job45.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job45);


		JobModel job46 = new JobModel();
		job46.setReqMain(ServiceID.NEWORDER);
		job46.setReqParam("ROBOT_REVIST_CALL");
		job46.setName("每10秒推送需要机器人外呼数据");
		job46.setJobDesc("每10秒推送需要机器人外呼数据");
		job46.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job46.setExecRule("10");
		job46.setExecDelay(0);
		job46.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job46.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job46);//暂时不能使用

		JobModel job47 = new JobModel();
		job47.setReqMain(ServiceID.NEWORDER);
		job47.setReqParam("ROBOT_REVIST_RESULT");
		job47.setName("每分钟定时处理智能外呼已经回访完成的工单");
		job47.setJobDesc("每分钟定时处理智能外呼已经回访完成的工单");
		job47.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job47.setExecRule("1");
		job47.setExecDelay(0);
		job47.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job47.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job47);

		JobModel job34 = new JobModel();
		job34.setReqMain(ServiceID.SYS_MARKET);
		job34.setReqParam(ServiceCommand.MARKET_SMS1);
		job34.setName("每天8:30点发送短信提醒");
		job34.setJobDesc("每天8:30点发送短信提醒");
		job34.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job34.setExecRule("08:30:00");
		job34.setExecDelay(0);
		job34.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job34.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job34);

		JobModel job48 = new JobModel();
		job48.setReqMain(ServiceID.SYS_MARKET);
		job48.setReqParam(ServiceCommand.MARKET_SMS2);
		job48.setName("每天18:30点发送短信提醒");
		job48.setJobDesc("每天18:30点发送短信提醒");
		job48.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job48.setExecRule("18:30:00");
		job48.setExecDelay(0);
		job48.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job48.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job48);

		JobModel job49 = new JobModel();
		job49.setReqMain(ServiceID.SYS_MARKET);
		job49.setReqParam(ServiceCommand.MARKET_SMS);
		job49.setName("每天11点发送短信提醒");
		job49.setJobDesc("每天11点发送短信提醒");
		job49.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job49.setExecRule("11:00:00");
		job49.setExecDelay(0);
		job49.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job49.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job49);

		JobModel job50 = new JobModel();
		job50.setReqMain("PLANNING-INTEFACE");
		job50.setReqParam("PLANNING-TIME-TO-DISTRIBUTE");
		job50.setName("企划通每分钟自动分配回访单");
		job50.setJobDesc("企划通每分钟自动分配回访单");
		job50.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job50.setExecRule("1");
		job50.setExecDelay(0);
		job50.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job50.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job50);

		JobModel job51 = new JobModel();
		job51.setReqMain("ONLINE_JOB");
		job51.setReqParam("UPDATE_CHANNEL_KEY");
		job51.setName("每分钟定时根据渠道配置的更改按键");
		job51.setJobDesc("每分钟定时根据渠道配置的更改按键");
		job51.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job51.setExecRule("1");
		job51.setExecDelay(0);
		job51.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job51.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job51);

		JobModel job52 = new JobModel();
		job52.setReqMain("employeeMgr");
		job52.setReqParam("cleanRole");
		job52.setName("每天早上2点定时清空离职人员角色");
		job52.setJobDesc("每天早上2点定时清空离职人员角色");

		job52.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job52.setExecRule("02:00:00");
		job52.setExecDelay(0);
		job52.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job52.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job52);

		JobModel job53 = new JobModel();
		job53.setReqMain("VIDEO-INTEFACE");
		job53.setReqParam("fmtVideoRecord");
		job53.setName("视频文件转码任务-按分钟");
		job53.setJobDesc("每隔5分钟定时转换遗漏未转码视频文件");

		job53.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job53.setExecRule("5");
		job53.setExecDelay(10);
		job53.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job53.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job53);

		JobModel job54 = new JobModel();
		job54.setReqMain("IOT-INTEFACE");
		job54.setReqParam("IOT-TIME-TO-CLEAN");
		job54.setName("每天凌晨1点自动回收IOT回访任务单");
		job54.setJobDesc("每天凌晨1点自动回收IOT回访任务单");
		job54.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job54.setExecRule("01:00:00");
		job54.setExecDelay(0);
		job54.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job54.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job54);

		JobModel job55 = new JobModel();
		job55.setReqMain("IOT-INTEFACE");
		job55.setReqParam("IOT-TIME-TO-DISTRIBUTE");
		job55.setName("每分钟自动分配IOT任务回访单");
		job55.setJobDesc("每分钟自动分配IOT任务回访单");
		job55.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job55.setExecRule("1");
		job55.setExecDelay(0);
		job55.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job55.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job55);

		JobModel job56 = new JobModel();
		job56.setReqMain("PEAK-END-JOB");
		job56.setReqParam("PEAKEND-TIME-TO-WARN");
		job56.setName("每五分钟峰终事业部积分统计并检测预警");
		job56.setJobDesc("每五分钟峰终事业部积分统计并检测预警");
		job56.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job56.setExecRule("5");
		job56.setExecDelay(0);
		job56.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job56.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job56);

		JobModel job57 = new JobModel();
		job57.setReqMain("PEAKEND-DISTRIBUTE-JOB");
		job57.setReqParam("PEAKEND-TIME-TO-DISTRIBUTE");
		job57.setName("每分钟自动分配峰终任务回访单");
		job57.setJobDesc("每分钟自动分配峰终任务回访单");
		job57.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job57.setExecRule("1");
		job57.setExecDelay(0);
		job57.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job57.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job57);

		JobModel job58 = new JobModel();
		job58.setReqMain("AS-REVISIT-SERVICE-INTEFACE");
		job58.setReqParam("AS_REVISIT_MANUAL_DISTRIBUTE");
		job58.setName("每分钟自动分配主动服务人工回访单");
		job58.setJobDesc("每分钟自动分配主动服务人工回访单");
		job58.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job58.setExecRule("1");
		job58.setExecDelay(0);
		job58.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job58.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job58);

		JobModel job59 = new JobModel();
		job59.setReqMain("AS-REVISIT-SERVICE-INTEFACE");
		job59.setReqParam("AS_REVISIT_ROBOT_DISTRIBUTE");
		job59.setName("每分钟自动分配主动服务机器人回访单");
		job59.setJobDesc("每分钟自动分配主动服务机器人回访单");
		job59.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job59.setExecRule("1");
		job59.setExecDelay(0);
		job59.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job59.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job59);

		JobModel job60 = new JobModel();
		job60.setReqMain("AS-REVISIT-SERVICE-INTEFACE");
		job60.setReqParam("AS_REVISIT_RECYCLE_ROBOTDATA");
		job60.setName("每30s处理主动服务机器人异常回访单");
		job60.setJobDesc("每30s处理主动服务机器人异常回访单");
		job60.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job60.setExecRule("30");
		job60.setExecDelay(0);
		job60.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job60.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job60);

		JobModel job61 = new JobModel();
		job61.setReqMain("WECHAT-WORK-STRATEGY-JOB-INTEFACE");
		job61.setReqParam("WECHAT-WORK-STRATEGY-JOB-INTEFACE");
		job61.setName("每1分钟处理主动服务企微策略");
		job61.setJobDesc("每1分钟处理主动服务企微策略");
		job61.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job61.setExecRule("1");
		job61.setExecDelay(0);
		job61.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job61.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job61);

		JobModel job62 = new JobModel();
		job62.setReqMain("WECHAT-WORK-RESULT-JOB-INTEFACE");
		job62.setReqParam("WECHAT-WORK-RESULT-JOB-INTEFACE");
		job62.setName("每天凌晨1点处理主动服务企微群发结果");
		job62.setJobDesc("每天凌晨1点处理主动服务企微群发结果");
		job62.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job62.setExecRule("01:00:00");
		job62.setExecDelay(0);
		job62.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job62.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job62);

		JobModel job63 = new JobModel();
		job63.setReqMain("AS-DRAINAGE-USER-DATA");
		job63.setReqParam("AS-DRAINAGE-USER-DATA");
		job63.setName("每3分钟执行将引流数据分配给企业微信坐席");
		job63.setJobDesc("每3分钟执行将引流数据分配给企业微信坐席");
		job63.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job63.setExecRule("3");
		job63.setExecDelay(0);
		job63.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job63.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job63);

		/*JobModel job64 = new JobModel();
		job64.setReqMain("VOC-HEART-BEAT");
		job64.setReqParam("VOC-HEART-BEAT");
		job64.setName("每5秒执行检测cc-voc-engine服务心跳");
		job64.setJobDesc("每5秒执行检测cc-voc-engine服务心跳");
		job64.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job64.setExecRule("5");
		job64.setExecDelay(0);
		job64.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job64.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job64);

		JobModel job65 = new JobModel();
		job65.setReqMain("VOC-ASYNC-ASR-STOCK");
		job65.setReqParam("VOC-ASYNC-ASR-STOCK");
		job65.setName("存量录音同步请求ASR转写");
		job65.setJobDesc("存量录音同步请求ASR转写");
		job65.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job65.setExecRule("1");
		job65.setExecDelay(16);
		job65.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job65.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job65);

		JobModel job66 = new JobModel();
		job66.setReqMain("VOC-ASYNC-ASR");
		job66.setReqParam("VOC-ASYNC-ASR");
		job66.setName("执行每小时录音转写任务执行器");
		job66.setJobDesc("执行每小时录音转写任务执行器");
		job66.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job66.setExecRule("1");
		job66.setExecDelay(0);
		job66.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job66.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job66);*/

		JobModel job67 = new JobModel();
		job67.setReqMain("ACTIVE-SERVICE-INTEFACE");
		job67.setReqParam("refreshData");
		job67.setName("ivr策略定时任务-按天");
		job67.setJobDesc("每天刷新ivr策略缓存");
		job67.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job67.setExecRule("02:00:00");
		job67.setExecDelay(0);
		job67.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job67.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job67);

		JobModel job68 = new JobModel();
		job68.setReqMain("ACTIVE-SERVICE-DDY-INTEFACE");
		job68.setReqParam("groupDetailSync");
		job68.setName("主动服务-每天早上6点kafak同步地动仪人群包明细");
		job68.setJobDesc("主动服务-每天早上6点kafak同步地动仪人群包明细");
		job68.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job68.setExecRule("06:00:00");
		job68.setExecDelay(0);
		job68.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job68.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job68);

		JobModel job69 = new JobModel();
		job69.setReqMain("ACTIVE-SERVICE-DDY-INTEFACE");
		job69.setReqParam("saveFailData");
		job69.setName("每天凌晨1点重新同步解密失败的人群包明细");
		job69.setJobDesc("每天凌晨1点重新同步解密失败的人群包明细");
		job69.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job69.setExecRule("01:00:00");
		job69.setExecDelay(0);
		job69.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job69.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job69);

		JobModel job70 = new JobModel();
		job70.setReqMain("ACTIVE-SERVICE-CROWD-INTEFACE");
		job70.setReqParam("dayActiveServiceStat");
		job70.setName("每天凌晨2点汇总统计主动服务各渠道服务埋点数据");
		job70.setJobDesc("每天凌晨2点汇总统计主动服务各渠道服务埋点数据");
		job70.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job70.setExecRule("02:00:00");
		job70.setExecDelay(0);
		job70.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job70.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job70);

		JobModel job71 = new JobModel();
		job71.setReqMain("CC-VOC-SERVICE");
		job71.setReqParam("vocCallRecord");
		job71.setName("每天凌晨1点同步VOC通话记录表");
		job71.setJobDesc("每天凌晨1点同步VOC通话记录表");
		job71.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job71.setExecRule("01:00:00");
//		job71.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
//		job71.setExecRule("60");
		job71.setExecDelay(0);
		job71.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job71.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job71);

		JobModel job72 = new JobModel();
		job72.setReqMain("CC-VOC-SERVICE");
		job72.setReqParam("vocRobotCallRecord");
		job72.setName("每天凌晨0点同步智能机器人数据到VOC通话记录表");
		job72.setJobDesc("每天凌晨0点同步智能机器人数据到VOC通话记录表");
		job72.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job72.setExecRule("00:01:00");
//		job72.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
//		job72.setExecRule("60");
		job72.setExecDelay(0);
		job72.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job72.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job72);

		JobModel job73 = new JobModel();
		job73.setReqMain("VOC-TIMMING-TASK-SERVICE");
		job73.setReqParam("vocCallRecord");
		job73.setName("每天上午11点执行VOC定时搜索任务");
		job73.setJobDesc("每天上午11点执行VOC定时搜索任务");
		job73.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job73.setExecRule("11:01:00");
		job73.setExecDelay(0);
		job73.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job73.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job73);
		
		JobModel job74 = new JobModel();
		job74.setReqMain("ACTIVE-SERVICE-CROWD-INTEFACE");
		job74.setReqParam("updateTriggerSyncInfo");
		job74.setName("每5分钟执行定时更新触发器推送到人群包的同步时间和用户数量");
		job74.setJobDesc("每5分钟执行定时更新触发器推送到人群包的同步时间和用户数量");
		job74.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job74.setExecRule("5");
		job74.setExecDelay(0);
		job74.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job74.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job74);
		
		JobModel job75 = new JobModel();
		job75.setReqMain("CC-VOC-SERVICE-INTEFACE");
		job75.setReqParam("vocDataStat");
		job75.setName("每5秒检测一次VOC问题单大屏数据统计");
		job75.setJobDesc("每5秒检测一次VOC问题单大屏数据统计");
		job75.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job75.setExecRule("5");
		job75.setExecDelay(0);
		job75.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job75.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job75);
		
		JobModel job76 = new JobModel();
		job76.setReqMain(ServiceID.MIXGW_ECM_INTEFACE);
		job76.setReqParam("synFlagshipStore");
		job76.setName("每小时同步旗舰店信息-新(对接美云销)");
		job76.setJobDesc("每小时同步旗舰店信息-新(对接美云销)");
		job76.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job76.setExecRule("1");
		job76.setExecDelay(0);
		job76.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job76.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job76);
		
		JobModel job77 = new JobModel();
		job77.setReqMain("CRON-MD_RED_BLACK_LIST_TIMER");
		job77.setReqParam("updateExpireState");
		job77.setName("每天凌晨1点更新已失效的红黑名单数据状态");
		job77.setJobDesc("每天凌晨1点更新已失效的红黑名单数据状态");
		job77.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job77.setExecRule("01:00:00");
		job77.setExecDelay(0);
		job77.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job77.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job77);
		
		
		JobModel job78 = new JobModel();
		job78.setReqMain("CC_WECOM_MESSAGE_INTERFACE");
		job78.setReqParam("organizeWecomChatDetail");
		job78.setName("每天凌晨2点汇总企微会话，生成会话记录");
		job78.setJobDesc("每天凌晨2点汇总企微会话，生成会话记录");
		job78.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job78.setExecRule("02:00:00");
		job78.setExecDelay(0);
		job78.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job78.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job78);
		
		JobModel job79 = new JobModel();
		job79.setReqMain(ServiceID.NEWORDER_CONTACT);
		job79.setReqParam("orderTiming");
		job79.setName("每分钟提醒坐席工单待提醒数据");
		job79.setJobDesc("每分钟提醒坐席工单待提醒数据");
		job79.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job79.setExecRule("1");
		job79.setExecDelay(0);
		job79.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job79.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job79);
		
		JobModel job80 = new JobModel();
		job80.setReqMain(ServiceID.NEWORDER_CONTACT);
		job80.setReqParam("orderUpgradeDataService");
		job80.setName("刷新工单升级缓存任务-按天");
		job80.setJobDesc("刷新工单升级缓存任务-按天");
		job80.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job80.setExecRule("02:00:00");
		job80.setExecDelay(0);
		job80.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job80.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job80);
		
		JobModel job81 = new JobModel();
		job81.setReqMain("QC_GW_TASK_INF");
		job81.setReqParam("SendQualtiyInfo");
		job81.setName("推送自动质检--每小时");
		job81.setJobDesc("推送自动质检-每小时");
		job81.setExecType(DictConstants.EXEC_TYPE_BY_HOUR);
		job81.setExecRule("15:00");
		job81.setExecDelay(0);
		job81.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job81.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job81);

		JobModel job82 = new JobModel();
		job82.setReqMain("AS-ROBOT-HISTORY-CLEAR");
		job82.setReqParam("clearAutoRobotData");
		job82.setName("每天凌晨1点-主动服务AI自动外呼数据老化");
		job82.setJobDesc("每天凌晨1点-主动服务AI自动外呼数据老化");
		job82.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job82.setExecRule("01:00:00");
		job82.setExecDelay(0);
		job82.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job82.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job82);

		JobModel job83 = new JobModel();
		job83.setReqMain("AS-ROBOT-POST-RETURN");
		job83.setReqParam("ReturnAIData");
		job83.setName("推送ai外呼结果--每小时");
		job83.setJobDesc("推送ai外呼结果-每小时");
		job83.setExecType(DictConstants.EXEC_TYPE_BY_HOUR);
		job83.setExecRule("15:00");
		job83.setExecDelay(0);
		job83.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job83.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job83);
		
		JobModel job84 = new JobModel();
		job84.setReqMain("AS-REVISIT-SERVICE-INTEFACE");
		job84.setReqParam("AS_AUTO_REVISIT_ROBOT_DISTRIBUTE");
		job84.setName("每分钟自动分配主动服务机器人回访单");
		job84.setJobDesc("每分钟自动分配主动服务机器人回访单");
		job84.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job84.setExecRule("1");
		job84.setExecDelay(0);
		job84.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job84.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job84);
		
		
//		JobModel job85 = new JobModel();
//		job85.setReqMain(ServiceID.PERFORMEXAMGW_INTEFACE);
//		job85.setReqParam("newMideaAutoQcRecordTask");
//		job85.setName("2023全媒体质检任务重跑-凌晨3点");
//		job85.setJobDesc("2023全媒体质检任务重跑-凌晨3点");
//		job85.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
//		job85.setExecRule("03:00:00");
//		job85.setExecDelay(0);
//		job85.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
//		job85.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
//		list.add(job85);
		

		JobModel job86 = new JobModel();
		job86.setReqMain("IOT-ACTIVE-SERVICEINTEFACE");
		job86.setReqParam("aiTriggerGroupUserSync");
		job86.setName("每5分钟推送iot数据到人群包");
		job86.setJobDesc("每5分钟推送iot数据到人群包");
		job86.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job86.setExecRule("5");
		job86.setExecDelay(0);
		job86.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job86.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job86);
		
		JobModel job87 = new JobModel();
		job87.setReqMain("ONLINE_JOB");
		job87.setReqParam("sendSatisfyByOnline");
		job87.setName("每分钟统计未评价超时在线数据");
		job87.setJobDesc("每分钟统计未评价超时在线数据 ");
		job87.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job87.setExecRule("1");
		job87.setExecDelay(0);
		job87.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job87.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job87);
		
		JobModel job88 = new JobModel();
		job88.setReqMain("ONLINE_JOB");
		job88.setReqParam("mideaPushNoRead");
		job88.setName("每分钟发送未读公众号消息提醒");
		job88.setJobDesc("每分钟发送未读公众号消息提醒");
		job88.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job88.setExecRule("1");
		job88.setExecDelay(0);
		job88.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job88.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job88);
		
		
		JobModel job89 = new JobModel();
		job89.setReqMain(ServiceID.SMSGW_INTEFACE);
		job89.setReqParam("smsPush2");
		job89.setName("手工短信发送任务-按秒");
		job89.setJobDesc("每隔几秒扫描待发送的手工短信进行发送");
		job89.setExecType(DictConstants.EXEC_TYPE_INTEVAL_SECOND);
		job89.setExecRule("5");
		job89.setExecDelay(60);
		job89.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		//注意别复制这个
		job89.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job89);
		
		JobModel job90 = new JobModel();
		job90.setReqMain("CC_WECOM_COMMON_INTERFACE");
		job90.setReqParam("syncWechatUserInfo");
		job90.setName("每天两点同步前一天群成员信息");
		job90.setJobDesc("每天两点同步前一天群成员信息");
		job90.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job90.setExecRule("02:00:00");
		job90.setExecDelay(0);
		job90.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job90.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job90);

		JobModel job91 = new JobModel();
		job91.setReqMain("QC_GW_TASK_INF");
		job91.setReqParam("sendHisQualtiyInfo");
		job91.setName("ASR未转写的历史数据重推自动质检--每小时");
		job91.setJobDesc("ASR未转写的历史数据重推自动质检-每小时");
		job91.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job91.setExecRule("1");
		job91.setExecDelay(0);
		job91.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job91.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job91);

		//暂时注释，等实时质检上线再开启
		JobModel job92 = new JobModel();
		job92.setReqMain("ALARM-CFG-SERVICE");
		job92.setReqParam("refreshData");
		job92.setName("实时质检配置更新--每四小时");
		job92.setJobDesc("实时质检配置更新-每四小时");
		job92.setExecType(DictConstants.EXEC_TYPE_INTEVAL_HOUR);
		job92.setExecRule("4");
		job92.setExecDelay(0);
		job92.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job92.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job92);

        JobModel job93 = new JobModel();
        job93.setReqMain("AI_TRAINING_INTERFACE");
        job93.setReqParam("sendReminderMessages");
        job93.setName("每隔一分钟检索一次AI陪练任务表并发送提醒");
        job93.setJobDesc("每隔一分钟检索一次AI陪练任务表并发送提醒");
        job93.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
        job93.setExecRule("1");
        job93.setExecDelay(60);
        job93.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job93.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
        list.add(job93);

		JobModel job94 = new JobModel();
		job94.setReqMain(ServiceID.STATGW_INTEFACE);
		job94.setReqParam("STATGW_STAT_NEW");
		job94.setName("每天凌晨一点执行一次统计坐席、区域、队列、渠道的话务量");
		job94.setJobDesc("每天凌晨一点执行一次统计坐席、区域、队列、渠道的话务量");
		job94.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
		job94.setExecRule("01:00:00");
		job94.setExecDelay(0);
		job94.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job94.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job94);

		JobModel job95 = new JobModel();
		job95.setReqMain(ServiceID.SENTIMENT_INTEFACE);
		job95.setReqParam("sentimentRedJob");
		job95.setName("每30分钟计算需要标红舆情工单");
		job95.setJobDesc("每30分钟计算需要标红舆情工单");
		job95.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job95.setExecRule("30");
		job95.setExecDelay(0);
		job95.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job95.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job95);


		JobModel job96 = new JobModel();
        job96.setReqMain("UP-REMIND-SERVICE");
        job96.setReqParam("upgradeRemind");
        job96.setName("实时质检预警通知管理人员");
        job96.setJobDesc("预警1H-2H不处理通知管理人员");
        job96.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
        job96.setExecRule("5");
        job96.setExecDelay(60);
        job96.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job96.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job96);

		JobModel job97 = new JobModel();
		job97.setReqMain("TASK_HEART_BEAT");
		job97.setReqParam("taskHeartBeat");
		job97.setName("每10分钟检测定时任务是否正在执行");
		job97.setJobDesc("每10分钟检测定时任务是否正在执行");
		job97.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job97.setExecRule("10");
		job97.setExecDelay(60);
		job97.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job97.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job97);


		JobModel job98 = new JobModel();
		job98.setReqMain("ASSIST_ALARM_SERVICE");
		job98.setReqParam("assistReceiveTimeout");
		job98.setName("每10分钟执行小助手上屏超时检测");
		job98.setJobDesc("每10分钟执行小助手上屏超时检测");
		job98.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job98.setExecRule("10");
		job98.setExecDelay(0);
		job98.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job98.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
		list.add(job98);

		JobModel job99 = new JobModel();
		job99.setReqMain("SYN_TRACES_SERVICE");
		job99.setReqParam("AgentCall");
		job99.setName("每10分钟统计热线-人工接通通话记录");
		job99.setJobDesc("每10分钟统计热线-人工接通通话记录");
		job99.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job99.setExecRule("10");
		job99.setExecDelay(0);
		job99.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job99.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job99);

		JobModel job100 = new JobModel();
		job100.setReqMain("SYN_TRACES_SERVICE");
		job100.setReqParam("AgentUnCall");
		job100.setName("每10分钟统计热线-排队未接通人工记录");
		job100.setJobDesc("每10分钟统计热线-排队未接通人工记录");
		job100.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job100.setExecRule("10");
		job100.setExecDelay(0);
		job100.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job100.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job100);

		JobModel job101 = new JobModel();
		job101.setReqMain("SYN_TRACES_SERVICE");
		job101.setReqParam("RobotCall");
		job101.setName("每10分钟统计热线-机器人自助受理记录");
		job101.setJobDesc("每10分钟统计热线-机器人自助受理记录");
		job101.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job101.setExecRule("10");
		job101.setExecDelay(0);
		job101.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job101.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job101);

		JobModel job102 = new JobModel();
		job102.setReqMain("SYN_TRACES_SERVICE");
		job102.setReqParam("OutBoundConnected");
		job102.setName("每10分钟统计外呼-人工外呼记录");
		job102.setJobDesc("每10分钟统计外呼-人工外呼记录");
		job102.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job102.setExecRule("10");
		job102.setExecDelay(0);
		job102.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job102.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job102);

		JobModel job103 = new JobModel();
		job103.setReqMain("SYN_TRACES_SERVICE");
		job103.setReqParam("OutBoundUnConnected");
		job103.setName("每10分钟统计外呼-未接通记录");
		job103.setJobDesc("每10分钟统计外呼-未接通记录");
		job103.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job103.setExecRule("10");
		job103.setExecDelay(0);
		job103.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job103.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job103);

		JobModel job104 = new JobModel();
		job104.setReqMain("SYN_TRACES_SERVICE");
		job104.setReqParam("OutBoundConnectedAi");
		job104.setName("每10分钟统计外呼-AI外呼记录");
		job104.setJobDesc("每10分钟统计外呼-AI外呼记录");
		job104.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job104.setExecRule("10");
		job104.setExecDelay(0);
		job104.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job104.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job104);

		JobModel job105 = new JobModel();
		job105.setReqMain("SYN_TRACES_SERVICE");
		job105.setReqParam("OnlineUnCall");
		job105.setName("每10分钟统计外呼-在线-用户未接入人工");
		job105.setJobDesc("每10分钟统计外呼-在线-用户未接入人工");
		job105.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job105.setExecRule("10");
		job105.setExecDelay(0);
		job105.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job105.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job105);

		JobModel job106 = new JobModel();
		job106.setReqMain("SYN_TRACES_SERVICE");
		job106.setReqParam("OnlineCall");
		job106.setName("每10分钟统计外呼-在线-用户接入人工");
		job106.setJobDesc("每10分钟统计外呼-在线-用户接入人工");
		job106.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
		job106.setExecRule("10");
		job106.setExecDelay(0);
		job106.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
		job106.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job106);

        JobModel job107 = new JobModel();
        job107.setReqMain("REAL_QUALITY_CACHE_REFRESH");
        job107.setReqParam("refreshInfoRecommendKeyArray");
        job107.setName("每2分钟执行刷新INFO_RECOMMEND_KEY_ARRAY任务");
        job107.setJobDesc("每2分钟执行刷新INFO_RECOMMEND_KEY_ARRAY任务");
        job107.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
        job107.setExecRule("2");
        job107.setExecDelay(0);
        job107.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job107.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
        list.add(job107);

        JobModel job108 = new JobModel();
        job108.setReqMain("AS-REVISIT-SERVICE-INTEFACE");
        job108.setReqParam("AS_REVISIT_ROBOT_DISTRIBUTE_STRATEGY_2");
        job108.setName("每分钟自动分配主动服务机器人回访单-按服务单分配策略");
        job108.setJobDesc("每分钟自动分配主动服务机器人回访单-按服务单分配策略");
        job108.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
        job108.setExecRule("1");
        job108.setExecDelay(0);
        job108.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job108.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
        list.add(job108);

        JobModel job109 = new JobModel();
        job109.setReqMain("AS-REVISIT-SERVICE-INTEFACE");
        job109.setReqParam("AS_REVISIT_ROBOT_DISTRIBUTE_STRATEGY_3");
        job109.setName("每分钟自动分配主动服务机器人回访单-不限制外呼分配策略");
        job109.setJobDesc("每分钟自动分配主动服务机器人回访单-不限制外呼分配策略");
        job109.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
        job109.setExecRule("1");
        job109.setExecDelay(0);
        job109.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job109.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
        list.add(job109);

        JobModel job110 = new JobModel();
        job110.setReqMain("REGULARLY_MODIFY_BLACKLIST");
        job110.setReqParam("MODIFY_BLACKLIST");
        job110.setName("每天修改满意度黑名单是否到期");
        job110.setJobDesc("每天修改满意度黑名单是否到期");
        job110.setExecType(DictConstants.EXEC_TYPE_BY_DAY);
        job110.setExecRule("02:00:00");
        job110.setExecDelay(120);
        job110.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job110.setRunInMarsNode(Constants.RUN_MARS_NODE_2);//运行节点
        list.add(job110);

		JobModel job111 = new JobModel();
        job111.setReqMain("PEAK-END-JOB");
        job111.setReqParam("PEAKEND-TIME-OUT");
        job111.setName("每五分应补未提超时校验");
        job111.setJobDesc("每五分应补未提超时校验");
        job111.setExecType(DictConstants.EXEC_TYPE_INTEVAL_MINUTE);
        job111.setExecRule("5");
        job111.setExecDelay(0);
        job111.setType(DictConstants.JOB_TYPE_SERVICE_INTEFACE);
        job111.setRunInMarsNode(Constants.RUN_MARS_NODE_1);//运行节点
		list.add(job111);

		saveJob(list);
		return true;
		
/**
* 				《每次添加定时任务请更新下面运行节点信息，均衡分配》
* 				----->运行节点1：1-35,
* 				----->运行节点2：36-77,
*/
	}


	/**
	 * 任务入库
	 * @param list
	 */
	private void saveJob(List<JobModel> list) {
		EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);

		try {
			//先删除为空的数据
			EasySQL delSql = new EasySQL();
			delSql.append("DELETE FROM C_CF_JOB T WHERE T.TYPE IS NULL AND T.REQ_MAIN IS NULL AND T.REQ_PARAM IS NULL");
			query.execute(delSql.getSQL(), delSql.getParams());
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 删除字段为空的定时任务失败:"+e.getMessage(),e);
		}

		String updateTime = DateUtil.getCurrentDateStr();

		int i = 0;
		for(JobModel model : list){
			try {
				String type = model.getType();
				String serviceId = model.getReqMain();
				String command = model.getReqParam();

				if(StringUtils.isBlank(type) ||StringUtils.isBlank(serviceId) ||StringUtils.isBlank(command) ){
					logger.error(CommonUtil.getClassNameAndMethod(this)+" 定时任务信息不完整,不入库:"+model.getName());
					continue;
				}

				EasySQL sql = new EasySQL();
				sql.append("SELECT COUNT(1) CNT from C_CF_JOB 	 where REQ_MAIN=? AND REQ_PARAM=?");
				int cnt = query.queryForInt(sql.getSQL(), new Object[]{serviceId,command});
				if(cnt>0){
					EasySQL sql2 = new EasySQL();
					sql2.append(" UPDATE C_CF_JOB SET  ");
					sql2.append(model.getName(),"  NAME=? ");
					sql2.append(model.getJobDesc()," , JOB_DESC=? ");
					sql2.append(model.getExecType()," , EXEC_TYPE=? ");
					sql2.append(model.getExecRule()," , EXEC_RULE=? ");
					sql2.append(model.getExecDelay()," , EXEC_DELAY=? ");
					sql2.append(model.getRunInMarsNode()," , RUN_IN_MARS_NODE=? ");
					sql2.append(updateTime," , UPDATE_TIME=? ");
					sql2.append(type," , TYPE=? ");
					sql2.append(" WHERE 1=1 ");
					sql2.append(serviceId," AND REQ_MAIN=? ");
					sql2.append(command," AND REQ_PARAM=? ");
					query.execute(sql2.getSQL(), sql2.getParams());
				}else{
					EasyRecord record = new EasyRecord("C_CF_JOB","REQ_MAIN","REQ_PARAM");
					record.put("ID", IDGenerator.getDefaultNUMID());
					record.put("REQ_MAIN", serviceId);
					record.put("REQ_PARAM", command);
					record.put("TYPE", type);
					record.put("NAME", model.getName());
					record.put("JOB_DESC", model.getJobDesc());
					record.put("EXEC_TYPE", model.getExecType());
					record.put("EXEC_RULE", model.getExecRule());
					record.put("EXEC_DELAY", model.getExecDelay());
					record.put("RUN_IN_MARS_NODE", model.getRunInMarsNode());
					record.put("CREATE_ACC", "system");
					record.put("CREATE_TIME", DateUtil.getCurrentDateStr());
					record.put("UPDATE_TIME", updateTime);
					record.put("ENABLE_STATUS", "01");//DictConstants.ENABEL_STATUS_ENABLE
					record.put("BAKUP", "自动生成,勿删");
					record.put("SORT_NUM", ++i);

					query.save(record);
				}

			} catch (Exception e) {
				logger.error(CommonUtil.getClassNameAndMethod(this)+" 加入定时任务失败.",e);
			}
		}

		try {
			//删除没被更新的记录
			EasySQL sql2 = new EasySQL();
			sql2.append(" DELETE from C_CF_JOB where UPDATE_TIME != ?");
			query.execute(sql2.getSQL(), updateTime);
		} catch (Exception e) {
			logger.error(CommonUtil.getClassNameAndMethod(this)+" 删除没有更新的定时任务失败.",e);
		}


	}


	public void contextDestroyed(ServletContextEvent arg0) {

		ThreadMgr.getInstance().shutDown();

		JobMgr.getInstance().stopJob();

		logger.info(CommonUtil.getClassNameAndMethod(this)+"定时任务管理模块已停止!");
	}
}
