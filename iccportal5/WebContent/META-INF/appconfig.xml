<?xml version="1.0" encoding="UTF-8"?>
<config>
	 <param key="LOGIN_JSP" name="登陆页面选择" type="string" description="登陆页面选择：如值为0 则选择login0.jsp的登陆界面 默认0" value="0"/>
	 <param key="MEDIA_PORTAL_URL" name="全媒体门户的url地址" type="string" description="全媒体门户的url地址" value="/online/servlet/portal/?action=index"/>
	 <param key="ATTACHMENT_ROOT_PAHT" name="附件根目录 如：D:\temp" type="string" description="附件根目录 如：D:\temp" value="/apps/attachments"/>
	 <param key="ATTACHMENT_TMP_ROOT_PAHT" name="附件临时存放根目录 如：D:\temp" type="string" description="附件临时存放根目录 如：D:\temp" value="/apps/temp"/>
	 <param key="ATTACHMENT_MAX_LEN" name="当个附件最大值(单位M)，如 50" type="string" description="当个附件最大值(单位M)，如 50" value="50"/>
	 <param key="ATTACHMENT_MAX_SIZE" name="业务对象附件最大值，如 10" type="string" description="业务对象附件最大值，如 10" value="50"/>
	 <param key="ATTACHMENT_SUFFIX" name="附件格式 如jpg;jpeg;xls;txt;" type="string" description="附件格式 如jpg;jpeg;xls;txt;" value="jpg;jpeg;"/>
	 <param key="ATTACHMENT_LOAD_PAHT" name="附件下载地址，url" type="string" description="附件下载地址，url" value="http://************:8080/attachment/servlet/attachment?action=download"/>
	 <param key="BUSS_TYPE" name="附件业务类型" type="string" description="附件业务类型" value="04"/>
	 <param key="NOTES_MAX" name="首页公告显示最大数量" type="string" description="首页公告显示最大数量" value="5"/>
	 <param key="YC_ENT_HOST" name="云呼企业HOST" type="string" description="云呼企业HOST" value="http://************:9060"/>
	 <param key="YC_ENT_PID" name="云呼企业PID" type="string" description="云呼企业PID" value="003"/>
	 <param key="YC_ENT_ID" name="云呼企业ID" type="string" description="云呼企业ID" value="1000"/>
	 <param key="YC_ENT_KEY" name="云呼企业Key" type="string" description="云呼企业Key" value="EQZTV8UXVBTQQ2VCAEW89PEVIWDJ4R7C"/>
	 <param key="ZXZJ_CHANNEL_ID" name="咨询专家全媒体渠道ID" type="string" description="咨询专家全媒体渠道ID" value=""/>
	 <param key="ZXZJ_CHANNEL_KEY" name="咨询专家全媒体渠道key" type="string" description="咨询专家全媒体渠道key" value=""/>
	 <param key="YC_AGENT_PWD" name="云呼坐席工号的默认密码" type="string" description="云呼坐席工号的默认密码" value="Midea@520"/>
	 <param key="YC_MEDIAGW_URL" name="全媒体Mediagw地址" type="string" description="全媒体Mediagw地址" value=""/>
	 
	 <param key="LOAD_EXTENSION_NUMBER" name="是否自动获取用户分机号" type="string" description="是否自动获取用户分机号 Y-是 N-否" value="Y"/>
	 <param key="VOICE_PLATFORM" name="语音呼叫平台" type="string" description="语音呼叫平台,取值如genesys" value="genesys"/>
	 <param key="UPDATE_MIP_PWD_URL" name="修改MIP密码的地址" type="string" description="修改MIP密码的地址,如果没有,可以不填" value="https://aq.midea.com/f/reset/"/>
	 <param key="DOMAIN_NAME" name="服务器域名" type="string" description="ccbar所有的域名地址，如http://************:8080,http://************:8080" value="http://***********:9060,http://***********:9060,http://***********:9060"/>
	 <param key="HEAD_IMAGE_PATTERN" name="首页头像格式" type="string" description="首页头像上传格式，如：.png,.gif;逗号隔开" value=".jpg,.gif,.png"/>
	 
	 <param key="appSecrect" name="埋点密匙" type="string" description="cf0f219e-c1fe-4a1b-abdf-4b16735037f3" value="cf0f219e-c1fe-4a1b-abdf-4b16735037f3"/>
	 <param key="appName" name="埋点系统名称" type="string" description="C_CC" value="C_CC"/>
	 <param key="track_Url" name="埋点系统名称" type="string" description="http://testbigdataservice.midea.com" value="http://testbigdataservice.midea.com"/>
	 
	 <param key="HTTP_JY" name="埋点系统名称" type="string" description="埋点系统名称 http://cssit.midea.com/c-css/views/uedSup/userfeedback/suggestion_list.jsp?identity=mipTodo" value="http://cssit.midea.com/c-css/views/uedSup/userfeedback/suggestion_list.jsp?identity=mipTodo"/>
	 <param key="HTTP_ZX" name="埋点系统名称" type="string" description="埋点系统名称 http://cssit.midea.com/c-css/views/uedSup/userfeedback/complaint/complaint_list.jsp?identity=mipTodo" value="http://cssit.midea.com/c-css/views/uedSup/userfeedback/complaint/complaint_list.jsp?identity=mipTodo"/>
	 <param key="SECRET_ID" name="权限平台接口密钥" type="string" description="cc接入平台注册就有，从权限平台系统获取，测试密钥：31066867769652628979306773435950" value=""/>
	 <param key="GET_LOGOUT_URL" name="登出跳转连接" type="string" description="登出跳转链接" value="/cx_sso/sso/logout?action=SsoLogout"/>
	 <param key="CX_SSO_LOGIN_URL" name="统一认证登录跳转地址" type="string" description="统一认证登录跳转地址" value="/cx_sso/login.do"/>
	 <param key="IS_OPEN_SSO" name="是否开启统一认证" type="string" description="是否开启统一认证" value="N"/>
	 <!--<param key="WEBRTC_3DES_PASSWORD" name="webrtc通信密码" type="string" description="webrtc通信密码" value="marspasswd"/>-->

	<param key="WHITE_LIST" name="获取访问白名单" type="string" description="获取访问白名单" value="127.0.0.1,*********"/>
	<param key="VIDEO_CONTENT_ID" name="获取短信模板ID" type="string" description="获取短信模板ID" value="9480331729461980"/>
	<param key="MODEL_ID" name="获取事业部ID" type="string" description="获取事业部ID" value="2"/>
	<param key="CATEGORY_ID" name="获取品牌ID" type="string" description="获取品牌ID" value="0"/>
	<param key="HS_COLLECT_ADDR" name="火山埋点引擎地址" type="string" description="火山埋点引擎地址" value=""/>
	<param key="IS_OPEN_5G_VIDEO" name="是否开启5g视频" type="string" description="是否开启5g视频" value="0"/>
	<param key="JSESSIONID_KEY" name="JSESSIONID前缀" type="string" description="JSESSIONID前缀" value=""/>
	<param key="CC_EP_CODE" name="企业id" type="string" description="企业id" value="001"/>
</config>
