Date.prototype.format = function(format) {
	var o = {
		"M+": this.getMonth() + 1, // month
		"d+": this.getDate(), // day
		"h+": this.getHours(), // hour
		"m+": this.getMinutes(), // minute
		"s+": this.getSeconds(), // second
		"q+": Math.floor((this.getMonth() + 3) / 3), // quarter
		"S": this.getMilliseconds() // millisecond
	};
	if (/(y+)/.test(format))
		format = format.replace(RegExp.$1, (this.getFullYear() + "")
			.substr(4 - RegExp.$1.length));
	for (var k in o)
		if (new RegExp("(" + k + ")").test(format))
			format = format.replace(RegExp.$1, RegExp.$1.length == 1 ? o[k] : ("00" + o[k]).substr(("" + o[k]).length));
	return format;
};


function checkIsDongzhi(data,isConf,genderc){
	// colom:075788295414
	// 东芝：075722394456
	// colom:29396299
//东芝：29396399
	let list = ['075722394456','29396399','75722394456','29396110']
	if($.inArray(data.caller,list)!=-1 || $.inArray(data.called,list)!=-1 || data.callNumber =='7995'  || ( data.userData && data.userData.callNumber =='7995')){
		console.log('是东芝',data.callNumber,data.caller,data.called)
		setDongzhiPhoto(isConf,genderc)
		return true
	}
	console.log('不是东芝')

	return false
}

/*设置虚拟头像*/
function setDongzhiPhoto(isConf,genderc){
	console.log('设置东芝头像')
	var imgUrl = location.origin+'/iccportal5/static/ccbar/dongzhi-' + (webrtcConfig.SEX == 0?'male.png' : 'female.png')
	if(isConf && genderc) imgUrl = location.origin+'/iccportal5/static/ccbar/dongzhi-third'+res.result.genderc+'.png'
	yqWebrtcUI.setPhoto(imgUrl,true)
	
}
var lastCallEvent = {}
var webrtcConfig = {}

var ccbarWebrtc = (function(){
	var webrtcEvent= {}
	var webrtcRecord= {}
	var lastCallId;
	var webrtcJs = ['yqWebrtcUI.js','jsQR.js'];
	if(typeof(Vue) == 'undefined'){
		webrtcJs.unshift('vue.min.js')
	}
	return {
		isWebrtcAgent:function(){
			return webrtcConfig && webrtcConfig.WR_PHONE_NUM 
		},
		setRoleCallback:function(){
			return true
		},
		getVideoRecordId:function(){
			return webrtcEvent.videoRecordId
		},
		init:function(){
			ccbarWebrtc.loadJs(webrtcJs,function(){
				window.webrtcSdkInit = true
			})

			// ccbarWebrtc.api.getSex({},function(res){

			// })
		},
		getWebrtcConfig:function(num){
			$.post('/iccportal5/servlet/webrtcConfig?action=getWebrtcConfig',{data:JSON.stringify({yxPhoneNum:num})},function(res){
				if(res.state == 1){
					//initEvent(res.data)
					webrtcConfig = res.data
					//ccbarWebrtc.loadJs(webrtcJs,ccbarWebrtc.initEvent)
					ccbarWebrtc.initEvent()
				}
			})
		},
		getLastConnect:function(){
			return {
				agentPhone:webrtcConfig.WR_PHONE_NUM,
				// targetAgentId:
				oCaller:lastCallEvent.caller,
				oCalled:lastCallEvent.called,
			}
		},
		initMonitor:function(){
			if(window.yqWebrtcMonitorUI){
				var config = $.extend(webrtcConfig,{
					url: webrtcConfig.WEBRTC_IN_URL,
					account: webrtcConfig.WR_PHONE_NUM,
					agentPhone: '',//监控目标的号码
					sender: webrtcConfig.WEBRTC_SENDER_SF || 'YUNQU_MOBILEAGENT',
					deskey: webrtcConfig.WEBRTC_3DES_KEY_SF,
					desPassword: webrtcConfig.WEBRTC_3DES_PASSWORD_SF
				,monitorType:1})
				console.log('yqWebrtcMonitorUI初始化',config)
				yqWebrtcMonitorUI.config(webrtcConfig)
			}
		},
		closeSip:function(){
			window.yqWebrtcUI && yqWebrtcUI.stop();
		},
		wakeupSip:function(){
			//启动webrtcUI
			if(window.yqWebrtcUI){
				var data = {
					url: webrtcConfig.WEBRTC_IN_URL,
					sender: webrtcConfig.WEBRTC_SENDER || 'YUNQU_MOBILEAGENT',
					deskey: webrtcConfig.WEBRTC_3DES_KEY,
					desPassword: webrtcConfig.WEBRTC_3DES_PASSWORD,
					account: webrtcConfig.WR_PHONE_NUM,
					password: webrtcConfig.PHONE_PWD,
					terminalType: 'PC',
					videoEnabled: webrtcConfig.IS_EXIST_CAMERA == 1 || typeof(webrtcConfig.IS_EXIST_CAMERA) == 'undefined'?true:false,
					//是否默认打开摄像头
					mikeEnabled: false,
					enabledDevice:webrtcConfig.IS_EXIST_CAMERA == 1 || typeof(webrtcConfig.IS_EXIST_CAMERA) == 'undefined'?true:false,
					cameraEnabled:webrtcConfig.IS_EXIST_CAMERA == 1 || typeof(webrtcConfig.IS_EXIST_CAMERA) == 'undefined'?true:false,
					monitrorEnabled:webrtcConfig.IS_ALLOW_SHOW == 1?true:false
					//不启用麦克风
				}
				//设置配置项
				yqWebrtcUI.config(data);
				if(data.cameraEnabled){
					yqWebrtcUI.start();

				}else{
					yqWebrtcUI.start(false);
				}
				//设置虚拟头像
				var imgUrl = location.origin+'/iccportal5/static/ccbar/' + (webrtcConfig.SEX == 0?'male.png' : 'female.png')
				yqWebrtcUI.setPhoto(imgUrl,true)
			}
		},
		initEvent:function(data){
			console.log('webrtc init')
			ccbarWebrtc.wakeupSip()
			if(webrtcConfig.IS_ALLOW_SHOW == 1){
				ccbarWebrtc.initMonitor()
			}
			if(window.isInitWebrtc) return
			window.ccbarEvent &&ccbarEvent.addEvent('logon',function(){
				$('#__videoCallOut').remove();
				ccbarWebrtc.wakeupSip()
				if(webrtcConfig.IS_ALLOW_SHOW == 1){
					ccbarWebrtc.initMonitor()
				}	
			})

			window.ccbarEvent &&ccbarEvent.addEvent('logoff',function(){
				$('#__videoCallOut').remove();
				ccbarWebrtc.closeSip()
				if(webrtcConfig.IS_ALLOW_SHOW == 1){
						yqWebrtcMonitorUI.destory()
					}
			})

			window.ccbarEvent &&ccbarEvent.addEvent('agentStateSync',function(e){
				if(webrtcConfig && webrtcConfig.WR_PHONE_NUM){
					
					if(e.state == "CONFERENCED" ){

						CallControl.agentGender(lastConsult.AGENT_PHONE,function(res){
							res = res.data || {}
							if(res.code == 'succ' && res.result && res.result.gender){
								//设置虚拟头像
								var imgUrl = location.origin+'/iccportal5/static/ccbar/third'+res.result.genderc+'.png'

								//东芝的要另外设置
								yqWebrtcUI.setPhoto(imgUrl,true)
								/*let flag = */checkIsDongzhi(lastCallEvent,true,res.result.genderc)
								// if(flag) setDongzhiPhoto(true,res.result.genderc)
							}	
						})

						
					}else{
						//设置虚拟头像
						var imgUrl = location.origin+'/iccportal5/static/ccbar/' + (webrtcConfig.SEX == 0?'male.png' : 'female.png')
						//东芝的要另外设置
						yqWebrtcUI.setPhoto(imgUrl,true)
						/*let flag = */checkIsDongzhi(lastCallEvent)
						// if(flag) setDongzhiPhoto()
					}
				}
			})

			window.ccbarEvent &&ccbarEvent.addEvent('evtConnected',function(event){
				//非外呼时调用
				$('#__videoCallOut').hide();
				lastCallEvent = event;

				yqWebrtcUI.hide()

				var custPhone = event.userData.custPhone || event.caller;
				if(event.createCause == 6 && event.userData.realCalled) custPhone = event.userData.realCalled
				yqWebrtcUI.setSmsUserData(custPhone,{
					caller:event.caller,
					called:event.called,
					custPhone:custPhone,
					callNumber:event.userData.callNumber,
					createCause:event.createCause,
					callSerialId:event.callSerialId,
					'agentAcc':ccbarMidea.config.userAcc,
					'agentNo':ccbarMidea.config.code
				})
				if(event.createCause != 6) yqWebrtcUI.ring(custPhone);

				console.log('接通事件',JSON.stringify(event))
				try{
					var imgUrl = location.origin+'/iccportal5/static/ccbar/' + (webrtcConfig.SEX == 0?'male.png' : 'female.png')
					//东芝的要另外设置
					yqWebrtcUI.setPhoto(imgUrl,true)
					checkIsDongzhi(lastCallEvent)
				}catch(e){

				}
				

				console.log('custPhone:',custPhone,'createCause:',event.createCause)
				if(custPhone && event.createCause == 6){
					ccbarWebrtc.api.GetVipLevel(custPhone,function(res){
						if(res.isAllowEntery == '1' || (res.data && res.isAllowEntery == '1')){
							ccbarWebrtc.setRoleCallback = function(){return true}
							yqWebrtcUI.connect();
							yqWebrtcUI.hide()
							
						}else{
							ccbarWebrtc.setRoleCallback = function(){return false}
						}
					})
				}



				var data = {
					caller:event.caller,
					called:event.called,
					'sessionId':event.userData.connId,
					'agentAcc':ccbarMidea.config.userAcc,
					'agentNo':ccbarMidea.config.code,
					'agentDept':'坐席部门',
					createCause:event.createCause,
					isZy:event.createCause == '3' || event.createCause == '10'


				}
				if(data.isZy && event.userData.beforeVideoRecordId){
					data.beforeVideoRecordId = event.userData.beforeVideoRecordId
				}
				
				getSupprotSelf(data)
				window.getSupprotClockCount = 0

				function getSupprotSelf(data){
					ccbarWebrtc.api.getSupprot(data,function(res){
						console.log('getSupprot:'+window.getSupprotClockCount+JSON.stringify(res))
						if(res.state == 1 && res.data){

							if(webrtcConfig.IS_ALLOW_CONTROL){
								yqWebrtcUI.show()
							}else{
								yqWebrtcUI.hide()
							}

							window.getSupprotClockCount = 0
							webrtcEvent = res.data;

							if(res.data.isAllowEntery == '1'){
								ccbarWebrtc.setRoleCallback = function(){return true}
							}else{
								ccbarWebrtc.setRoleCallback = function(){return false}
							}
							
							if(res.data.isSupportVideo == 0){
								console.log('隐藏转视频')
								$('#volteIcon').css('display','none')
								yqWebrtcUI.setActionType(1)
								if(event.createCause == 2) yqWebrtcUI.connect();
							}else{
								$('#volteIcon').css('display','inline-block')
								console.log('显示转视频')

								yqWebrtcUI.setActionType(2)
								if(event.createCause == 2) yqWebrtcUI.connect();
							}

							/*let flag = */checkIsDongzhi(lastCallEvent,true,res.result.genderc)
							// if(flag) setDongzhiPhoto(true,res.result.genderc)
							
						}else{
							window.getSupprotClockCount++
							if(window.getSupprotClockCount>30) return;
							window.getSupprotClock && clearTimeout(window.getSupprotClock)
							window.getSupprotClock =  setTimeout(function(){
								getSupprotSelf(data)
							},3000)
						}
					})
				}

				
			})

			window.ccbarEvent && window.ccbarEvent.addEvent('evtConferencedBegin',function(event){
				console.log('sss',event)
				lastConfEvent = event;
				if(webrtcConfig.IS_ALLOW_SHOW == 1){
					console.log('yqWebrtcMonitorUI启动',lastConfEvent.userData,{agentPhone:lastConfEvent.userData && lastConfEvent.userData.agentPhone})
					ccbarWebrtc.api.getIsVideo({
						beforeCaller:lastConfEvent.userData && lastConfEvent.userData.oCaller,
						beforeCalled:lastConfEvent.userData && lastConfEvent.userData.oCalled},function(res){
						if(res.isNowVideo == '1'){
							yqWebrtcMonitorUI.config({agentPhone:lastConfEvent.userData && lastConfEvent.userData.agentPhone})
							yqWebrtcMonitorUI.show()
						}
					})
					
				}
			})

			window.ccbarEvent && window.ccbarEvent.addEvent('evtConferencedEnd',function(event){
				$("[data-agentinfo='curstatus']").text('通话');
				//设置虚拟头像
				var imgUrl = location.origin+'/iccportal5/static/ccbar/' + (webrtcConfig.SEX == 0?'male.png' : 'female.png')
				yqWebrtcUI.setPhoto(imgUrl,true)
				/*let flag = */checkIsDongzhi(lastCallEvent,true,res.result.genderc)
				// if(flag) setDongzhiPhoto(true,res.result.genderc)
			})

			window.ccbarEvent &&ccbarEvent.addEvent('evtDisConnected',function(event){
				lastCallEvent = {};

				window.getSupprotClock && clearTimeout(window.getSupprotClock)
				$('#__videoCallOut').show();
				yqWebrtcUI.disconnect();
				$('#volteIcon').css('display','none')
				if(webrtcConfig.IS_ALLOW_SHOW == 1){
						yqWebrtcMonitorUI.hide()
					}
				var data = {}
				//ccbarWebrtc.api.UpdateVideoEndRecord(data)
			})

			yqWebrtcUI.onAction(function(e){
				var action = e.action;
				switch(action){
					case 'takePhoto'://截图
						ccbarWebrtc.screenShot(e.data.image)
						break;
					case 'agent'://截图
						ccbar_plugin.callControl.agentSel('consultationcall');
						break;
					case 'callSms'://截图
						layer.alert(e.data)
						break;
					case 'smsInvite'://短信邀请
						console.log('smsInvite',e.data)
						var msg = e.data || '确定要下发短信邀请视频通话吗?'
						layer.confirm('温馨提示',msg,function(ee){
							layer.close(ee)
							e.data.next && e.data.next()
						})

						break;
					case 'callConnect'://connect



						var callId = yqWebrtcUI.getHandler().getCallId();
						var callType = yqWebrtcUI.getHandler().getCallType();
						if (callId && callId == lastCallId ) {
							return
						}

						console.log('UpdateVideoBeginRecord',callId , lastCallId)

						ccbarWebrtc.api.UpdateVideoBeginRecord({
							beginTime:new Date().format('yyyy-MM-dd hh:mm:ss'),
							videoRecordId:webrtcEvent.videoRecordId,
							callId:callId,
							caller:lastCallEvent.caller,
							called:lastCallEvent.called,
						},function(res){
							if(res.state == 1){
								webrtcRecord[webrtcEvent.videoRecordId] = res.data.videoRecordExId
							}
						})
						lastCallId = callId
						break;
					case 'callHangup'://挂断
						var callId = yqWebrtcUI.getHandler().getCallId();
						var callType = yqWebrtcUI.getHandler().getCallType();
						ccbarWebrtc.api.UpdateVideoEndRecord({
							endTime:new Date().format('yyyy-MM-dd hh:mm:ss'),
							videoRecordId:webrtcEvent.videoRecordId,
							callId:callId,
							caller:lastCallEvent.caller,
							called:lastCallEvent.called,
							videoRecordExId:webrtcRecord[webrtcEvent.videoRecordId]
						})
						break;
					case  'serverReady':
						yqWebrtcUI.setRoleCallback(function(){
							return ccbarWebrtc.setRoleCallback();
						});
						break;
				}
			})

			window.isInitWebrtc = true;
		},
		loadJs:function(scriptUrls, callback) {
			let loadedScripts = new Set();
			let basePath = '/iccportal5/static/ccbar/'
			function loadScript(url) {
				return new Promise((resolve, reject) => {
					if (loadedScripts.has(url)) {
						resolve();
					} else {
						let script = document.createElement('script');
						script.src = basePath+url;
						script.onload = resolve;
						script.onerror = reject;
						document.head.appendChild(script);
						loadedScripts.add(url);
					}
				});
			}

			async function loadAllScripts() {
				for (let url of scriptUrls) {
					await loadScript(url);
				}
			}

			loadAllScripts().then(callback).catch(error => console.error('Error loading script:', error));
		},
		api:{
			getSupprot:function(data,callback){
				var url = '/iccportal5/servlet/videoRecordInfo?action=getSupprot'
				$.post(url,{data:JSON.stringify(data)},callback)
			},
			UpdateVideoBeginRecord:function(data,callback){
				var url = '/iccportal5/servlet/videoRecordInfo?action=UpdateVideoBeginRecord'
				$.post(url,{data:JSON.stringify(data)},callback)
			},
			UpdateVideoEndRecord:function(data,callback){
				var url = '/iccportal5/servlet/videoRecordInfo?action=UpdateVideoEndRecord'
				$.post(url,{data:JSON.stringify(data)},callback)
			},
			getSex:function(data,callback){
				var url = '/iccportal5/servlet/videoRecordInfo?action=getSex'
				$.post(url,{data:JSON.stringify(data)},callback)
			},
			GetVipLevel:function(data,callback){
				var url = '/iccportal5/servlet/videoRecordInfo?action=GetVipLevel'
				$.post(url,{data:JSON.stringify({phoneNum:data})},callback)
			},
			getIsVideo:function(data,callback){
				var url = '/iccportal5/servlet/videoRecordInfo?action=getIsVideo'
				$.post(url,{data:JSON.stringify(data)},callback)
			}

		},
		screenShot:function(base64Image){
			//showImagePopup(base64Image)
			function base64ToBlob(base64) {
				base64 = base64.replace('data:image/png;base64,','')
				const byteCharacters = atob(base64);
				const byteNumbers = new Array(byteCharacters.length);
				for (let i = 0; i < byteCharacters.length; i++) {
					byteNumbers[i] = byteCharacters.charCodeAt(i);
				}
				const byteArray = new Uint8Array(byteNumbers);
				return new Blob([byteArray], { type: 'image/png' });
			}

			function uploadBase64Image2(base64Image) {
				var blob = base64ToBlob(base64Image);
				var formData = new FormData();
				formData.append('file', blob);

				$.ajax({
					url: '/iccportal5/servlet/videoRecordInfo?action=analyzeQR',
					type: 'POST',
					data: formData,
					processData: false,
					contentType: false,
					success: function(response) {
			            console.log('Image uploaded successfully:', response);
			            //layer.alert(response.data)
			            if(response.data){
			            	window.open(response.data)
			            }else{
			            	layer.alert('未能识别到二维码')
			            }
			        },
					error: function(xhr, status, error) {
						console.error('Error uploading image:', error);
					}
				});
			}

			function uploadBase64Image(base64Image) {
				var img = new Image();
				img.src = base64Image;
				img.onload = function(){
				    var cav = document.createElement('canvas');
				    var ctx = cav.getContext('2d');
				    cav.width = this.naturalWidth;
				    cav.height = this.naturalHeight;
				    ctx.drawImage(this, 0, 0);
				    var imageData = ctx.getImageData(0, 0, cav.width, cav.height);
				    var code = jsQR(imageData.data, imageData.width, imageData.height, {});
				    if(code && code.data){
				    	console.log(code.data);
		            	window.open(code.data)
		            }else{
		            	layer.alert('未能识别到二维码')
		            }
				};
			}


			function showImagePopup(base64Image) {
				//console.log('图片',base64Image);

				layer.open({
					type: 1,
					title: false,
					closeBtn: 2,
					area: ['auto', 'auto'],
					content: `<img style="width:30vw;height:80vh" src="${base64Image}" />`,
					btn: ['识别二维码', '下载'],
					btn1: function() {
						// Upload logic here
						layer.msg('识别中...');
						uploadBase64Image(base64Image)
					},
					btn2: function() {
						// Download logic here
						var link = document.createElement('a');
						link.href = `${base64Image}`;
						link.download = 'screenshot-'+new Date().getTime()+'.png';
						link.click();
					}
				});
			}
		}
	}
})()

ccbarWebrtc.init()

CallControl.sstransfer  = function ( called,displayNumber,skillId, callType, userData ,callback){
	userData = ( userData == null || typeof userData == 'undefined' ) ? "" : userData;
	if(ccbarWebrtc.getVideoRecordId()){
		userData = $.extend({beforeVideoRecordId:ccbarWebrtc.getVideoRecordId()},userData)
	}
	this.sendCTICommand({
		messageId:ccbar_config['command']['sstransfer'],
		called:called,
		displayNumber:displayNumber,
		callType:callType,
		userData:userData,
		skillId:skillId,
		callback:callback
	},callback);
}
CallControl.makeCall =  function (called,caller, userData ,callback, callType)
{
	called = ( called == null || typeof called == 'undefined' ) ? "" : called;
	caller = ( caller == null || typeof caller == 'undefined' ) ? "" : caller;
	callType = ( callType == null || typeof callType == 'undefined' ) ? "2" : callType;
	userData = ( userData == null || typeof userData == 'undefined' ) ? "" : userData;
	var data = {
		called:called,
		caller: caller,
		userData:userData,
		callType:callType
	};
	$.ajax({
		dataType:'jsonp',
		jsonp: "callbackFunc",
		jsonpCallback:"jsonpCallbackMakecall",
		contentType : "application/x-www-form-urlencoded; charset=UTF-8",
		url: this.getContextPath() + '/yc-ccbar/CallEvent?action=Makecall',
		data:{'cmdJson':JSON.stringify(data)},
		timeout : 5000,	//5秒超时
		success:function(result){
			if ($.isFunction(callback)) { callback(result); }
			if(ccbarWebrtc.isWebrtcAgent()) yqWebrtcUI.audioCallOut(called)
			ccbarWebrtc.api.GetVipLevel(called,function(res){
				if(res.isAllowEntery == '1' || (res.data && res.isAllowEntery == '1')){
					ccbarWebrtc.setRoleCallback = function(){return true}
				}else{
					ccbarWebrtc.setRoleCallback = function(){return false}
				}
			})
		},
		error:function( XMLHttpRequest, textStatus,	errorThrown ){}
	});

}

CallControl.consultation = function ( called,displayNumber,skillId, callType, userData ,callback){
		userData = ( userData == null || typeof userData == 'undefined' ) ? "" : userData;
		userData = $.extend(ccbarWebrtc.getLastConnect(),userData)
		if(window.agentListLast){
			for (var i = 0; i < window.agentListLast.length; i++) {
				if(window.agentListLast[i].AGENT_ID == called){
					window.lastConsult = window.agentListLast[i]
				}
			}

		}

		this.sendCTICommand({
			messageId:ccbar_config['command']['consultation'],
			called:called,
			displayNumber:displayNumber,
			callType:callType,
			skillId:skillId,
			userData:userData,
			callback:callback
		},callback);
	}
	/**
	 * [conference 三方]
	 * @param  {String}   called        [1:被转移坐席工号 2:被转移Ivr字冠 3:被转移客户电话 4:被转移技能组ID]
	 * @param  {String}   displayNumber [被转移对象的主显号码]
	 * @param  {String}   callType      [1:呼叫座席 2:呼叫IVR 3:呼叫外线 4:技能组]
	 * @param  {Object}   userData      [业务私有数据]
	 * @param  {Function} callback      [回调函数]
	 */
	CallControl.conference = function (userData,callback){
		userData = ( userData == null || typeof userData == 'undefined' ) ? "" : userData;
		userData = $.extend(ccbarWebrtc.getLastConnect(),userData)
		this.sendCTICommand({
			messageId:ccbar_config['command']['conference'],
			// called:called,
			// displayNumber:displayNumber,
			// callType:callType,
			userData:userData,
			callback:callback
		},callback);
	}

CallControl.agentGender = function(phone,callback){
	var  data = {
		"event":"agentGender","agentPhone":phone
	}
	$.ajax({
		dataType:'jsonp',
		jsonp: "callbackFunc",
		jsonpCallback:"jsonpCallbackagentGender",
		contentType : "application/x-www-form-urlencoded; charset=UTF-8",
		url: this.getContextPath() + '/yc-ccbar/AgentEvent?action=AgentGender',
		data:{'cmdJson':JSON.stringify(data)},
		timeout : 5000,	//5秒超时
		success:function(result){
			if ($.isFunction(callback)) { callback(result); }
		},
		error:function( XMLHttpRequest, textStatus,	errorThrown ){}
	});
}