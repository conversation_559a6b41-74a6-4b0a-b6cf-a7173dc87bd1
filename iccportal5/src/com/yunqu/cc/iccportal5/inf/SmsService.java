package com.yunqu.cc.iccportal5.inf;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yq.busi.common.base.ServiceCommand;
import com.yq.busi.common.base.ServiceID;
import com.yq.busi.common.dict.DictCache;
import com.yq.busi.common.servlet.model.GWConstants;
import com.yq.busi.common.util.CommonUtil;
import com.yq.busi.common.util.IDGenerator;
import com.yq.busi.common.util.UserUtil;
import com.yunqu.cc.iccportal5.base.CommonLogger;
import com.yunqu.cc.iccportal5.base.Constants;
import org.apache.commons.lang3.StringUtils;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import java.security.SecureRandom;
import java.util.*;

public class SmsService extends IService {
    private static Logger smsLogger = CommonLogger.getLogger("SMS");
    private static EasyQuery query = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_NAME);

    private EasyCache cache = CacheManager.getMemcache();


    @Override
    public JSONObject invoke(JSONObject json) {
        String command = json.getString("command");
        if ("sendSMSBy5GVideo".equals(command)) {
            return sendSMSBy5GVideo(json);
        } else {
            JSONObject result = new JSONObject();
            result.put("respCode", GWConstants.RET_CODE_COMMAND_ERROR);
            result.put("respDesc", "不存在的command,请检查！");
            return result;
        }
    }

    /**
     * 下发邀请接口
     */
    private JSONObject sendSMSBy5GVideo(JSONObject json) {

        JSONObject result = new JSONObject();
        String reqIp = json.getString("reqIp");
        smsLogger.info("发送短信参数入参reqIp"+reqIp);
        JSONObject data = json.getJSONObject("data");
        JSONObject userdata = data.getJSONObject("userdata");
        //2呼入  6呼出
        String createCause = userdata.getString("createCause");
        String called = userdata.getString("called");

        String epCode = "001";
        if(StringUtils.isNotBlank(Constants.CC_EP_CODE)){
            epCode = Constants.CC_EP_CODE;
        }
        if(StringUtils.equals(createCause,"6")){
            called = userdata.getString("callNumber");
        }
        String smsTemplateCode = DictCache.getDictVal(epCode,"5G_VIDEO_SMS_TEMPLATE",called);
        String smsSign = DictCache.getDictVal(epCode,"5G_VIDEO_SMS_SIGN",called);
        if(StringUtils.isBlank(smsTemplateCode)){
            smsTemplateCode = Constants.MSG_CODE;
        }
        if(StringUtils.isBlank(smsSign)){
            smsSign = Constants.getCategoryId();
        }
        smsLogger.info("发送短信参数入参"+data.toJSONString());
        //白名单校验，必须符合才能进行短信下发邀请
        String whiteList = Constants.getWhiteList();
        if(StringUtils.isNotBlank(reqIp)){
            String[] reqIpArray = reqIp.split(",");
            boolean allIpsNotInWhitelist = true;
            for (String ip : reqIpArray) {
                if (whiteList.contains(ip.trim())) {
                    allIpsNotInWhitelist = false;
                    break;
                }
            }
            if (allIpsNotInWhitelist) {
                smsLogger.error("IService请求失败,访问IP不在白名单之中:" + reqIp);
                result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                result.put("respDesc", "IP不在白名单之中,无法访问接口");
                return result;
            }
        }
        JSONObject params = new JSONObject();
        //生成随机六位验证码
        String verifyCode = generateUniqueString();
        String userPhone = data.getString("userPhone");
        String url = data.getString("url");
        String content = "";
        //获取短信模板
        EasySQL sql = new EasySQL("select * from C_SMS_TEMPLATE where 1=1 and MSG_CODE = ?");
        try {
            JSONObject smsJson = query.queryForRow(sql.getSQL(), new Object[]{smsTemplateCode}, new JSONMapperImpl());
            if (smsJson.isEmpty() || "2".equals(smsJson.getString("STATUS"))){
                smsLogger.info(CommonUtil.getClassNameAndMethod(this) + "短信模板处于禁用状态,停止下发短信邀请接口");
                result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                result.put("respDesc", "短信模板处于禁用状态");
                return result;
            }
            content = smsJson.getString("CONTENT");
            if (StringUtils.isBlank(content)) {
                smsLogger.info(CommonUtil.getClassNameAndMethod(this) + "未获取短信模板内容,停止下发短信邀请接口");
                result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
                result.put("respDesc", "未获取短信模板内容");
                return result;
            }
            url = url + "?code=" + verifyCode;
            //调用短链接口
            String shortUrl = getShortUrl(url);
            //替换短信模板内容
            content = content.replace("[URL]", shortUrl);
            smsLogger.info(CommonUtil.getClassNameAndMethod(this) + "下发邀请视频短信内容为:" + content + ",接收用户为:" + userPhone);
            //下发短信
            params.put("command", ServiceCommand.SENDMESSAGE);
            params.put("sender", Constants.APP_NAME);
            params.put("serialId", IDGenerator.getDefaultNUMID());
            params.put("password", "YQ_85521717");
            params.put("source", "04");
            params.put("busiId", IDGenerator.getIDByCurrentTime(20));
            params.put("sendTime", EasyCalendar.newInstance().getDateTime("-"));
            params.put("model",Constants.getModelId()); // 事业部标识
            params.put("category", smsSign); /// 品类标识
            params.put("receivers", spiltByRegex(userPhone, ";", content));
            params.put("userAcc", "system");
            smsLogger.info("发送短信 IService请求参数" + JSON.toJSONString(params));
            IService service = ServiceContext.getService(ServiceID.SMSGW_INTEFACE);
            service.invoke(params);
        } catch (Exception e) {
            smsLogger.error("IService请求失败,请求参数" + JSON.toJSONString(params) + ",原因" + e.getMessage());
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "下发短信接口异常");
            return result;
        }
        //存储业务参数信息 时效五分钟
//        String codeKey = reqIp + "_" +verifyCode;
        int smsIsExpire = 300;
        String smsIsExpireString  = Constants.getSmsIsExpire();
        if (smsIsExpireString != null && !"".equals(smsIsExpireString)) {
            smsIsExpire = Integer.parseInt(smsIsExpireString);
        }
        cache.put(verifyCode,data.toJSONString(),smsIsExpire);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "下发短信发送成功");
        result.put("respContent", content);
        return result;
    }

    /**
     * 生成一个六位数的随机数字验证码。
     *
     * @return 随机生成的六位数验证码。
     */
    public static String generateVerificationCode() {
        SecureRandom random = new SecureRandom();
        // 生成一个介于100000到999999之间的随机数
        int code = 100000 + random.nextInt(900000);
        return String.valueOf(code);
    }

    /**
     * 生成唯一字符串
     */
    public static String generateUniqueString() {
        UUID uuid = UUID.randomUUID();
        return uuid.toString();
    }


    /**
     * 短信收发内容处理
     */
    public static List<JSONObject> spiltByRegex(String value, String regex, String content) {
        List<JSONObject> list = new ArrayList<JSONObject>();
        if (StringUtils.isNotBlank(value) && StringUtils.isNotBlank(regex)) {
            String[] str = value.split(regex);
            for (String s : str) {
                JSONObject obj = new JSONObject();
                obj.put("receiver", s);
                obj.put("content", content);
                obj.put("userType", "");
                list.add(obj);
            }
        }
        return list;
    }

    /**
     * 生成短连接
     *
     */
    public String getShortUrl(String url) {
        JSONObject shortResult = new JSONObject();
        //生成短链
        try {
            JSONObject obj = new JSONObject();
            Map<String, Object> param = new HashMap<String, Object>();
            obj.put("command", "longToShort");
            param.put("longLinkUrl", url);
            obj.put("params", param);
            try {
                IService service = ServiceContext.getService("MIXGW_MCSP_INTEFACE");
                shortResult = service.invoke(obj);
            } catch (ServiceException e) {
                smsLogger.error("IService请求失败,请求参数" + JSON.toJSONString(obj) + ",原因" + e.getMessage());
            }
            String respCode = shortResult.getString("respCode");
            if (GWConstants.RET_CODE_SUCCESS.equals(respCode)) {
                JSONObject respData = shortResult.getJSONObject("respData");
                if (respData != null && respData.getJSONObject("data") != null && StringUtils.isNotBlank(respData.getJSONObject("data").getString("shortLinkUrl"))) {
                    smsLogger.info("短链接生成成功" + url + "->" + respData.getJSONObject("data").getString("shortLinkUrl"));
                    url = respData.getJSONObject("data").getString("shortLinkUrl");
                }
            }
        } catch (Exception e) {
            smsLogger.error("短链接生成失败" + e.getMessage(), e);
        }
        return url;
    }

}
