package com.yunqu.cc.iccportal5.base;

import com.yq.busi.common.util.ConfigUtil;
import com.yq.busi.common.util.ParamUtil;

/**
 * 常量
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String DS_NAME = "yw-ds";     //默认数据源名称
	public final static String MARS_DS_NAME = "mars-ds";     //mars数据源名称
	public final static String WEBRTC_DS_NAME = "webrtc_ds";

	public static final String VOICE_CFG_DS_NAME = "voice-cfg-ds"; //语音呼叫平台的配置数据源,如genesys的配置数据源
	
	public final static String APP_NAME = "iccportal5";     //应用
	
	//登陆页面样式选择
	public static final String LOGIN_JSP = ConfigUtil.getString(APP_NAME, "LOGIN_JSP");

	//全媒体门户的url地址
	public static final String MEDIA_PORTAL_URL = ConfigUtil.getString(APP_NAME, "MEDIA_PORTAL_URL");
	
	//附件根目录 如：D:\temp
	public static final String ATTACHMENT_ROOT_PAHT = ConfigUtil.getString(APP_NAME, "ATTACHMENT_ROOT_PAHT");
	//附件临时存放根目录 如：D:\temp
	public static final String ATTACHMENT_TMP_ROOT_PAHT = ConfigUtil.getString(APP_NAME, "ATTACHMENT_TMP_ROOT_PAHT");
	
	//当个附件最大值(单位M)，如 50
	public static final String ATTACHMENT_MAX_LEN = ConfigUtil.getString(APP_NAME, "ATTACHMENT_MAX_LEN");
	//业务对象附件最大值，如 10
	public static final String ATTACHMENT_MAX_SIZE = ConfigUtil.getString(APP_NAME, "ATTACHMENT_MAX_SIZE");
	
	//附件格式 如jpg;jpeg;xls;txt;
	public static final String ATTACHMENT_SUFFIX = ConfigUtil.getString(APP_NAME, "ATTACHMENT_SUFFIX");
	//附件下载地址，url
	public static final String ATTACHMENT_LOAD_PAHT = ConfigUtil.getString(APP_NAME, "ATTACHMENT_LOAD_PAHT");
	//附件业务类型
	public static final String BUSS_TYPE = ConfigUtil.getString(APP_NAME, "BUSS_TYPE");
	
	//首页公告显示最大数量
	public static final String NOTES_MAX = ConfigUtil.getString(APP_NAME, "NOTES_MAX");
	//是否自动获取用户分机号 Y-是 N-否
	public static final String LOAD_EXTENSION_NUMBER = ConfigUtil.getString(APP_NAME, "LOAD_EXTENSION_NUMBER","Y");
	//语音呼叫平台,取值如genesys
	public static final String VOICE_PLATFORM = ConfigUtil.getString(APP_NAME, "VOICE_PLATFORM");
	//修改mip密码的地址
	public static final String UPDATE_MIP_PWD_URL = ConfigUtil.getString(APP_NAME, "UPDATE_MIP_PWD_URL");
	
	//常量:平台类型:genesys
	public static final String PLATFORM_GENESYS = "genesys";
		
	//首页头像上传格式
	public static final String HEAD_IMAGE_PATTERN = ConfigUtil.getString(APP_NAME, "HEAD_IMAGE_PATTERN");
	
	//埋点参数
	public static final String appName = ConfigUtil.getString(APP_NAME, "appName");
	//埋点
	public static final String appSecrect = ConfigUtil.getString(APP_NAME, "appSecrect");
	//埋点地址
	public static final String track_Url = ConfigUtil.getString(APP_NAME, "track_Url");
	//权限平台接口密钥
	public static final String SECRET_ID = ConfigUtil.getString(APP_NAME, "SECRET_ID");
	/**
	 * 反馈状态
	 */
	public final static int ZX_STATUS_NOT = 1;     //未接收
	public final static int ZX_STATUS_DOING = 2;     //已接收
	
	//http-jy
	public static final String HTTP_JY = ConfigUtil.getString(APP_NAME,"HTTP_JY" ,"http://cssit.midea.com/c-css/views/uedSup/userfeedback/suggestion_list.jsp?identity=mipTodo");
	//http-zx
	public static final String HTTP_ZX = ConfigUtil.getString(APP_NAME, "HTTP_ZX","http://cssit.midea.com/c-css/views/uedSup/userfeedback/complaint/complaint_list.jsp?identity=mipTodo");

	public static String getLogoutUrl = ConfigUtil.getString(APP_NAME, "GET_LOGOUT_URL");
	
	public static String CX_SSO_LOGIN_URL = ConfigUtil.getString(APP_NAME, "CX_SSO_LOGIN_URL");
	
	//是否开启统一认证
	public static String isOPenSso = ConfigUtil.getString(APP_NAME, "IS_OPEN_SSO");

	/**
	 * webrtc网关内网地址
	 */
	public static String webrtcInUrl (){
		return   ParamUtil.getParam(APP_NAME, "WEBRTC_IN_URL","https://************/airauthgw/openApi");
	}
	/**
	 * webrtc网关外网地址
	 */
	public static String webrtcOutUrl (){
		return ParamUtil.getParam(APP_NAME, "WEBRTC_OUT_URL","https://************/airauthgw/openApi");
	}
	/**
	 *webrtc客户端类型
	 */
	public static String webrtcSender (){
		return ParamUtil.getParam(APP_NAME, "WEBRTC_SENDER","YUNQU_VIDEO_PETRA_152");
	}
	/**
	 *webrtc通信key
	 */
	public static String webrtc3DESKey  (){
		return ParamUtil.getParam(APP_NAME, "WEBRTC_3DES_KEY","webrtc_3des_key_12345678");
	}
	/**
	 *webrtc通信密码
	 */
	public static String webrtc3DESPassword (){
		return 	ParamUtil.getParam(APP_NAME, "WEBRTC_3DES_PASSWORD","marspasswd");
	}

	/**
	 *三方webrtc客户端类型
	 */
	public static String webrtcSenderSf (){
		return ParamUtil.getParam(APP_NAME, "WEBRTC_SENDER_SF","YUNQU_VIDEO_PETRA_monitor");
	}
	/**
	 *三方webrtc通信key
	 */
	public static String webrtc3DESKeySf  (){
		return ParamUtil.getParam(APP_NAME, "WEBRTC_3DES_KEY_SF","webrtc_3des_key_12345678");
	}
	/**
	 *三方webrtc通信密码
	 */
	public static String webrtc3DESPasswordSf (){
		return 	ParamUtil.getParam(APP_NAME, "WEBRTC_3DES_PASSWORD_SF","marspasswd");
	}

	/**
	 *判断是内网还是外网 1-内网 2-外网
	 */
	public static String isInOrOutsideNode (){
		return ParamUtil.getParam(APP_NAME, "IS_IN_OR_OUTSIDE");
	}

	/**
	 * 满足发起视频满意度的会员等级
	 * 美的会员  钻石会员-v6 铂金会员-v5 黄金会员-v4 白银会员-v3 美的会员-v2 普通会员-v1
	 * colmo会员  黑金会员-cv6 钻石会员-cv5 铂金会员-cv4 黄金会员-cv3 白银会员-cv2
	 * 未知会员    星耀会员lv5 至尊会员-lv4 钻石会员-lv3 黄金会员-lv2
	 */
	public static String getHyLevel (){
		return ParamUtil.getParam(APP_NAME, "ALLOW_VIDEO_HY_LEVEL","cv2,cv3,cv4,cv5,cv6");
	}


	//获取访问白名单
	public static String getWhiteList() {
		return ConfigUtil.getString(APP_NAME, "WHITE_LIST");
	}

	//获取5G短信模板编号(约定好按这个配置)
	public static String MSG_CODE = "5G_VIDEO_SMS_1";

	//获取事业部ID
	public static String getModelId() {
		return ConfigUtil.getString(APP_NAME, "MODEL_ID");
	}

	//获取品牌ID
	public static String getCategoryId() {
		return ConfigUtil.getString(APP_NAME, "CATEGORY_ID");
	}

	/**
	 * 短信验证码有效期
	 */
	public static String getSmsIsExpire (){
		return ParamUtil.getParam(APP_NAME, "CC_SMS_IS_EXPIRE","300");
	}


	/**
	 * 火山埋点地址
	 */
	public static String getHsCollectAddr (){
		return ConfigUtil.getString(APP_NAME, "HS_COLLECT_ADDR");
	}

	/**
	 * 是否开启5g视频 0-不开启 1-开启
	 */
	public static String isOpen5gVideo (){
		return ConfigUtil.getString(APP_NAME, "IS_OPEN_5G_VIDEO","0");
	}

	//获取jsessionId的key
	public static String getJSessionKey() {
		return ConfigUtil.getString(APP_NAME, "JSESSIONID_KEY");
	}

	public final static String CC_EP_CODE = ConfigUtil.getString(APP_NAME, "CC_EP_CODE", "001");

}
